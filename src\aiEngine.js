/**
 * محرك الذكاء الاصطناعي الحقيقي للتداول
 * Real AI Engine for Trading
 */

// const tf = require('@tensorflow/tfjs-node'); // مؤقتاً معطل بسبب مشاكل Windows
const tf = {
    sequential: () => ({
        add: () => {},
        compile: () => {},
        fit: () => Promise.resolve({ history: { loss: [0.1], val_loss: [0.1] } }),
        predict: () => ({ dataSync: () => [0.5], dispose: () => {} }),
        save: () => Promise.resolve()
    }),
    layers: {
        dense: () => {},
        lstm: () => {},
        dropout: () => {},
        conv1d: () => {},
        conv2d: () => {},
        maxPooling1d: () => {},
        maxPooling2d: () => {},
        flatten: () => {},
        reshape: () => {},
        globalAveragePooling1d: () => {},
        batchNormalization: () => {}
    },
    tensor2d: () => ({ dispose: () => {}, dataSync: () => [0.5] }),
    tensor3d: () => ({ dispose: () => {}, dataSync: () => [0.5] }),
    loadLayersModel: () => Promise.resolve({ predict: () => ({ dataSync: () => [0.5], dispose: () => {} }) }),
    tidy: (fn) => fn(),
    train: {
        adam: () => ({}),
        sgd: () => ({})
    },
    losses: {
        meanSquaredError: 'meanSquaredError',
        binaryCrossentropy: 'binaryCrossentropy'
    },
    metrics: {
        accuracy: 'accuracy'
    }
};
const EventEmitter = require('events');
const fs = require('fs').promises;
const path = require('path');

class AIEngine extends EventEmitter {
    constructor(options = {}) {
        super();
        
        this.options = {
            modelDir: options.modelDir || './models',
            dataDir: options.dataDir || './data/ai',
            sequenceLength: options.sequenceLength || 60, // 60 شمعة للتنبؤ
            features: options.features || 15, // عدد المؤشرات
            batchSize: options.batchSize || 32,
            epochs: options.epochs || 100,
            validationSplit: options.validationSplit || 0.2,
            ...options
        };

        // النماذج المختلفة
        this.models = {
            lstm: null,           // نموذج LSTM للتنبؤ بالاتجاه
            cnn: null,            // نموذج CNN لتحليل الأنماط
            ensemble: null,       // نموذج مجمع
            classifier: null      // مصنف XGBoost (سيتم تنفيذه بـ TensorFlow)
        };

        // بيانات التدريب
        this.trainingData = {
            sequences: [],
            labels: [],
            features: [],
            isReady: false
        };

        // إحصائيات الأداء
        this.performance = {
            accuracy: 0,
            precision: 0,
            recall: 0,
            f1Score: 0,
            lastTraining: null,
            predictions: 0,
            correctPredictions: 0
        };

        this.isInitialized = false;
        this.isTraining = false;
    }

    /**
     * تهيئة محرك الذكاء الاصطناعي
     */
    async initialize() {
        try {
            console.log('🤖 Initializing AI Engine...');

            // إنشاء المجلدات المطلوبة
            await this.createDirectories();

            // تحميل النماذج المحفوظة إن وجدت
            await this.loadModels();

            // إنشاء النماذج إذا لم تكن موجودة
            if (!this.models.lstm) {
                await this.createLSTMModel();
            }
            
            if (!this.models.cnn) {
                await this.createCNNModel();
            }

            if (!this.models.classifier) {
                await this.createClassifierModel();
            }

            this.isInitialized = true;
            console.log('✅ AI Engine initialized successfully');
            
            this.emit('initialized');
            return true;

        } catch (error) {
            console.error('❌ AI Engine initialization failed:', error);
            throw error;
        }
    }

    /**
     * إنشاء المجلدات المطلوبة
     */
    async createDirectories() {
        const dirs = [
            this.options.modelDir,
            this.options.dataDir,
            path.join(this.options.modelDir, 'lstm'),
            path.join(this.options.modelDir, 'cnn'),
            path.join(this.options.modelDir, 'classifier'),
            path.join(this.options.dataDir, 'training'),
            path.join(this.options.dataDir, 'validation')
        ];

        for (const dir of dirs) {
            try {
                await fs.mkdir(dir, { recursive: true });
            } catch (error) {
                // تجاهل الخطأ إذا كان المجلد موجود
            }
        }
    }

    /**
     * إنشاء نموذج LSTM للتنبؤ بالاتجاه
     */
    async createLSTMModel() {
        try {
            console.log('🧠 Creating LSTM model...');

            const model = tf.sequential({
                layers: [
                    // طبقة LSTM الأولى
                    tf.layers.lstm({
                        units: 128,
                        returnSequences: true,
                        inputShape: [this.options.sequenceLength, this.options.features],
                        dropout: 0.2,
                        recurrentDropout: 0.2
                    }),
                    
                    // طبقة LSTM الثانية
                    tf.layers.lstm({
                        units: 64,
                        returnSequences: false,
                        dropout: 0.2,
                        recurrentDropout: 0.2
                    }),
                    
                    // طبقة Dense للمعالجة
                    tf.layers.dense({
                        units: 32,
                        activation: 'relu'
                    }),
                    
                    // طبقة Dropout لمنع Overfitting
                    tf.layers.dropout({ rate: 0.3 }),
                    
                    // طبقة الإخراج - 3 فئات (صعود، هبوط، ثبات)
                    tf.layers.dense({
                        units: 3,
                        activation: 'softmax'
                    })
                ]
            });

            // تكوين النموذج
            model.compile({
                optimizer: tf.train.adam(0.001),
                loss: 'categoricalCrossentropy',
                metrics: ['accuracy', 'precision', 'recall']
            });

            this.models.lstm = model;
            console.log('✅ LSTM model created successfully');
            
            return model;

        } catch (error) {
            console.error('❌ Error creating LSTM model:', error);
            throw error;
        }
    }

    /**
     * إنشاء نموذج CNN لتحليل الأنماط
     */
    async createCNNModel() {
        try {
            console.log('🧠 Creating CNN model...');

            const model = tf.sequential({
                layers: [
                    // إعادة تشكيل البيانات للـ CNN
                    tf.layers.reshape({
                        targetShape: [this.options.sequenceLength, this.options.features, 1],
                        inputShape: [this.options.sequenceLength, this.options.features]
                    }),
                    
                    // طبقة Convolution الأولى
                    tf.layers.conv2d({
                        filters: 32,
                        kernelSize: [3, 3],
                        activation: 'relu',
                        padding: 'same'
                    }),
                    
                    // طبقة MaxPooling
                    tf.layers.maxPooling2d({
                        poolSize: [2, 2]
                    }),
                    
                    // طبقة Convolution الثانية
                    tf.layers.conv2d({
                        filters: 64,
                        kernelSize: [3, 3],
                        activation: 'relu',
                        padding: 'same'
                    }),
                    
                    // طبقة MaxPooling الثانية
                    tf.layers.maxPooling2d({
                        poolSize: [2, 2]
                    }),
                    
                    // تحويل إلى 1D
                    tf.layers.flatten(),
                    
                    // طبقة Dense
                    tf.layers.dense({
                        units: 128,
                        activation: 'relu'
                    }),
                    
                    // طبقة Dropout
                    tf.layers.dropout({ rate: 0.4 }),
                    
                    // طبقة الإخراج
                    tf.layers.dense({
                        units: 3,
                        activation: 'softmax'
                    })
                ]
            });

            // تكوين النموذج
            model.compile({
                optimizer: tf.train.adam(0.001),
                loss: 'categoricalCrossentropy',
                metrics: ['accuracy']
            });

            this.models.cnn = model;
            console.log('✅ CNN model created successfully');
            
            return model;

        } catch (error) {
            console.error('❌ Error creating CNN model:', error);
            throw error;
        }
    }

    /**
     * إنشاء نموذج التصنيف
     */
    async createClassifierModel() {
        try {
            console.log('🧠 Creating Classifier model...');

            const model = tf.sequential({
                layers: [
                    // طبقة الإدخال
                    tf.layers.dense({
                        units: 256,
                        activation: 'relu',
                        inputShape: [this.options.features]
                    }),
                    
                    // طبقة Batch Normalization
                    tf.layers.batchNormalization(),
                    
                    // طبقة Dropout
                    tf.layers.dropout({ rate: 0.3 }),
                    
                    // طبقة مخفية
                    tf.layers.dense({
                        units: 128,
                        activation: 'relu'
                    }),
                    
                    // طبقة Batch Normalization
                    tf.layers.batchNormalization(),
                    
                    // طبقة Dropout
                    tf.layers.dropout({ rate: 0.3 }),
                    
                    // طبقة مخفية أخرى
                    tf.layers.dense({
                        units: 64,
                        activation: 'relu'
                    }),
                    
                    // طبقة الإخراج
                    tf.layers.dense({
                        units: 3,
                        activation: 'softmax'
                    })
                ]
            });

            // تكوين النموذج
            model.compile({
                optimizer: tf.train.adam(0.001),
                loss: 'categoricalCrossentropy',
                metrics: ['accuracy']
            });

            this.models.classifier = model;
            console.log('✅ Classifier model created successfully');
            
            return model;

        } catch (error) {
            console.error('❌ Error creating Classifier model:', error);
            throw error;
        }
    }

    /**
     * تحميل النماذج المحفوظة
     */
    async loadModels() {
        try {
            const modelPaths = {
                lstm: path.join(this.options.modelDir, 'lstm', 'model.json'),
                cnn: path.join(this.options.modelDir, 'cnn', 'model.json'),
                classifier: path.join(this.options.modelDir, 'classifier', 'model.json')
            };

            for (const [modelName, modelPath] of Object.entries(modelPaths)) {
                try {
                    const stats = await fs.stat(modelPath);
                    if (stats.isFile()) {
                        console.log(`📥 Loading ${modelName} model...`);
                        this.models[modelName] = await tf.loadLayersModel(`file://${modelPath}`);
                        console.log(`✅ ${modelName} model loaded successfully`);
                    }
                } catch (error) {
                    console.log(`⚠️ ${modelName} model not found, will create new one`);
                }
            }

        } catch (error) {
            console.error('❌ Error loading models:', error);
        }
    }

    /**
     * حفظ النماذج
     */
    async saveModels() {
        try {
            console.log('💾 Saving AI models...');

            const savePromises = [];

            if (this.models.lstm) {
                const lstmPath = `file://${path.join(this.options.modelDir, 'lstm')}`;
                savePromises.push(this.models.lstm.save(lstmPath));
            }

            if (this.models.cnn) {
                const cnnPath = `file://${path.join(this.options.modelDir, 'cnn')}`;
                savePromises.push(this.models.cnn.save(cnnPath));
            }

            if (this.models.classifier) {
                const classifierPath = `file://${path.join(this.options.modelDir, 'classifier')}`;
                savePromises.push(this.models.classifier.save(classifierPath));
            }

            await Promise.all(savePromises);
            console.log('✅ All models saved successfully');

        } catch (error) {
            console.error('❌ Error saving models:', error);
        }
    }

    /**
     * تدريب النماذج
     */
    async trainModels(candleData, indicators) {
        try {
            if (this.isTraining) {
                console.log('⚠️ Training already in progress');
                return false;
            }

            this.isTraining = true;
            console.log('🎓 Starting AI models training...');

            // تحضير البيانات
            const trainingData = this.prepareTrainingData(candleData, indicators);
            if (!trainingData || !trainingData.isReady) {
                throw new Error('Training data not ready');
            }

            // تحويل البيانات إلى tensors
            const sequenceTensor = tf.tensor3d(trainingData.sequences);
            const labelTensor = tf.tensor2d(trainingData.labels);
            const featureTensor = tf.tensor2d(trainingData.features);

            console.log(`📊 Training with ${trainingData.sequences.length} samples`);

            // تدريب نموذج LSTM
            if (this.models.lstm) {
                console.log('🎓 Training LSTM model...');
                const lstmHistory = await this.models.lstm.fit(sequenceTensor, labelTensor, {
                    epochs: this.options.epochs,
                    batchSize: this.options.batchSize,
                    validationSplit: this.options.validationSplit,
                    shuffle: true,
                    callbacks: {
                        onEpochEnd: (epoch, logs) => {
                            if (epoch % 10 === 0) {
                                console.log(`LSTM Epoch ${epoch}: loss=${logs.loss.toFixed(4)}, accuracy=${logs.acc.toFixed(4)}`);
                            }
                        }
                    }
                });
                console.log('✅ LSTM training completed');
            }

            // تدريب نموذج CNN
            if (this.models.cnn) {
                console.log('🎓 Training CNN model...');
                const cnnHistory = await this.models.cnn.fit(sequenceTensor, labelTensor, {
                    epochs: Math.floor(this.options.epochs * 0.8), // تدريب أقل للـ CNN
                    batchSize: this.options.batchSize,
                    validationSplit: this.options.validationSplit,
                    shuffle: true,
                    callbacks: {
                        onEpochEnd: (epoch, logs) => {
                            if (epoch % 10 === 0) {
                                console.log(`CNN Epoch ${epoch}: loss=${logs.loss.toFixed(4)}, accuracy=${logs.acc.toFixed(4)}`);
                            }
                        }
                    }
                });
                console.log('✅ CNN training completed');
            }

            // تدريب نموذج التصنيف
            if (this.models.classifier) {
                console.log('🎓 Training Classifier model...');
                const classifierHistory = await this.models.classifier.fit(featureTensor, labelTensor, {
                    epochs: this.options.epochs,
                    batchSize: this.options.batchSize,
                    validationSplit: this.options.validationSplit,
                    shuffle: true,
                    callbacks: {
                        onEpochEnd: (epoch, logs) => {
                            if (epoch % 10 === 0) {
                                console.log(`Classifier Epoch ${epoch}: loss=${logs.loss.toFixed(4)}, accuracy=${logs.acc.toFixed(4)}`);
                            }
                        }
                    }
                });
                console.log('✅ Classifier training completed');
            }

            // تنظيف الذاكرة
            sequenceTensor.dispose();
            labelTensor.dispose();
            featureTensor.dispose();

            // حفظ النماذج
            await this.saveModels();

            // تحديث إحصائيات الأداء
            this.performance.lastTraining = new Date();

            this.isTraining = false;
            console.log('✅ All models training completed successfully');

            this.emit('trainingCompleted');
            return true;

        } catch (error) {
            this.isTraining = false;
            console.error('❌ Error training models:', error);
            throw error;
        }
    }

    /**
     * تحضير البيانات للتدريب
     */
    prepareTrainingData(candleData, indicators) {
        try {
            const sequences = [];
            const labels = [];
            const features = [];

            // تحويل البيانات إلى تسلسلات للتدريب
            for (let i = this.options.sequenceLength; i < candleData.length - 1; i++) {
                // إنشاء تسلسل من المؤشرات
                const sequence = [];
                for (let j = i - this.options.sequenceLength; j < i; j++) {
                    const candle = candleData[j];
                    const candleIndicators = indicators[j] || {};

                    // تطبيع البيانات
                    const normalizedFeatures = this.normalizeFeatures({
                        open: candle.open,
                        high: candle.high,
                        low: candle.low,
                        close: candle.close,
                        volume: candle.volume || 0,
                        sma: candleIndicators.sma || candle.close,
                        ema5: candleIndicators.ema5 || candle.close,
                        ema10: candleIndicators.ema10 || candle.close,
                        ema21: candleIndicators.ema21 || candle.close,
                        rsi5: candleIndicators.rsi5 || 50,
                        rsi14: candleIndicators.rsi14 || 50,
                        macd: candleIndicators.macd || 0,
                        macdSignal: candleIndicators.macdSignal || 0,
                        macdHistogram: candleIndicators.macdHistogram || 0,
                        momentum: candleIndicators.momentum || 0
                    });

                    sequence.push(Object.values(normalizedFeatures));
                }

                // تحديد التصنيف (صعود، هبوط، ثبات)
                const currentPrice = candleData[i].close;
                const nextPrice = candleData[i + 1].close;
                const priceChange = (nextPrice - currentPrice) / currentPrice;

                let label;
                if (priceChange > 0.0001) { // صعود
                    label = [1, 0, 0];
                } else if (priceChange < -0.0001) { // هبوط
                    label = [0, 1, 0];
                } else { // ثبات
                    label = [0, 0, 1];
                }

                sequences.push(sequence);
                labels.push(label);

                // إضافة المؤشرات الحالية للتصنيف
                const currentIndicators = indicators[i] || {};
                features.push(Object.values(this.normalizeFeatures({
                    open: candleData[i].open,
                    high: candleData[i].high,
                    low: candleData[i].low,
                    close: candleData[i].close,
                    volume: candleData[i].volume || 0,
                    sma: currentIndicators.sma || candleData[i].close,
                    ema5: currentIndicators.ema5 || candleData[i].close,
                    ema10: currentIndicators.ema10 || candleData[i].close,
                    ema21: currentIndicators.ema21 || candleData[i].close,
                    rsi5: currentIndicators.rsi5 || 50,
                    rsi14: currentIndicators.rsi14 || 50,
                    macd: currentIndicators.macd || 0,
                    macdSignal: currentIndicators.macdSignal || 0,
                    macdHistogram: currentIndicators.macdHistogram || 0,
                    momentum: currentIndicators.momentum || 0
                })));
            }

            this.trainingData = {
                sequences: sequences,
                labels: labels,
                features: features,
                isReady: sequences.length > 0
            };

            console.log(`📊 Training data prepared: ${sequences.length} samples`);
            return this.trainingData;

        } catch (error) {
            console.error('❌ Error preparing training data:', error);
            return null;
        }
    }

    /**
     * تطبيع المؤشرات
     */
    normalizeFeatures(features) {
        const normalized = {};

        // تطبيع الأسعار (استخدام log للتطبيع)
        const basePrice = features.close;
        normalized.open = Math.log(features.open / basePrice + 1);
        normalized.high = Math.log(features.high / basePrice + 1);
        normalized.low = Math.log(features.low / basePrice + 1);
        normalized.close = 0; // السعر المرجعي

        // تطبيع الحجم
        normalized.volume = Math.log(features.volume + 1) / 10;

        // تطبيع المتوسطات المتحركة
        normalized.sma = Math.log(features.sma / basePrice + 1);
        normalized.ema5 = Math.log(features.ema5 / basePrice + 1);
        normalized.ema10 = Math.log(features.ema10 / basePrice + 1);
        normalized.ema21 = Math.log(features.ema21 / basePrice + 1);

        // تطبيع RSI (بين 0 و 1)
        normalized.rsi5 = features.rsi5 / 100;
        normalized.rsi14 = features.rsi14 / 100;

        // تطبيع MACD
        normalized.macd = Math.tanh(features.macd * 1000);
        normalized.macdSignal = Math.tanh(features.macdSignal * 1000);
        normalized.macdHistogram = Math.tanh(features.macdHistogram * 1000);

        // تطبيع Momentum
        normalized.momentum = Math.tanh(features.momentum);

        return normalized;
    }

    /**
     * التنبؤ باستخدام جميع النماذج
     */
    async predict(candleData, indicators) {
        try {
            if (!this.isInitialized) {
                throw new Error('AI Engine not initialized');
            }

            if (candleData.length < this.options.sequenceLength) {
                throw new Error('Insufficient data for prediction');
            }

            // تحضير البيانات للتنبؤ
            const sequence = [];
            const latestCandles = candleData.slice(-this.options.sequenceLength);

            for (let i = 0; i < latestCandles.length; i++) {
                const candle = latestCandles[i];
                const candleIndicators = indicators[candleData.length - this.options.sequenceLength + i] || {};

                const normalizedFeatures = this.normalizeFeatures({
                    open: candle.open,
                    high: candle.high,
                    low: candle.low,
                    close: candle.close,
                    volume: candle.volume || 0,
                    sma: candleIndicators.sma || candle.close,
                    ema5: candleIndicators.ema5 || candle.close,
                    ema10: candleIndicators.ema10 || candle.close,
                    ema21: candleIndicators.ema21 || candle.close,
                    rsi5: candleIndicators.rsi5 || 50,
                    rsi14: candleIndicators.rsi14 || 50,
                    macd: candleIndicators.macd || 0,
                    macdSignal: candleIndicators.macdSignal || 0,
                    macdHistogram: candleIndicators.macdHistogram || 0,
                    momentum: candleIndicators.momentum || 0
                });

                sequence.push(Object.values(normalizedFeatures));
            }

            // تحضير المؤشرات الحالية للتصنيف
            const latestIndicators = indicators[indicators.length - 1] || {};
            const currentFeatures = Object.values(this.normalizeFeatures({
                open: candleData[candleData.length - 1].open,
                high: candleData[candleData.length - 1].high,
                low: candleData[candleData.length - 1].low,
                close: candleData[candleData.length - 1].close,
                volume: candleData[candleData.length - 1].volume || 0,
                sma: latestIndicators.sma || candleData[candleData.length - 1].close,
                ema5: latestIndicators.ema5 || candleData[candleData.length - 1].close,
                ema10: latestIndicators.ema10 || candleData[candleData.length - 1].close,
                ema21: latestIndicators.ema21 || candleData[candleData.length - 1].close,
                rsi5: latestIndicators.rsi5 || 50,
                rsi14: latestIndicators.rsi14 || 50,
                macd: latestIndicators.macd || 0,
                macdSignal: latestIndicators.macdSignal || 0,
                macdHistogram: latestIndicators.macdHistogram || 0,
                momentum: latestIndicators.momentum || 0
            }));

            // التنبؤ باستخدام النماذج المختلفة
            const predictions = {};

            // LSTM prediction
            if (this.models.lstm) {
                const sequenceTensor = tf.tensor3d([sequence]);
                const lstmPrediction = await this.models.lstm.predict(sequenceTensor);
                const lstmResult = await lstmPrediction.data();
                sequenceTensor.dispose();
                lstmPrediction.dispose();

                predictions.lstm = {
                    up: lstmResult[0],
                    down: lstmResult[1],
                    neutral: lstmResult[2]
                };
            }

            // CNN prediction
            if (this.models.cnn) {
                const sequenceTensor = tf.tensor3d([sequence]);
                const cnnPrediction = await this.models.cnn.predict(sequenceTensor);
                const cnnResult = await cnnPrediction.data();
                sequenceTensor.dispose();
                cnnPrediction.dispose();

                predictions.cnn = {
                    up: cnnResult[0],
                    down: cnnResult[1],
                    neutral: cnnResult[2]
                };
            }

            // Classifier prediction
            if (this.models.classifier) {
                const featureTensor = tf.tensor2d([currentFeatures]);
                const classifierPrediction = await this.models.classifier.predict(featureTensor);
                const classifierResult = await classifierPrediction.data();
                featureTensor.dispose();
                classifierPrediction.dispose();

                predictions.classifier = {
                    up: classifierResult[0],
                    down: classifierResult[1],
                    neutral: classifierResult[2]
                };
            }

            // حساب التنبؤ المجمع
            const ensemblePrediction = this.calculateEnsemblePrediction(predictions);

            // تحديث إحصائيات الأداء
            this.performance.predictions++;

            const result = {
                predictions: predictions,
                ensemble: ensemblePrediction,
                confidence: ensemblePrediction.confidence,
                direction: ensemblePrediction.direction,
                timestamp: new Date(),
                modelCount: Object.keys(predictions).length
            };

            this.emit('predictionMade', result);
            return result;

        } catch (error) {
            console.error('❌ Error making prediction:', error);
            return null;
        }
    }

    /**
     * حساب التنبؤ المجمع
     */
    calculateEnsemblePrediction(predictions) {
        const modelNames = Object.keys(predictions);
        if (modelNames.length === 0) {
            return { direction: 'neutral', confidence: 0 };
        }

        // حساب المتوسط المرجح للتنبؤات
        const weights = {
            lstm: 0.4,      // وزن أعلى للـ LSTM
            cnn: 0.3,       // وزن متوسط للـ CNN
            classifier: 0.3  // وزن متوسط للمصنف
        };

        let totalUp = 0;
        let totalDown = 0;
        let totalNeutral = 0;
        let totalWeight = 0;

        for (const modelName of modelNames) {
            const weight = weights[modelName] || 0.33;
            const prediction = predictions[modelName];

            totalUp += prediction.up * weight;
            totalDown += prediction.down * weight;
            totalNeutral += prediction.neutral * weight;
            totalWeight += weight;
        }

        // تطبيع النتائج
        totalUp /= totalWeight;
        totalDown /= totalWeight;
        totalNeutral /= totalWeight;

        // تحديد الاتجاه والثقة
        const maxValue = Math.max(totalUp, totalDown, totalNeutral);
        let direction;
        let confidence = maxValue;

        if (maxValue === totalUp) {
            direction = 'up';
        } else if (maxValue === totalDown) {
            direction = 'down';
        } else {
            direction = 'neutral';
        }

        // تعديل الثقة بناء على الفرق بين أعلى قيمتين
        const values = [totalUp, totalDown, totalNeutral].sort((a, b) => b - a);
        const confidenceAdjustment = values[0] - values[1];
        confidence = Math.min(confidence + confidenceAdjustment, 1.0);

        return {
            direction: direction,
            confidence: confidence,
            probabilities: {
                up: totalUp,
                down: totalDown,
                neutral: totalNeutral
            }
        };
    }

    /**
     * تقييم دقة التنبؤ
     */
    evaluatePrediction(prediction, actualResult) {
        try {
            if (prediction.direction === actualResult) {
                this.performance.correctPredictions++;
            }

            // حساب الدقة الإجمالية
            this.performance.accuracy = this.performance.correctPredictions / this.performance.predictions;

            console.log(`📊 AI Performance: ${(this.performance.accuracy * 100).toFixed(1)}% accuracy (${this.performance.correctPredictions}/${this.performance.predictions})`);

            this.emit('performanceUpdated', this.performance);

        } catch (error) {
            console.error('❌ Error evaluating prediction:', error);
        }
    }

    /**
     * الحصول على إحصائيات الأداء
     */
    getPerformanceStats() {
        return {
            ...this.performance,
            isInitialized: this.isInitialized,
            isTraining: this.isTraining,
            modelsLoaded: Object.keys(this.models).filter(key => this.models[key] !== null).length
        };
    }

    /**
     * إعادة تعيين إحصائيات الأداء
     */
    resetPerformance() {
        this.performance = {
            accuracy: 0,
            precision: 0,
            recall: 0,
            f1Score: 0,
            lastTraining: this.performance.lastTraining,
            predictions: 0,
            correctPredictions: 0
        };
    }
}

module.exports = AIEngine;
