# ANGLE Development Update - July 4, 2012

We haven't posted an update on the development status of ANGLE in quite some
time and we'd like to provide an update on some of the new features and
improvements that we've been working on.

## Conformance

As announced in the [Chromium Blog]
(http://blog.chromium.org/2011/11/opengl-es-20-certification-for-angle.html),
ANGLE v1.0 has passed the Khronos OpenGL ES 2.0 certification process and is now
a [conformant](http://www.khronos.org/conformance/adopters/conformant-products/)
OpenGL ES 2.0 implementation.

## Extensions

We have recently completed the implementation of depth texture support
([ANGLE\_depth\_texture]
(https://code.google.com/p/angleproject/source/browse/extensions/ANGLE_depth_texture.txt?name=master))
and earlier in the year we added support for instancing via attribute array
divisors ([ANGLE\_instanced\_arrays]
(https://code.google.com/p/angleproject/source/browse/extensions/ANGLE_instanced_arrays.txt?name=master)).
See ExtensionSupport for a complete list of extensions that are supported by
ANGLE.

## Shader Compiler

We have also made a number of improvements in the shader compiler.

* We addressed a number of defects related to scoping differences between HLSL and
GLSL and improved the scoping support in ANGLE's compiler front-end. We also
worked with The Khronos Group to get an ESSL spec bug fixed and several items
clarified.
* We addressed a number of correctness issues in the GLSL to HLSL
translation process. We fixed some bugs related to constant propagation and
comma conditional assignments. More importantly, we fully implemented support
for short-circuiting boolean logic operations. In GLSL, Boolean expressions do
short-circuit evaluation as in C, but HLSL evaluates them entirely. This only
has an observable effect if a short-circuited operation has side effects, such
as a function call that modifies global variables.
* We implemented detection
for discontinuous gradient or derivative computations inside loops and replace
them with explicitly defined continuous behaviour. HLSL and GLSL differ in their
specified behaviour for operations which compute gradients or derivatives.
Gradients are computed by texture sampling functions which don't specify a
specific mipmap LOD level, and by the OES\_standard\_derivatives built-in
functions. To determine the gradient, the corresponding values in neighbouring
pixels are differentiated. If neighbouring pixels execute different paths
through the shader this can cause a discontinuity in the gradient. GLSL
specifies that in these cases the gradient is undefined. HLSL tries to avoid the
discontinuity in the compiler by unrolling loops so that every pixel executes
all iterations. This can make the D3D HLSL compiler spend a long time generating
code permutations, and possibly even fail compilation due to running out of
instruction slots or registers. Because the GLSL specification allows undefined
behaviour, we can define such texture sampling functions to use mipmap LOD level
0, and have the derivatives functions return 0.0. To do this we examine the GLSL
code's abstract syntax tree and detect whether the shader contains any loops
with discontinuities and gradient operations. Within such loops, we generate
HLSL code that uses explicitly defined texture LODs and derivative information.
One additional consideration is that within these loops there can be calls to
user-defined functions which may contain gradient operations. In this case, we
generate variants of user-defined functions where these operations are
explicitly defined. We use these new functions instead of the original ones in
loops with discontinuities.

These fixes result in ANGLE being able successfully compile a number of the more
complex shaders. Unfortunately there are still some complex shaders which we
have not yet been able to obtain solutions for. Ultimately Direct3D 9 SM3
shaders are more restricted than what can be expressed in GLSL.  Most of the
problematic shaders we've encountered will also not compile successfully on
current ES 2.0 implementations.  We would only be able to achieve parity with
Desktop GL implementations by using Direct3D 10 or above.

## Texture Origin Changes

We have also made a major change to ANGLE in the way the origin difference
between D3D and OpenGL is handled. This difference is normally observable when
using render-to-texture techniques, and if not accounted for, it would appear
that images rendered to textures are upside down. In recent versions of ANGLE
(r536 (on Google Code)-r1161 (on Google Code)), we have been storing surfaces
following the D3D Y convention where (0, 0) is the top-left, rather than GL's
bottom-left convention. This was done by vertically flipping textures on load
and then adjusting the texture coordinates in the shaders to compensate. This
approach worked well, but it did leave the orientation of pbuffers inverted when
compared to native GL implementations. As of ANGLE r1162 (on Google Code), we
have changed this back to the original way it was implemented - textures are
loaded and stored in the GL orientation, and the final rendered scene is flipped
when it is displayed to a window by eglSwapBuffers. This should be essentially
transparent to applications except that orientation of pbuffers will change.  In
addition to fixing the pbuffer orientation, this change:

* eliminates
dependent-texture look-ups in the shaders, caused by flipping the texture
y-coordinates
* rounding of texture coordinates (while previously within spec)
will be more consistent with other implementations, and
* allows potential
faster paths for loading texture data to be implemented. The only potential
downside to this approach is that window-based rendering may be a bit slower for
simple scenes. The good news is that this path is not used by browser
implementations on most versions of Windows.

## Preprocessor

Finally, Alok P. from Google has been working on implementing a new shader
preprocessor for the last number of months and this effort is nearly complete.
This new preprocessor should be more robust and much more maintainable. It also
includes many (~5000) unit tests and passes all WebGL conformance tests. If you
wish to try this out before it is enabled by default, define
ANGLE\_USE\_NEW\_PREPROCESSOR=1 in your project settings for the
translator\_common project.

## Contributions

As always we welcome contributions either in the bug reports (preferably with an
isolated test-case) or in the form of code contributions. We have added a
[ContributingCode](ContributingCode.md) wiki page documenting the preferred
process for contributing code. We do need to ask that you sign a Contributor
License Agreement before we can integrate your patches.
