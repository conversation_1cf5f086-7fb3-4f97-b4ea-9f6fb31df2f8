/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../../index';
import { ALL_ENVS, describeWithFlags } from '../../jasmine_util';
import { expectArraysClose } from '../../test_util';
describeWithFlags('stringToHashBucketFast', ALL_ENVS, () => {
    it('throw error if negative buckets', async () => {
        expect(() => tf.string.stringToHashBucketFast(['a', 'b', 'c'], -1))
            .toThrowError(/must be at least 1/);
    });
    it('throw error if zero buckets', async () => {
        expect(() => tf.string.stringToHashBucketFast(['a', 'b', 'c'], 0))
            .toThrowError(/must be at least 1/);
    });
    it('one bucket maps values to zero', async () => {
        const result = tf.string.stringToHashBucketFast(['a', 'b', 'c'], 1);
        expectArraysClose(await result.data(), [0, 0, 0]);
    });
    it('multiple buckets', async () => {
        const result = tf.string.stringToHashBucketFast(['a', 'b', 'c', 'd'], 10);
        // fingerPrint64('a') -> 12917804110809363939 -> mod 10 -> 9
        // fingerPrint64('b') -> 11795596070477164822 -> mod 10 -> 2
        // fingerPrint64('c') -> 11430444447143000872 -> mod 10 -> 2
        // fingerPrint64('d') -> 4470636696479570465 -> mod 10 -> 5
        expectArraysClose(await result.data(), [9, 2, 2, 5]);
    });
    it('empty input', async () => {
        const result = tf.string.stringToHashBucketFast(tf.tensor1d([], 'string'), 2147483648);
        expectArraysClose(await result.data(), []);
    });
    it('preserve size', async () => {
        const result = tf.string.stringToHashBucketFast([[['a'], ['b']], [['c'], ['d']], [['a'], ['b']]], 10);
        expectArraysClose(await result.data(), [9, 2, 2, 5, 9, 2]);
        expect(result.shape).toEqual([3, 2, 1]);
    });
});
//# sourceMappingURL=data:application/json;base64,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