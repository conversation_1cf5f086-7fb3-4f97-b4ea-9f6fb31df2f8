/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
/// <amd-module name="@tensorflow/tfjs-core/dist/io/router_registry" />
import { IOHandler, LoadOptions } from './types';
export type IORouter = (url: string | string[], loadOptions?: LoadOptions) => IOHandler;
export declare class IORouterRegistry {
    private static instance;
    private saveRouters;
    private loadRouters;
    private constructor();
    private static getInstance;
    /**
     * Register a save-handler router.
     *
     * @param saveRouter A function that maps a URL-like string onto an instance
     * of `IOHandler` with the `save` method defined or `null`.
     */
    static registerSaveRouter(saveRouter: IORouter): void;
    /**
     * Register a load-handler router.
     *
     * @param loadRouter A function that maps a URL-like string onto an instance
     * of `IOHandler` with the `load` method defined or `null`.
     */
    static registerLoadRouter(loadRouter: IORouter): void;
    /**
     * Look up IOHandler for saving, given a URL-like string.
     *
     * @param url
     * @returns If only one match is found, an instance of IOHandler with the
     * `save` method defined. If no match is found, `null`.
     * @throws Error, if more than one match is found.
     */
    static getSaveHandlers(url: string | string[]): IOHandler[];
    /**
     * Look up IOHandler for loading, given a URL-like string.
     *
     * @param url
     * @param loadOptions Optional, custom load options.
     * @returns All valid handlers for `url`, given the currently registered
     *   handler routers.
     */
    static getLoadHandlers(url: string | string[], loadOptions?: LoadOptions): IOHandler[];
    private static getHandlers;
}
export declare const registerSaveRouter: (loudRouter: IORouter) => void;
export declare const registerLoadRouter: (loudRouter: IORouter) => void;
export declare const getSaveHandlers: (url: string | string[]) => IOHandler[];
export declare const getLoadHandlers: (url: string | string[], loadOptions?: LoadOptions) => IOHandler[];
