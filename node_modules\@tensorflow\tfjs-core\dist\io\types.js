/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
/* Type definitions for exporting and importing of models. */
/**
 * A map from Tensor dtype to number of bytes per element of the Tensor.
 */
export const DTYPE_VALUE_SIZE_MAP = {
    'float32': 4,
    'float16': 2,
    'int32': 4,
    'uint16': 2,
    'uint8': 1,
    'bool': 1,
    'complex64': 8
};
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidHlwZXMuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi90ZmpzLWNvcmUvc3JjL2lvL3R5cGVzLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7Ozs7Ozs7Ozs7R0FlRztBQUVILDZEQUE2RDtBQUU3RDs7R0FFRztBQUNILE1BQU0sQ0FBQyxNQUFNLG9CQUFvQixHQUE4QjtJQUM3RCxTQUFTLEVBQUUsQ0FBQztJQUNaLFNBQVMsRUFBRSxDQUFDO0lBQ1osT0FBTyxFQUFFLENBQUM7SUFDVixRQUFRLEVBQUUsQ0FBQztJQUNYLE9BQU8sRUFBRSxDQUFDO0lBQ1YsTUFBTSxFQUFFLENBQUM7SUFDVCxXQUFXLEVBQUUsQ0FBQztDQUNmLENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgMjAxOCBHb29nbGUgTExDLiBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqIGh0dHA6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKiA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICovXG5cbi8qIFR5cGUgZGVmaW5pdGlvbnMgZm9yIGV4cG9ydGluZyBhbmQgaW1wb3J0aW5nIG9mIG1vZGVscy4gKi9cblxuLyoqXG4gKiBBIG1hcCBmcm9tIFRlbnNvciBkdHlwZSB0byBudW1iZXIgb2YgYnl0ZXMgcGVyIGVsZW1lbnQgb2YgdGhlIFRlbnNvci5cbiAqL1xuZXhwb3J0IGNvbnN0IERUWVBFX1ZBTFVFX1NJWkVfTUFQOiB7W2R0eXBlOiBzdHJpbmddOiBudW1iZXJ9ID0ge1xuICAnZmxvYXQzMic6IDQsXG4gICdmbG9hdDE2JzogMixcbiAgJ2ludDMyJzogNCxcbiAgJ3VpbnQxNic6IDIsXG4gICd1aW50OCc6IDEsXG4gICdib29sJzogMSxcbiAgJ2NvbXBsZXg2NCc6IDhcbn07XG5cbi8qKlxuICogQSB3ZWlnaHQgbWFuaWZlc3QuXG4gKlxuICogVGhlIHdlaWdodCBtYW5pZmVzdCBjb25zaXN0cyBvZiBhbiBvcmRlcmVkIGxpc3Qgb2Ygd2VpZ2h0LW1hbmlmZXN0IGdyb3Vwcy5cbiAqIEVhY2ggd2VpZ2h0LW1hbmlmZXN0IGdyb3VwIChcImdyb3VwXCIgZm9yIHNob3J0IGhlcmVhZnRlcikgY29uc2lzdHMgb2YgYVxuICogbnVtYmVyIG9mIHdlaWdodCB2YWx1ZXMgc3RvcmVkIGluIGEgbnVtYmVyIG9mIHBhdGhzLlxuICogU2VlIHRoZSBkb2N1bWVudGF0aW9uIG9mIGBXZWlnaHRNYW5pZmVzdEdyb3VwQ29uZmlnYCBiZWxvdyBmb3IgbW9yZSBkZXRhaWxzLlxuICovXG5leHBvcnQgZGVjbGFyZSB0eXBlIFdlaWdodHNNYW5pZmVzdENvbmZpZyA9IFdlaWdodHNNYW5pZmVzdEdyb3VwQ29uZmlnW107XG5cbi8qKlxuICogQSB3ZWlnaHQtbWFuaWZlc3QgZ3JvdXAuXG4gKlxuICogQ29uc2lzdHMgb2YgYW4gb3JkZXJlZCBsaXN0IG9mIHdlaWdodCB2YWx1ZXMgZW5jb2RlZCBpbiBiaW5hcnkgZm9ybWF0LFxuICogc3RvcmVkIGluIGFuIG9yZGVyZWQgbGlzdCBvZiBwYXRocy5cbiAqL1xuZXhwb3J0IGRlY2xhcmUgaW50ZXJmYWNlIFdlaWdodHNNYW5pZmVzdEdyb3VwQ29uZmlnIHtcbiAgLyoqXG4gICAqIEFuIG9yZGVyZWQgbGlzdCBvZiBwYXRocy5cbiAgICpcbiAgICogUGF0aHMgYXJlIGludGVudGlvbmFsbHkgYWJzdHJhY3QgaW4gb3JkZXIgdG8gYmUgZ2VuZXJhbC4gRm9yIGV4YW1wbGUsIHRoZXlcbiAgICogY2FuIGJlIHJlbGF0aXZlIFVSTCBwYXRocyBvciByZWxhdGl2ZSBwYXRocyBvbiB0aGUgZmlsZSBzeXN0ZW0uXG4gICAqL1xuICBwYXRoczogc3RyaW5nW107XG5cbiAgLyoqXG4gICAqIFNwZWNpZmljYXRpb25zIG9mIHRoZSB3ZWlnaHRzIHN0b3JlZCBpbiB0aGUgcGF0aHMuXG4gICAqL1xuICB3ZWlnaHRzOiBXZWlnaHRzTWFuaWZlc3RFbnRyeVtdO1xufVxuXG4vKipcbiAqIEdyb3VwIHRvIHdoaWNoIHRoZSB3ZWlnaHQgYmVsb25ncy5cbiAqXG4gKiAtICdvcHRpbWl6ZXInOiBXZWlnaHQgZnJvbSBhIHN0YXRlZnVsIG9wdGltaXplci5cbiAqL1xuZXhwb3J0IHR5cGUgV2VpZ2h0R3JvdXAgPSAnbW9kZWwnfCdvcHRpbWl6ZXInO1xuXG4vKipcbiAqIEFuIGVudHJ5IGluIHRoZSB3ZWlnaHQgbWFuaWZlc3QuXG4gKlxuICogVGhlIGVudHJ5IGNvbnRhaW5zIHNwZWNpZmljYXRpb24gb2YgYSB3ZWlnaHQuXG4gKi9cbmV4cG9ydCBkZWNsYXJlIGludGVyZmFjZSBXZWlnaHRzTWFuaWZlc3RFbnRyeSB7XG4gIC8qKlxuICAgKiBOYW1lIG9mIHRoZSB3ZWlnaHQsIGUuZy4sICdEZW5zZV8xL2JpYXMnXG4gICAqL1xuICBuYW1lOiBzdHJpbmc7XG5cbiAgLyoqXG4gICAqIFNoYXBlIG9mIHRoZSB3ZWlnaHQuXG4gICAqL1xuICBzaGFwZTogbnVtYmVyW107XG5cbiAgLyoqXG4gICAqIERhdGEgdHlwZSBvZiB0aGUgd2VpZ2h0LlxuICAgKi9cbiAgZHR5cGU6ICdmbG9hdDMyJ3wnaW50MzInfCdib29sJ3wnc3RyaW5nJ3wnY29tcGxleDY0JztcblxuICAvKipcbiAgICogVHlwZSBvZiB0aGUgd2VpZ2h0LlxuICAgKlxuICAgKiBPcHRpb25hbC5cbiAgICpcbiAgICogVGhlIHZhbHVlICdvcHRpbWl6ZXInIGluZGljYXRlcyB0aGUgd2VpZ2h0IGJlbG9uZ3MgdG8gYW4gb3B0aW1pemVyXG4gICAqIChpLmUuLCB1c2VkIG9ubHkgZHVyaW5nIG1vZGVsIHRyYWluaW5nIGFuZCBub3QgZHVyaW5nIGluZmVyZW5jZSkuXG4gICAqL1xuICBncm91cD86IFdlaWdodEdyb3VwO1xuXG4gIC8qKlxuICAgKiBJbmZvcm1hdGlvbiBmb3IgZGVxdWFudGl6YXRpb24gb2YgdGhlIHdlaWdodC5cbiAgICovXG4gIHF1YW50aXphdGlvbj86IHtcbiAgICBzY2FsZT86IG51bWJlciwgIC8vIFRoZSBzY2FsaW5nIGNvbnN0YW50IHRvIG11bHRpcGx5IGJ5LlxuICAgIG1pbj86IG51bWJlciwgICAgLy8gVGhlIChwb3NzaWJseSBudWRnZWQpIG1pbmltdW0gd2VpZ2h0IHRvIGFkZC5cbiAgICAgICBkdHlwZTogJ3VpbnQxNid8J3VpbnQ4J3wnZmxvYXQxNicgIC8vIFRoZSBkdHlwZSBvZiB0aGUgcXVhbnRpemVkIHdlaWdodHMuXG4gIH07XG59XG5cbi8qKlxuICogT3B0aW9ucyBmb3Igc2F2aW5nIGEgbW9kZWwuXG4gKiBAaW5uYW1lc3BhY2UgaW9cbiAqL1xuZXhwb3J0IGludGVyZmFjZSBTYXZlQ29uZmlnIHtcbiAgLyoqXG4gICAqIFdoZXRoZXIgdG8gc2F2ZSBvbmx5IHRoZSB0cmFpbmFibGUgd2VpZ2h0cyBvZiB0aGUgbW9kZWwsIGlnbm9yaW5nIHRoZVxuICAgKiBub24tdHJhaW5hYmxlIG9uZXMuXG4gICAqL1xuICB0cmFpbmFibGVPbmx5PzogYm9vbGVhbjtcblxuICAvKipcbiAgICogV2hldGhlciB0aGUgb3B0aW1pemVyIHdpbGwgYmUgc2F2ZWQgKGlmIGV4aXN0cykuXG4gICAqXG4gICAqIERlZmF1bHQ6IGBmYWxzZWAuXG4gICAqL1xuICBpbmNsdWRlT3B0aW1pemVyPzogYm9vbGVhbjtcbn1cblxuLyoqXG4gKiBSZXN1bHQgb2YgYSBzYXZpbmcgb3BlcmF0aW9uLlxuICovXG5leHBvcnQgaW50ZXJmYWNlIFNhdmVSZXN1bHQge1xuICAvKipcbiAgICogSW5mb3JtYXRpb24gYWJvdXQgdGhlIG1vZGVsIGFydGlmYWN0cyBzYXZlZC5cbiAgICovXG4gIG1vZGVsQXJ0aWZhY3RzSW5mbzogTW9kZWxBcnRpZmFjdHNJbmZvO1xuXG4gIC8qKlxuICAgKiBIVFRQIHJlc3BvbnNlcyBmcm9tIHRoZSBzZXJ2ZXIgdGhhdCBoYW5kbGVkIHRoZSBtb2RlbC1zYXZpbmcgcmVxdWVzdCAoaWZcbiAgICogYW55KS4gVGhpcyBpcyBhcHBsaWNhYmxlIG9ubHkgdG8gc2VydmVyLWJhc2VkIHNhdmluZyByb3V0ZXMuXG4gICAqL1xuICByZXNwb25zZXM/OiBSZXNwb25zZVtdO1xuXG4gIC8qKlxuICAgKiBFcnJvciBtZXNzYWdlcyBhbmQgcmVsYXRlZCBkYXRhIChpZiBhbnkpLlxuICAgKi9cbiAgZXJyb3JzPzogQXJyYXk8e318c3RyaW5nPjtcbn1cblxuZXhwb3J0IGRlY2xhcmUgaW50ZXJmYWNlIE1vZGVsQXJ0aWZhY3RzSW5mbyB7XG4gIC8qKlxuICAgKiBUaW1lc3RhbXAgZm9yIHdoZW4gdGhlIG1vZGVsIGlzIHNhdmVkLlxuICAgKi9cbiAgZGF0ZVNhdmVkOiBEYXRlO1xuXG4gIC8qKlxuICAgKiBUT0RPIChjYWlzLHlhc3NvZ2JhKSBjb25zaWRlciByZW1vdmluZyBHcmFwaERlZiBhcyBHcmFwaERlZnMgbm93XG4gICAqIGNvbWUgaW4gYSBKU09OIGZvcm1hdCBhbmQgbm9uZSBvZiBvdXIgSU9IYW5kbGVycyBzdXBwb3J0IGEgbm9uIGpzb25cbiAgICogZm9ybWF0LiBXZSBjb3VsZCBjb25kZXIgcmVwbGFjaW5nIHRoaXMgd2l0aCAnQmluYXJ5JyBpZiB3ZSB3YW50IHRvXG4gICAqIGFsbG93IGZ1dHVyZSBoYW5kbGVycyB0byBzYXZlIHRvIG5vbiBqc29uIGZvcm1hdHMgKHRob3VnaCB0aGV5IHdpbGxcbiAgICogcHJvYmFibHkgd2FudCBtb3JlIGluZm9ybWF0aW9uIHRoYW4gJ0JpbmFyeScpLlxuICAgKiBUeXBlIG9mIHRoZSBtb2RlbCB0b3BvbG9neVxuICAgKlxuICAgKiBUeXBlIG9mIHRoZSBtb2RlbCB0b3BvbG9neVxuICAgKlxuICAgKiBQb3NzaWJsZSB2YWx1ZXM6XG4gICAqICAgLSBKU09OOiBKU09OIGNvbmZpZyAoaHVtYW4tcmVhZGFibGUsIGUuZy4sIEtlcmFzIEpTT04pLlxuICAgKiAgIC0gR3JhcGhEZWY6IFRlbnNvckZsb3dcbiAgICogICAgIFtHcmFwaERlZl0oaHR0cHM6Ly93d3cudGVuc29yZmxvdy5vcmcvZXh0ZW5kL3Rvb2xfZGV2ZWxvcGVycy8jZ3JhcGhkZWYpXG4gICAqICAgICBwcm90b2NvbCBidWZmZXIgKGJpbmFyeSkuXG4gICAqL1xuICBtb2RlbFRvcG9sb2d5VHlwZTogJ0pTT04nfCdHcmFwaERlZic7XG5cbiAgLyoqXG4gICAqIFNpemUgb2YgbW9kZWwgdG9wb2xvZ3kgKEtlcmFzIEpTT04gb3IgR3JhcGhEZWYpLCBpbiBieXRlcy5cbiAgICovXG4gIG1vZGVsVG9wb2xvZ3lCeXRlcz86IG51bWJlcjtcblxuICAvKipcbiAgICogU2l6ZSBvZiB3ZWlnaHQgc3BlY2lmaWNhdGlvbiBvciBtYW5pZmVzdCwgaW4gYnl0ZXMuXG4gICAqL1xuICB3ZWlnaHRTcGVjc0J5dGVzPzogbnVtYmVyO1xuXG4gIC8qKlxuICAgKiBTaXplIG9mIHdlaWdodCB2YWx1ZSBkYXRhLCBpbiBieXRlcy5cbiAgICovXG4gIHdlaWdodERhdGFCeXRlcz86IG51bWJlcjtcbn1cblxuLyoqIE1vZGVsIHRyYWluaW5nIGNvbmZpZ3VyYXRpb24uICovXG5leHBvcnQgZGVjbGFyZSBpbnRlcmZhY2UgVHJhaW5pbmdDb25maWcge1xuICAvLyBUT0RPKGNhaXMpOiBUaWdodGVuIHRoZSB0eXBpbmcgb25jZSBrZXJhcyBzcGVjIGlzIGF2YWlsYWJsZSB0byB0ZmpzLWNvcmUuXG4gIC8vIFNlZVxuICAvLyB0c2xpbnQ6ZGlzYWJsZS1uZXh0LWxpbmU6bWF4LWxpbmUtbGVuZ3RoXG4gIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS90ZW5zb3JmbG93L3RmanMtbGF5ZXJzL2Jsb2IvbWFzdGVyL3NyYy9rZXJhc19mb3JtYXQvdHJhaW5pbmdfY29uZmlnLnRzXG4gIC8qKiBPcHRpbWl6ZXIgdXNlZCBmb3IgdGhlIG1vZGVsIHRyYWluaW5nLiAqL1xuICBvcHRpbWl6ZXJfY29uZmlnOiB7fTtcblxuICAvLyBUT0RPKGNhaXMpOiBUaWdodGVuIHRoZSB0eXBpbmcgb25jZSBrZXJhcyBzcGVjIGlzIGF2YWlsYWJsZSB0byB0ZmpzLWNvcmUuXG4gIC8qKiBMb3NzIGZ1bmN0aW9uKHMpIGZvciB0aGUgbW9kZWwncyBvdXRwdXQocykuICovXG4gIGxvc3M6IHN0cmluZ3xzdHJpbmdbXXx7W2tleTogc3RyaW5nXTogc3RyaW5nfTtcblxuICAvLyBUT0RPKGNhaXMpOiBUaWdodGVuIHRoZSB0eXBpbmcgb25jZSBrZXJhcyBzcGVjIGlzIGF2YWlsYWJsZSB0byB0ZmpzLWNvcmUuXG4gIC8qKiBNZXRyaWMgZnVuY3Rpb24ocykgZm9yIHRoZSBtb2RlbCdzIG91dHB1dChzKS4gKi9cbiAgbWV0cmljcz86IHN0cmluZ1tdfHtba2V5OiBzdHJpbmddOiBzdHJpbmd9O1xuXG4gIC8vIFRPRE8oY2Fpcyk6IFRpZ2h0ZW4gdGhlIHR5cGluZyBvbmNlIGtlcmFzIHNwZWMgaXMgYXZhaWxhYmxlIHRvIHRmanMtY29yZS5cbiAgd2VpZ2h0ZWRfbWV0cmljcz86IHN0cmluZ1tdO1xuXG4gIC8vIFRPRE8oY2Fpcyk6IFRpZ2h0ZW4gdGhlIHR5cGluZyBvbmNlIGtlcmFzIHNwZWMgaXMgYXZhaWxhYmxlIHRvIHRmanMtY29yZS5cbiAgc2FtcGxlX3dlaWdodF9tb2RlPzogc3RyaW5nO1xuXG4gIGxvc3Nfd2VpZ2h0cz86IG51bWJlcltdfHtba2V5OiBzdHJpbmddOiBudW1iZXJ9O1xufVxuXG5leHBvcnQgdHlwZSBXZWlnaHREYXRhID0gQXJyYXlCdWZmZXIgfCBBcnJheUJ1ZmZlcltdO1xuXG4vKipcbiAqIFRoZSBzZXJpYWxpemVkIGFydGlmYWN0cyBvZiBhIG1vZGVsLCBpbmNsdWRpbmcgdG9wb2xvZ3kgYW5kIHdlaWdodHMuXG4gKlxuICogVGhlIGBtb2RlbFRvcG9sb2d5YCwgYHRyYWluaW5nQ29uZmlnYCwgYHdlaWdodFNwZWNzYCBhbmQgYHdlaWdodERhdGFgIGZpZWxkc1xuICogb2YgdGhpcyBpbnRlcmZhY2UgYXJlIG9wdGlvbmFsLCBpbiBvcmRlciB0byBzdXBwb3J0IHRvcG9sb2d5LSBvciB3ZWlnaHRzLW9ubHlcbiAqIHNhdmluZyBhbmQgbG9hZGluZy5cbiAqXG4gKiBOb3RlIHRoaXMgaW50ZXJmYWNlIGlzIHVzZWQgaW50ZXJuYWxseSBpbiBJT0hhbmRsZXJzLiAgRm9yIHRoZSBmaWxlIGZvcm1hdFxuICogd3JpdHRlbiB0byBkaXNrIGFzIGBtb2RlbC5qc29uYCwgc2VlIGBNb2RlbEpTT05gLlxuICovXG5leHBvcnQgZGVjbGFyZSBpbnRlcmZhY2UgTW9kZWxBcnRpZmFjdHMge1xuICAvKipcbiAgICogTW9kZWwgdG9wb2xvZ3kuXG4gICAqXG4gICAqIEZvciBLZXJhcy1zdHlsZSBgdGYuTW9kZWxgcywgdGhpcyBpcyBhIEpTT04gb2JqZWN0LlxuICAgKiBGb3IgVGVuc29yRmxvdy1zdHlsZSBtb2RlbHMgKGUuZy4sIGBTYXZlZE1vZGVsYCksIHRoaXMgaXMgdGhlIEpTT05cbiAgICogZW5jb2Rpbmcgb2YgdGhlIGBHcmFwaERlZmAgcHJvdG9jb2wgYnVmZmVyLlxuICAgKi9cbiAgbW9kZWxUb3BvbG9neT86IHt9fEFycmF5QnVmZmVyO1xuXG4gIC8qKlxuICAgKiBTZXJpYWxpemVkIGNvbmZpZ3VyYXRpb24gZm9yIHRoZSBtb2RlbCdzIHRyYWluaW5nLlxuICAgKi9cbiAgdHJhaW5pbmdDb25maWc/OiBUcmFpbmluZ0NvbmZpZztcblxuICAvKipcbiAgICogV2VpZ2h0IHNwZWNpZmljYXRpb25zLlxuICAgKlxuICAgKiBUaGlzIGNvcnJlc3BvbmRzIHRvIHRoZSB3ZWlnaHRzRGF0YSBiZWxvdy5cbiAgICovXG4gIHdlaWdodFNwZWNzPzogV2VpZ2h0c01hbmlmZXN0RW50cnlbXTtcblxuICAvKipcbiAgICogQmluYXJ5IGJ1ZmZlcihzKSBmb3IgYWxsIHdlaWdodCB2YWx1ZXMgaW4gdGhlIG9yZGVyIHNwZWNpZmllZCBieVxuICAgKiBgd2VpZ2h0U3BlY3NgLiBUaGlzIG1heSBiZSBhIHNpbmdsZSBBcnJheUJ1ZmZlciBvZiBhbGwgdGhlIHdlaWdodHNcbiAgICogY29uY2F0ZW5hdGVkIHRvZ2V0aGVyIG9yIGFuIEFycmF5IG9mIEFycmF5QnVmZmVycyBjb250YWluaW5nIHRoZSB3ZWlnaHRzXG4gICAqICh3ZWlnaHRzIG1heSBiZSBzaGFyZGVkIGFjcm9zcyBtdWx0aXBsZSBBcnJheUJ1ZmZlcnMpLlxuICAgKi9cbiAgd2VpZ2h0RGF0YT86IFdlaWdodERhdGE7XG5cbiAgLyoqXG4gICAqIFJldHVybnMgYSBzdHJlYW0gb2YgdGhlIHdlaWdodHMuIFNvbWUgbW9kZWxzIGFyZSB0b28gbGFyZ2UgdG8gZml0IGluXG4gICAqIFY4J3MgbWVtb3J5IGhlYXAsIGFuZCBgZ2V0V2VpZ2h0U3RyZWFtYCBsb2FkcyB0aGVpciB3ZWlnaHRzIHdpdGhvdXQgc3RvcmluZ1xuICAgKiB0aGVtIGFsbCBpbiBtZW1vcnkgYXQgdGhlIHNhbWUgdGltZS5cbiAgICovXG4gIGdldFdlaWdodFN0cmVhbT86ICgpID0+IFJlYWRhYmxlU3RyZWFtPEFycmF5QnVmZmVyPjtcblxuICAvKipcbiAgICogSGFyZC1jb2RlZCBmb3JtYXQgbmFtZSBmb3IgbW9kZWxzIHNhdmVkIGZyb20gVGVuc29yRmxvdy5qcyBvciBjb252ZXJ0ZWRcbiAgICogYnkgVGVuc29yRmxvdy5qcyBDb252ZXJ0ZXIuXG4gICAqL1xuICBmb3JtYXQ/OiBzdHJpbmc7XG5cbiAgLyoqXG4gICAqIFdoYXQgbGlicmFyeSBpcyByZXNwb25zaWJsZSBmb3Igb3JpZ2luYWxseSBnZW5lcmF0aW5nIHRoaXMgYXJ0aWZhY3QuXG4gICAqXG4gICAqIFVzZWQgZm9yIGRlYnVnZ2luZyBwdXJwb3Nlcy4gRS5nLiwgJ1RlbnNvckZsb3cuanMgdjEuMC4wJy5cbiAgICovXG4gIGdlbmVyYXRlZEJ5Pzogc3RyaW5nO1xuXG4gIC8qKlxuICAgKiBXaGF0IGxpYnJhcnkgb3IgdG9vbCBpcyByZXNwb25zaWJsZSBmb3IgY29udmVydGluZyB0aGUgb3JpZ2luYWwgbW9kZWxcbiAgICogdG8gdGhpcyBmb3JtYXQsIGFwcGxpY2FibGUgb25seSBpZiB0aGUgbW9kZWwgaXMgb3V0cHV0IGJ5IGEgY29udmVydGVyLlxuICAgKlxuICAgKiBVc2VkIGZvciBkZWJ1Z2dpbmcgcHVycG9zZXMuICBFLmcuLCAnVGVuc29yRmxvdy5qcyBDb252ZXJ0ZXIgdjEuMC4wJy5cbiAgICpcbiAgICogQSB2YWx1ZSBvZiBgbnVsbGAgbWVhbnMgdGhlIG1vZGVsIGFydGlmYWN0cyBhcmUgZ2VuZXJhdGVkIHdpdGhvdXQgYW55XG4gICAqIGNvbnZlcnNpb24gcHJvY2VzcyAoZS5nLiwgc2F2ZWQgZGlyZWN0bHkgZnJvbSBhIFRlbnNvckZsb3cuanNcbiAgICogYHRmLkxheWVyc01vZGVsYCBpbnN0YW5jZS4pXG4gICAqL1xuICBjb252ZXJ0ZWRCeT86IHN0cmluZ3xudWxsO1xuXG4gIC8qKlxuICAgKiBJbnB1dHMgYW5kIG91dHB1dHMgc2lnbmF0dXJlIGZvciBzYXZlZCBtb2RlbC5cbiAgICovXG4gIHNpZ25hdHVyZT86IHt9O1xuXG4gIC8qKlxuICAgKiBVc2VyLWRlZmluZWQgbWV0YWRhdGEgYWJvdXQgdGhlIG1vZGVsLlxuICAgKi9cbiAgdXNlckRlZmluZWRNZXRhZGF0YT86IHtba2V5OiBzdHJpbmddOiB7fX07XG5cbiAgLyoqXG4gICAqIEluaXRpYWxpemVyIGZvciB0aGUgbW9kZWwuXG4gICAqL1xuICBtb2RlbEluaXRpYWxpemVyPzoge307XG5cbiAgLyoqXG4gICAqIElucHV0cyBhbmQgb3V0cHV0cyBzaWduYXR1cmUgZm9yIG1vZGVsIGluaXRpYWxpemVyLlxuICAgKi9cbiAgaW5pdGlhbGl6ZXJTaWduYXR1cmU/OiB7fTtcbn1cblxuLyoqXG4gKiBUaGUgb24tZGlzayBmb3JtYXQgb2YgdGhlIGBtb2RlbC5qc29uYCBmaWxlLlxuICpcbiAqIFRGLmpzIDEuMCBhbHdheXMgcG9wdWxhdGVzIHRoZSBvcHRpb25hbCBmaWVsZHMgd2hlbiB3cml0aW5nIG1vZGVsLmpzb24uXG4gKiBQcmlvciB2ZXJzaW9ucyBkaWQgbm90IHByb3ZpZGUgdGhvc2UgZmllbGRzLlxuICovXG5leHBvcnQgZGVjbGFyZSBpbnRlcmZhY2UgTW9kZWxKU09OIHtcbiAgLyoqXG4gICAqIE1vZGVsIHRvcG9sb2d5LlxuICAgKlxuICAgKiBGb3IgS2VyYXMtc3R5bGUgYHRmLk1vZGVsYHMsIHRoaXMgaXMgYSBKU09OIG9iamVjdC5cbiAgICogRm9yIFRlbnNvckZsb3ctc3R5bGUgbW9kZWxzIChlLmcuLCBgU2F2ZWRNb2RlbGApLCB0aGlzIGlzIHRoZSBKU09OXG4gICAqIGVuY29kaW5nIG9mIHRoZSBgR3JhcGhEZWZgIHByb3RvY29sIGJ1ZmZlci5cbiAgICovXG4gIG1vZGVsVG9wb2xvZ3k6IHt9O1xuXG4gIC8qKiBNb2RlbCB0cmFpbmluZyBjb25maWd1cmF0aW9uLiAqL1xuICB0cmFpbmluZ0NvbmZpZz86IFRyYWluaW5nQ29uZmlnO1xuXG4gIC8qKlxuICAgKiBXZWlnaHRzIG1hbmlmZXN0LlxuICAgKlxuICAgKiBUaGUgd2VpZ2h0cyBtYW5pZmVzdCBjb25zaXN0cyBvZiBhbiBvcmRlcmVkIGxpc3Qgb2Ygd2VpZ2h0LW1hbmlmZXN0XG4gICAqIGdyb3Vwcy4gRWFjaCB3ZWlnaHQtbWFuaWZlc3QgZ3JvdXAgY29uc2lzdHMgb2YgYSBudW1iZXIgb2Ygd2VpZ2h0IHZhbHVlc1xuICAgKiBzdG9yZWQgaW4gYSBudW1iZXIgb2YgcGF0aHMuIFNlZSB0aGUgZG9jdW1lbnRhdGlvbiBvZlxuICAgKiBgV2VpZ2h0c01hbmlmZXN0Q29uZmlnYCBmb3IgbW9yZSBkZXRhaWxzLlxuICAgKi9cbiAgd2VpZ2h0c01hbmlmZXN0OiBXZWlnaHRzTWFuaWZlc3RDb25maWc7XG5cbiAgLyoqXG4gICAqIEhhcmQtY29kZWQgZm9ybWF0IG5hbWUgZm9yIG1vZGVscyBzYXZlZCBmcm9tIFRlbnNvckZsb3cuanMgb3IgY29udmVydGVkXG4gICAqIGJ5IFRlbnNvckZsb3cuanMgQ29udmVydGVyLlxuICAgKi9cbiAgZm9ybWF0Pzogc3RyaW5nO1xuXG4gIC8qKlxuICAgKiBXaGF0IGxpYnJhcnkgaXMgcmVzcG9uc2libGUgZm9yIG9yaWdpbmFsbHkgZ2VuZXJhdGluZyB0aGlzIGFydGlmYWN0LlxuICAgKlxuICAgKiBVc2VkIGZvciBkZWJ1Z2dpbmcgcHVycG9zZXMuIEUuZy4sICdUZW5zb3JGbG93LmpzIHYxLjAuMCcuXG4gICAqL1xuICBnZW5lcmF0ZWRCeT86IHN0cmluZztcblxuICAvKipcbiAgICogV2hhdCBsaWJyYXJ5IG9yIHRvb2wgaXMgcmVzcG9uc2libGUgZm9yIGNvbnZlcnRpbmcgdGhlIG9yaWdpbmFsIG1vZGVsXG4gICAqIHRvIHRoaXMgZm9ybWF0LCBhcHBsaWNhYmxlIG9ubHkgaWYgdGhlIG1vZGVsIGlzIG91dHB1dCBieSBhIGNvbnZlcnRlci5cbiAgICpcbiAgICogVXNlZCBmb3IgZGVidWdnaW5nIHB1cnBvc2VzLiAgRS5nLiwgJ1RlbnNvckZsb3cuanMgQ29udmVydGVyIHYxLjAuMCcuXG4gICAqXG4gICAqIEEgdmFsdWUgb2YgYG51bGxgIG1lYW5zIHRoZSBtb2RlbCBhcnRpZmFjdHMgYXJlIGdlbmVyYXRlZCB3aXRob3V0IGFueVxuICAgKiBjb252ZXJzaW9uIHByb2Nlc3MgKGUuZy4sIHNhdmVkIGRpcmVjdGx5IGZyb20gYSBUZW5zb3JGbG93LmpzXG4gICAqIGB0Zi5MYXllcnNNb2RlbGAgaW5zdGFuY2UuKVxuICAgKi9cbiAgY29udmVydGVkQnk/OiBzdHJpbmd8bnVsbDtcblxuICAvKipcbiAgICogSW5wdXRzIGFuZCBvdXRwdXRzIHNpZ25hdHVyZSBmb3Igc2F2ZWQgbW9kZWwuXG4gICAqL1xuICBzaWduYXR1cmU/OiB7fTtcblxuICAvKipcbiAgICogVXNlci1kZWZpbmVkIG1ldGFkYXRhIGFib3V0IHRoZSBtb2RlbC5cbiAgICovXG4gIHVzZXJEZWZpbmVkTWV0YWRhdGE/OiB7W2tleTogc3RyaW5nXToge319O1xuXG4gIC8qKlxuICAgKiBJbml0aWFsaXplciBmb3IgdGhlIG1vZGVsLlxuICAgKi9cbiAgbW9kZWxJbml0aWFsaXplcj86IHt9O1xuXG4gIC8qKlxuICAgKiBJbnB1dHMgYW5kIG91dHB1dHMgc2lnbmF0dXJlIGZvciBtb2RlbCBpbml0aWFsaXplci5cbiAgICovXG4gIGluaXRpYWxpemVyU2lnbmF0dXJlPzoge307XG59XG5cbi8qKlxuICogVHlwZSBkZWZpbml0aW9uIGZvciBoYW5kbGVycyBvZiBsb2FkaW5nIG9wZXJhdGlvbnMuXG4gKi9cbmV4cG9ydCB0eXBlIExvYWRIYW5kbGVyID0gKCkgPT4gUHJvbWlzZTxNb2RlbEFydGlmYWN0cz47XG5cbi8qKlxuICogVHlwZSBkZWZpbml0aW9uIGZvciBoYW5kbGVycyBvZiBzYXZpbmcgb3BlcmF0aW9ucy5cbiAqL1xuZXhwb3J0IHR5cGUgU2F2ZUhhbmRsZXIgPSAobW9kZWxBcnRpZmFjdDogTW9kZWxBcnRpZmFjdHMpID0+XG4gICAgUHJvbWlzZTxTYXZlUmVzdWx0PjtcblxuLyoqXG4gKiBJbnRlcmZhY2UgZm9yIGEgbW9kZWwgaW1wb3J0L2V4cG9ydCBoYW5kbGVyLlxuICpcbiAqIFRoZSBgc2F2ZWAgYW5kIGBsb2FkYCBoYW5kbGVycyBhcmUgYm90aCBvcHRpb25hbCwgaW4gb3JkZXIgdG8gYWxsb3cgaGFuZGxlcnNcbiAqIHRoYXQgc3VwcG9ydCBvbmx5IHNhdmluZyBvciBsb2FkaW5nLlxuICovXG4vLyB0c2xpbnQ6ZGlzYWJsZS1uZXh0LWxpbmU6aW50ZXJmYWNlLW5hbWVcbmV4cG9ydCBpbnRlcmZhY2UgSU9IYW5kbGVyIHtcbiAgc2F2ZT86IFNhdmVIYW5kbGVyO1xuICBsb2FkPzogTG9hZEhhbmRsZXI7XG59XG5cbi8qKlxuICogVHlwZSBkZWZpbml0aW9uIGZvciBoYW5kbGVycyBvZiBzeW5jaHJvbm91cyBsb2FkaW5nIG9wZXJhdGlvbnMuXG4gKi9cbmV4cG9ydCB0eXBlIExvYWRIYW5kbGVyU3luYyA9ICgpID0+IE1vZGVsQXJ0aWZhY3RzO1xuXG4vKipcbiAqIFR5cGUgZGVmaW5pdGlvbiBmb3IgaGFuZGxlcnMgb2Ygc3luY2hyb25vdXMgc2F2aW5nIG9wZXJhdGlvbnMuXG4gKi9cbmV4cG9ydCB0eXBlIFNhdmVIYW5kbGVyU3luYyA9IChtb2RlbEFydGlmYWN0OiBNb2RlbEFydGlmYWN0cykgPT4gU2F2ZVJlc3VsdDtcblxuLyoqXG4gKiBJbnRlcmZhY2UgZm9yIGEgc3luY2hyb25vdXMgbW9kZWwgaW1wb3J0L2V4cG9ydCBoYW5kbGVyLlxuICpcbiAqIFRoZSBgc2F2ZWAgYW5kIGBsb2FkYCBoYW5kbGVycyBhcmUgYm90aCBvcHRpb25hbCwgaW4gb3JkZXIgdG8gYWxsb3cgaGFuZGxlcnNcbiAqIHRoYXQgc3VwcG9ydCBvbmx5IHNhdmluZyBvciBsb2FkaW5nLlxuICovXG4vLyB0c2xpbnQ6ZGlzYWJsZS1uZXh0LWxpbmU6aW50ZXJmYWNlLW5hbWVcbmV4cG9ydCB0eXBlIElPSGFuZGxlclN5bmMgPSB7XG4gIHNhdmU/OiBTYXZlSGFuZGxlclN5bmM7XG4gIGxvYWQ/OiBMb2FkSGFuZGxlclN5bmM7XG59O1xuXG4vKipcbiAqIEFuIGludGVyZmFjZSBmb3IgdGhlIG1hbmFnZXIgb2YgYSBtb2RlbCBzdG9yZS5cbiAqXG4gKiBBIG1vZGVsIHN0b3JlIGlzIGRlZmluZWQgYXMgYSBzdG9yYWdlIG1lZGl1bSBvbiB3aGljaCBtdWx0aXBsZSBtb2RlbHMgY2FuXG4gKiBiZSBzdG9yZWQuIEVhY2ggc3RvcmVkIG1vZGVsIGhhcyBhIHVuaXF1ZSBgcGF0aGAgYXMgaXRzIGlkZW50aWZpZXIuXG4gKiBBIGBNb2RlbFN0b3JlTWFuYWdlcmAgZm9yIHRoZSBzdG9yZSBhbGxvd3MgYWN0aW9ucyBpbmNsdWRpbmdcbiAqXG4gKiAtIExpc3RpbmcgdGhlIG1vZGVscyBzdG9yZWQgaW4gdGhlIHN0b3JlLlxuICogLSBEZWxldGluZyBhIG1vZGVsIGZyb20gdGhlIHN0b3JlLlxuICovXG5leHBvcnQgaW50ZXJmYWNlIE1vZGVsU3RvcmVNYW5hZ2VyIHtcbiAgLyoqXG4gICAqIExpc3QgYWxsIG1vZGVscyBpbiB0aGUgbW9kZWwgc3RvcmUuXG4gICAqXG4gICAqIEByZXR1cm5zIEEgZGljdGlvbmFyeSBtYXBwaW5nIHBhdGhzIG9mIGV4aXN0aW5nIG1vZGVscyB0byB0aGVpclxuICAgKiAgIG1vZGVsIGFydGlmYWN0cyBpbmZvLiBNb2RlbCBhcnRpZmFjdHMgaW5mbyBpbmNsdWRlIHR5cGUgb2YgdGhlIG1vZGVsJ3NcbiAgICogICB0b3BvbG9neSwgYnl0ZSBzaXplcyBvZiB0aGUgdG9wb2xvZ3ksIHdlaWdodHMsIGV0Yy5cbiAgICovXG4gIGxpc3RNb2RlbHMoKTogUHJvbWlzZTx7W3BhdGg6IHN0cmluZ106IE1vZGVsQXJ0aWZhY3RzSW5mb30+O1xuXG4gIC8qKlxuICAgKiBSZW1vdmUgYSBtb2RlbCBzcGVjaWZpZWQgYnkgYHBhdGhgLlxuICAgKlxuICAgKiBAcGFyYW0gcGF0aFxuICAgKiBAcmV0dXJucyBNb2RlbEFydGlmYWN0c0luZm8gb2YgdGhlIGRlbGV0ZWQgbW9kZWwgKGlmIGFuZCBvbmx5IGlmIGRlbGV0aW9uXG4gICAqICAgaXMgc3VjY2Vzc2Z1bCkuXG4gICAqIEB0aHJvd3MgRXJyb3IgaWYgZGVsZXRpb24gZmFpbHMsIGUuZy4sIGlmIG5vIG1vZGVsIGV4aXN0cyBhdCBgcGF0aGAuXG4gICAqL1xuICByZW1vdmVNb2RlbChwYXRoOiBzdHJpbmcpOiBQcm9taXNlPE1vZGVsQXJ0aWZhY3RzSW5mbz47XG59XG5cbi8qKlxuICogQ2FsbGJhY2sgZm9yIHRoZSBwcm9ncmVzcyBvZiBhIGxvbmctcnVubmluZyBhY3Rpb24gc3VjaCBhcyBhbiBIVFRQXG4gKiByZXF1ZXN0IGZvciBhIGxhcmdlIGJpbmFyeSBvYmplY3QuXG4gKlxuICogYGZyYWN0aW9uYCBzaG91bGQgYmUgYSBudW1iZXIgaW4gdGhlIFswLCAxXSBpbnRlcnZhbCwgaW5kaWNhdGluZyBob3dcbiAqIG11Y2ggb2YgdGhlIGFjdGlvbiBoYXMgY29tcGxldGVkLlxuICovXG5leHBvcnQgdHlwZSBPblByb2dyZXNzQ2FsbGJhY2sgPSAoZnJhY3Rpb246IG51bWJlcikgPT4gdm9pZDtcblxuLyoqIEBpbm5hbWVzcGFjZSBpbyAqL1xuZXhwb3J0IGludGVyZmFjZSBMb2FkT3B0aW9ucyB7XG4gIC8qKlxuICAgKiBSZXF1ZXN0SW5pdCAob3B0aW9ucykgZm9yIEhUVFAgcmVxdWVzdHMuXG4gICAqXG4gICAqIEZvciBkZXRhaWxlZCBpbmZvcm1hdGlvbiBvbiB0aGUgc3VwcG9ydGVkIGZpZWxkcywgc2VlXG4gICAqIFtodHRwczovL2RldmVsb3Blci5tb3ppbGxhLm9yZy9lbi1VUy9kb2NzL1dlYi9BUEkvUmVxdWVzdC9SZXF1ZXN0XShcbiAgICogICAgIGh0dHBzOi8vZGV2ZWxvcGVyLm1vemlsbGEub3JnL2VuLVVTL2RvY3MvV2ViL0FQSS9SZXF1ZXN0L1JlcXVlc3QpXG4gICAqL1xuICByZXF1ZXN0SW5pdD86IFJlcXVlc3RJbml0O1xuXG4gIC8qKlxuICAgKiBQcm9ncmVzcyBjYWxsYmFjay5cbiAgICovXG4gIG9uUHJvZ3Jlc3M/OiBPblByb2dyZXNzQ2FsbGJhY2s7XG5cbiAgLyoqXG4gICAqIEEgZnVuY3Rpb24gdXNlZCB0byBvdmVycmlkZSB0aGUgYHdpbmRvdy5mZXRjaGAgZnVuY3Rpb24uXG4gICAqL1xuICBmZXRjaEZ1bmM/OiB0eXBlb2YgZmV0Y2g7XG5cbiAgLyoqXG4gICAqIFN0cmljdCBsb2FkaW5nIG1vZGVsOiB3aGV0aGVyIGV4dHJhbmVvdXMgd2VpZ2h0cyBvciBtaXNzaW5nXG4gICAqIHdlaWdodHMgc2hvdWxkIHRyaWdnZXIgYW4gYEVycm9yYC5cbiAgICpcbiAgICogSWYgYHRydWVgLCByZXF1aXJlIHRoYXQgdGhlIHByb3ZpZGVkIHdlaWdodHMgZXhhY3RseSBtYXRjaCB0aG9zZVxuICAgKiByZXF1aXJlZCBieSB0aGUgbGF5ZXJzLiBgZmFsc2VgIG1lYW5zIHRoYXQgYm90aCBleHRyYSB3ZWlnaHRzXG4gICAqIGFuZCBtaXNzaW5nIHdlaWdodHMgd2lsbCBiZSBzaWxlbnRseSBpZ25vcmVkLlxuICAgKlxuICAgKiBEZWZhdWx0OiBgdHJ1ZWAuXG4gICAqL1xuICBzdHJpY3Q/OiBib29sZWFuO1xuXG4gIC8qKlxuICAgKiBQYXRoIHByZWZpeCBmb3Igd2VpZ2h0IGZpbGVzLCBieSBkZWZhdWx0IHRoaXMgaXMgY2FsY3VsYXRlZCBmcm9tIHRoZVxuICAgKiBwYXRoIG9mIHRoZSBtb2RlbCBKU09OIGZpbGUuXG4gICAqXG4gICAqIEZvciBpbnN0YW5jZSwgaWYgdGhlIHBhdGggdG8gdGhlIG1vZGVsIEpTT04gZmlsZSBpc1xuICAgKiBgaHR0cDovL2xvY2FsaG9zdC9mb28vbW9kZWwuanNvbmAsIHRoZW4gdGhlIGRlZmF1bHQgcGF0aCBwcmVmaXggd2lsbCBiZVxuICAgKiBgaHR0cDovL2xvY2FsaG9zdC9mb28vYC4gSWYgYSB3ZWlnaHQgZmlsZSBoYXMgdGhlIHBhdGggdmFsdWVcbiAgICogYGdyb3VwMS1zaGFyZDFvZjJgIGluIHRoZSB3ZWlnaHQgbWFuaWZlc3QsIHRoZW4gdGhlIHdlaWdodCBmaWxlIHdpbGwgYmVcbiAgICogbG9hZGVkIGZyb20gYGh0dHA6Ly9sb2NhbGhvc3QvZm9vL2dyb3VwMS1zaGFyZDFvZjJgIGJ5IGRlZmF1bHQuIEhvd2V2ZXIsXG4gICAqIGlmIHlvdSBwcm92aWRlIGEgYHdlaWdodFBhdGhQcmVmaXhgIHZhbHVlIG9mXG4gICAqIGBodHRwOi8vbG9jYWxob3N0L2Zvby9hbHQtd2VpZ2h0c2AsIHRoZW4gdGhlIHdlaWdodCBmaWxlIHdpbGwgYmUgbG9hZGVkXG4gICAqIGZyb20gdGhlIHBhdGggYGh0dHA6Ly9sb2NhbGhvc3QvZm9vL2FsdC13ZWlnaHRzL2dyb3VwMS1zaGFyZDFvZjJgIGluc3RlYWQuXG4gICAqL1xuICB3ZWlnaHRQYXRoUHJlZml4Pzogc3RyaW5nO1xuXG4gIC8qKlxuICAgKiBXaGV0aGVyIHRoZSBtb2R1bGUgb3IgbW9kZWwgaXMgdG8gYmUgbG9hZGVkIGZyb20gVEYgSHViLlxuICAgKlxuICAgKiBTZXR0aW5nIHRoaXMgdG8gYHRydWVgIGFsbG93cyBwYXNzaW5nIGEgVEYtSHViIG1vZHVsZSBVUkwsIG9taXR0aW5nIHRoZVxuICAgKiBzdGFuZGFyZCBtb2RlbCBmaWxlIG5hbWUgYW5kIHRoZSBxdWVyeSBwYXJhbWV0ZXJzLlxuICAgKlxuICAgKiBEZWZhdWx0OiBgZmFsc2VgLlxuICAgKi9cbiAgZnJvbVRGSHViPzogYm9vbGVhbjtcblxuICAvKipcbiAgICogQW4gYXN5bmMgZnVuY3Rpb24gdG8gY29udmVydCB3ZWlnaHQgZmlsZSBuYW1lIHRvIFVSTC4gVGhlIHdlaWdodCBmaWxlXG4gICAqIG5hbWVzIGFyZSBzdG9yZWQgaW4gbW9kZWwuanNvbidzIHdlaWdodHNNYW5pZmVzdC5wYXRocyBmaWVsZC4gQnkgZGVmYXVsdCB3ZVxuICAgKiBjb25zaWRlciB3ZWlnaHQgZmlsZXMgYXJlIGNvbG9jYXRlZCB3aXRoIHRoZSBtb2RlbC5qc29uIGZpbGUuIEZvciBleGFtcGxlOlxuICAgKiAgICAgbW9kZWwuanNvbiBVUkw6IGh0dHBzOi8vd3d3Lmdvb2dsZS5jb20vbW9kZWxzLzEvbW9kZWwuanNvblxuICAgKiAgICAgZ3JvdXAxLXNoYXJkMW9mMS5iaW4gdXJsOlxuICAgKiAgICAgICAgaHR0cHM6Ly93d3cuZ29vZ2xlLmNvbS9tb2RlbHMvMS9ncm91cDEtc2hhcmQxb2YxLmJpblxuICAgKlxuICAgKiBXaXRoIHRoaXMgZnVuYyB5b3UgY2FuIGNvbnZlcnQgdGhlIHdlaWdodCBmaWxlIG5hbWUgdG8gYW55IFVSTC5cbiAgICovXG4gIHdlaWdodFVybENvbnZlcnRlcj86ICh3ZWlnaHRGaWxlTmFtZTogc3RyaW5nKSA9PiBQcm9taXNlPHN0cmluZz47XG5cbiAgLyoqXG4gICAqIFdoZXRoZXIgdG8gc3RyZWFtIHRoZSBtb2RlbCBkaXJlY3RseSB0byB0aGUgYmFja2VuZCBvciBjYWNoZSBhbGwgaXRzXG4gICAqIHdlaWdodHMgb24gQ1BVIGZpcnN0LiBVc2VmdWwgZm9yIGxhcmdlIG1vZGVscy5cbiAgICovXG4gIHN0cmVhbVdlaWdodHM/OiBib29sZWFuO1xufVxuXG4vKipcbiAqIEFkZGl0aW9uYWwgb3B0aW9ucyBmb3IgUGxhdGZvcm0uZmV0Y2hcbiAqL1xuZXhwb3J0IGludGVyZmFjZSBSZXF1ZXN0RGV0YWlscyB7XG4gIC8qKlxuICAgKiBJcyB0aGlzIHJlcXVlc3QgZm9yIGEgYmluYXJ5IGZpbGUgKGFzIG9wcG9zZWQgdG8gYSBqc29uIGZpbGUpXG4gICAqL1xuICBpc0JpbmFyeT86IGJvb2xlYW47XG59XG4iXX0=