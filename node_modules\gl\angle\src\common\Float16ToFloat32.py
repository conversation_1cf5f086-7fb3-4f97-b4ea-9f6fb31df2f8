# Copyright (c) 2012 The ANGLE Project Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.
#

# This script generates a function that converts 16-bit precision floating
# point numbers to 32-bit.
# It is based on ftp://ftp.fox-toolkit.org/pub/fasthalffloatconversion.pdf.

#include "common/mathutil.h"

def convertMantissa(i):
    if i == 0:
        return 0
    elif i < 1024:
        m = i << 13
        e = 0
        while not (m & 0x00800000):
            e -= 0x00800000
            m = m << 1
        m &= ~0x00800000
        e += 0x38800000
        return m | e
    else:
        return 0x38000000 + ((i - 1024) << 13)

def convertExponent(i):
    if i == 0:
        return 0
    elif i in range(1, 31):
        return i << 23
    elif i == 31:
        return 0x47800000
    elif i == 32:
        return 0x80000000
    elif i in range(33, 63):
        return 0x80000000 + ((i - 32) << 23)
    else:
        return 0xC7800000

def convertOffset(i):
    if i == 0 or i == 32:
        return 0
    else:
        return 1024

print """//
// Copyright (c) 2012 The ANGLE Project Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.
//

// This file is automatically generated.

namespace gl
{
"""

print "const static unsigned g_mantissa[2048] = {"
for i in range(0, 2048):
    print "    %#010x," % convertMantissa(i)
print "};\n"

print "const static unsigned g_exponent[64] = {"
for i in range(0, 64):
    print "    %#010x," % convertExponent(i)
print "};\n"

print "const static unsigned g_offset[64] = {"
for i in range(0, 64):
    print "    %#010x," % convertOffset(i)
print "};\n"

print """float float16ToFloat32(unsigned short h)
{
    unsigned i32 = g_mantissa[g_offset[h >> 10] + (h & 0x3ff)] + g_exponent[h >> 10];
    return bitCast<float>(i32);
}
}
"""
