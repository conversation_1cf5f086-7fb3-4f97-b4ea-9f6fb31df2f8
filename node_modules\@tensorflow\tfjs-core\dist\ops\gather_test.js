/**
 * @license
 * Copyright 2020 Google Inc. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../index';
import { ALL_ENVS, describeWithFlags } from '../jasmine_util';
import { expectArraysClose } from '../test_util';
describeWithFlags('gather', ALL_ENVS, (env) => {
    it('1D (gather), scalar indices', async () => {
        const t = tf.tensor1d([1, 2, 3]);
        const t2 = tf.gather(t, tf.scalar(1, 'int32'), 0);
        expect(t2.shape).toEqual([]);
        expectArraysClose(await t2.data(), [2]);
    });
    it('1D (gather), 1D indices', async () => {
        const t = tf.tensor1d([1, 2, 3]);
        const t2 = tf.gather(t, tf.tensor1d([0, 2, 0, 1], 'int32'), 0);
        expect(t2.shape).toEqual([4]);
        expectArraysClose(await t2.data(), [1, 3, 1, 2]);
    });
    it('1D (gather), 2D indices', async () => {
        const t = tf.tensor1d([1, 2, 3]);
        const t2 = tf.gather(t, tf.tensor2d([0, 2, 0, 1], [1, 4], 'int32'), 0);
        expect(t2.shape).toEqual([1, 4]);
        expectArraysClose(await t2.data(), [1, 3, 1, 2]);
    });
    it('2D (gather), scalar indices', async () => {
        const t = tf.tensor2d([1, 11, 2, 22], [2, 2]);
        let t2 = tf.gather(t, tf.scalar(1, 'int32'), 0);
        expect(t2.shape).toEqual([2]);
        expectArraysClose(await t2.data(), [2, 22]);
        t2 = tf.gather(t, tf.scalar(1, 'int32'), 1);
        expect(t2.shape).toEqual([2]);
        expectArraysClose(await t2.data(), [11, 22]);
    });
    it('2D (gather), 1D indices', async () => {
        const t = tf.tensor2d([1, 11, 2, 22], [2, 2]);
        let t2 = tf.gather(t, tf.tensor1d([1, 0, 0, 1], 'int32'), 0);
        expect(t2.shape).toEqual([4, 2]);
        expectArraysClose(await t2.data(), [2, 22, 1, 11, 1, 11, 2, 22]);
        t2 = tf.gather(t, tf.tensor1d([1, 0, 0, 1], 'int32'), 1);
        expect(t2.shape).toEqual([2, 4]);
        expectArraysClose(await t2.data(), [11, 1, 1, 11, 22, 2, 2, 22]);
    });
    it('2D (gather), 2D indices', async () => {
        const t = tf.tensor2d([1, 11, 2, 22], [2, 2]);
        let t2 = tf.gather(t, tf.tensor2d([1, 0, 0, 1], [2, 2], 'int32'), 0);
        expect(t2.shape).toEqual([2, 2, 2]);
        expectArraysClose(await t2.data(), [2, 22, 1, 11, 1, 11, 2, 22]);
        t2 = tf.gather(t, tf.tensor2d([1, 0, 0, 1], [2, 2], 'int32'), 1);
        expect(t2.shape).toEqual([2, 2, 2]);
        expectArraysClose(await t2.data(), [11, 1, 1, 11, 22, 2, 2, 22]);
    });
    it('2D (gather), 2D indices, non-zero batchDims', async () => {
        const t = tf.tensor2d([1, 11, 2, 22], [2, 2]);
        const t2 = tf.gather(t, tf.tensor2d([1, 0, 0, 1], [2, 2], 'int32'), 1, 1);
        expect(t2.shape).toEqual([2, 2]);
        expectArraysClose(await t2.data(), [11, 1, 2, 22]);
    });
    it('2D (gather), 2D indices, negative batchDims', async () => {
        const t = tf.tensor2d([1, 11, 2, 22], [2, 2]);
        const t2 = tf.gather(t, tf.tensor2d([1, 0, 0, 1], [2, 2], 'int32'), 1, -1);
        expect(t2.shape).toEqual([2, 2]);
        expectArraysClose(await t2.data(), [11, 1, 2, 22]);
    });
    it('3D (gather), 1D indices', async () => {
        const t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8], [2, 2, 2]);
        const t2 = tf.gather(t, tf.tensor1d([1, 0, 0, 1], 'int32'), 2);
        expect(t2.shape).toEqual([2, 2, 4]);
        expectArraysClose(await t2.data(), [2, 1, 1, 2, 4, 3, 3, 4, 6, 5, 5, 6, 8, 7, 7, 8]);
    });
    it('3D (gather), 2D indices', async () => {
        const t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8], [2, 2, 2]);
        const t2 = tf.gather(t, tf.tensor2d([1, 0, 0, 1], [2, 2], 'int32'), 2);
        expect(t2.shape).toEqual([2, 2, 2, 2]);
        expectArraysClose(await t2.data(), [2, 1, 1, 2, 4, 3, 3, 4, 6, 5, 5, 6, 8, 7, 7, 8]);
    });
    it('3D (gather), 2D indices, non-zero batchDims', async () => {
        const t = tf.tensor3d([1, 2, 3, 4], [1, 2, 2]);
        const t2 = tf.gather(t, tf.tensor2d([1, 0, 1], [1, 3], 'int32'), 2, 1);
        expect(t2.shape).toEqual([1, 2, 3]);
        expectArraysClose(await t2.data(), [2, 1, 2, 4, 3, 4]);
    });
    it('throws when batch dims greater than axis', () => {
        const t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8], [2, 2, 2]);
        expect(() => tf.gather(t, tf.tensor3d([1, 0, 1], [1, 1, 3], 'int32'), 2, 3))
            .toThrowError(/must be less than or equal to axis/);
    });
    it('throws when batch dims greater than indices rank', () => {
        const t = tf.tensor4d([1, 2, 3, 4, 5, 6, 7, 8], [1, 2, 2, 2]);
        expect(() => tf.gather(t, tf.tensor2d([1, 0, 1], [1, 3], 'int32'), 2, 3))
            .toThrowError(/Expect batchDims in the range of /);
    });
    it('throws when batch dims do not match', () => {
        const t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8], [2, 2, 2]);
        expect(() => tf.gather(t, tf.tensor2d([1, 0, 1], [1, 3], 'int32'), 2, 1))
            .toThrowError(/should be equal to indices.shape/);
    });
    it('bool (gather), 1D indices', async () => {
        const t = tf.tensor1d([true, false, true], 'bool');
        const t2 = tf.gather(t, tf.tensor1d([0, 2, 0, 1], 'int32'), 0);
        expect(t2.shape).toEqual([4]);
        expect(t2.dtype).toBe('bool');
        expect(await t2.data()).toEqual(new Uint8Array([1, 1, 1, 0]));
    });
    it('bool (gather), 2D indices', async () => {
        const t = tf.tensor1d([true, false, true], 'bool');
        const t2 = tf.gather(t, tf.tensor2d([0, 2, 0, 1], [2, 2], 'int32'), 0);
        expect(t2.shape).toEqual([2, 2]);
        expect(t2.dtype).toBe('bool');
        expect(await t2.data()).toEqual(new Uint8Array([1, 1, 1, 0]));
    });
    it('int32 (gather), 1D indices', async () => {
        const t = tf.tensor1d([1, 2, 5], 'int32');
        const t2 = tf.gather(t, tf.tensor1d([0, 2, 0, 1], 'int32'), 0);
        expect(t2.shape).toEqual([4]);
        expect(t2.dtype).toBe('int32');
        expect(await t2.data()).toEqual(new Int32Array([1, 5, 1, 2]));
    });
    it('int32 (gather), 2D indices', async () => {
        const t = tf.tensor1d([1, 2, 5], 'int32');
        const t2 = tf.gather(t, tf.tensor2d([0, 2, 0, 1], [2, 2], 'int32'), 0);
        expect(t2.shape).toEqual([2, 2]);
        expect(t2.dtype).toBe('int32');
        expect(await t2.data()).toEqual(new Int32Array([1, 5, 1, 2]));
    });
    it('propagates NaNs', async () => {
        const t = tf.tensor1d([1, 2, NaN]);
        const t2 = tf.gather(t, tf.tensor1d([0, 2, 0, 1], 'int32'), 0);
        expect(t2.shape).toEqual([4]);
        expectArraysClose(await t2.data(), [1, NaN, 1, 2]);
    });
    it('chaining, axis=1', () => {
        const x = tf.zeros([2, 4, 6]);
        // [0, 2, 4]
        const indices = tf.range(0, 6, 2, 'int32');
        const axis = 2;
        expect(x.gather(indices, axis).shape).toEqual([2, 4, 3]);
    });
    it('indices not int32 throws error', () => {
        const x = tf.zeros([2, 4, 6]);
        // [0, 2, 4]
        const indices = tf.range(0, 6, 2);
        const axis = 2;
        expect(() => x.gather(indices, axis)).toThrowError();
    });
    it('throws when passed x as a non-tensor', () => {
        expect(() => tf.gather({}, tf.tensor1d([1])))
            .toThrowError(/Argument 'x' passed to 'gather' must be a Tensor/);
    });
    it('throws when passed indices as a non-tensor', () => {
        // tslint:disable-next-line:no-any
        expect(() => tf.gather(tf.tensor1d([1]), {}))
            .toThrowError(/Argument 'indices' passed to 'gather' must be a Tensor/);
    });
    it('throws when index is out of bound', async () => {
        const t = tf.tensor2d([1, 11, 2, 22], [2, 2]);
        expect(() => tf.gather(t, tf.tensor1d([100], 'int32')))
            .toThrowError(/GatherV2: the index value 100 is not in \[0, 1\]/);
        expect(() => tf.gather(t, tf.tensor1d([-1], 'int32')))
            .toThrowError(/GatherV2: the index value -1 is not in \[0, 1\]/);
    });
    it('accepts a tensor-like object', async () => {
        const res = tf.gather([1, 2, 3], [0, 2, 0, 1], 0);
        expect(res.shape).toEqual([4]);
        expectArraysClose(await res.data(), [1, 3, 1, 2]);
    });
    it('gradient 1D (gather), 1D indices', async () => {
        const t = tf.tensor1d([1, 2, 3]);
        const indices = tf.tensor1d([0, 2, 0, 1], 'int32');
        const dy = tf.tensor([3, 4, 5, 6]);
        const gradients = tf.grad(t => tf.gather(t, indices))(t, dy);
        expect(gradients.shape).toEqual(t.shape);
        expectArraysClose(await gradients.data(), [8, 6, 4]);
    });
    it('gradient with clones', () => {
        const t = tf.tensor1d([1, 2, 3]);
        const indices = tf.tensor1d([0, 2, 0, 1], 'int32');
        const gradF = tf.grad(t => tf.gather(t.clone(), indices.clone()).clone());
        const dt = gradF(t);
        expect(dt.shape).toEqual(t.shape);
    });
    it('gradient 1D (gather), 2D indices', async () => {
        const t = tf.tensor1d([1, 2, 3]);
        const indices = tf.tensor2d([0, 2, 0, 1], [2, 2], 'int32');
        const dy = tf.tensor2d([3, 4, 5, 6], [2, 2]);
        const gradients = tf.grad(t => tf.gather(t, indices))(t, dy);
        expect(gradients.shape).toEqual(t.shape);
        expectArraysClose(await gradients.data(), [8, 6, 4]);
    });
    it('gradient 2D (gather) axis=0 shape=[2, 2] 1D indices', async () => {
        const t = tf.tensor2d([1, 11, 2, 22], [2, 2]);
        const indices = tf.tensor1d([1, 0, 0, 1], 'int32');
        const dy = tf.tensor([3, 4, 5, 6, 7, 8, 9, 10], [4, 2]);
        const axis = 0;
        const gradients = tf.grad(t => tf.gather(t, indices, axis))(t, dy);
        expect(gradients.shape).toEqual(t.shape);
        expectArraysClose(await gradients.data(), [12, 14, 12, 14]);
    });
    it('gradient 2D (gather) axis=0 shape=[2, 2] 2D indices', async () => {
        const t = tf.tensor2d([1, 11, 2, 22], [2, 2]);
        const indices = tf.tensor2d([1, 0, 0, 1], [2, 2], 'int32');
        const dy = tf.tensor([3, 4, 5, 6, 7, 8, 9, 10], [2, 2, 2]);
        const axis = 0;
        const gradients = tf.grad(t => tf.gather(t, indices, axis))(t, dy);
        expect(gradients.shape).toEqual(t.shape);
        expectArraysClose(await gradients.data(), [12, 14, 12, 14]);
    });
    it('gradient 2D (gather) axis=0 shape=[4, 1] 1D indices', async () => {
        const t = tf.tensor2d([1, 11, 2, 22], [4, 1]);
        const indices = tf.tensor1d([1, 0, 0, 1], 'int32');
        const dy = tf.tensor([23, 7, 19, 13], [4, 1]);
        const axis = 0;
        const gradients = tf.grad(t => tf.gather(t, indices, axis))(t, dy);
        expect(gradients.shape).toEqual(t.shape);
        expectArraysClose(await gradients.data(), [26, 36, 0, 0]);
    });
    it('gradient 2D (gather) axis=0 shape=[4, 1] 2D indices', async () => {
        const t = tf.tensor2d([1, 11, 2, 22], [4, 1]);
        const indices = tf.tensor2d([1, 0, 0, 1], [2, 2], 'int32');
        const dy = tf.tensor([23, 7, 19, 13], [2, 2, 1]);
        const axis = 0;
        const gradients = tf.grad(t => tf.gather(t, indices, axis))(t, dy);
        expect(gradients.shape).toEqual(t.shape);
        expectArraysClose(await gradients.data(), [26, 36, 0, 0]);
    });
    it('gradient 2D (gather) axis=1 shape=[4, 2] 1D indices batchDims 1', async () => {
        const t = tf.variable(tf.tensor([[0, 1],
            [1, 2],
            [2, 3],
            [3, 4]]));
        const indices = tf.tensor([0, 1, 0, 1], [4, 1], 'int32');
        const dy = tf.tensor([1, 1, 1, 1], [4, 1]);
        const axis = 1;
        const gradients = tf.grad(t => tf.gather(t, indices, axis, 1))(t, dy);
        expect(gradients.shape).toEqual(t.shape);
        expectArraysClose(await gradients.data(), [1, 0, 0, 1, 1, 0, 0, 1]);
    });
    it('gradient 2D (gather) axis=1 shape=[2, 2] 1D indices', async () => {
        const t = tf.tensor2d([1, 11, 2, 22], [2, 2]);
        const indices = tf.tensor1d([1, 0, 0, 1], 'int32');
        const dy = tf.tensor([3, 4, 5, 6, 7, 8, 9, 10], [2, 4]);
        const axis = 1;
        const gradients = tf.grad(t => tf.gather(t, indices, axis))(t, dy);
        expect(gradients.shape).toEqual(t.shape);
        expectArraysClose(await gradients.data(), [9, 9, 17, 17]);
    });
    it('gradient 2D (gather) axis=1 shape=[2, 2] 2D indices', async () => {
        const t = tf.tensor2d([1, 11, 2, 22], [2, 2]);
        const indices = tf.tensor2d([1, 0, 0, 1], [2, 2], 'int32');
        const dy = tf.tensor([3, 4, 5, 6, 7, 8, 9, 10], [2, 2, 2]);
        const axis = 1;
        const gradients = tf.grad(t => tf.gather(t, indices, axis))(t, dy);
        expect(gradients.shape).toEqual(t.shape);
        expectArraysClose(await gradients.data(), [9, 9, 17, 17]);
    });
    it('gradient 2D (gather) axis=1 shape=[4, 1] 1D indices', async () => {
        const t = tf.tensor2d([1, 11, 2, 22], [4, 1]);
        const indices = tf.tensor1d([0, 0, 0, 0], 'int32');
        const dy = tf.tensor([3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18], [4, 4]);
        const axis = 1;
        const gradients = tf.grad(t => tf.gather(t, indices, axis))(t, dy);
        expect(gradients.shape).toEqual(t.shape);
        expectArraysClose(await gradients.data(), [18, 34, 50, 66]);
    });
    it('gradient 2D (gather) axis=1 shape=[4, 1] 2D indices', async () => {
        const t = tf.tensor2d([1, 11, 2, 22], [4, 1]);
        const indices = tf.tensor2d([0, 0, 0, 0], [2, 2], 'int32');
        const dy = tf.tensor([3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18], [4, 2, 2]);
        const axis = 1;
        const gradients = tf.grad(t => tf.gather(t, indices, axis))(t, dy);
        expect(gradients.shape).toEqual(t.shape);
        expectArraysClose(await gradients.data(), [18, 34, 50, 66]);
    });
    it('gradient 3D (gather) axis=0 shape=[2, 3, 2] 1D indices', async () => {
        const t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [2, 3, 2]);
        const indices = tf.tensor1d([1, 0, 0, 1], 'int32');
        const dy = tf.tensor([
            2, -3, 4, 15, 6, 0.7, 1, 18, 0.01, 0, 12, 13,
            4, 15, 12, -7, 18, 19, 2, 21, 6, 23, 24, 25
        ], [4, 3, 2]);
        const axis = 0;
        const gradients = tf.grad(t => tf.gather(t, indices, axis))(t, dy);
        expect(gradients.shape).toEqual(t.shape);
        expectArraysClose(await gradients.data(), [5, 33, 12.01, -7, 30, 32, 4, 18, 10, 38, 30, 25.7]);
    });
    it('gradient 3D (gather) axis=0 shape=[2, 3, 2] 2D indices', async () => {
        const t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [2, 3, 2]);
        const indices = tf.tensor2d([1, 0, 0, 1], [2, 2], 'int32');
        const dy = tf.tensor([
            2, -3, 4, 15, 6, 0.7, 1, 18, 0.01, 0, 12, 13,
            4, 15, 12, -7, 18, 19, 2, 21, 6, 23, 24, 25
        ], [2, 2, 3, 2]);
        const axis = 0;
        const gradients = tf.grad(t => tf.gather(t, indices, axis))(t, dy);
        expect(gradients.shape).toEqual(t.shape);
        expectArraysClose(await gradients.data(), [5, 33, 12.01, -7, 30, 32, 4, 18, 10, 38, 30, 25.7]);
    });
    it('gradient 3D (gather) axis=0 shape=[1, 4, 4]', async () => {
        const t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16], [1, 4, 4]);
        const indices = tf.tensor1d([0, 0], 'int32');
        const dy = tf.tensor([
            2, -3, 4, 15, 6, 0.7, 1, 18, 0.01, 0, 12, 13, 4, 15, 12, -7,
            18, 19, 2, 21, 6, 23, 24, 25, 101, 31, 34, 54, 1, 0, -3, -4
        ], [2, 4, 4]);
        const axis = 0;
        const gradients = tf.grad(t => tf.gather(t, indices, axis))(t, dy);
        expect(gradients.shape).toEqual(t.shape);
        expectArraysClose(await gradients.data(), [20, 16, 6, 36, 12, 23.7, 25, 43, 101.01, 31, 46, 67, 5, 15, 9, -11]);
    });
    it('gradient 3D (gather) axis=0 shape=[1, 4, 4] 1D indices', async () => {
        const t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16], [1, 4, 4]);
        const indices = tf.tensor1d([0, 0], 'int32');
        const dy = tf.tensor([
            2, -3, 4, 15, 6, 0.7, 1, 18, 0.01, 0, 12, 13, 4, 15, 12, -7,
            18, 19, 2, 21, 6, 23, 24, 25, 101, 31, 34, 54, 1, 0, -3, -4
        ], [2, 4, 4]);
        const axis = 0;
        const gradients = tf.grad(t => tf.gather(t, indices, axis))(t, dy);
        expect(gradients.shape).toEqual(t.shape);
        expectArraysClose(await gradients.data(), [20, 16, 6, 36, 12, 23.7, 25, 43, 101.01, 31, 46, 67, 5, 15, 9, -11]);
    });
    it('gradient 3D (gather) axis=1 shape=[2, 3, 2] 2D indices', async () => {
        const t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [2, 3, 2]);
        const indices = tf.tensor2d([1, 2, 2, 1], [2, 2], 'int32');
        const dy = tf.tensor([2, -3, 4, 15, 6, 0.7, 1, 18, 0.01, 0, 12, 13, 4, 15, 12, -7], [2, 2, 2, 2]);
        const axis = 1;
        const gradients = tf.grad(t => tf.gather(t, indices, axis))(t, dy);
        expect(gradients.shape).toEqual(t.shape);
        expectArraysClose(await gradients.data(), [0, 0, 3, 15, 10, 15.7, 0, 0, 12.01, -7, 16, 28]);
    });
    it('gradient 3D (gather) axis=1 shape=[1, 4, 4] 1D indices', async () => {
        const t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16], [1, 4, 4]);
        const indices = tf.tensor1d([1, 2, 2, 1], 'int32');
        const dy = tf.tensor([2, -3, 4, 15, 6, 0.7, 1, 18, 0.01, 0, 12, 13, 4, 15, 12, -7], [1, 4, 4]);
        const axis = 1;
        const gradients = tf.grad(t => tf.gather(t, indices, axis))(t, dy);
        expect(gradients.shape).toEqual(t.shape);
        expectArraysClose(await gradients.data(), [0, 0, 0, 0, 6, 12, 16, 8, 6.01, .7, 13, 31, 0, 0, 0, 0]);
    });
    it('gradient 3D (gather) axis=1 shape=[1, 4, 4] 2D indices', async () => {
        const t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16], [1, 4, 4]);
        const indices = tf.tensor2d([1, 2, 2, 1], [2, 2], 'int32');
        const dy = tf.tensor([2, -3, 4, 15, 6, 0.7, 1, 18, 0.01, 0, 12, 13, 4, 15, 12, -7], [1, 2, 2, 4]);
        const axis = 1;
        const gradients = tf.grad(t => tf.gather(t, indices, axis))(t, dy);
        expect(gradients.shape).toEqual(t.shape);
        expectArraysClose(await gradients.data(), [0, 0, 0, 0, 6, 12, 16, 8, 6.01, .7, 13, 31, 0, 0, 0, 0]);
    });
    it('gradient 3D (gather) axis=2 shape=[2, 3, 2] 1D indices', async () => {
        const t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [2, 3, 2]);
        const indices = tf.tensor1d([1, 0, 1, 0], 'int32');
        const dy = tf.tensor([
            2, -3, 4, 15, 6, 0.7, 1, 18, 0.01, 0, 12, 13,
            4, 15, 12, -7, 18, 19, 2, 21, 6, 23, 24, 25
        ], [2, 3, 4]);
        const axis = 2;
        const gradients = tf.grad(t => tf.gather(t, indices, axis))(t, dy);
        expect(gradients.shape).toEqual(t.shape);
        expectArraysClose(await gradients.data(), [12, 6, 18.7, 7, 13, 12.01, 8, 16, 40, 20, 48, 30]);
    });
    it('gradient 3D (gather) axis=2 shape=[2, 3, 2] 2D indices', async () => {
        const t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [2, 3, 2]);
        const indices = tf.tensor2d([1, 0, 1, 0], [2, 2], 'int32');
        const dy = tf.tensor([
            2, -3, 4, 15, 6, 0.7, 1, 18, 0.01, 0, 12, 13,
            4, 15, 12, -7, 18, 19, 2, 21, 6, 23, 24, 25
        ], [2, 3, 2, 2]);
        const axis = 2;
        const gradients = tf.grad(t => tf.gather(t, indices, axis))(t, dy);
        expect(gradients.shape).toEqual(t.shape);
        expectArraysClose(await gradients.data(), [12, 6, 18.7, 7, 13, 12.01, 8, 16, 40, 20, 48, 30]);
    });
    it('gradient 3D (gather) axis=2 shape=[4, 1, 4] 1D indices', async () => {
        const t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16], [4, 1, 4]);
        const indices = tf.tensor1d([1, 3, 1], 'int32');
        const dy = tf.tensor([2, -3, 4, 15, 6, 0.7, 1, 18, 0.01, 0, 4, 15], [4, 1, 3]);
        const axis = 2;
        const gradients = tf.grad(t => tf.gather(t, indices, axis))(t, dy);
        expect(gradients.shape).toEqual(t.shape);
        expectArraysClose(await gradients.data(), [0, 6, 0, -3, 0, 15.7, 0, 6, 0, 1.01, 0, 18, 0, 15, 0, 4]);
    });
    it('gradient 3D (gather) axis=2 shape=[4, 1, 4] 2D indices', async () => {
        const t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16], [4, 1, 4]);
        const indices = tf.tensor2d([1, 3, 1], [1, 3], 'int32');
        const dy = tf.tensor([2, -3, 4, 15, 6, 0.7, 1, 18, 0.01, 0, 4, 15], [4, 1, 1, 3]);
        const axis = 2;
        const gradients = tf.grad(t => tf.gather(t, indices, axis))(t, dy);
        expect(gradients.shape).toEqual(t.shape);
        expectArraysClose(await gradients.data(), [0, 6, 0, -3, 0, 15.7, 0, 6, 0, 1.01, 0, 18, 0, 15, 0, 4]);
    });
    it('ensure no memory leak', async () => {
        const numTensorsBefore = tf.memory().numTensors;
        const numDataIdBefore = tf.engine().backend.numDataIds();
        const t = tf.tensor1d([1, 2, 3]);
        const t1 = tf.scalar(1, 'int32');
        const t2 = tf.gather(t, t1, 0);
        expect(t2.shape).toEqual([]);
        expectArraysClose(await t2.data(), [2]);
        t.dispose();
        t1.dispose();
        t2.dispose();
        const numTensorsAfter = tf.memory().numTensors;
        const numDataIdAfter = tf.engine().backend.numDataIds();
        expect(numTensorsAfter).toBe(numTensorsBefore);
        expect(numDataIdAfter).toBe(numDataIdBefore);
    });
    it('fills with zero when index is out of bound', async () => {
        if (env.backendName === 'webgl' || env.backendName === 'webgpu') {
            const t = tf.tensor2d([1, 11, 2, 22], [2, 2]);
            const tInt = tf.tensor2d([1, 11, 2, 22], [2, 2], 'int32');
            const index = tf.tensor1d([0, 1, 100, -1, 2, -4], 'int32');
            const res = tf.gather(t, index);
            const resInt = tf.gather(tInt, index);
            const expected = [1, 11, 2, 22, 0, 0, 0, 0, 0, 0, 0, 0];
            expectArraysClose(await res.data(), expected);
            expectArraysClose(await resInt.data(), expected);
        }
    });
});
//# sourceMappingURL=data:application/json;base64,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