/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as seedrandom from 'seedrandom';
import { expectNumbersClose, testEpsilon } from '../test_util';
// https://en.wikipedia.org/wiki/Marsaglia_polar_method
export class MPRandGauss {
    constructor(mean, stdDeviation, dtype, truncated, seed) {
        this.mean = mean;
        this.stdDev = stdDeviation;
        this.dtype = dtype;
        this.nextVal = NaN;
        this.truncated = truncated;
        if (this.truncated) {
            this.upper = this.mean + this.stdDev * 2;
            this.lower = this.mean - this.stdDev * 2;
        }
        const seedValue = seed ? seed : Math.random();
        this.random = seedrandom.alea(seedValue.toString());
    }
    /** Returns next sample from a Gaussian distribution. */
    nextValue() {
        if (!isNaN(this.nextVal)) {
            const value = this.nextVal;
            this.nextVal = NaN;
            return value;
        }
        let resultX, resultY;
        let isValid = false;
        while (!isValid) {
            let v1, v2, s;
            do {
                v1 = 2 * this.random() - 1;
                v2 = 2 * this.random() - 1;
                s = v1 * v1 + v2 * v2;
            } while (s >= 1 || s === 0);
            const mul = Math.sqrt(-2.0 * Math.log(s) / s);
            resultX = this.mean + this.stdDev * v1 * mul;
            resultY = this.mean + this.stdDev * v2 * mul;
            if (!this.truncated || this.isValidTruncated(resultX)) {
                isValid = true;
            }
        }
        if (!this.truncated || this.isValidTruncated(resultY)) {
            this.nextVal = this.convertValue(resultY);
        }
        return this.convertValue(resultX);
    }
    /** Handles proper rounding for non-floating-point numbers. */
    convertValue(value) {
        if (this.dtype == null || this.dtype === 'float32') {
            return value;
        }
        return Math.round(value);
    }
    /** Returns true if less than 2-standard-deviations from the mean. */
    isValidTruncated(value) {
        return value <= this.upper && value >= this.lower;
    }
}
// Marsaglia, George, and Wai Wan Tsang. 2000. "A Simple Method for Generating
// Gamma Variables."
export class RandGamma {
    constructor(alpha, beta, dtype, seed) {
        this.alpha = alpha;
        this.beta = 1 / beta; // convert rate to scale parameter
        this.dtype = dtype;
        const seedValue = seed ? seed : Math.random();
        this.randu = seedrandom.alea(seedValue.toString());
        this.randn = new MPRandGauss(0, 1, dtype, false, this.randu());
        if (alpha < 1) {
            this.d = alpha + (2 / 3);
        }
        else {
            this.d = alpha - (1 / 3);
        }
        this.c = 1 / Math.sqrt(9 * this.d);
    }
    /** Returns next sample from a gamma distribution. */
    nextValue() {
        let x2, v0, v1, x, u, v;
        while (true) {
            do {
                x = this.randn.nextValue();
                v = 1 + (this.c * x);
            } while (v <= 0);
            v *= v * v;
            x2 = x * x;
            v0 = 1 - (0.331 * x2 * x2);
            v1 = (0.5 * x2) + (this.d * (1 - v + Math.log(v)));
            u = this.randu();
            if (u < v0 || Math.log(u) < v1) {
                break;
            }
        }
        v = (1 / this.beta) * this.d * v;
        if (this.alpha < 1) {
            v *= Math.pow(this.randu(), 1 / this.alpha);
        }
        return this.convertValue(v);
    }
    /** Handles proper rounding for non-floating-point numbers. */
    convertValue(value) {
        if (this.dtype === 'float32') {
            return value;
        }
        return Math.round(value);
    }
}
export class UniformRandom {
    constructor(min = 0, max = 1, dtype, seed) {
        /** Handles proper rounding for non floating point numbers. */
        this.canReturnFloat = () => (this.dtype == null || this.dtype === 'float32');
        this.min = min;
        this.range = max - min;
        this.dtype = dtype;
        if (seed == null) {
            seed = Math.random();
        }
        if (typeof seed === 'number') {
            seed = seed.toString();
        }
        if (!this.canReturnFloat() && this.range <= 1) {
            throw new Error(`The difference between ${min} - ${max} <= 1 and dtype is not float`);
        }
        this.random = seedrandom.alea(seed);
    }
    convertValue(value) {
        if (this.canReturnFloat()) {
            return value;
        }
        return Math.round(value);
    }
    nextValue() {
        return this.convertValue(this.min + this.range * this.random());
    }
}
export function jarqueBeraNormalityTest(values) {
    // https://en.wikipedia.org/wiki/Jarque%E2%80%93Bera_test
    const n = values.length;
    const s = skewness(values);
    const k = kurtosis(values);
    const jb = n / 6 * (Math.pow(s, 2) + 0.25 * Math.pow(k - 3, 2));
    // JB test requires 2-degress of freedom from Chi-Square @ 0.95:
    // http://www.itl.nist.gov/div898/handbook/eda/section3/eda3674.htm
    const CHI_SQUARE_2DEG = 5.991;
    if (jb > CHI_SQUARE_2DEG) {
        throw new Error(`Invalid p-value for JB: ${jb}`);
    }
}
export function expectArrayInMeanStdRange(actual, expectedMean, expectedStdDev, epsilon) {
    if (epsilon == null) {
        epsilon = testEpsilon();
    }
    const actualMean = mean(actual);
    expectNumbersClose(actualMean, expectedMean, epsilon);
    expectNumbersClose(standardDeviation(actual, actualMean), expectedStdDev, epsilon);
}
function mean(values) {
    let sum = 0;
    for (let i = 0; i < values.length; i++) {
        sum += values[i];
    }
    return sum / values.length;
}
function standardDeviation(values, mean) {
    let squareDiffSum = 0;
    for (let i = 0; i < values.length; i++) {
        const diff = values[i] - mean;
        squareDiffSum += diff * diff;
    }
    return Math.sqrt(squareDiffSum / values.length);
}
function kurtosis(values) {
    // https://en.wikipedia.org/wiki/Kurtosis
    const valuesMean = mean(values);
    const n = values.length;
    let sum2 = 0;
    let sum4 = 0;
    for (let i = 0; i < n; i++) {
        const v = values[i] - valuesMean;
        sum2 += Math.pow(v, 2);
        sum4 += Math.pow(v, 4);
    }
    return (1 / n) * sum4 / Math.pow((1 / n) * sum2, 2);
}
function skewness(values) {
    // https://en.wikipedia.org/wiki/Skewness
    const valuesMean = mean(values);
    const n = values.length;
    let sum2 = 0;
    let sum3 = 0;
    for (let i = 0; i < n; i++) {
        const v = values[i] - valuesMean;
        sum2 += Math.pow(v, 2);
        sum3 += Math.pow(v, 3);
    }
    return (1 / n) * sum3 / Math.pow((1 / (n - 1)) * sum2, 3 / 2);
}
//# sourceMappingURL=data:application/json;base64,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