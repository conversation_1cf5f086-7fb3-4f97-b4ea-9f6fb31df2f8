/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../../index';
import { ALL_ENVS, describeWithFlags } from '../../jasmine_util';
import { expectArraysClose } from '../../test_util';
import { scalar, tensor1d, tensor2d, tensor3d, tensor4d } from '../ops';
describeWithFlags('qr', ALL_ENVS, () => {
    it('1x1', async () => {
        const x = tensor2d([[10]], [1, 1]);
        const [q, r] = tf.linalg.qr(x);
        expectArraysClose(await q.array(), [[-1]]);
        expectArraysClose(await r.array(), [[-10]]);
    });
    it('2x2', async () => {
        const x = tensor2d([[1, 3], [-2, -4]], [2, 2]);
        const [q, r] = tf.linalg.qr(x);
        expectArraysClose(await q.array(), [[-0.4472, -0.8944], [0.8944, -0.4472]]);
        expectArraysClose(await r.array(), [[-2.2361, -4.9193], [0, -0.8944]]);
    });
    it('2x2x2', async () => {
        const x = tensor3d([[[-1, -3], [2, 4]], [[1, 3], [-2, -4]]], [2, 2, 2]);
        const [q, r] = tf.linalg.qr(x);
        expectArraysClose(await q.array(), [
            [[-0.4472, -0.8944], [0.8944, -0.4472]],
            [[-0.4472, -0.8944], [0.8944, -0.4472]]
        ]);
        expectArraysClose(await r.array(), [[[2.2361, 4.9193], [0, 0.8944]], [[-2.2361, -4.9193], [0, -0.8944]]]);
    });
    it('2x1x2x2', async () => {
        const x = tensor4d([[[[-1, -3], [2, 4]]], [[[1, 3], [-2, -4]]]], [2, 1, 2, 2]);
        const [q, r] = tf.linalg.qr(x);
        expectArraysClose(await q.array(), [
            [[[-0.4472, -0.8944], [0.8944, -0.4472]]],
            [[[-0.4472, -0.8944], [0.8944, -0.4472]]],
        ]);
        expectArraysClose(await r.array(), [
            [[[2.2361, 4.9193], [0, 0.8944]]], [[[-2.2361, -4.9193], [0, -0.8944]]]
        ]);
    });
    it('3x3', async () => {
        const x = tensor2d([[1, 3, 2], [-2, 0, 7], [8, -9, 4]], [3, 3]);
        const [q, r] = tf.linalg.qr(x);
        expectArraysClose(await q.array(), [
            [-0.1204, 0.8729, 0.4729], [0.2408, -0.4364, 0.8669],
            [-0.9631, -0.2182, 0.1576]
        ]);
        expectArraysClose(await r.array(), [[-8.3066, 8.3066, -2.4077], [0, 4.5826, -2.1822], [0, 0, 7.6447]]);
    });
    it('3x3, zero on diagonal', async () => {
        const x = tensor2d([[0, 2, 2], [1, 1, 1], [0, 1, 2]], [3, 3]);
        const [q, r] = tf.linalg.qr(x);
        expectArraysClose(await q.data(), [
            [0., -0.89442719, 0.4472136], [1., 0., 0.], [0., -0.4472136, -0.89442719]
        ]);
        expectArraysClose(await r.data(), [[1., 1., 1.], [0., -2.23606798, -2.68328157], [0., 0., -0.89442719]]);
    });
    it('3x2, fullMatrices = default false', async () => {
        const x = tensor2d([[1, 2], [3, -3], [-2, 1]], [3, 2]);
        const [q, r] = tf.linalg.qr(x);
        expectArraysClose(await q.array(), [[-0.2673, 0.9221], [-0.8018, -0.3738], [0.5345, -0.0997]]);
        expectArraysClose(await r.array(), [[-3.7417, 2.4054], [0, 2.8661]]);
    });
    it('3x2, fullMatrices = true', async () => {
        const x = tensor2d([[1, 2], [3, -3], [-2, 1]], [3, 2]);
        const [q, r] = tf.linalg.qr(x, true);
        expectArraysClose(await q.array(), [
            [-0.2673, 0.9221, 0.2798], [-0.8018, -0.3738, 0.4663],
            [0.5345, -0.0997, 0.8393]
        ]);
        expectArraysClose(await r.array(), [[-3.7417, 2.4054], [0, 2.8661], [0, 0]]);
    });
    it('2x3, fullMatrices = default false', async () => {
        const x = tensor2d([[1, 2, 3], [-3, -2, 1]], [2, 3]);
        const [q, r] = tf.linalg.qr(x);
        expectArraysClose(await q.array(), [[-0.3162278, -0.9486833], [0.9486833, -0.31622773]]);
        expectArraysClose(await r.array(), [[-3.162, -2.5298, -2.3842e-07], [0, -1.2649, -3.162]]);
    });
    it('2x3, fullMatrices = true', async () => {
        const x = tensor2d([[1, 2, 3], [-3, -2, 1]], [2, 3]);
        const [q, r] = tf.linalg.qr(x, true);
        expectArraysClose(await q.array(), [[-0.3162278, -0.9486833], [0.9486833, -0.31622773]]);
        expectArraysClose(await r.array(), [[-3.162, -2.5298, -2.3842e-07], [0, -1.2649, -3.162]]);
    });
    it('Does not leak memory', () => {
        const x = tensor2d([[1, 3], [-2, -4]], [2, 2]);
        // The first call to qr creates and keeps internal singleton tensors.
        // Subsequent calls should always create exactly two tensors.
        tf.linalg.qr(x);
        // Count before real call.
        const numTensors = tf.memory().numTensors;
        tf.linalg.qr(x);
        expect(tf.memory().numTensors).toEqual(numTensors + 2);
    });
    it('Insuffient input tensor rank leads to error', () => {
        const x1 = scalar(12);
        expect(() => tf.linalg.qr(x1)).toThrowError(/rank >= 2.*got rank 0/);
        const x2 = tensor1d([12]);
        expect(() => tf.linalg.qr(x2)).toThrowError(/rank >= 2.*got rank 1/);
    });
});
//# sourceMappingURL=data:application/json;base64,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