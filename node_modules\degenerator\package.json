{"name": "degenerator", "version": "5.0.1", "description": "Compiles sync functions into async generator functions", "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "author": "<PERSON> <<EMAIL>> (http://n8.io/)", "repository": {"type": "git", "url": "https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/degenerator"}, "engines": {"node": ">= 14"}, "license": "MIT", "dependencies": {"ast-types": "^0.13.4", "escodegen": "^2.1.0", "esprima": "^4.0.1"}, "devDependencies": {"@tootallnate/quickjs-emscripten": "^0.23.0", "@types/escodegen": "^0.0.7", "@types/esprima": "^4.0.3", "@types/jest": "^29.5.2", "@types/node": "^14.18.52", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.1.6", "tsconfig": "0.0.0"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}}