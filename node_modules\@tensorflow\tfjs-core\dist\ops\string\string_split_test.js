/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../../index';
import { ALL_ENVS, describeWithFlags } from '../../jasmine_util';
import { expectArraysEqual } from '../../test_util';
async function expectResult(result, indices, values, shape) {
    expectArraysEqual(await result.indices.data(), indices);
    expectArraysEqual(await result.values.data(), values);
    expectArraysEqual(await result.shape.data(), shape);
    expect(result.indices.shape).toEqual([indices.length, 2]);
    expect(result.values.shape).toEqual([values.length]);
    expect(result.shape.shape).toEqual([2]);
    expect(result.indices.dtype).toEqual('int32');
    expect(result.values.dtype).toEqual('string');
    expect(result.shape.dtype).toEqual('int32');
}
describeWithFlags('stringSplit', ALL_ENVS, () => {
    it('white space delimiter', async () => {
        const result = tf.string.stringSplit(['pigs on the wing', 'animals'], ' ');
        await expectResult(result, [[0, 0], [0, 1], [0, 2], [0, 3], [1, 0]], ['pigs', 'on', 'the', 'wing', 'animals'], [2, 4]);
    });
    it('empty delimiter', async () => {
        const result = tf.string.stringSplit(['hello', 'hola', 'hi'], '');
        await expectResult(result, [
            [0, 0], [0, 1], [0, 2], [0, 3], [0, 4], [1, 0], [1, 1], [1, 2],
            [1, 3], [2, 0], [2, 1]
        ], ['h', 'e', 'l', 'l', 'o', 'h', 'o', 'l', 'a', 'h', 'i'], [3, 5]);
    });
    it('empty token', async () => {
        const result = tf.string.stringSplit(['', ' a', 'b ', ' c', ' ', ' d ', '  e', 'f  ', '  g  ', '  '], ' ');
        await expectResult(result, [[1, 0], [2, 0], [3, 0], [5, 0], [6, 0], [7, 0], [8, 0]], ['a', 'b', 'c', 'd', 'e', 'f', 'g'], [10, 1]);
    });
    it('set empty token', async () => {
        const result = tf.string.stringSplit(['', ' a', 'b ', ' c', ' ', ' d ', '. e', 'f .', ' .g. ', ' .'], ' .');
        await expectResult(result, [[1, 0], [2, 0], [3, 0], [5, 0], [6, 0], [7, 0], [8, 0]], ['a', 'b', 'c', 'd', 'e', 'f', 'g'], [10, 1]);
    });
    it('with delimiter', async () => {
        const input = ['hello|world', 'hello world'];
        let result = tf.string.stringSplit(input, '|');
        await expectResult(result, [[0, 0], [0, 1], [1, 0]], ['hello', 'world', 'hello world'], [2, 2]);
        result = tf.string.stringSplit(input, '| ');
        await expectResult(result, [[0, 0], [0, 1], [1, 0], [1, 1]], ['hello', 'world', 'hello', 'world'], [2, 2]);
        result =
            tf.string.stringSplit(['hello.cruel,world', 'hello cruel world'], '.,');
        await expectResult(result, [[0, 0], [0, 1], [0, 2], [1, 0]], ['hello', 'cruel', 'world', 'hello cruel world'], [2, 3]);
    });
    it('no skip empty', async () => {
        const input = ['#a', 'b#', '#c#'];
        let result = tf.string.stringSplit(input, '#', false);
        await expectResult(result, [[0, 0], [0, 1], [1, 0], [1, 1], [2, 0], [2, 1], [2, 2]], ['', 'a', 'b', '', '', 'c', ''], [3, 3]);
        result = tf.string.stringSplit(input, '#');
        await expectResult(result, [[0, 0], [1, 0], [2, 0]], ['a', 'b', 'c'], [3, 1]);
    });
    it('large input does not cause an argument overflow', async () => {
        const input = 'a'.repeat(200000);
        const result = tf.string.stringSplit([input], '');
        await expectResult(result, Array(input.length).fill(0).map((_, i) => [0, i]), input.split(''), [1, input.length]);
    });
});
//# sourceMappingURL=data:application/json;base64,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