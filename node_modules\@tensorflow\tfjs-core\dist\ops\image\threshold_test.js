/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../../index';
import { ALL_ENVS, describeWithFlags } from '../../jasmine_util';
import { expectArraysEqual } from '../../test_util';
describeWithFlags('threshold', ALL_ENVS, () => {
    let originalTimeout;
    beforeEach(() => {
        originalTimeout = jasmine.DEFAULT_TIMEOUT_INTERVAL;
        jasmine.DEFAULT_TIMEOUT_INTERVAL = 80000;
    });
    afterAll(() => {
        jasmine.DEFAULT_TIMEOUT_INTERVAL = originalTimeout;
    });
    it('default method binary, no arguments passed but input image', async () => {
        const image = tf.tensor3d([144, 255, 51, 13, 32, 59, 222, 100, 51, 69, 71, 222], [2, 2, 3]);
        const thresholded = tf.image.threshold(image);
        expect(thresholded.shape).toEqual([2, 2, 1]);
        expect(thresholded.dtype).toBe('int32');
        expectArraysEqual(await thresholded.data(), [255, 0, 255, 0]);
    });
    it('default method binary, inverted: false, threshValue = 0.7', async () => {
        const image = tf.tensor3d([
            255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 253, 252, 235,
            195, 252, 234, 192, 255, 254, 253, 255, 255, 255, 255, 255, 255, 255,
            255, 255, 255, 255, 255, 255, 255, 255, 253, 245, 237, 247, 198, 107,
            239, 156, 43, 236, 139, 68, 246, 201, 139, 255, 255, 255, 255, 255,
            255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 252, 239, 235, 241,
            171, 122, 233, 125, 44, 250, 221, 183, 252, 240, 229, 255, 255, 255,
            255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
            255, 251, 233, 227, 229, 105, 42, 249, 221, 190, 255, 255, 255, 255,
            255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
            255, 255, 255, 254, 247, 245, 243, 184, 144, 255, 254, 254, 255, 255,
            255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 253, 244, 241, 249,
            217, 206, 251, 231, 224, 251, 230, 223, 250, 225, 217, 250, 224, 215,
            250, 228, 220, 247, 235, 231, 235, 234, 234, 255, 255, 255, 255, 253,
            253, 252, 235, 230, 253, 243, 240, 251, 233, 226, 253, 242, 238, 254,
            247, 244, 252, 235, 230, 242, 232, 229, 240, 240, 240, 255, 255, 255
        ], [7, 10, 3]);
        const method = 'binary';
        const inverted = false;
        const threshValue = 0.7;
        const output = tf.image.threshold(image, method, inverted, threshValue);
        expect(output.shape).toEqual([7, 10, 1]);
        expect(output.dtype).toBe('int32');
        expectArraysEqual(await output.data(), [
            255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
            0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 0, 255, 255, 255,
            255, 255, 255, 255, 255, 255, 0, 255, 255, 255, 255, 255, 255, 255,
            255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
            255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255
        ]);
    });
    it('default method binary, inverted: true, threshValue = 0.7', async () => {
        const image = tf.tensor3d([
            255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 253, 252, 235,
            195, 252, 234, 192, 255, 254, 253, 255, 255, 255, 255, 255, 255, 255,
            255, 255, 255, 255, 255, 255, 255, 255, 253, 245, 237, 247, 198, 107,
            239, 156, 43, 236, 139, 68, 246, 201, 139, 255, 255, 255, 255, 255,
            255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 252, 239, 235, 241,
            171, 122, 233, 125, 44, 250, 221, 183, 252, 240, 229, 255, 255, 255,
            255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
            255, 251, 233, 227, 229, 105, 42, 249, 221, 190, 255, 255, 255, 255,
            255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
            255, 255, 255, 254, 247, 245, 243, 184, 144, 255, 254, 254, 255, 255,
            255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 253, 244, 241, 249,
            217, 206, 251, 231, 224, 251, 230, 223, 250, 225, 217, 250, 224, 215,
            250, 228, 220, 247, 235, 231, 235, 234, 234, 255, 255, 255, 255, 253,
            253, 252, 235, 230, 253, 243, 240, 251, 233, 226, 253, 242, 238, 254,
            247, 244, 252, 235, 230, 242, 232, 229, 240, 240, 240, 255, 255, 255
        ], [7, 10, 3]);
        const threshValue = 0.7;
        const method = 'binary';
        const inverted = true;
        const output = tf.image.threshold(image, method, inverted, threshValue);
        expect(output.shape).toEqual([7, 10, 1]);
        expect(output.dtype).toBe('int32');
        expectArraysEqual(await output.data(), [
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 0, 0,
            0, 0, 0, 0, 0, 0, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
        ]);
    });
    it('method otsu', async () => {
        const image = tf.tensor3d([
            255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 253, 252, 235,
            195, 252, 234, 192, 255, 254, 253, 255, 255, 255, 255, 255, 255, 255,
            255, 255, 255, 255, 255, 255, 255, 255, 253, 245, 237, 247, 198, 107,
            239, 156, 43, 236, 139, 68, 246, 201, 139, 255, 255, 255, 255, 255,
            255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 252, 239, 235, 241,
            171, 122, 233, 125, 44, 250, 221, 183, 252, 240, 229, 255, 255, 255,
            255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
            255, 251, 233, 227, 229, 105, 42, 249, 221, 190, 255, 255, 255, 255,
            255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
            255, 255, 255, 254, 247, 245, 243, 184, 144, 255, 254, 254, 255, 255,
            255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 253, 244, 241, 249,
            217, 206, 251, 231, 224, 251, 230, 223, 250, 225, 217, 250, 224, 215,
            250, 228, 220, 247, 235, 231, 235, 234, 234, 255, 255, 255, 255, 253,
            253, 252, 235, 230, 253, 243, 240, 251, 233, 226, 253, 242, 238, 254,
            247, 244, 252, 235, 230, 242, 232, 229, 240, 240, 240, 255, 255, 255
        ], [7, 10, 3]);
        const method = 'otsu';
        const output = tf.image.threshold(image, method);
        expect(output.shape).toEqual([7, 10, 1]);
        expect(output.dtype).toBe('int32');
        expectArraysEqual(await output.data(), [
            255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0,
            0, 0, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255,
            255, 255, 255, 255, 255, 255, 0, 255, 255, 255, 255, 255, 255, 255,
            255, 255, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
            255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255
        ]);
    });
    it('method otsu, inverted = true', async () => {
        const image = tf.tensor3d([
            255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 253, 252, 235,
            195, 252, 234, 192, 255, 254, 253, 255, 255, 255, 255, 255, 255, 255,
            255, 255, 255, 255, 255, 255, 255, 255, 253, 245, 237, 247, 198, 107,
            239, 156, 43, 236, 139, 68, 246, 201, 139, 255, 255, 255, 255, 255,
            255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 252, 239, 235, 241,
            171, 122, 233, 125, 44, 250, 221, 183, 252, 240, 229, 255, 255, 255,
            255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
            255, 251, 233, 227, 229, 105, 42, 249, 221, 190, 255, 255, 255, 255,
            255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
            255, 255, 255, 254, 247, 245, 243, 184, 144, 255, 254, 254, 255, 255,
            255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 253, 244, 241, 249,
            217, 206, 251, 231, 224, 251, 230, 223, 250, 225, 217, 250, 224, 215,
            250, 228, 220, 247, 235, 231, 235, 234, 234, 255, 255, 255, 255, 253,
            253, 252, 235, 230, 253, 243, 240, 251, 233, 226, 253, 242, 238, 254,
            247, 244, 252, 235, 230, 242, 232, 229, 240, 240, 240, 255, 255, 255
        ], [7, 10, 3]);
        const method = 'otsu';
        const inverted = true;
        const output = tf.image.threshold(image, method, inverted);
        expect(output.shape).toEqual([7, 10, 1]);
        expect(output.dtype).toBe('int32');
        expectArraysEqual(await output.data(), [
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 0, 0,
            0, 0, 0, 0, 0, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
        ]);
    });
});
//# sourceMappingURL=data:application/json;base64,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