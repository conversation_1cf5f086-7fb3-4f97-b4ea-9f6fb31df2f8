{"symbol": "EURJPY", "timeframe": 300, "lastUpdate": "2025-07-07T02:42:31.827Z", "candlesCount": 501, "candles": [{"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T08:58:41.291Z", "timeframe": 300, "open": 1.1155758646411449, "high": 1.1161677620472872, "low": 1.114652206704086, "close": 1.1160695550748532, "volume": 154, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T09:03:41.291Z", "timeframe": 300, "open": 1.137637765137852, "high": 1.1380211224315508, "low": 1.136782372104139, "close": 1.137580751958559, "volume": 1003, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T09:08:41.291Z", "timeframe": 300, "open": 1.1088137567308816, "high": 1.1091419955338166, "low": 1.1082034774247302, "close": 1.1084615889968856, "volume": 840, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T09:13:41.291Z", "timeframe": 300, "open": 1.1068210546416586, "high": 1.1071590983287647, "low": 1.1066020800697647, "close": 1.1066020800697647, "volume": 1023, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T09:18:41.291Z", "timeframe": 300, "open": 1.1271108857385324, "high": 1.1274664179017726, "low": 1.1262556352245776, "close": 1.1266227255043233, "volume": 398, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T09:23:41.291Z", "timeframe": 300, "open": 1.1972761119943285, "high": 1.1981983204643576, "low": 1.1968216113930341, "close": 1.197203545241606, "volume": 990, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T09:28:41.291Z", "timeframe": 300, "open": 1.1376235953890805, "high": 1.1380465401657642, "low": 1.1368897013128993, "close": 1.137987536228993, "volume": 856, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T09:33:41.291Z", "timeframe": 300, "open": 1.1818566775491184, "high": 1.1826157252745055, "low": 1.181668654680412, "close": 1.1817727809833853, "volume": 704, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T09:38:41.291Z", "timeframe": 300, "open": 1.1782673742649528, "high": 1.1789941433437214, "low": 1.1777160932898725, "close": 1.1780330113587256, "volume": 659, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T09:43:41.291Z", "timeframe": 300, "open": 1.1220101469665154, "high": 1.1228576281502045, "low": 1.1218143239622351, "close": 1.1218143239622351, "volume": 738, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T09:48:41.291Z", "timeframe": 300, "open": 1.1974618922562241, "high": 1.1982449705181424, "low": 1.1969323529437688, "close": 1.1976482306661433, "volume": 937, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T09:53:41.291Z", "timeframe": 300, "open": 1.1082261970648293, "high": 1.1091305297197875, "low": 1.1081562914394487, "close": 1.1083064207904414, "volume": 878, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T09:58:41.291Z", "timeframe": 300, "open": 1.1345748787446635, "high": 1.1348110339731905, "low": 1.1344162981674608, "close": 1.1347640265320793, "volume": 773, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T10:03:41.291Z", "timeframe": 300, "open": 1.1407188827180321, "high": 1.1411470814923508, "low": 1.140315008990012, "close": 1.140525139401861, "volume": 118, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T10:08:41.291Z", "timeframe": 300, "open": 1.1826551031964916, "high": 1.183594255813228, "low": 1.181823007577694, "close": 1.1825341479995284, "volume": 366, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T10:13:41.291Z", "timeframe": 300, "open": 1.1047413130057588, "high": 1.1049918486648749, "low": 1.1040600523439816, "close": 1.1049918486648749, "volume": 124, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T10:18:41.291Z", "timeframe": 300, "open": 1.121418248891299, "high": 1.122378048034419, "low": 1.1207478808862492, "close": 1.1218266040931872, "volume": 794, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T10:23:41.291Z", "timeframe": 300, "open": 1.163289344084309, "high": 1.164026213191395, "low": 1.162672927488287, "close": 1.1635012724031149, "volume": 560, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T10:28:41.291Z", "timeframe": 300, "open": 1.165560798556054, "high": 1.1661551764658784, "low": 1.1649329632081458, "close": 1.1652154144495557, "volume": 190, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T10:33:41.291Z", "timeframe": 300, "open": 1.1275977428176276, "high": 1.128575391155891, "low": 1.1270047646776733, "close": 1.1280332703224947, "volume": 852, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T10:38:41.291Z", "timeframe": 300, "open": 1.1067586093552448, "high": 1.1075168591241704, "low": 1.1058568438645746, "close": 1.1067115195807182, "volume": 577, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T10:43:41.291Z", "timeframe": 300, "open": 1.1705278365511964, "high": 1.1713506321939302, "low": 1.170070484386279, "close": 1.170070484386279, "volume": 780, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T10:48:41.291Z", "timeframe": 300, "open": 1.1062473019286543, "high": 1.1069234290106962, "low": 1.105538724880829, "close": 1.1066453266057625, "volume": 951, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T10:53:41.291Z", "timeframe": 300, "open": 1.110699822218231, "high": 1.1107381380846864, "low": 1.11048280443112, "close": 1.1106659000568215, "volume": 163, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T10:58:41.291Z", "timeframe": 300, "open": 1.1910451001541367, "high": 1.1911344471513772, "low": 1.1907488636744337, "close": 1.1908386042833778, "volume": 416, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T11:03:41.291Z", "timeframe": 300, "open": 1.1238094824905356, "high": 1.1241552019716743, "low": 1.1231846584455303, "close": 1.1237319939095198, "volume": 934, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T11:08:41.291Z", "timeframe": 300, "open": 1.104317303872357, "high": 1.1052728815039403, "low": 1.1039683051287923, "close": 1.1045446300381347, "volume": 845, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T11:13:41.291Z", "timeframe": 300, "open": 1.1845291836468246, "high": 1.185008561168056, "low": 1.1838485984948643, "close": 1.1845636457841184, "volume": 179, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T11:18:41.291Z", "timeframe": 300, "open": 1.134131464916745, "high": 1.1345240280434405, "low": 1.1332071696568766, "close": 1.1345240280434405, "volume": 245, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T11:23:41.291Z", "timeframe": 300, "open": 1.101148101865014, "high": 1.1019715147552764, "low": 1.1001791305938484, "close": 1.1010611936749792, "volume": 1094, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T11:28:41.291Z", "timeframe": 300, "open": 1.1614334387291596, "high": 1.1617932099346693, "low": 1.1610242689520833, "close": 1.1617932099346693, "volume": 965, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T11:33:41.291Z", "timeframe": 300, "open": 1.121497883961018, "high": 1.121963417171707, "low": 1.1205664600965375, "close": 1.1214541452296332, "volume": 993, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T11:38:41.291Z", "timeframe": 300, "open": 1.1021722147975455, "high": 1.102907824014654, "low": 1.101227013843841, "close": 1.1020718042373467, "volume": 725, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T11:43:41.291Z", "timeframe": 300, "open": 1.1305244628546265, "high": 1.1309011428182116, "low": 1.1298740883786644, "close": 1.1302928414116327, "volume": 257, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T11:48:41.291Z", "timeframe": 300, "open": 1.102028184099494, "high": 1.1024705985814125, "low": 1.1015948108429112, "close": 1.1024705985814125, "volume": 337, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T11:53:41.291Z", "timeframe": 300, "open": 1.1192526458132954, "high": 1.119767440706011, "low": 1.118801820031774, "close": 1.1190565116918665, "volume": 330, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T11:58:41.291Z", "timeframe": 300, "open": 1.187383492726728, "high": 1.1878943964904791, "low": 1.1867007653955521, "close": 1.1873936831757252, "volume": 900, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T12:03:41.291Z", "timeframe": 300, "open": 1.138539280613578, "high": 1.1385957312943273, "low": 1.1375746557792528, "close": 1.1383321090181346, "volume": 1037, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T12:08:41.291Z", "timeframe": 300, "open": 1.1511015552949782, "high": 1.151280183650522, "low": 1.150579146193569, "close": 1.1509260895896738, "volume": 126, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T12:13:41.291Z", "timeframe": 300, "open": 1.1389589573466927, "high": 1.1396468236570474, "low": 1.1381040232459458, "close": 1.1390411831461522, "volume": 343, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T12:18:41.291Z", "timeframe": 300, "open": 1.1541442409977016, "high": 1.155104025416735, "low": 1.1540578208895158, "close": 1.1544891711975622, "volume": 1026, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T12:23:41.291Z", "timeframe": 300, "open": 1.1210324883786462, "high": 1.1214082772725686, "low": 1.1209890495421897, "close": 1.1211453650284906, "volume": 292, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T12:28:41.291Z", "timeframe": 300, "open": 1.1857429201728071, "high": 1.1860503178533164, "low": 1.1850097305683218, "close": 1.1853102792497323, "volume": 109, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T12:33:41.291Z", "timeframe": 300, "open": 1.171658596178919, "high": 1.1725208131775804, "low": 1.1706923325733656, "close": 1.1716487659828159, "volume": 533, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T12:38:41.291Z", "timeframe": 300, "open": 1.1338879736769771, "high": 1.1346815345334778, "low": 1.1334421729031892, "close": 1.1336730465930551, "volume": 104, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T12:43:41.291Z", "timeframe": 300, "open": 1.1666045890821712, "high": 1.1671793472805214, "low": 1.1662634690190707, "close": 1.1662634690190707, "volume": 1023, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T12:48:41.291Z", "timeframe": 300, "open": 1.1907062312978218, "high": 1.1915681850149302, "low": 1.1904447658124053, "close": 1.190899768197712, "volume": 405, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T12:53:41.291Z", "timeframe": 300, "open": 1.1355848031472873, "high": 1.1360823017424464, "low": 1.1351077617732008, "close": 1.1360823017424464, "volume": 1092, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T12:58:41.291Z", "timeframe": 300, "open": 1.197220396697201, "high": 1.197910637761365, "low": 1.196487570469974, "close": 1.1971246114874434, "volume": 121, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T13:03:41.291Z", "timeframe": 300, "open": 1.1565631860025847, "high": 1.1569384352642538, "low": 1.1555670279913977, "close": 1.1564767575944448, "volume": 846, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T13:08:41.291Z", "timeframe": 300, "open": 1.1287897899448551, "high": 1.1289096816965092, "low": 1.1284112618006927, "close": 1.1286465058796855, "volume": 975, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T13:13:41.291Z", "timeframe": 300, "open": 1.1646613612198569, "high": 1.1649627317254243, "low": 1.1639606314493627, "close": 1.1646931136229348, "volume": 850, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T13:18:41.291Z", "timeframe": 300, "open": 1.1452368525715708, "high": 1.1460113191409822, "low": 1.144771650933369, "close": 1.144771650933369, "volume": 431, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T13:23:41.291Z", "timeframe": 300, "open": 1.1307472952931796, "high": 1.1309581860852784, "low": 1.1300488020762467, "close": 1.130810800953655, "volume": 987, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T13:28:41.291Z", "timeframe": 300, "open": 1.1931785299433069, "high": 1.193583032622999, "low": 1.1924446683540373, "close": 1.1929193279258428, "volume": 643, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T13:33:41.291Z", "timeframe": 300, "open": 1.1394902804736127, "high": 1.1401799379673727, "low": 1.1390260945636594, "close": 1.1391150370434362, "volume": 573, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T13:38:41.291Z", "timeframe": 300, "open": 1.178591617196724, "high": 1.1789046985656042, "low": 1.1778576254383182, "close": 1.178622551819315, "volume": 181, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T13:43:41.291Z", "timeframe": 300, "open": 1.158035004496744, "high": 1.1584325291502064, "low": 1.1572139753041342, "close": 1.1584325291502064, "volume": 596, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T13:48:41.291Z", "timeframe": 300, "open": 1.179437366949048, "high": 1.1802759083835719, "low": 1.1788813428057483, "close": 1.1792554381996483, "volume": 946, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T13:53:41.291Z", "timeframe": 300, "open": 1.1492070788677275, "high": 1.1501548933730878, "low": 1.1483555261025429, "close": 1.1494565256865783, "volume": 690, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T13:58:41.291Z", "timeframe": 300, "open": 1.1979678371038327, "high": 1.1983239299455262, "low": 1.1972560817818603, "close": 1.1983239299455262, "volume": 793, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T14:03:41.291Z", "timeframe": 300, "open": 1.1774802288454305, "high": 1.1777453976557692, "low": 1.1770800342136427, "close": 1.177528527879463, "volume": 130, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T14:08:41.291Z", "timeframe": 300, "open": 1.145095312258501, "high": 1.146045323932661, "low": 1.145059488253282, "close": 1.1450861034040898, "volume": 913, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T14:13:41.291Z", "timeframe": 300, "open": 1.1977536480787507, "high": 1.1986171169602016, "low": 1.1972225309222448, "close": 1.1973948991057857, "volume": 1054, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T14:18:41.291Z", "timeframe": 300, "open": 1.1210254537716238, "high": 1.1213611905923053, "low": 1.1202381211578178, "close": 1.1213399417415895, "volume": 722, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T14:23:41.291Z", "timeframe": 300, "open": 1.1001159108951704, "high": 1.1007512452963912, "low": 1.0998866589626455, "close": 1.100251938616291, "volume": 172, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T14:28:41.291Z", "timeframe": 300, "open": 1.179658072986831, "high": 1.1802810025925425, "low": 1.1790216240118867, "close": 1.1796890333950942, "volume": 989, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T14:33:41.291Z", "timeframe": 300, "open": 1.1608833551383861, "high": 1.161208731907604, "low": 1.1607294522575657, "close": 1.1611245552024962, "volume": 559, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T14:38:41.291Z", "timeframe": 300, "open": 1.123612951693805, "high": 1.1239414610016998, "low": 1.1231641195022768, "close": 1.1239414610016998, "volume": 865, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T14:43:41.291Z", "timeframe": 300, "open": 1.1053381814481835, "high": 1.1061755473287769, "low": 1.1050207741955815, "close": 1.1050207741955815, "volume": 592, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T14:48:41.291Z", "timeframe": 300, "open": 1.1507143833344136, "high": 1.1517111932942228, "low": 1.1503821577539408, "close": 1.1503821577539408, "volume": 942, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T14:53:41.291Z", "timeframe": 300, "open": 1.1238219112993617, "high": 1.1246190144842023, "low": 1.1232547247790319, "close": 1.1242693460449578, "volume": 691, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T14:58:41.291Z", "timeframe": 300, "open": 1.1760014254420592, "high": 1.176076240271193, "low": 1.175635899840053, "close": 1.175635899840053, "volume": 934, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T15:03:41.291Z", "timeframe": 300, "open": 1.1943618777349816, "high": 1.1949629025703479, "low": 1.1939972468918953, "close": 1.194091453606591, "volume": 1042, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T15:08:41.291Z", "timeframe": 300, "open": 1.1879591127244238, "high": 1.1881690654503576, "low": 1.187401451156914, "close": 1.1881690654503576, "volume": 401, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T15:13:41.291Z", "timeframe": 300, "open": 1.1052671785234789, "high": 1.105942358234511, "low": 1.1050276079519192, "close": 1.1055626067223239, "volume": 538, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T15:18:41.291Z", "timeframe": 300, "open": 1.1786780172468203, "high": 1.1787795068655467, "low": 1.1780361110502144, "close": 1.1784917659557341, "volume": 182, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T15:23:41.291Z", "timeframe": 300, "open": 1.1939775313229006, "high": 1.1944004712836878, "low": 1.193917462408379, "close": 1.1943740900279805, "volume": 353, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T15:28:41.291Z", "timeframe": 300, "open": 1.1742649687205804, "high": 1.1749501003084093, "low": 1.1733932621387555, "close": 1.1740113018797438, "volume": 802, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T15:33:41.291Z", "timeframe": 300, "open": 1.1968447146503443, "high": 1.1977337367210417, "low": 1.1961517400060457, "close": 1.1970190948327861, "volume": 382, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T15:38:41.291Z", "timeframe": 300, "open": 1.162133484887053, "high": 1.1623845878817398, "low": 1.1614848487278953, "close": 1.161662197383732, "volume": 1066, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T15:43:41.291Z", "timeframe": 300, "open": 1.111332034180771, "high": 1.1121517565924557, "low": 1.110785794505362, "close": 1.1109363063380717, "volume": 569, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T15:48:41.291Z", "timeframe": 300, "open": 1.1589469069629088, "high": 1.159277088409855, "low": 1.1583856703067308, "close": 1.158496141811235, "volume": 362, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T15:53:41.291Z", "timeframe": 300, "open": 1.175744540171268, "high": 1.1760269831930317, "low": 1.1748238494698742, "close": 1.1753272488535809, "volume": 209, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T15:58:41.291Z", "timeframe": 300, "open": 1.101302409655341, "high": 1.101855524972394, "low": 1.1003749705153567, "close": 1.1011689333072712, "volume": 474, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T16:03:41.291Z", "timeframe": 300, "open": 1.126347544854099, "high": 1.1269037262340744, "low": 1.1258150621977936, "close": 1.1265187565353798, "volume": 462, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T16:08:41.291Z", "timeframe": 300, "open": 1.1363090237497584, "high": 1.1366457834763468, "low": 1.1353538866169655, "close": 1.1363446264588826, "volume": 767, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T16:13:41.291Z", "timeframe": 300, "open": 1.1357301410537253, "high": 1.1366130353404098, "low": 1.1348091232492024, "close": 1.135838465411255, "volume": 144, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T16:18:41.291Z", "timeframe": 300, "open": 1.1274758796462836, "high": 1.1277554287731018, "low": 1.126881105447789, "close": 1.1276785880602087, "volume": 725, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T16:23:41.291Z", "timeframe": 300, "open": 1.178374300677521, "high": 1.1793620256469022, "low": 1.1780004514747668, "close": 1.1780004514747668, "volume": 705, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T16:28:41.291Z", "timeframe": 300, "open": 1.1481250018631686, "high": 1.1490206931409102, "low": 1.147669975604447, "close": 1.1479192454197966, "volume": 220, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T16:33:41.291Z", "timeframe": 300, "open": 1.1692594038750124, "high": 1.1692662038923418, "low": 1.1685048000784364, "close": 1.16888841263084, "volume": 764, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T16:38:41.291Z", "timeframe": 300, "open": 1.1506603841934586, "high": 1.1514541410214705, "low": 1.1501106697742949, "close": 1.1504797724742177, "volume": 1031, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T16:43:41.291Z", "timeframe": 300, "open": 1.1590494055490521, "high": 1.1594502445347246, "low": 1.1584120356394425, "close": 1.1588421618156053, "volume": 506, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T16:48:41.291Z", "timeframe": 300, "open": 1.1240276132889957, "high": 1.1247989954463766, "low": 1.1235513041895417, "close": 1.1235513041895417, "volume": 854, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T16:53:41.291Z", "timeframe": 300, "open": 1.1307237689367544, "high": 1.131457147133052, "low": 1.1303956511308022, "close": 1.1308476528360403, "volume": 276, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T16:58:41.291Z", "timeframe": 300, "open": 1.1893753330323271, "high": 1.1902885775332188, "low": 1.1890435156019001, "close": 1.189529782746067, "volume": 322, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T17:03:41.291Z", "timeframe": 300, "open": 1.1311582424061355, "high": 1.1313336227980242, "low": 1.1306984634033692, "close": 1.1306984634033692, "volume": 763, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T17:08:41.291Z", "timeframe": 300, "open": 1.1157302802344575, "high": 1.1159806298462234, "low": 1.1153052504776708, "close": 1.1158562283715485, "volume": 514, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T17:13:41.291Z", "timeframe": 300, "open": 1.175709045489162, "high": 1.176687142250767, "low": 1.1747855241083491, "close": 1.1754129839892924, "volume": 382, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T17:18:41.291Z", "timeframe": 300, "open": 1.1646940928846428, "high": 1.1654506084082752, "low": 1.163697280945658, "close": 1.1644263023314647, "volume": 894, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T17:23:41.291Z", "timeframe": 300, "open": 1.1776548053573652, "high": 1.1784715915108492, "low": 1.1767824924797528, "close": 1.1772478981684333, "volume": 969, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T17:28:41.291Z", "timeframe": 300, "open": 1.1244346138062706, "high": 1.1253936132117202, "low": 1.1238427191365585, "close": 1.1241531850323594, "volume": 177, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T17:33:41.291Z", "timeframe": 300, "open": 1.1819883818584274, "high": 1.1822062207369872, "low": 1.1815494168050327, "close": 1.1815494168050327, "volume": 402, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T17:38:41.291Z", "timeframe": 300, "open": 1.1983553018980309, "high": 1.198404985183292, "low": 1.1982535906332792, "close": 1.198404985183292, "volume": 195, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T17:43:41.291Z", "timeframe": 300, "open": 1.1607197053613194, "high": 1.1607345978802044, "low": 1.160366858050985, "close": 1.160366858050985, "volume": 774, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T17:48:41.291Z", "timeframe": 300, "open": 1.1533017278336288, "high": 1.153978664047349, "low": 1.1523486063098927, "close": 1.1536488258891267, "volume": 671, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T17:53:41.291Z", "timeframe": 300, "open": 1.1393684690277035, "high": 1.139590577130339, "low": 1.1389681959158564, "close": 1.1392104269587302, "volume": 1040, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T17:58:41.291Z", "timeframe": 300, "open": 1.1257982360758207, "high": 1.1265654838080028, "low": 1.1249442795715654, "close": 1.125636862255793, "volume": 135, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T18:03:41.291Z", "timeframe": 300, "open": 1.100095801037663, "high": 1.100806415926018, "low": 1.0991822750558187, "close": 1.0997564080725486, "volume": 320, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.293Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T18:08:41.291Z", "timeframe": 300, "open": 1.150359696968744, "high": 1.150408806141566, "low": 1.150284324441442, "close": 1.150408806141566, "volume": 868, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T18:13:41.291Z", "timeframe": 300, "open": 1.195135625995956, "high": 1.1961071597487223, "low": 1.1944665379152213, "close": 1.1954346511289535, "volume": 851, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T18:18:41.291Z", "timeframe": 300, "open": 1.146822136418236, "high": 1.1477945546441977, "low": 1.146357891923379, "close": 1.146357891923379, "volume": 199, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T18:23:41.291Z", "timeframe": 300, "open": 1.187367353546408, "high": 1.1876231074841808, "low": 1.1865389514692066, "close": 1.1874221569306085, "volume": 561, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T18:28:41.291Z", "timeframe": 300, "open": 1.170057657896286, "high": 1.1704666823350136, "low": 1.1698294947873475, "close": 1.1701263696256752, "volume": 193, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T18:33:41.291Z", "timeframe": 300, "open": 1.192102222776115, "high": 1.1927939403174488, "low": 1.1912516087633769, "close": 1.1920384032519742, "volume": 869, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T18:38:41.291Z", "timeframe": 300, "open": 1.1480005718903188, "high": 1.148781885365495, "low": 1.1470297969666905, "close": 1.1482210090039475, "volume": 826, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T18:43:41.291Z", "timeframe": 300, "open": 1.1694871030946328, "high": 1.1695353519837204, "low": 1.1686603366716253, "close": 1.16907575961385, "volume": 993, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T18:48:41.291Z", "timeframe": 300, "open": 1.118064246897097, "high": 1.1184859305221795, "low": 1.1174897846217229, "close": 1.118000009253807, "volume": 550, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T18:53:41.291Z", "timeframe": 300, "open": 1.1978250610987784, "high": 1.1978496924274236, "low": 1.197380992362588, "close": 1.197669958905195, "volume": 1043, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T18:58:41.291Z", "timeframe": 300, "open": 1.1621961619199004, "high": 1.1628669034505434, "low": 1.1616157525569428, "close": 1.1620811025877558, "volume": 224, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T19:03:41.291Z", "timeframe": 300, "open": 1.103228240695274, "high": 1.1041094328979868, "low": 1.1029274615141311, "close": 1.1034197977582452, "volume": 981, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T19:08:41.291Z", "timeframe": 300, "open": 1.145805825564361, "high": 1.1465873857901492, "low": 1.1448680203526016, "close": 1.1462922704867724, "volume": 952, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T19:13:41.291Z", "timeframe": 300, "open": 1.1885193320083693, "high": 1.1890210371449175, "low": 1.1877940358857098, "close": 1.1887104490013058, "volume": 293, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T19:18:41.291Z", "timeframe": 300, "open": 1.1818102754721405, "high": 1.1827076861058603, "low": 1.1810907106110338, "close": 1.18167900637664, "volume": 766, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T19:23:41.291Z", "timeframe": 300, "open": 1.1615918685357245, "high": 1.1616871703050737, "low": 1.1609714547428913, "close": 1.1615751125958036, "volume": 243, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T19:28:41.291Z", "timeframe": 300, "open": 1.1328010445915084, "high": 1.1330748285251027, "low": 1.1319903879780346, "close": 1.1330748285251027, "volume": 976, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T19:33:41.291Z", "timeframe": 300, "open": 1.105558599926305, "high": 1.1057338704551611, "low": 1.1052329491813613, "close": 1.105527791361854, "volume": 351, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T19:38:41.291Z", "timeframe": 300, "open": 1.1814409602535074, "high": 1.182437736647602, "low": 1.180560337770587, "close": 1.1813058512178412, "volume": 118, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T19:43:41.291Z", "timeframe": 300, "open": 1.1710824122803325, "high": 1.1719254824776746, "low": 1.17014894005823, "close": 1.1712334350272624, "volume": 887, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T19:48:41.291Z", "timeframe": 300, "open": 1.1031814265584825, "high": 1.1035697411198706, "low": 1.1031297730063998, "close": 1.1032908368380654, "volume": 234, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T19:53:41.291Z", "timeframe": 300, "open": 1.1079038103437033, "high": 1.1080956314455999, "low": 1.1069262506731854, "close": 1.107956793183827, "volume": 1084, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T19:58:41.291Z", "timeframe": 300, "open": 1.1615430345868067, "high": 1.162029195475073, "low": 1.1608964419027705, "close": 1.161610362229897, "volume": 469, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T20:03:41.291Z", "timeframe": 300, "open": 1.143327541713292, "high": 1.144054451534427, "low": 1.142916553055653, "close": 1.143549080545512, "volume": 391, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T20:08:41.291Z", "timeframe": 300, "open": 1.114667234923105, "high": 1.1150855725086128, "low": 1.114479933003587, "close": 1.1144983600708323, "volume": 1043, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T20:13:41.291Z", "timeframe": 300, "open": 1.1558985372202215, "high": 1.1563147023430052, "low": 1.1556060380731257, "close": 1.1563031969422204, "volume": 218, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T20:18:41.291Z", "timeframe": 300, "open": 1.1978252650978818, "high": 1.1982972865122405, "low": 1.197518782806659, "close": 1.1980317096605173, "volume": 685, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T20:23:41.291Z", "timeframe": 300, "open": 1.1890264005828606, "high": 1.189554761538948, "low": 1.1888276232246786, "close": 1.1890369029469134, "volume": 605, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T20:28:41.291Z", "timeframe": 300, "open": 1.1748779265368232, "high": 1.1750582465691422, "low": 1.1745981235186649, "close": 1.1747057488916277, "volume": 669, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T20:33:41.291Z", "timeframe": 300, "open": 1.1464141691505427, "high": 1.1472231009967853, "low": 1.145698228310698, "close": 1.1459798396895242, "volume": 711, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T20:38:41.291Z", "timeframe": 300, "open": 1.1119902559586905, "high": 1.1123101576426297, "low": 1.1116493034318609, "close": 1.1116761921904172, "volume": 972, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T20:43:41.291Z", "timeframe": 300, "open": 1.1274743761377595, "high": 1.1274882786356528, "low": 1.1268019017637, "close": 1.1270500239375045, "volume": 793, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T20:48:41.291Z", "timeframe": 300, "open": 1.133132540758025, "high": 1.133743209354828, "low": 1.1328130506115237, "close": 1.1331309837291859, "volume": 860, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T20:53:41.291Z", "timeframe": 300, "open": 1.1565269968295455, "high": 1.1568444591561586, "low": 1.155801599663558, "close": 1.1566715206047675, "volume": 653, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T20:58:41.291Z", "timeframe": 300, "open": 1.131938653840369, "high": 1.132479947501918, "low": 1.131746940499517, "close": 1.131746940499517, "volume": 1052, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T21:03:41.291Z", "timeframe": 300, "open": 1.1828382660231034, "high": 1.1836233460277794, "low": 1.1824935353535067, "close": 1.1832766950660476, "volume": 825, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T21:08:41.291Z", "timeframe": 300, "open": 1.150682220242703, "high": 1.1510808058444466, "low": 1.150319409284406, "close": 1.150319409284406, "volume": 400, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T21:13:41.291Z", "timeframe": 300, "open": 1.1879716939827278, "high": 1.1885809141558479, "low": 1.1874029401538055, "close": 1.1882753772375123, "volume": 881, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T21:18:41.291Z", "timeframe": 300, "open": 1.1442173296895113, "high": 1.1445029209267745, "low": 1.1440085965097484, "close": 1.1440085965097484, "volume": 496, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T21:23:41.291Z", "timeframe": 300, "open": 1.1475410501640932, "high": 1.1476329298821768, "low": 1.1471042927047386, "close": 1.1476329298821768, "volume": 507, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T21:28:41.291Z", "timeframe": 300, "open": 1.1937134392634812, "high": 1.1937985122066859, "low": 1.1935648791138147, "close": 1.1936728369698377, "volume": 475, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T21:33:41.291Z", "timeframe": 300, "open": 1.1156391291740493, "high": 1.1159237734205991, "low": 1.1154413888615904, "close": 1.1154413888615904, "volume": 877, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T21:38:41.291Z", "timeframe": 300, "open": 1.117301459897457, "high": 1.1173743960718638, "low": 1.116877508517564, "close": 1.116877508517564, "volume": 538, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T21:43:41.291Z", "timeframe": 300, "open": 1.193363637297941, "high": 1.1934013175687883, "low": 1.192845366401265, "close": 1.1934013175687883, "volume": 660, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T21:48:41.291Z", "timeframe": 300, "open": 1.1622341823558227, "high": 1.162303235648808, "low": 1.1612963837966528, "close": 1.1617787891508147, "volume": 397, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T21:53:41.291Z", "timeframe": 300, "open": 1.1776778400708894, "high": 1.1783149252897294, "low": 1.177200692476845, "close": 1.1781373613853041, "volume": 1030, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T21:58:41.291Z", "timeframe": 300, "open": 1.1248185055384974, "high": 1.125220837810533, "low": 1.1239127484993348, "close": 1.1247162956002537, "volume": 662, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T22:03:41.291Z", "timeframe": 300, "open": 1.1377585361433475, "high": 1.1383231613321612, "low": 1.137423670570552, "close": 1.137423670570552, "volume": 100, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T22:08:41.291Z", "timeframe": 300, "open": 1.1997024473071842, "high": 1.2000232881929649, "low": 1.1996230159255046, "close": 1.1998593294658366, "volume": 311, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T22:13:41.291Z", "timeframe": 300, "open": 1.1631412058891455, "high": 1.1634777831563534, "low": 1.1625865471807724, "close": 1.1629147477707633, "volume": 431, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T22:18:41.291Z", "timeframe": 300, "open": 1.1257422247539524, "high": 1.1266071839679874, "low": 1.1253504690572138, "close": 1.1254580118732325, "volume": 222, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T22:23:41.291Z", "timeframe": 300, "open": 1.1572400895374306, "high": 1.1577276096035198, "low": 1.1568815311398193, "close": 1.1577276096035198, "volume": 785, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T22:28:41.291Z", "timeframe": 300, "open": 1.1100448642909655, "high": 1.110582859301305, "low": 1.1096932890606925, "close": 1.1104190884827145, "volume": 726, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T22:33:41.291Z", "timeframe": 300, "open": 1.18406111787478, "high": 1.1847414039065889, "low": 1.1835918807851378, "close": 1.1841936672164726, "volume": 518, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T22:38:41.291Z", "timeframe": 300, "open": 1.1857047804491365, "high": 1.1864792678803433, "low": 1.1851319380227596, "close": 1.1860467781337782, "volume": 518, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T22:43:41.291Z", "timeframe": 300, "open": 1.1476425978088676, "high": 1.148343404144197, "low": 1.1473848236293533, "close": 1.1480260138732759, "volume": 814, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T22:48:41.291Z", "timeframe": 300, "open": 1.1352908498921623, "high": 1.1357575644661608, "low": 1.1352777954996487, "close": 1.1354898299585592, "volume": 968, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T22:53:41.291Z", "timeframe": 300, "open": 1.1895417754339166, "high": 1.190494341793446, "low": 1.1886026352451577, "close": 1.1899415404364122, "volume": 549, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T22:58:41.291Z", "timeframe": 300, "open": 1.1267420746913388, "high": 1.126946100420627, "low": 1.1263267259020837, "close": 1.126946100420627, "volume": 169, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T23:03:41.291Z", "timeframe": 300, "open": 1.1355162977805586, "high": 1.1364524752301584, "low": 1.1346198020430698, "close": 1.1352176610133118, "volume": 601, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T23:08:41.291Z", "timeframe": 300, "open": 1.1834141063732773, "high": 1.1840877836955122, "low": 1.1831493706474636, "close": 1.18380215264393, "volume": 400, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T23:13:41.291Z", "timeframe": 300, "open": 1.1139156506893642, "high": 1.1140627019393643, "low": 1.1131827479549885, "close": 1.1134356439476054, "volume": 292, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T23:18:41.291Z", "timeframe": 300, "open": 1.1026626014467997, "high": 1.102976069593664, "low": 1.102020381432779, "close": 1.1023171689062927, "volume": 383, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T23:23:41.291Z", "timeframe": 300, "open": 1.1283419746689602, "high": 1.1291588347268493, "low": 1.1282440246274323, "close": 1.1287928632111348, "volume": 926, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T23:28:41.291Z", "timeframe": 300, "open": 1.1053140422942733, "high": 1.1056647947694653, "low": 1.1044847781523477, "close": 1.1056647947694653, "volume": 703, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T23:33:41.291Z", "timeframe": 300, "open": 1.1911113818376309, "high": 1.1913138906748255, "low": 1.190510032571801, "close": 1.1911664741892571, "volume": 623, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T23:38:41.291Z", "timeframe": 300, "open": 1.1493843729805389, "high": 1.1494965974275213, "low": 1.14892647494865, "close": 1.1493258469528354, "volume": 249, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T23:43:41.291Z", "timeframe": 300, "open": 1.1737871518237541, "high": 1.1744048807744902, "low": 1.1732698915523143, "close": 1.1735148865236078, "volume": 581, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T23:48:41.291Z", "timeframe": 300, "open": 1.1182941369592525, "high": 1.1188430702088963, "low": 1.1177275445996286, "close": 1.1178114679754534, "volume": 293, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T23:53:41.291Z", "timeframe": 300, "open": 1.1053283717620261, "high": 1.1058861901389103, "low": 1.1046942461562197, "close": 1.1053100678125445, "volume": 702, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-05T23:58:41.291Z", "timeframe": 300, "open": 1.12598197031747, "high": 1.1268824454908055, "low": 1.1253521622467548, "close": 1.1259520469082034, "volume": 496, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T00:03:41.291Z", "timeframe": 300, "open": 1.1930579656276539, "high": 1.1935222117803457, "low": 1.1925101785476713, "close": 1.1931058990229768, "volume": 772, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T00:08:41.291Z", "timeframe": 300, "open": 1.1434752262746295, "high": 1.143759124454873, "low": 1.1424903023327213, "close": 1.1430414256538974, "volume": 976, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T00:13:41.291Z", "timeframe": 300, "open": 1.106809233211009, "high": 1.107064662561, "low": 1.1060326723574223, "close": 1.1066059682358433, "volume": 780, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T00:18:41.291Z", "timeframe": 300, "open": 1.159604400268582, "high": 1.1596901009863216, "low": 1.1594777852562523, "close": 1.1596901009863216, "volume": 1024, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T00:23:41.291Z", "timeframe": 300, "open": 1.1878086400003145, "high": 1.1882369369791055, "low": 1.187170982539037, "close": 1.1882369369791055, "volume": 814, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T00:28:41.291Z", "timeframe": 300, "open": 1.1958186945921696, "high": 1.1962217371640527, "low": 1.1950025162393225, "close": 1.1954624418711537, "volume": 106, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T00:33:41.291Z", "timeframe": 300, "open": 1.1785690775367483, "high": 1.17894037067534, "low": 1.1776124242534043, "close": 1.1784800806210722, "volume": 828, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T00:38:41.291Z", "timeframe": 300, "open": 1.1633003287624837, "high": 1.1641884094763533, "low": 1.1625889113299392, "close": 1.1632719418334072, "volume": 998, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T00:43:41.291Z", "timeframe": 300, "open": 1.106122834287783, "high": 1.1066838774785615, "low": 1.1052203902347872, "close": 1.1059829699867503, "volume": 591, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T00:48:41.291Z", "timeframe": 300, "open": 1.119762445395359, "high": 1.1205832183549198, "low": 1.1197227301152373, "close": 1.1202415761564108, "volume": 1048, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T00:53:41.291Z", "timeframe": 300, "open": 1.1038507700779792, "high": 1.104298930178405, "low": 1.1028814725961749, "close": 1.104298930178405, "volume": 558, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T00:58:41.291Z", "timeframe": 300, "open": 1.1891129301847478, "high": 1.1898138681870463, "low": 1.1890472655023872, "close": 1.1894944225918507, "volume": 594, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T01:03:41.291Z", "timeframe": 300, "open": 1.1130044072223009, "high": 1.113912403842944, "low": 1.1123869482421609, "close": 1.1126560326547013, "volume": 788, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T01:08:41.291Z", "timeframe": 300, "open": 1.123748453443695, "high": 1.1237811185198212, "low": 1.1235199816807682, "close": 1.1237087807448576, "volume": 309, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T01:13:41.291Z", "timeframe": 300, "open": 1.1533962104880215, "high": 1.154239859567464, "low": 1.1526438115650588, "close": 1.1530466658668914, "volume": 1048, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T01:18:41.291Z", "timeframe": 300, "open": 1.176225544096579, "high": 1.1762743588989693, "low": 1.1757413980751867, "close": 1.1757413980751867, "volume": 784, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T01:23:41.291Z", "timeframe": 300, "open": 1.1112552489903706, "high": 1.1119392889540385, "low": 1.110728520812413, "close": 1.1113714323553467, "volume": 1027, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T01:28:41.291Z", "timeframe": 300, "open": 1.1473984365230911, "high": 1.147525639577541, "low": 1.147222524579344, "close": 1.147525639577541, "volume": 464, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T01:33:41.291Z", "timeframe": 300, "open": 1.120338580474464, "high": 1.1212425636567815, "low": 1.1198792226033785, "close": 1.1198792226033785, "volume": 616, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T01:38:41.291Z", "timeframe": 300, "open": 1.1686726943359755, "high": 1.1687243109035426, "low": 1.1683521398481238, "close": 1.168536917578722, "volume": 919, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T01:43:41.291Z", "timeframe": 300, "open": 1.166799916679359, "high": 1.166930526353628, "low": 1.166405202689249, "close": 1.1664834350195288, "volume": 201, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T01:48:41.291Z", "timeframe": 300, "open": 1.1443128499408557, "high": 1.1447974246269252, "low": 1.1433598780521856, "close": 1.1447974246269252, "volume": 960, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T01:53:41.291Z", "timeframe": 300, "open": 1.1764798628002, "high": 1.177119145948773, "low": 1.176016670445803, "close": 1.1768084062253317, "volume": 821, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T01:58:41.291Z", "timeframe": 300, "open": 1.1980869959873768, "high": 1.19888028654689, "low": 1.1976713918307413, "close": 1.1984785364277795, "volume": 511, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T02:03:41.291Z", "timeframe": 300, "open": 1.1996065074362143, "high": 1.2000693560415259, "low": 1.1992200130921513, "close": 1.2000693560415259, "volume": 1088, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T02:08:41.291Z", "timeframe": 300, "open": 1.1762769197015597, "high": 1.1771061476165923, "low": 1.1759397454939517, "close": 1.1766661607272195, "volume": 732, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T02:13:41.291Z", "timeframe": 300, "open": 1.1770963850925547, "high": 1.1776672457124615, "low": 1.1767099149881879, "close": 1.177125102189257, "volume": 293, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T02:18:41.291Z", "timeframe": 300, "open": 1.156625642169931, "high": 1.157065502727206, "low": 1.156169592529135, "close": 1.157065502727206, "volume": 1073, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T02:23:41.291Z", "timeframe": 300, "open": 1.1497121270256638, "high": 1.1501573712828108, "low": 1.1493961871173874, "close": 1.1501573712828108, "volume": 753, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T02:28:41.291Z", "timeframe": 300, "open": 1.1334250093974876, "high": 1.1336566917919206, "low": 1.1330995467890943, "close": 1.1335451908345346, "volume": 195, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T02:33:41.291Z", "timeframe": 300, "open": 1.1219434826916346, "high": 1.12283206322398, "low": 1.1211928160604867, "close": 1.1216267369399668, "volume": 209, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T02:38:41.291Z", "timeframe": 300, "open": 1.1526524627643056, "high": 1.153417238558771, "low": 1.1524008840731714, "close": 1.152893478465912, "volume": 736, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T02:43:41.291Z", "timeframe": 300, "open": 1.1524070572704592, "high": 1.1531655144425954, "low": 1.1520922361208892, "close": 1.1520922361208892, "volume": 238, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T02:48:41.291Z", "timeframe": 300, "open": 1.1721327031374875, "high": 1.172643319571026, "low": 1.1716263983092665, "close": 1.1722014576403854, "volume": 717, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T02:53:41.291Z", "timeframe": 300, "open": 1.1553349913101236, "high": 1.155994921030534, "low": 1.1548182453101732, "close": 1.1557092504185524, "volume": 991, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T02:58:41.291Z", "timeframe": 300, "open": 1.1644095256688518, "high": 1.1650912457981613, "low": 1.1641923589777676, "close": 1.1642864426703536, "volume": 779, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T03:03:41.291Z", "timeframe": 300, "open": 1.1783114316822256, "high": 1.179192475654706, "low": 1.1780802765163563, "close": 1.178519570081008, "volume": 663, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T03:08:41.291Z", "timeframe": 300, "open": 1.1913366651534019, "high": 1.1917087287682213, "low": 1.1910952406048554, "close": 1.1917087287682213, "volume": 1045, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T03:13:41.291Z", "timeframe": 300, "open": 1.1156280759160189, "high": 1.116010102316539, "low": 1.1147999397560697, "close": 1.1159690545843357, "volume": 661, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T03:18:41.291Z", "timeframe": 300, "open": 1.1570943807647411, "high": 1.1573502742764714, "low": 1.156695330573239, "close": 1.156695330573239, "volume": 256, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T03:23:41.291Z", "timeframe": 300, "open": 1.1411846916781483, "high": 1.1412514255597592, "low": 1.1404342459879553, "close": 1.141142959801058, "volume": 939, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T03:28:41.291Z", "timeframe": 300, "open": 1.185971244384537, "high": 1.186723044225621, "low": 1.1849969635628943, "close": 1.1856867707335412, "volume": 693, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T03:33:41.291Z", "timeframe": 300, "open": 1.1378111928804029, "high": 1.138132495598541, "low": 1.1374477066799311, "close": 1.138132495598541, "volume": 593, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T03:38:41.291Z", "timeframe": 300, "open": 1.1101765717453458, "high": 1.1111556395025115, "low": 1.1096839391631657, "close": 1.1098630988389735, "volume": 180, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T03:43:41.291Z", "timeframe": 300, "open": 1.1755892751215553, "high": 1.1765228270569716, "low": 1.1747195072238688, "close": 1.175462812356496, "volume": 852, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T03:48:41.291Z", "timeframe": 300, "open": 1.180106762891044, "high": 1.1810919647199547, "low": 1.179619179937987, "close": 1.179619179937987, "volume": 599, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T03:53:41.291Z", "timeframe": 300, "open": 1.1033317977769697, "high": 1.1041943433471317, "low": 1.1023581043851742, "close": 1.1030527766466154, "volume": 492, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T03:58:41.291Z", "timeframe": 300, "open": 1.1098929795105619, "high": 1.1107672419189583, "low": 1.109723449604097, "close": 1.1102094497934958, "volume": 955, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T04:03:41.291Z", "timeframe": 300, "open": 1.1687210397817216, "high": 1.1687328993728432, "low": 1.1678947984675998, "close": 1.1682516113427344, "volume": 878, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T04:08:41.291Z", "timeframe": 300, "open": 1.1930492065231886, "high": 1.1935078862421182, "low": 1.1922750805460205, "close": 1.1927749334305784, "volume": 880, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T04:13:41.291Z", "timeframe": 300, "open": 1.1712861690714171, "high": 1.1713601904569966, "low": 1.1710084243078989, "close": 1.1710084243078989, "volume": 902, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T04:18:41.291Z", "timeframe": 300, "open": 1.1427769731758806, "high": 1.1430255637194149, "low": 1.142307391346853, "close": 1.142307391346853, "volume": 384, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T04:23:41.291Z", "timeframe": 300, "open": 1.1450483469418835, "high": 1.1454208669587707, "low": 1.1444910546431692, "close": 1.1451167874092119, "volume": 1050, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T04:28:41.291Z", "timeframe": 300, "open": 1.1575802677142277, "high": 1.157654968123414, "low": 1.15742171928046, "close": 1.157528195580887, "volume": 929, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T04:33:41.291Z", "timeframe": 300, "open": 1.1064108012739733, "high": 1.1069411691568936, "low": 1.1062693276586195, "close": 1.1064704453978422, "volume": 905, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T04:38:41.291Z", "timeframe": 300, "open": 1.1204441959593119, "high": 1.1206048687223646, "low": 1.1194895937284635, "close": 1.1201935689047549, "volume": 779, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T04:43:41.291Z", "timeframe": 300, "open": 1.1111748165768414, "high": 1.1115593223405271, "low": 1.1101748881178282, "close": 1.1115593223405271, "volume": 620, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T04:48:41.291Z", "timeframe": 300, "open": 1.1236088199147434, "high": 1.1245444396670408, "low": 1.1226307759815373, "close": 1.12331654903119, "volume": 194, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T04:53:41.291Z", "timeframe": 300, "open": 1.15761979515611, "high": 1.158270519468992, "low": 1.1573988310756789, "close": 1.1574028106263474, "volume": 635, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T04:58:41.291Z", "timeframe": 300, "open": 1.1061539269355958, "high": 1.1069978963248595, "low": 1.1051564902740478, "close": 1.1058947795425424, "volume": 630, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T05:03:41.291Z", "timeframe": 300, "open": 1.1909910955778196, "high": 1.191726017489144, "low": 1.190629385979213, "close": 1.1907105419801374, "volume": 686, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T05:08:41.291Z", "timeframe": 300, "open": 1.1295120226956035, "high": 1.1295857437241914, "low": 1.1287246980803773, "close": 1.1291201699297913, "volume": 997, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T05:13:41.291Z", "timeframe": 300, "open": 1.1384039355579276, "high": 1.1388119680790751, "low": 1.1382554291153177, "close": 1.1388119680790751, "volume": 379, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T05:18:41.291Z", "timeframe": 300, "open": 1.125655662192013, "high": 1.1260274394103216, "low": 1.1252584073844263, "close": 1.1252584073844263, "volume": 310, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T05:23:41.291Z", "timeframe": 300, "open": 1.1831269858067734, "high": 1.1834640657562012, "low": 1.1830315292596945, "close": 1.1834640657562012, "volume": 835, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T05:28:41.291Z", "timeframe": 300, "open": 1.1012936760281522, "high": 1.1017229127908446, "low": 1.1009536578602113, "close": 1.1017229127908446, "volume": 564, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T05:33:41.291Z", "timeframe": 300, "open": 1.1027026606740367, "high": 1.1029765495264672, "low": 1.1022087710682444, "close": 1.1022087710682444, "volume": 979, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T05:38:41.291Z", "timeframe": 300, "open": 1.1279803830773503, "high": 1.128741528477441, "low": 1.1270181120486054, "close": 1.1281367890759901, "volume": 714, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T05:43:41.291Z", "timeframe": 300, "open": 1.195187101075619, "high": 1.195469943253038, "low": 1.1946878506915513, "close": 1.1953338130892128, "volume": 417, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T05:48:41.291Z", "timeframe": 300, "open": 1.1349068722668685, "high": 1.1352064510140147, "low": 1.1345404492294318, "close": 1.1352064510140147, "volume": 763, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T05:53:41.291Z", "timeframe": 300, "open": 1.1347682265792214, "high": 1.1355130423229247, "low": 1.1340262071222482, "close": 1.1351044799639542, "volume": 704, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T05:58:41.291Z", "timeframe": 300, "open": 1.172792410410422, "high": 1.17326670583004, "low": 1.1720077994181564, "close": 1.1727009445615215, "volume": 614, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T06:03:41.291Z", "timeframe": 300, "open": 1.1991940866547028, "high": 1.1997714365474796, "low": 1.1989949739577297, "close": 1.1991881692095436, "volume": 743, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T06:08:41.291Z", "timeframe": 300, "open": 1.1533547439773937, "high": 1.153935510264345, "low": 1.1531842662056264, "close": 1.1531842662056264, "volume": 1021, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T06:13:41.291Z", "timeframe": 300, "open": 1.1136221362950993, "high": 1.114145835138752, "low": 1.1133117673484278, "close": 1.1133117673484278, "volume": 894, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T06:18:41.291Z", "timeframe": 300, "open": 1.1193956111688486, "high": 1.1197078889279195, "low": 1.1185322640392685, "close": 1.1191762796459919, "volume": 298, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T06:23:41.291Z", "timeframe": 300, "open": 1.1286224631801276, "high": 1.1294348454853198, "low": 1.1281817060291703, "close": 1.12882464857853, "volume": 226, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T06:28:41.291Z", "timeframe": 300, "open": 1.126927361994909, "high": 1.1273541738134885, "low": 1.1259869254112327, "close": 1.1273428539893176, "volume": 583, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T06:33:41.291Z", "timeframe": 300, "open": 1.1548852815024468, "high": 1.1557186388526037, "low": 1.1544616463523083, "close": 1.1547877573205896, "volume": 867, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T06:38:41.291Z", "timeframe": 300, "open": 1.1485766633943486, "high": 1.1487999380430094, "low": 1.1480305260999495, "close": 1.148284780981735, "volume": 1073, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T06:43:41.291Z", "timeframe": 300, "open": 1.141125746321207, "high": 1.1415649956992218, "low": 1.1410501424479327, "close": 1.1415649956992218, "volume": 370, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T06:48:41.291Z", "timeframe": 300, "open": 1.1979931153327308, "high": 1.1981106033153714, "low": 1.1975421256580794, "close": 1.1979057325415285, "volume": 456, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T06:53:41.291Z", "timeframe": 300, "open": 1.1376260438407297, "high": 1.1383612625478678, "low": 1.1370629375067727, "close": 1.1377725821623923, "volume": 872, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T06:58:41.291Z", "timeframe": 300, "open": 1.1629439444028105, "high": 1.1635981935659434, "low": 1.162476908937021, "close": 1.1627495795714007, "volume": 360, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T07:03:41.291Z", "timeframe": 300, "open": 1.163918479493784, "high": 1.1639736798243225, "low": 1.1635057873418306, "close": 1.1635057873418306, "volume": 117, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T07:08:41.291Z", "timeframe": 300, "open": 1.1189866320559243, "high": 1.1196007085109774, "low": 1.118612170970509, "close": 1.118612170970509, "volume": 883, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T07:13:41.291Z", "timeframe": 300, "open": 1.1113417501250293, "high": 1.1121066636945374, "low": 1.1112579140707461, "close": 1.1114215656630646, "volume": 288, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T07:18:41.291Z", "timeframe": 300, "open": 1.1994734091026014, "high": 1.2000225778394764, "low": 1.19932443118774, "close": 1.1995916448345387, "volume": 211, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T07:23:41.291Z", "timeframe": 300, "open": 1.1274872394524946, "high": 1.1276250937275734, "low": 1.1270726492058851, "close": 1.1271454816045277, "volume": 281, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T07:28:41.291Z", "timeframe": 300, "open": 1.1623220396046308, "high": 1.1624612769576192, "low": 1.161856287056512, "close": 1.161856287056512, "volume": 465, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T07:33:41.291Z", "timeframe": 300, "open": 1.1551906090307766, "high": 1.1560613953465473, "low": 1.1546496121393948, "close": 1.1547797903656076, "volume": 1014, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T07:38:41.291Z", "timeframe": 300, "open": 1.1236713022198996, "high": 1.1240282320650683, "low": 1.123206177612817, "close": 1.123206177612817, "volume": 638, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T07:43:41.291Z", "timeframe": 300, "open": 1.123420318721252, "high": 1.1237449189406072, "low": 1.123111477804857, "close": 1.1234237552405104, "volume": 330, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T07:48:41.291Z", "timeframe": 300, "open": 1.1237577501295406, "high": 1.1245110020132627, "low": 1.1233462019223182, "close": 1.1239752892156614, "volume": 655, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T07:53:41.291Z", "timeframe": 300, "open": 1.1498882803196773, "high": 1.150290701268213, "low": 1.1492467647384896, "close": 1.1494803816350279, "volume": 392, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T07:58:41.291Z", "timeframe": 300, "open": 1.1174498014973582, "high": 1.1175844978935046, "low": 1.116633072682028, "close": 1.1169670657401174, "volume": 578, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T08:03:41.291Z", "timeframe": 300, "open": 1.1733886691631896, "high": 1.173473355623584, "low": 1.1732977221709875, "close": 1.173473355623584, "volume": 100, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T08:08:41.291Z", "timeframe": 300, "open": 1.141064667547071, "high": 1.1411402677512914, "low": 1.1403711784966022, "close": 1.140675933772024, "volume": 369, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T08:13:41.291Z", "timeframe": 300, "open": 1.1747388709235773, "high": 1.175552141751103, "low": 1.1741705173907167, "close": 1.174347247111293, "volume": 1083, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T08:18:41.291Z", "timeframe": 300, "open": 1.1361766187920745, "high": 1.1364201442492292, "low": 1.135988385694682, "close": 1.135988385694682, "volume": 797, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T08:23:41.291Z", "timeframe": 300, "open": 1.1197695113622672, "high": 1.1202804314113355, "low": 1.119012299266578, "close": 1.1200370540259643, "volume": 133, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T08:28:41.291Z", "timeframe": 300, "open": 1.1945066003620222, "high": 1.1951942597790572, "low": 1.1943296887554562, "close": 1.1943296887554562, "volume": 705, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T08:33:41.291Z", "timeframe": 300, "open": 1.164354210209233, "high": 1.164996700668732, "low": 1.1633814644359657, "close": 1.1648520710739918, "volume": 1007, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T08:38:41.291Z", "timeframe": 300, "open": 1.1311028521896545, "high": 1.1317763726409749, "low": 1.1307548248733024, "close": 1.1315088770119262, "volume": 163, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T08:43:41.291Z", "timeframe": 300, "open": 1.17953524869559, "high": 1.1796814330202245, "low": 1.1788692244556513, "close": 1.1793000538691618, "volume": 495, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T08:48:41.291Z", "timeframe": 300, "open": 1.1249300141207759, "high": 1.124934328695873, "low": 1.1246440225154077, "close": 1.1247638487825768, "volume": 977, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T08:53:41.291Z", "timeframe": 300, "open": 1.175670426546256, "high": 1.175788307277686, "low": 1.1756212921052744, "close": 1.1756212921052744, "volume": 434, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T08:58:41.291Z", "timeframe": 300, "open": 1.138606766061427, "high": 1.1387771728932137, "low": 1.1383520543143304, "close": 1.1383520543143304, "volume": 949, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T09:03:41.291Z", "timeframe": 300, "open": 1.1945738325673576, "high": 1.1951842504955055, "low": 1.193692613232559, "close": 1.1944937955799548, "volume": 328, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T09:08:41.291Z", "timeframe": 300, "open": 1.1798669530790766, "high": 1.180190378687199, "low": 1.1794995255210379, "close": 1.1798184989729228, "volume": 844, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T09:13:41.291Z", "timeframe": 300, "open": 1.1986107610843513, "high": 1.1995807945785115, "low": 1.198542019871875, "close": 1.1990885595553975, "volume": 704, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T09:18:41.291Z", "timeframe": 300, "open": 1.1274846571799493, "high": 1.1277365766416134, "low": 1.126945810143376, "close": 1.1276687365171516, "volume": 308, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T09:23:41.291Z", "timeframe": 300, "open": 1.1888831553043908, "high": 1.1889081326086794, "low": 1.1882293464412286, "close": 1.1887065286164271, "volume": 811, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T09:28:41.291Z", "timeframe": 300, "open": 1.1328151069966228, "high": 1.1335122794373913, "low": 1.1318596420286862, "close": 1.1326595710746123, "volume": 552, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T09:33:41.291Z", "timeframe": 300, "open": 1.1428682479285948, "high": 1.1432355411671378, "low": 1.1424645914103004, "close": 1.1432355411671378, "volume": 634, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T09:38:41.291Z", "timeframe": 300, "open": 1.123418121650248, "high": 1.1234222425598914, "low": 1.1229540343411217, "close": 1.1229540343411217, "volume": 421, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T09:43:41.291Z", "timeframe": 300, "open": 1.1432640935136302, "high": 1.143517522388386, "low": 1.1431933989198153, "close": 1.1434208837677349, "volume": 294, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T09:48:41.291Z", "timeframe": 300, "open": 1.1182552303748603, "high": 1.1185373382455066, "low": 1.1174352681455522, "close": 1.1182464763468878, "volume": 201, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T09:53:41.291Z", "timeframe": 300, "open": 1.1596765540962495, "high": 1.1599229134664544, "low": 1.1591133043848962, "close": 1.1599229134664544, "volume": 515, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T09:58:41.291Z", "timeframe": 300, "open": 1.1333220064687648, "high": 1.1342428397978248, "low": 1.1327714174800558, "close": 1.1333217274112946, "volume": 306, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T10:03:41.291Z", "timeframe": 300, "open": 1.1861551593780713, "high": 1.1868474114595844, "low": 1.1860733453901298, "close": 1.1864796405630582, "volume": 695, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T10:08:41.291Z", "timeframe": 300, "open": 1.1133350421549775, "high": 1.1135199451312443, "low": 1.1125850207208359, "close": 1.1128663984781717, "volume": 301, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T10:13:41.291Z", "timeframe": 300, "open": 1.1192250852736905, "high": 1.1196150944837866, "low": 1.118299559400694, "close": 1.1195716102683024, "volume": 276, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T10:18:41.291Z", "timeframe": 300, "open": 1.1745919620359622, "high": 1.1754694267417598, "low": 1.1741374867327576, "close": 1.1742444615570906, "volume": 523, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T10:23:41.291Z", "timeframe": 300, "open": 1.190728564704075, "high": 1.1915200999990347, "low": 1.1901226956818844, "close": 1.1910762429522515, "volume": 249, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T10:28:41.291Z", "timeframe": 300, "open": 1.142148144014254, "high": 1.1430164918471455, "low": 1.141782677073038, "close": 1.1420833793863745, "volume": 342, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T10:33:41.291Z", "timeframe": 300, "open": 1.1963234979579371, "high": 1.1967206207792822, "low": 1.1959254552833658, "close": 1.1963944853698936, "volume": 296, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T10:38:41.291Z", "timeframe": 300, "open": 1.176726894269665, "high": 1.1771008993077563, "low": 1.1762974316299208, "close": 1.1771008993077563, "volume": 892, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T10:43:41.291Z", "timeframe": 300, "open": 1.1565349760439179, "high": 1.1573460260519393, "low": 1.156307658671236, "close": 1.156307658671236, "volume": 1090, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T10:48:41.291Z", "timeframe": 300, "open": 1.1705007439575117, "high": 1.1707893727741634, "low": 1.1696310981071396, "close": 1.1700910741411872, "volume": 817, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T10:53:41.291Z", "timeframe": 300, "open": 1.1130781949339656, "high": 1.1135111000083568, "low": 1.1126028395220406, "close": 1.1134061417336112, "volume": 922, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T10:58:41.291Z", "timeframe": 300, "open": 1.1302653467744577, "high": 1.131085337684529, "low": 1.129532777896196, "close": 1.1298229533992976, "volume": 226, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T11:03:41.291Z", "timeframe": 300, "open": 1.1930862874108252, "high": 1.193386243597277, "low": 1.1927556220613282, "close": 1.193386243597277, "volume": 1085, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T11:08:41.291Z", "timeframe": 300, "open": 1.165628893344632, "high": 1.1664611443575243, "low": 1.1653894295085585, "close": 1.1659977190439088, "volume": 482, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T11:13:41.291Z", "timeframe": 300, "open": 1.1076633535297875, "high": 1.1082392348782284, "low": 1.1067373116371089, "close": 1.1080727635998093, "volume": 979, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T11:18:41.291Z", "timeframe": 300, "open": 1.1820667011487598, "high": 1.182615702335779, "low": 1.1812391433999823, "close": 1.1821934278949124, "volume": 204, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T11:23:41.291Z", "timeframe": 300, "open": 1.1312923421469305, "high": 1.1315292430085286, "low": 1.1303078496583874, "close": 1.131019516426462, "volume": 1041, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T11:28:41.291Z", "timeframe": 300, "open": 1.100236365742988, "high": 1.100563366794122, "low": 1.0998998621157263, "close": 1.1004443894926625, "volume": 482, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T11:33:41.291Z", "timeframe": 300, "open": 1.1957147494712022, "high": 1.1963932929463865, "low": 1.194780764152368, "close": 1.196001249632695, "volume": 850, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T11:38:41.291Z", "timeframe": 300, "open": 1.1346514739931244, "high": 1.135062962420425, "low": 1.133848350676815, "close": 1.135062962420425, "volume": 665, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T11:43:41.291Z", "timeframe": 300, "open": 1.1204079902665094, "high": 1.1209834496536004, "low": 1.1199538186298235, "close": 1.120892999114984, "volume": 609, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T11:48:41.291Z", "timeframe": 300, "open": 1.129697682823173, "high": 1.129997868802763, "low": 1.1292826109284737, "close": 1.1293879310844641, "volume": 251, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T11:53:41.291Z", "timeframe": 300, "open": 1.1252266328286662, "high": 1.1258916822382097, "low": 1.124734779163458, "close": 1.1254974615614208, "volume": 253, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T11:58:41.291Z", "timeframe": 300, "open": 1.1055997720176094, "high": 1.1062944875886949, "low": 1.1053381306792516, "close": 1.1060667520123524, "volume": 211, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T12:03:41.291Z", "timeframe": 300, "open": 1.192199004024682, "high": 1.1922537369427273, "low": 1.191787977816629, "close": 1.191787977816629, "volume": 400, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T12:08:41.291Z", "timeframe": 300, "open": 1.1147246658080445, "high": 1.1156249700728118, "low": 1.1146577718938753, "close": 1.11514879030036, "volume": 333, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T12:13:41.291Z", "timeframe": 300, "open": 1.1085520000513893, "high": 1.1089579708976058, "low": 1.1082753430414922, "close": 1.1086049496795731, "volume": 323, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T12:18:41.291Z", "timeframe": 300, "open": 1.1047467437055614, "high": 1.105175755330214, "low": 1.1046910181982663, "close": 1.1048840801686466, "volume": 107, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T12:23:41.291Z", "timeframe": 300, "open": 1.1239117100737195, "high": 1.1243732826018027, "low": 1.1232618242053212, "close": 1.1243732826018027, "volume": 226, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T12:28:41.291Z", "timeframe": 300, "open": 1.182325005751079, "high": 1.1828480652940512, "low": 1.181678196060122, "close": 1.181997823583083, "volume": 301, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T12:33:41.291Z", "timeframe": 300, "open": 1.1438357047581496, "high": 1.144210414496793, "low": 1.1434640883420217, "close": 1.1440389855296582, "volume": 679, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T12:38:41.291Z", "timeframe": 300, "open": 1.1090552832523144, "high": 1.1099698787860426, "low": 1.1090499236043663, "close": 1.1093597974399518, "volume": 691, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T12:43:41.291Z", "timeframe": 300, "open": 1.113198548356615, "high": 1.113349912154538, "low": 1.1127386315581043, "close": 1.1127386315581043, "volume": 624, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T12:48:41.291Z", "timeframe": 300, "open": 1.1799224168473004, "high": 1.1807360398709468, "low": 1.1798149689812667, "close": 1.180090375390571, "volume": 141, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T12:53:41.291Z", "timeframe": 300, "open": 1.1398753616166204, "high": 1.1405647001650994, "low": 1.1389032483431922, "close": 1.1394159598017723, "volume": 607, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T12:58:41.291Z", "timeframe": 300, "open": 1.1865798394092573, "high": 1.187286780289, "low": 1.1864993425626653, "close": 1.186628567299778, "volume": 186, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T13:03:41.291Z", "timeframe": 300, "open": 1.1876049933100281, "high": 1.1883669572134798, "low": 1.1873522610152791, "close": 1.1877904089564753, "volume": 1080, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T13:08:41.291Z", "timeframe": 300, "open": 1.1399559879560806, "high": 1.1401790836089412, "low": 1.1396988984431333, "close": 1.1401790836089412, "volume": 366, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T13:13:41.291Z", "timeframe": 300, "open": 1.1474696459633573, "high": 1.148326263759833, "low": 1.1472948525344087, "close": 1.1476542202510698, "volume": 253, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T13:18:41.291Z", "timeframe": 300, "open": 1.1127694446928849, "high": 1.1136967618254123, "low": 1.1123177545264136, "close": 1.1127540532102813, "volume": 923, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T13:23:41.291Z", "timeframe": 300, "open": 1.1745342262114968, "high": 1.1754466577521996, "low": 1.1739469719549962, "close": 1.1740464235257277, "volume": 137, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T13:28:41.291Z", "timeframe": 300, "open": 1.1226015353452032, "high": 1.1234303900124818, "low": 1.122368034764803, "close": 1.1229453187498435, "volume": 636, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T13:33:41.291Z", "timeframe": 300, "open": 1.1624121523704303, "high": 1.1633028500818172, "low": 1.1617412546703096, "close": 1.1620183558466506, "volume": 1034, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T13:38:41.291Z", "timeframe": 300, "open": 1.1792280345614845, "high": 1.1799431771183966, "low": 1.1782452652480568, "close": 1.1790173617091708, "volume": 747, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T13:43:41.291Z", "timeframe": 300, "open": 1.1691147967811542, "high": 1.1697643282291519, "low": 1.1681472539088114, "close": 1.1687278818485274, "volume": 888, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T13:48:41.291Z", "timeframe": 300, "open": 1.1008386235276562, "high": 1.1011717887702959, "low": 1.1002404923709241, "close": 1.1003779691228408, "volume": 878, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T13:53:41.291Z", "timeframe": 300, "open": 1.1228535648344535, "high": 1.123461287641342, "low": 1.121953508081392, "close": 1.122373858841732, "volume": 112, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T13:58:41.291Z", "timeframe": 300, "open": 1.139565245957602, "high": 1.140471205266634, "low": 1.1387110664150644, "close": 1.1399365936095787, "volume": 428, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T14:03:41.291Z", "timeframe": 300, "open": 1.1023879236188834, "high": 1.1030424426951684, "low": 1.1021990392927274, "close": 1.1023524159255724, "volume": 771, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T14:08:41.291Z", "timeframe": 300, "open": 1.154051070403286, "high": 1.1543658934180354, "low": 1.1540212851841885, "close": 1.1543658934180354, "volume": 1077, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T14:13:41.291Z", "timeframe": 300, "open": 1.1482041158658152, "high": 1.148879958886945, "low": 1.147433757413552, "close": 1.1477048600186344, "volume": 784, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T14:18:41.291Z", "timeframe": 300, "open": 1.10125704613397, "high": 1.101741207221251, "low": 1.1008565100246674, "close": 1.101741207221251, "volume": 741, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T14:23:41.291Z", "timeframe": 300, "open": 1.1352376225379024, "high": 1.1352572702530574, "low": 1.134430013150437, "close": 1.1349544682869568, "volume": 296, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T14:28:41.291Z", "timeframe": 300, "open": 1.109560920186643, "high": 1.1102695640549498, "low": 1.1091515279078448, "close": 1.1091515279078448, "volume": 212, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T14:33:41.291Z", "timeframe": 300, "open": 1.14682124239387, "high": 1.1470989752115726, "low": 1.1465522603353573, "close": 1.1470989752115726, "volume": 571, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T14:38:41.291Z", "timeframe": 300, "open": 1.1599956530223587, "high": 1.1609035381013253, "low": 1.1590384055840057, "close": 1.1602142515867817, "volume": 1092, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T14:43:41.291Z", "timeframe": 300, "open": 1.1136211206731264, "high": 1.1140034825870722, "low": 1.113054888234495, "close": 1.1140034825870722, "volume": 156, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T14:48:41.291Z", "timeframe": 300, "open": 1.1114639991340942, "high": 1.1118097631002295, "low": 1.11091778770024, "close": 1.1118097631002295, "volume": 390, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.294Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T14:53:41.291Z", "timeframe": 300, "open": 1.1671331193878314, "high": 1.1673175012074208, "low": 1.167005402650793, "close": 1.1670363332952005, "volume": 294, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T14:58:41.291Z", "timeframe": 300, "open": 1.1126886780632588, "high": 1.1135270423995063, "low": 1.1119959471262475, "close": 1.1129486975618328, "volume": 173, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T15:03:41.291Z", "timeframe": 300, "open": 1.1286890220452723, "high": 1.1287476235832847, "low": 1.12849273495517, "close": 1.1287412115967463, "volume": 741, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T15:08:41.291Z", "timeframe": 300, "open": 1.1685286226905909, "high": 1.1693601350076437, "low": 1.1684270656284441, "close": 1.1686767052341238, "volume": 739, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T15:13:41.291Z", "timeframe": 300, "open": 1.1152561085788315, "high": 1.1153389244129763, "low": 1.1144820209169108, "close": 1.115289854744859, "volume": 256, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T15:18:41.291Z", "timeframe": 300, "open": 1.165277946349356, "high": 1.1656256165273893, "low": 1.165053265950221, "close": 1.165053265950221, "volume": 408, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T15:23:41.291Z", "timeframe": 300, "open": 1.1210561661700653, "high": 1.1212165408496988, "low": 1.1202548496918738, "close": 1.1206148536088663, "volume": 239, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T15:28:41.291Z", "timeframe": 300, "open": 1.1451749249867924, "high": 1.145777839891169, "low": 1.144803230932872, "close": 1.1452325524615636, "volume": 1064, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T15:33:41.291Z", "timeframe": 300, "open": 1.1183799188022978, "high": 1.1192859033529647, "low": 1.1179328460902611, "close": 1.118284893137262, "volume": 567, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T15:38:41.291Z", "timeframe": 300, "open": 1.1374367393483196, "high": 1.138429161313657, "low": 1.136733096492935, "close": 1.1376541075923021, "volume": 774, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T15:43:41.291Z", "timeframe": 300, "open": 1.1224715606626243, "high": 1.1228511357632098, "low": 1.1220556000670943, "close": 1.1228511357632098, "volume": 1095, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T15:48:41.291Z", "timeframe": 300, "open": 1.1030248176534418, "high": 1.1032694433148642, "low": 1.1020686664297443, "close": 1.1029074515386608, "volume": 889, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T15:53:41.291Z", "timeframe": 300, "open": 1.1225065895511017, "high": 1.1226177399210389, "low": 1.1221037320966059, "close": 1.1226177399210389, "volume": 187, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T15:58:41.291Z", "timeframe": 300, "open": 1.1965120969904774, "high": 1.1969649836402092, "low": 1.1958589538062936, "close": 1.196591089268321, "volume": 500, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T16:03:41.291Z", "timeframe": 300, "open": 1.1988615162538634, "high": 1.1997219876551257, "low": 1.198799100610679, "close": 1.198799100610679, "volume": 958, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T16:08:41.291Z", "timeframe": 300, "open": 1.116970282825445, "high": 1.117075581930504, "low": 1.116401090937376, "close": 1.1170150440064235, "volume": 968, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T16:13:41.291Z", "timeframe": 300, "open": 1.1942991333242663, "high": 1.1947558848060276, "low": 1.193335268632122, "close": 1.1947558848060276, "volume": 979, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T16:18:41.291Z", "timeframe": 300, "open": 1.132319763651235, "high": 1.132334650589434, "low": 1.1320321389598311, "close": 1.1320321389598311, "volume": 1001, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T16:23:41.291Z", "timeframe": 300, "open": 1.1721107173780039, "high": 1.1723700421504297, "low": 1.1720007697381534, "close": 1.1723700421504297, "volume": 900, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T16:28:41.291Z", "timeframe": 300, "open": 1.1928020583044652, "high": 1.193128020857585, "low": 1.1922762685470607, "close": 1.193128020857585, "volume": 113, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T16:33:41.291Z", "timeframe": 300, "open": 1.135584551650119, "high": 1.1362903618301612, "low": 1.1354344130587752, "close": 1.1358004384988734, "volume": 224, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T16:38:41.291Z", "timeframe": 300, "open": 1.103844232915478, "high": 1.104830221522376, "low": 1.1032356203266664, "close": 1.103488785892606, "volume": 515, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T16:43:41.291Z", "timeframe": 300, "open": 1.1906150061943421, "high": 1.1913556978786244, "low": 1.190025461445206, "close": 1.1901154099764257, "volume": 1067, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T16:48:41.291Z", "timeframe": 300, "open": 1.149058419940437, "high": 1.1497362151306438, "low": 1.148515986397871, "close": 1.1485873624212217, "volume": 353, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T16:53:41.291Z", "timeframe": 300, "open": 1.1633828985480874, "high": 1.1633946903592347, "low": 1.162769679615795, "close": 1.162890828185171, "volume": 436, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T16:58:41.291Z", "timeframe": 300, "open": 1.1770216532613997, "high": 1.177899731776101, "low": 1.1765340654102918, "close": 1.1770900398224529, "volume": 202, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T17:03:41.291Z", "timeframe": 300, "open": 1.1987090524024602, "high": 1.199547951798914, "low": 1.1985038145988343, "close": 1.1985038145988343, "volume": 944, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T17:08:41.291Z", "timeframe": 300, "open": 1.1022576282975385, "high": 1.1029262425019772, "low": 1.1012707303138178, "close": 1.1019987308040466, "volume": 559, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T17:13:41.291Z", "timeframe": 300, "open": 1.1859870264466954, "high": 1.1868885736770887, "low": 1.1854535336792227, "close": 1.1863668250449981, "volume": 526, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T17:18:41.291Z", "timeframe": 300, "open": 1.1753905928162096, "high": 1.1760219565714196, "low": 1.1743988378789563, "close": 1.1750129156025897, "volume": 836, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T17:23:41.291Z", "timeframe": 300, "open": 1.1184419697186911, "high": 1.119336341045571, "low": 1.1174580689108293, "close": 1.1184383529535635, "volume": 495, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T17:28:41.291Z", "timeframe": 300, "open": 1.1199787874295357, "high": 1.1200417306823032, "low": 1.119291871847079, "close": 1.1195132305356117, "volume": 1031, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T17:33:41.291Z", "timeframe": 300, "open": 1.1489300204334099, "high": 1.149551796543074, "low": 1.1485896183418864, "close": 1.149246019952921, "volume": 813, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T17:38:41.291Z", "timeframe": 300, "open": 1.17234183398106, "high": 1.172926670774299, "low": 1.1714469587183987, "close": 1.1721140560478551, "volume": 552, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T17:43:41.291Z", "timeframe": 300, "open": 1.1206457496899511, "high": 1.1208470119390768, "low": 1.119997231412773, "close": 1.1208470119390768, "volume": 952, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T17:48:41.291Z", "timeframe": 300, "open": 1.1024879269117376, "high": 1.103023223153065, "low": 1.1021519059232927, "close": 1.1021519059232927, "volume": 781, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T17:53:41.291Z", "timeframe": 300, "open": 1.1631585258440649, "high": 1.1637735188884062, "low": 1.1628525106568732, "close": 1.1635568724129253, "volume": 748, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T17:58:41.291Z", "timeframe": 300, "open": 1.173313963212793, "high": 1.1740532102533676, "low": 1.172790145017678, "close": 1.1738003687975844, "volume": 195, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T18:03:41.291Z", "timeframe": 300, "open": 1.162785974348901, "high": 1.1635057512180256, "low": 1.1626783233520328, "close": 1.1628967465113869, "volume": 535, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T18:08:41.291Z", "timeframe": 300, "open": 1.1702389389686778, "high": 1.1709019571305772, "low": 1.1696909205624186, "close": 1.1699155066464408, "volume": 277, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T18:13:41.291Z", "timeframe": 300, "open": 1.1753964514134414, "high": 1.1760670779680131, "low": 1.1746349385786894, "close": 1.1753833460312184, "volume": 403, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T18:18:41.291Z", "timeframe": 300, "open": 1.1623541555631918, "high": 1.1629441515756975, "low": 1.162155579085609, "close": 1.1626103853681442, "volume": 936, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T18:23:41.291Z", "timeframe": 300, "open": 1.1002621065471696, "high": 1.101251932835172, "low": 1.0998010966456788, "close": 1.1003548639048601, "volume": 423, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T18:28:41.291Z", "timeframe": 300, "open": 1.1598809046680838, "high": 1.1604930096013446, "low": 1.1591666623647436, "close": 1.1599996431647885, "volume": 752, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T18:33:41.291Z", "timeframe": 300, "open": 1.1955109810488649, "high": 1.1961813324320978, "low": 1.1946576125852149, "close": 1.1958296788145608, "volume": 135, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T18:38:41.291Z", "timeframe": 300, "open": 1.1477843718058995, "high": 1.1482117088703787, "low": 1.1472917529577185, "close": 1.147428833105896, "volume": 502, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T18:43:41.291Z", "timeframe": 300, "open": 1.1706022496661554, "high": 1.1711263658639386, "low": 1.170122645149819, "close": 1.170318036337346, "volume": 357, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T18:48:41.291Z", "timeframe": 300, "open": 1.1886548588137118, "high": 1.1889195426418504, "low": 1.1881028800856896, "close": 1.1883302432022094, "volume": 114, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T18:53:41.291Z", "timeframe": 300, "open": 1.1923477382978964, "high": 1.1929380477356206, "low": 1.1914982962172969, "close": 1.1922451581286058, "volume": 668, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T18:58:41.291Z", "timeframe": 300, "open": 1.1809931305917192, "high": 1.1816361372714757, "low": 1.180340762546871, "close": 1.180823855467729, "volume": 324, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T19:03:41.291Z", "timeframe": 300, "open": 1.1928132713143076, "high": 1.1936769659863569, "low": 1.1921942485058163, "close": 1.1924555818624394, "volume": 794, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T19:08:41.291Z", "timeframe": 300, "open": 1.1977658204641792, "high": 1.1986996215958199, "low": 1.1971375714496284, "close": 1.1979687877485083, "volume": 858, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T19:13:41.291Z", "timeframe": 300, "open": 1.1076269791688464, "high": 1.107731149585859, "low": 1.107353458718285, "close": 1.107353458718285, "volume": 452, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T19:18:41.291Z", "timeframe": 300, "open": 1.171387229369794, "high": 1.171877347345291, "low": 1.1706570658620128, "close": 1.171093813221483, "volume": 960, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T19:23:41.291Z", "timeframe": 300, "open": 1.1794531838910796, "high": 1.1797323682699854, "low": 1.178897694469181, "close": 1.1795714230959602, "volume": 120, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T19:28:41.291Z", "timeframe": 300, "open": 1.199577829521262, "high": 1.199636306880371, "low": 1.1990138468704163, "close": 1.1993209364381099, "volume": 264, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T19:33:41.291Z", "timeframe": 300, "open": 1.1153501415344496, "high": 1.1160265217666565, "low": 1.114611296637186, "close": 1.115750649337765, "volume": 712, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T19:38:41.291Z", "timeframe": 300, "open": 1.133658876087315, "high": 1.1337883480951476, "low": 1.1331711058820368, "close": 1.1332071335954577, "volume": 942, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T19:43:41.291Z", "timeframe": 300, "open": 1.1675266321325302, "high": 1.1679341756814248, "low": 1.1670938420122785, "close": 1.1675179817678676, "volume": 493, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T19:48:41.291Z", "timeframe": 300, "open": 1.1111317279304336, "high": 1.1119154492211425, "low": 1.1108708948355563, "close": 1.1110060043103516, "volume": 123, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T19:53:41.291Z", "timeframe": 300, "open": 1.1590827799670997, "high": 1.1594174558476824, "low": 1.1584560730826197, "close": 1.1593567488070597, "volume": 133, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T19:58:41.291Z", "timeframe": 300, "open": 1.1382781706405132, "high": 1.139035040392769, "low": 1.137608826448423, "close": 1.1379825862444264, "volume": 312, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T20:03:41.291Z", "timeframe": 300, "open": 1.1174200198905686, "high": 1.1181395726025032, "low": 1.1171143446396612, "close": 1.1173358052758728, "volume": 131, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T20:08:41.291Z", "timeframe": 300, "open": 1.1348776961120464, "high": 1.1355037186888626, "low": 1.1340531712129782, "close": 1.1346447956374612, "volume": 870, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T20:13:41.291Z", "timeframe": 300, "open": 1.1055260129010047, "high": 1.1058916381141786, "low": 1.105509931765456, "close": 1.1057486203801892, "volume": 555, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T20:18:41.291Z", "timeframe": 300, "open": 1.1776031022412088, "high": 1.1784174940192913, "low": 1.1766105620117384, "close": 1.1775870379948195, "volume": 718, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T20:23:41.291Z", "timeframe": 300, "open": 1.1169907427752521, "high": 1.1177073546385212, "low": 1.1165261232677084, "close": 1.1170033239163253, "volume": 174, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T20:28:41.291Z", "timeframe": 300, "open": 1.1900686557742188, "high": 1.1906892535443623, "low": 1.1894716013073432, "close": 1.1903905174978358, "volume": 211, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T20:33:41.291Z", "timeframe": 300, "open": 1.1226967255408626, "high": 1.1234964065984683, "low": 1.1218665614500583, "close": 1.1224631203180866, "volume": 721, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T20:38:41.291Z", "timeframe": 300, "open": 1.1608755499560188, "high": 1.1611811933109746, "low": 1.1604911700068035, "close": 1.1608542300110594, "volume": 712, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T20:43:41.291Z", "timeframe": 300, "open": 1.1264973479200577, "high": 1.1268332157472576, "low": 1.1262159125057785, "close": 1.1262752379293186, "volume": 1051, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T20:48:41.291Z", "timeframe": 300, "open": 1.1522439979909942, "high": 1.152696227493165, "low": 1.1515105262035772, "close": 1.1518094497139366, "volume": 162, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T20:53:41.291Z", "timeframe": 300, "open": 1.1938262097110388, "high": 1.1946618937917548, "low": 1.1937441540003768, "close": 1.1940128119976472, "volume": 1060, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T20:58:41.291Z", "timeframe": 300, "open": 1.1851493300786249, "high": 1.1858520416160763, "low": 1.184635199854381, "close": 1.185474418447008, "volume": 158, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T21:03:41.291Z", "timeframe": 300, "open": 1.1094591948181505, "high": 1.109636871303995, "low": 1.1086005684717133, "close": 1.109636871303995, "volume": 475, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T21:08:41.291Z", "timeframe": 300, "open": 1.1380436023698246, "high": 1.1380743463294791, "low": 1.1375668119862847, "close": 1.1375668119862847, "volume": 336, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T21:13:41.291Z", "timeframe": 300, "open": 1.1442590747132204, "high": 1.1442811794870682, "low": 1.1436442946552874, "close": 1.144208063021525, "volume": 799, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T21:18:41.291Z", "timeframe": 300, "open": 1.1346545171598525, "high": 1.1353015336022902, "low": 1.134546679852326, "close": 1.134576209988409, "volume": 353, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T21:23:41.291Z", "timeframe": 300, "open": 1.132645398029168, "high": 1.133502598744869, "low": 1.1318734051905703, "close": 1.1330460386324406, "volume": 288, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T21:28:41.291Z", "timeframe": 300, "open": 1.1267036148617051, "high": 1.1276272572104207, "low": 1.126208693435131, "close": 1.1264821051468825, "volume": 1001, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T21:33:41.291Z", "timeframe": 300, "open": 1.1549019476937181, "high": 1.1553259961206916, "low": 1.154572271935273, "close": 1.1549359818127394, "volume": 353, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T21:38:41.291Z", "timeframe": 300, "open": 1.1377410132844736, "high": 1.1384297947322883, "low": 1.1373717937958723, "close": 1.1376274574415661, "volume": 845, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T21:43:41.291Z", "timeframe": 300, "open": 1.1365866602190773, "high": 1.1374539161738035, "low": 1.1361686594126355, "close": 1.1367788757828998, "volume": 1026, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T21:48:41.291Z", "timeframe": 300, "open": 1.1486403719045102, "high": 1.1489900050806003, "low": 1.1483211823497481, "close": 1.1489900050806003, "volume": 1051, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T21:53:41.291Z", "timeframe": 300, "open": 1.1924683901790851, "high": 1.1934370090380926, "low": 1.1921018921527329, "close": 1.192507381766261, "volume": 247, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T21:58:41.291Z", "timeframe": 300, "open": 1.177846567863055, "high": 1.1784048161950313, "low": 1.1771212100995532, "close": 1.1776105486637127, "volume": 886, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T22:03:41.291Z", "timeframe": 300, "open": 1.1632866401687763, "high": 1.163909146781788, "low": 1.1632412715910692, "close": 1.1632514408703634, "volume": 169, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T22:08:41.291Z", "timeframe": 300, "open": 1.1529968843205263, "high": 1.1531115888750112, "low": 1.1527641191653824, "close": 1.1527641191653824, "volume": 953, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T22:13:41.291Z", "timeframe": 300, "open": 1.1774914067415907, "high": 1.1783665013919304, "low": 1.1770696575904804, "close": 1.1776771409113775, "volume": 552, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T22:18:41.291Z", "timeframe": 300, "open": 1.106106362657872, "high": 1.1070512813564934, "low": 1.1051073605579163, "close": 1.1064943202219788, "volume": 911, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T22:23:41.291Z", "timeframe": 300, "open": 1.1473578969876945, "high": 1.1480648511104254, "low": 1.1467480378406558, "close": 1.147783615386515, "volume": 432, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T22:28:41.291Z", "timeframe": 300, "open": 1.1509864119578463, "high": 1.1517078418344426, "low": 1.1503744248855652, "close": 1.150620443256554, "volume": 570, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T22:33:41.291Z", "timeframe": 300, "open": 1.1685780043573142, "high": 1.1690577795091044, "low": 1.1680378034835281, "close": 1.1690153094647564, "volume": 637, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T22:38:41.291Z", "timeframe": 300, "open": 1.1475625172080248, "high": 1.1480155507165684, "low": 1.1473015871511913, "close": 1.1477273044129501, "volume": 724, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T22:43:41.291Z", "timeframe": 300, "open": 1.1845295778257647, "high": 1.1852189686005168, "low": 1.1843547803780734, "close": 1.1843880786375065, "volume": 915, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T22:48:41.291Z", "timeframe": 300, "open": 1.1158504681524422, "high": 1.116222676110335, "low": 1.1155327630534786, "close": 1.1155327630534786, "volume": 546, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T22:53:41.291Z", "timeframe": 300, "open": 1.106828275952292, "high": 1.1073772670287736, "low": 1.1068175646850256, "close": 1.1072007302758327, "volume": 217, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T22:58:41.291Z", "timeframe": 300, "open": 1.1856081617231153, "high": 1.1863783113117783, "low": 1.1855212116555627, "close": 1.185896193671305, "volume": 116, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T23:03:41.291Z", "timeframe": 300, "open": 1.1518175993744464, "high": 1.1527566542736032, "low": 1.150898448828458, "close": 1.1515831581354437, "volume": 636, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T23:08:41.291Z", "timeframe": 300, "open": 1.177790380154377, "high": 1.1784921145770093, "low": 1.1773146170656812, "close": 1.1782056766047506, "volume": 668, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T23:13:41.291Z", "timeframe": 300, "open": 1.1686168173587437, "high": 1.1694071705323865, "low": 1.1683553804013114, "close": 1.1686021342259942, "volume": 780, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T23:18:41.291Z", "timeframe": 300, "open": 1.1736277273873263, "high": 1.1743043405686941, "low": 1.1736276758808637, "close": 1.1739777846859512, "volume": 284, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T23:23:41.291Z", "timeframe": 300, "open": 1.173790100692471, "high": 1.1742244671555022, "low": 1.1728792555045253, "close": 1.173638826353804, "volume": 1085, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T23:28:41.291Z", "timeframe": 300, "open": 1.1315383071908502, "high": 1.131812466970009, "low": 1.1310714516196807, "close": 1.1314213812750264, "volume": 1094, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T23:33:41.291Z", "timeframe": 300, "open": 1.1100263061522613, "high": 1.1102361390379, "low": 1.1098908538749135, "close": 1.1101004697707133, "volume": 723, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T23:38:41.291Z", "timeframe": 300, "open": 1.1344141642268992, "high": 1.1350518307376016, "low": 1.1338563426597676, "close": 1.134312672214965, "volume": 602, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T23:43:41.291Z", "timeframe": 300, "open": 1.1914067155041503, "high": 1.1915964283343883, "low": 1.1912027600432926, "close": 1.1915964283343883, "volume": 730, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T23:48:41.291Z", "timeframe": 300, "open": 1.1165804294671986, "high": 1.1174264208791729, "low": 1.115742486492375, "close": 1.1162857770324344, "volume": 1045, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T23:53:41.291Z", "timeframe": 300, "open": 1.189417447264925, "high": 1.1898365958877728, "low": 1.1887082083509868, "close": 1.1890689467710498, "volume": 699, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-06T23:58:41.291Z", "timeframe": 300, "open": 1.1677290924903505, "high": 1.1680629573438852, "low": 1.167027594900385, "close": 1.167483141512051, "volume": 868, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T00:03:41.291Z", "timeframe": 300, "open": 1.19584583568222, "high": 1.1959630804248456, "low": 1.1953513963413094, "close": 1.1953513963413094, "volume": 203, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T00:08:41.291Z", "timeframe": 300, "open": 1.1136703040967089, "high": 1.1138182355670871, "low": 1.1134109060808968, "close": 1.1134712352126652, "volume": 946, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T00:13:41.291Z", "timeframe": 300, "open": 1.144652782749653, "high": 1.1453763929216354, "low": 1.1440452424621272, "close": 1.1447148498622384, "volume": 705, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T00:18:41.291Z", "timeframe": 300, "open": 1.1842425198624493, "high": 1.1848039722227734, "low": 1.1838883840686452, "close": 1.1838883840686452, "volume": 738, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T00:23:41.291Z", "timeframe": 300, "open": 1.1223970740742673, "high": 1.1231256892542485, "low": 1.1220917108551745, "close": 1.1220917108551745, "volume": 404, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T00:28:41.291Z", "timeframe": 300, "open": 1.1446965224263863, "high": 1.1448473662967957, "low": 1.1440913618556356, "close": 1.144757067179386, "volume": 285, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T00:33:41.291Z", "timeframe": 300, "open": 1.1716140996543034, "high": 1.1723450739876131, "low": 1.1710367112933315, "close": 1.1718934669742702, "volume": 664, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T00:38:41.291Z", "timeframe": 300, "open": 1.1242935348871692, "high": 1.1252768822234676, "low": 1.1241595709559216, "close": 1.1241745258359286, "volume": 216, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T00:43:41.291Z", "timeframe": 300, "open": 1.1111936733555647, "high": 1.1114829989855992, "low": 1.11081682704408, "close": 1.11081682704408, "volume": 768, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T00:48:41.291Z", "timeframe": 300, "open": 1.1500770231916455, "high": 1.1505376898100454, "low": 1.1496712784518321, "close": 1.150328201077444, "volume": 238, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T00:53:41.291Z", "timeframe": 300, "open": 1.181171061658919, "high": 1.1817925053418865, "low": 1.1810529284348825, "close": 1.181244600686849, "volume": 928, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T00:58:41.291Z", "timeframe": 300, "open": 1.1669525018753448, "high": 1.1679139795765534, "low": 1.1668321217784317, "close": 1.1668321217784317, "volume": 1049, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T01:03:41.291Z", "timeframe": 300, "open": 1.1592907789162248, "high": 1.1602004377710788, "low": 1.1586222677843516, "close": 1.159233643119416, "volume": 631, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T01:08:41.291Z", "timeframe": 300, "open": 1.1653795757327565, "high": 1.165440794731642, "low": 1.165122446312637, "close": 1.165267917419018, "volume": 937, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T01:13:41.291Z", "timeframe": 300, "open": 1.1941937597507815, "high": 1.194612331735337, "low": 1.193711910452261, "close": 1.1938716089531882, "volume": 236, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T01:18:41.291Z", "timeframe": 300, "open": 1.1943769573673102, "high": 1.1950116661821184, "low": 1.1938304241589424, "close": 1.1945619633333189, "volume": 1009, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T01:23:41.291Z", "timeframe": 300, "open": 1.1046342408413268, "high": 1.1051668820636802, "low": 1.103960015489036, "close": 1.1045885032546654, "volume": 815, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T01:28:41.291Z", "timeframe": 300, "open": 1.1963492537732276, "high": 1.1965708803498283, "low": 1.1957566572005494, "close": 1.1959109487085475, "volume": 833, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T01:33:41.291Z", "timeframe": 300, "open": 1.1291193842200704, "high": 1.129921760160745, "low": 1.128477928553055, "close": 1.129350213437675, "volume": 993, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T01:38:41.291Z", "timeframe": 300, "open": 1.142530682089155, "high": 1.1427723192845922, "low": 1.1423792450936752, "close": 1.1423792450936752, "volume": 710, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T01:43:41.291Z", "timeframe": 300, "open": 1.121705018858542, "high": 1.1220055884096345, "low": 1.120847999926111, "close": 1.1220055884096345, "volume": 249, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T01:48:41.291Z", "timeframe": 300, "open": 1.10624110574873, "high": 1.106945750263897, "low": 1.1060274520627018, "close": 1.1060274520627018, "volume": 435, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T01:53:41.291Z", "timeframe": 300, "open": 1.1041149316528518, "high": 1.1043441110826724, "low": 1.1034349797921914, "close": 1.103818411017682, "volume": 329, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T01:58:41.291Z", "timeframe": 300, "open": 1.127714494258444, "high": 1.1284112824270232, "low": 1.1267925993390486, "close": 1.127893181110021, "volume": 275, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T02:03:41.291Z", "timeframe": 300, "open": 1.1237933723956908, "high": 1.124754152288806, "low": 1.1235137353502114, "close": 1.1239104900612111, "volume": 170, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T02:08:41.291Z", "timeframe": 300, "open": 1.199470203433244, "high": 1.200021126945413, "low": 1.198723366675671, "close": 1.1999618427865315, "volume": 1076, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T02:13:41.291Z", "timeframe": 300, "open": 1.1102626218650222, "high": 1.1107599066535616, "low": 1.1095270999809648, "close": 1.1107599066535616, "volume": 934, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T02:18:41.291Z", "timeframe": 300, "open": 1.17555433416658, "high": 1.1760395096629492, "low": 1.175022235254175, "close": 1.1760395096629492, "volume": 167, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T02:23:41.291Z", "timeframe": 300, "open": 1.1241818048318946, "high": 1.1248695244883766, "low": 1.1240513098656753, "close": 1.1240513098656753, "volume": 395, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T02:28:41.291Z", "timeframe": 300, "open": 1.1727737627628587, "high": 1.1730100813276965, "low": 1.1718573038671103, "close": 1.1729858277893355, "volume": 229, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T02:33:41.291Z", "timeframe": 300, "open": 1.1994640161251378, "high": 1.1999673312607395, "low": 1.1986595923516667, "close": 1.1990804957980483, "volume": 916, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:41.295Z"}}, {"assetSymbol": "EURJPY", "assetName": "EUR/JPY", "timestamp": "2025-07-07T02:37:29.129Z", "timeframe": 300, "open": 1.1582494477732137, "high": 1.1584638136596066, "low": 1.1581164324376982, "close": 1.1583924798101397, "volume": 213, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:42:29.360Z"}}], "metadata": {"source": "historical_data_manager", "version": "1.0"}}