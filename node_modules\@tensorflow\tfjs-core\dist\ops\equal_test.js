/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../index';
import { ALL_ENVS, describeWithFlags } from '../jasmine_util';
import { expectArraysClose, expectArraysEqual } from '../test_util';
describeWithFlags('equal', ALL_ENVS, () => {
    it('Tensor1D - int32', async () => {
        let a = tf.tensor1d([1, 4, 5], 'int32');
        let b = tf.tensor1d([2, 3, 5], 'int32');
        expectArraysClose(await tf.equal(a, b).data(), [0, 0, 1]);
        a = tf.tensor1d([2, 2, 2], 'int32');
        b = tf.tensor1d([2, 2, 2], 'int32');
        expectArraysClose(await tf.equal(a, b).data(), [1, 1, 1]);
        a = tf.tensor1d([0, 0], 'int32');
        b = tf.tensor1d([3, 3], 'int32');
        expectArraysClose(await tf.equal(a, b).data(), [0, 0]);
    });
    it('Tensor1D - float32', async () => {
        let a = tf.tensor1d([1.1, 4.1, 5.1], 'float32');
        let b = tf.tensor1d([2.2, 3.2, 5.1], 'float32');
        expectArraysClose(await tf.equal(a, b).data(), [0, 0, 1]);
        a = tf.tensor1d([2.31, 2.31, 2.31], 'float32');
        b = tf.tensor1d([2.31, 2.31, 2.31], 'float32');
        expectArraysClose(await tf.equal(a, b).data(), [1, 1, 1]);
        a = tf.tensor1d([0.45, 0.123], 'float32');
        b = tf.tensor1d([3.123, 3.321], 'float32');
        expectArraysClose(await tf.equal(a, b).data(), [0, 0]);
    });
    it('upcasts when dtypes dont match', async () => {
        const a = [1.1, 4.1, 5];
        const b = [2.2, 3.2, 5];
        let res = tf.equal(tf.tensor(a, [3], 'float32'), tf.tensor(b, [3], 'int32'));
        expect(res.dtype).toBe('bool');
        expect(res.shape).toEqual([3]);
        expectArraysClose(await res.data(), [0, 0, 1]);
        res = tf.equal(tf.tensor(a, [3], 'int32'), tf.tensor(b, [3], 'bool'));
        expect(res.dtype).toBe('bool');
        expect(res.shape).toEqual([3]);
        expectArraysClose(await res.data(), [1, 0, 0]);
    });
    it('TensorLike', async () => {
        const a = [1.1, 4.1, 5.1];
        const b = [2.2, 3.2, 5.1];
        expectArraysClose(await tf.equal(a, b).data(), [0, 0, 1]);
    });
    it('TensorLike chained', async () => {
        const a = tf.tensor1d([1.1, 4.1, 5.1], 'float32');
        const b = [2.2, 3.2, 5.1];
        expectArraysClose(await a.equal(b).data(), [0, 0, 1]);
    });
    it('mismatched Tensor1D shapes - int32', () => {
        const a = tf.tensor1d([1, 2], 'int32');
        const b = tf.tensor1d([1, 2, 3], 'int32');
        const f = () => {
            tf.equal(a, b);
        };
        expect(f).toThrowError();
    });
    it('mismatched Tensor1D shapes - float32', () => {
        const a = tf.tensor1d([1.1, 2.1], 'float32');
        const b = tf.tensor1d([1.1, 2.1, 3.1], 'float32');
        const f = () => {
            tf.equal(a, b);
        };
        expect(f).toThrowError();
    });
    it('NaNs in Tensor1D - float32', async () => {
        const a = tf.tensor1d([1.1, NaN, 2.1], 'float32');
        const b = tf.tensor1d([2.1, 3.1, NaN], 'float32');
        expectArraysClose(await tf.equal(a, b).data(), [0, 0, 0]);
    });
    it('scalar and 1D broadcast', async () => {
        const a = tf.scalar(2);
        const b = tf.tensor1d([1, 2, 3, 4, 5, 2]);
        const res = tf.equal(a, b);
        expect(res.dtype).toBe('bool');
        expect(res.shape).toEqual([6]);
        expectArraysEqual(await res.data(), [0, 1, 0, 0, 0, 1]);
    });
    // Tensor2D:
    it('Tensor2D - int32', async () => {
        let a = tf.tensor2d([[1, 4, 5], [8, 9, 12]], [2, 3], 'int32');
        let b = tf.tensor2d([[2, 3, 6], [7, 10, 11]], [2, 3], 'int32');
        expectArraysClose(await tf.equal(a, b).data(), [0, 0, 0, 0, 0, 0]);
        a = tf.tensor2d([[0, 0], [1, 1]], [2, 2], 'int32');
        b = tf.tensor2d([[0, 0], [1, 1]], [2, 2], 'int32');
        expectArraysClose(await tf.equal(a, b).data(), [1, 1, 1, 1]);
    });
    it('Tensor2D - float32', async () => {
        let a = tf.tensor2d([[1.1, 4.1, 5.1], [8.1, 9.1, 12.1]], [2, 3], 'float32');
        let b = tf.tensor2d([[2.1, 4.1, 5.1], [7.1, 10.1, 11.1]], [2, 3], 'float32');
        expectArraysClose(await tf.equal(a, b).data(), [0, 1, 1, 0, 0, 0]);
        a = tf.tensor2d([[0.2, 0.2], [1.2, 1.2]], [2, 2], 'float32');
        b = tf.tensor2d([[0.2, 0.2], [1.2, 1.2]], [2, 2], 'float32');
        expectArraysClose(await tf.equal(a, b).data(), [1, 1, 1, 1]);
    });
    it('broadcasting Tensor2D shapes - int32', async () => {
        const a = tf.tensor2d([[3], [7]], [2, 1], 'int32');
        const b = tf.tensor2d([[2, 3, 4], [7, 8, 9]], [2, 3], 'int32');
        expectArraysClose(await tf.equal(a, b).data(), [0, 1, 0, 1, 0, 0]);
    });
    it('broadcasting Tensor2D shapes - float32', async () => {
        const a = tf.tensor2d([[1.1], [7.1]], [2, 1], 'float32');
        const b = tf.tensor2d([[0.1, 1.1, 2.1], [7.1, 8.1, 9.1]], [2, 3], 'float32');
        expectArraysClose(await tf.equal(a, b).data(), [0, 1, 0, 1, 0, 0]);
    });
    it('NaNs in Tensor2D - float32', async () => {
        const a = tf.tensor2d([[1.1, NaN], [1.1, NaN]], [2, 2], 'float32');
        const b = tf.tensor2d([[0.1, NaN], [1.1, NaN]], [2, 2], 'float32');
        expectArraysClose(await tf.equal(a, b).data(), [0, 0, 1, 0]);
    });
    it('broadcasting Tensor2D shapes each with 1 dim', async () => {
        const a = tf.tensor2d([1, 2, 5], [1, 3]);
        const b = tf.tensor2d([5, 1], [2, 1]);
        const res = tf.equal(a, b);
        expect(res.dtype).toBe('bool');
        expect(res.shape).toEqual([2, 3]);
        expectArraysEqual(await res.data(), [0, 0, 1, 1, 0, 0]);
    });
    it('2D and scalar broadcast', async () => {
        const a = tf.tensor2d([1, 2, 3, 2, 5, 6], [2, 3]);
        const b = tf.scalar(2);
        const res = tf.equal(a, b);
        expect(res.dtype).toBe('bool');
        expect(res.shape).toEqual([2, 3]);
        expectArraysEqual(await res.data(), [0, 1, 0, 1, 0, 0]);
    });
    // Tensor3D:
    it('Tensor3D - int32', async () => {
        let a = tf.tensor3d([[[1], [4], [5]], [[8], [9], [12]]], [2, 3, 1], 'int32');
        let b = tf.tensor3d([[[2], [3], [6]], [[7], [10], [12]]], [2, 3, 1], 'int32');
        expectArraysClose(await tf.equal(a, b).data(), [0, 0, 0, 0, 0, 1]);
        a = tf.tensor3d([[[0], [0], [0]], [[1], [1], [1]]], [2, 3, 1], 'int32');
        b = tf.tensor3d([[[0], [0], [0]], [[1], [1], [1]]], [2, 3, 1], 'int32');
        expectArraysClose(await tf.equal(a, b).data(), [1, 1, 1, 1, 1, 1]);
    });
    it('Tensor3D - float32', async () => {
        let a = tf.tensor3d([[[1.1], [4.1], [5.1]], [[8.1], [9.1], [12.1]]], [2, 3, 1], 'float32');
        let b = tf.tensor3d([[[2.1], [3.1], [6.1]], [[7.1], [10.1], [12.1]]], [2, 3, 1], 'float32');
        expectArraysClose(await tf.equal(a, b).data(), [0, 0, 0, 0, 0, 1]);
        a = tf.tensor3d([[[0.1], [0.1], [0.1]], [[1.1], [1.1], [1.1]]], [2, 3, 1], 'float32');
        b = tf.tensor3d([[[0.1], [0.1], [0.1]], [[1.1], [1.1], [1.1]]], [2, 3, 1], 'float32');
        expectArraysClose(await tf.equal(a, b).data(), [1, 1, 1, 1, 1, 1]);
    });
    it('broadcasting Tensor3D shapes - int32', async () => {
        const a = tf.tensor3d([[[1, 0], [2, 3], [4, 5]], [[6, 7], [9, 8], [10, 11]]], [2, 3, 2], 'int32');
        const b = tf.tensor3d([[[1], [2], [3]], [[7], [10], [9]]], [2, 3, 1], 'int32');
        expectArraysClose(await tf.equal(a, b).data(), [1, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0]);
    });
    it('broadcasting Tensor3D shapes - float32', async () => {
        const a = tf.tensor3d([
            [[1.1, 0.1], [2.1, 3.1], [4.1, 5.1]],
            [[6.1, 7.1], [9.1, 8.1], [10.1, 11.1]]
        ], [2, 3, 2], 'float32');
        const b = tf.tensor3d([[[1.1], [2.1], [3.1]], [[7.1], [10.1], [9.1]]], [2, 3, 1], 'float32');
        expectArraysClose(await tf.equal(a, b).data(), [1, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0]);
    });
    it('NaNs in Tensor3D - float32', async () => {
        const a = tf.tensor3d([[[1.1], [NaN], [1.1]], [[0.1], [0.1], [0.1]]], [2, 3, 1], 'float32');
        const b = tf.tensor3d([[[0.1], [0.1], [1.1]], [[1.1], [0.1], [NaN]]], [2, 3, 1], 'float32');
        expectArraysClose(await tf.equal(a, b).data(), [0, 0, 1, 0, 1, 0]);
    });
    it('3D and scalar', async () => {
        const a = tf.tensor3d([1, 2, 3, 4, 5, -1], [2, 3, 1]);
        const b = tf.scalar(-1);
        const res = tf.equal(a, b);
        expect(res.dtype).toBe('bool');
        expect(res.shape).toEqual([2, 3, 1]);
        expectArraysEqual(await res.data(), [0, 0, 0, 0, 0, 1]);
    });
    // Tensor4D:
    it('Tensor4D - int32', async () => {
        let a = tf.tensor4d([1, 4, 5, 8], [2, 2, 1, 1], 'int32');
        let b = tf.tensor4d([2, 3, 6, 8], [2, 2, 1, 1], 'int32');
        expectArraysClose(await tf.equal(a, b).data(), [0, 0, 0, 1]);
        a = tf.tensor4d([0, 1, 2, 3], [2, 2, 1, 1], 'int32');
        b = tf.tensor4d([0, 1, 2, 3], [2, 2, 1, 1], 'int32');
        expectArraysClose(await tf.equal(a, b).data(), [1, 1, 1, 1]);
        a = tf.tensor4d([1, 1, 1, 1], [2, 2, 1, 1], 'int32');
        b = tf.tensor4d([2, 2, 2, 2], [2, 2, 1, 1], 'int32');
        expectArraysClose(await tf.equal(a, b).data(), [0, 0, 0, 0]);
    });
    it('Tensor4D - float32', async () => {
        let a = tf.tensor4d([1.1, 4.1, 5.1, 8.1], [2, 2, 1, 1], 'float32');
        let b = tf.tensor4d([2.1, 3.1, 6.1, 8.1], [2, 2, 1, 1], 'float32');
        expectArraysClose(await tf.equal(a, b).data(), [0, 0, 0, 1]);
        a = tf.tensor4d([0.1, 1.1, 2.2, 3.3], [2, 2, 1, 1], 'float32');
        b = tf.tensor4d([0.1, 1.1, 2.2, 3.3], [2, 2, 1, 1], 'float32');
        expectArraysClose(await tf.equal(a, b).data(), [1, 1, 1, 1]);
        a = tf.tensor4d([0.1, 0.1, 0.1, 0.1], [2, 2, 1, 1], 'float32');
        b = tf.tensor4d([1.1, 1.1, 1.1, 1.1], [2, 2, 1, 1], 'float32');
        expectArraysClose(await tf.equal(a, b).data(), [0, 0, 0, 0]);
    });
    it('broadcasting Tensor4D shapes - int32', async () => {
        const a = tf.tensor4d([1, 2, 5, 9], [2, 2, 1, 1], 'int32');
        const b = tf.tensor4d([[[[1, 2]], [[3, 4]]], [[[5, 6]], [[7, 8]]]], [2, 2, 1, 2], 'int32');
        expectArraysClose(await tf.equal(a, b).data(), [1, 0, 0, 0, 1, 0, 0, 0]);
    });
    it('broadcasting Tensor4D shapes - float32', async () => {
        const a = tf.tensor4d([1.1, 2.1, 5.1, 9.1], [2, 2, 1, 1], 'float32');
        const b = tf.tensor4d([[[[1.1, 2.1]], [[3.1, 4.1]]], [[[5.1, 6.1]], [[7.1, 8.1]]]], [2, 2, 1, 2], 'float32');
        expectArraysClose(await tf.equal(a, b).data(), [1, 0, 0, 0, 1, 0, 0, 0]);
    });
    it('NaNs in Tensor4D - float32', async () => {
        const a = tf.tensor4d([1.1, NaN, 1.1, 0.1], [2, 2, 1, 1], 'float32');
        const b = tf.tensor4d([0.1, 1.1, 1.1, NaN], [2, 2, 1, 1], 'float32');
        expectArraysClose(await tf.equal(a, b).data(), [0, 0, 1, 0]);
    });
    it('throws when passed a as a non-tensor', () => {
        expect(() => tf.equal({}, tf.scalar(1)))
            .toThrowError(/Argument 'a' passed to 'equal' must be a Tensor/);
    });
    it('throws when passed b as a non-tensor', () => {
        expect(() => tf.equal(tf.scalar(1), {}))
            .toThrowError(/Argument 'b' passed to 'equal' must be a Tensor/);
    });
    it('accepts a tensor-like object', async () => {
        const a = [1, 4, 5];
        const b = [2, 3, 5];
        expectArraysClose(await tf.equal(a, b).data(), [0, 0, 1]);
    });
    it('should support string comparison', async () => {
        const tensorA = tf.tensor('aa', [], 'string');
        const tensorB = tf.tensor(['aa', 'ab', 'aaa'], [3], 'string');
        const result = await tf.equal(tensorA, tensorB);
        expectArraysEqual(result.shape, [3]);
        expectArraysEqual(await result.data(), [1, 0, 0]);
    });
});
//# sourceMappingURL=data:application/json;base64,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