/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../index';
import { ALL_ENVS, describeWithFlags } from '../jasmine_util';
import { expectArraysClose } from '../test_util';
import { backend } from '../index';
describeWithFlags('min', ALL_ENVS, () => {
    it('Tensor1D', async () => {
        const a = tf.tensor1d([3, -1, 0, 100, -7, 2]);
        expectArraysClose(await tf.min(a).data(), -7);
    });
    it('return NaNs', async () => {
        const a = tf.tensor1d([3, NaN, 2]);
        expectArraysClose(await tf.min(a).data(), NaN);
    });
    it('2D', async () => {
        const a = tf.tensor2d([3, -1, 0, 100, -7, 2], [2, 3]);
        expectArraysClose(await tf.min(a).data(), -7);
    });
    it('2D axis=[0,1]', async () => {
        const a = tf.tensor2d([3, -1, 0, 100, -7, 2], [2, 3]);
        expectArraysClose(await tf.min(a, [0, 1]).data(), -7);
    });
    it('2D, axis=0', async () => {
        const a = tf.tensor2d([3, -1, 0, 100, -7, 2], [2, 3]);
        const r = tf.min(a, 0);
        expect(r.shape).toEqual([3]);
        expectArraysClose(await r.data(), [3, -7, 0]);
    });
    it('2D, axis=0, keepDims', async () => {
        const a = tf.tensor2d([3, -1, 0, 100, -7, 2], [2, 3]);
        const r = tf.min(a, 0, true /* keepDims */);
        expect(r.shape).toEqual([1, 3]);
        expectArraysClose(await r.data(), [3, -7, 0]);
    });
    it('2D, axis=1 provided as a number', async () => {
        const a = tf.tensor2d([3, 2, 5, 100, -7, 2], [2, 3]);
        const r = tf.min(a, 1);
        expectArraysClose(await r.data(), [2, -7]);
    });
    it('2D, axis = -1 provided as a number', async () => {
        const a = tf.tensor2d([3, 2, 5, 100, -7, 2], [2, 3]);
        const r = tf.min(a, -1);
        expectArraysClose(await r.data(), [2, -7]);
    });
    it('2D, axis=[1]', async () => {
        const a = tf.tensor2d([3, 2, 5, 100, -7, 2], [2, 3]);
        const r = tf.min(a, [1]);
        expectArraysClose(await r.data(), [2, -7]);
    });
    it('axis permutation does not change input', async () => {
        const input = tf.tensor2d([3, -1, 0, 100, -7, 2], [2, 3]);
        const inputDataBefore = await input.data();
        tf.min(input, [1, 0]);
        const inputDataAfter = await input.data();
        expectArraysClose(inputDataBefore, inputDataAfter);
    });
    it('throws when passed a non-tensor', () => {
        expect(() => tf.min({}))
            .toThrowError(/Argument 'x' passed to 'min' must be a Tensor/);
    });
    it('accepts a tensor-like object', async () => {
        expectArraysClose(await tf.min([3, -1, 0, 100, -7, 2]).data(), -7);
    });
    it('accpets int32 input', async () => {
        if (backend() && backend().floatPrecision() === 32) {
            // TODO: Use skip() instead when it is implemented
            const a = tf.tensor1d([12345678, 12345679], 'int32');
            expect(a.dtype).toEqual('int32');
            expectArraysClose(await tf.min(a).data(), 12345678);
        }
    });
    it('min gradient: Scalar', async () => {
        const x = tf.scalar(42);
        const dy = tf.scalar(-1);
        const gradients = tf.grad(v => tf.min(v))(x, dy);
        expectArraysClose(await gradients.data(), -1);
    });
    it('gradient with clones', async () => {
        const x = tf.scalar(42);
        const dy = tf.scalar(-1);
        const gradients = tf.grad(v => tf.min(v.clone()).clone())(x, dy);
        expectArraysClose(await gradients.data(), -1);
    });
    it('min gradient: 1D, ties', async () => {
        const x = tf.tensor1d([-1, -3, -7, -7]);
        const dy = tf.scalar(-1);
        const gradients = tf.grad(v => tf.min(v))(x, dy);
        expectArraysClose(await gradients.data(), [0, 0, -1, -1]);
    });
    it('min gradient: 2D, axes=-1, keepDims=false', async () => {
        const x = tf.tensor2d([[-0, -20, -10], [10, 30, 20]]);
        const dy = tf.tensor1d([-1, -1]);
        const axis = -1;
        const gradients = tf.grad(v => tf.min(v, axis))(x, dy);
        expectArraysClose(await gradients.data(), [0, -1, 0, -1, 0, 0]);
        expect(gradients.shape).toEqual([2, 3]);
    });
    it('min gradient: ties, 2D, axes=-1, keepDims=false', async () => {
        const x = tf.tensor2d([[0, -20, -20], [10, 30, 10]]);
        const dy = tf.tensor1d([-1, -1]);
        const axis = -1;
        const gradients = tf.grad(v => tf.min(v, axis))(x, dy);
        expectArraysClose(await gradients.data(), [0, -1, -1, -1, 0, -1]);
        expect(gradients.shape).toEqual([2, 3]);
    });
    it('min gradient: 2D, axes=0, keepDims=false', async () => {
        const x = tf.tensor2d([[0, 20, 10], [-10, -30, 20]]);
        const dy = tf.tensor1d([-1, -1, -1]);
        const axis = 0;
        const gradients = tf.grad(v => tf.max(v, axis))(x, dy);
        expectArraysClose(await gradients.data(), [-1, -1, 0, 0, 0, -1]);
        expect(gradients.shape).toEqual([2, 3]);
    });
    it('min gradient: 2D, axes=-1, keepDims=true', async () => {
        const x = tf.tensor2d([[0, -20, -10], [10, 30, 20]]);
        const dy = tf.tensor2d([[-1], [-1]]);
        const axis = -1;
        const keepDims = true;
        const gradients = tf.grad(v => tf.min(v, axis, keepDims))(x, dy);
        expectArraysClose(await gradients.data(), [0, -1, 0, -1, 0, 0]);
        expect(gradients.shape).toEqual([2, 3]);
    });
    it('min gradient: 2D, axes=0, keepDims=true', async () => {
        const x = tf.tensor2d([[0, -20, -10], [10, 30, -20]]);
        const dy = tf.tensor2d([[-1, -1, -1]]);
        const axis = 0;
        const keepDims = true;
        const gradients = tf.grad(v => tf.min(v, axis, keepDims))(x, dy);
        expectArraysClose(await gradients.data(), [-1, -1, 0, 0, 0, -1]);
        expect(gradients.shape).toEqual([2, 3]);
    });
    it('max gradient: 3D, axis=1 keepDims=false', async () => {
        const x = tf.ones([2, 1, 250]);
        const axis = 1;
        const gradients = tf.grad(v => tf.min(v, axis))(x);
        expect(gradients.shape).toEqual(x.shape);
    });
    it('min gradient: 3D, axes=[1, 2], keepDims=false', async () => {
        const x = tf.tensor3d([[[0, -20], [-10, -15]], [[10, 30], [20, 15]]]);
        const dy = tf.tensor1d([-1, -1]);
        const axis = [1, 2];
        const gradients = tf.grad(v => tf.min(v, axis))(x, dy);
        expectArraysClose(await gradients.data(), [0, -1, 0, 0, -1, 0, 0, 0]);
        expect(gradients.shape).toEqual([2, 2, 2]);
    });
    it('min gradient: ties, 3D, axes=[1, 2], keepDims=false', async () => {
        const x = tf.tensor3d([[[0, -20], [-20, -20]], [[10, 30], [10, 15]]]);
        const dy = tf.tensor1d([-1, -1]);
        const axis = [1, 2];
        const gradients = tf.grad(v => tf.min(v, axis))(x, dy);
        expectArraysClose(await gradients.data(), [0, -1, -1, -1, -1, 0, -1, 0]);
        expect(gradients.shape).toEqual([2, 2, 2]);
    });
    it('min gradient: 3D, axes=2, keepDims=false', async () => {
        const x = tf.tensor3d([[[0, -20], [-10, -15]], [[10, 30], [20, 15]]]);
        const dy = tf.tensor2d([[-1, -1], [-1, -1]]);
        const axis = 2;
        const gradients = tf.grad(v => tf.min(v, axis))(x, dy);
        expectArraysClose(await gradients.data(), [0, -1, 0, -1, -1, 0, 0, -1]);
        expect(gradients.shape).toEqual([2, 2, 2]);
    });
    it('min gradient: 3D, axes=2, keepDims=true', async () => {
        const x = tf.tensor3d([[[0, -20], [-10, -15]], [[10, 30], [20, 15]]]);
        const dy = tf.tensor3d([[[-1], [-1]], [[-1], [-1]]]);
        const axis = 2;
        const keepDims = true;
        const gradients = tf.grad(v => tf.min(v, axis, keepDims))(x, dy);
        expectArraysClose(await gradients.data(), [0, -1, 0, -1, -1, 0, 0, -1]);
        expect(gradients.shape).toEqual([2, 2, 2]);
    });
    it('min gradient: ties, 4D, axes=[1, 2, 3], keepDims=false', async () => {
        const x = tf.tensor4d([
            [[[0, -20], [-20, -20]], [[10, 30], [10, 30]]],
            [[[0, 20], [20, 20]], [[-10, -30], [-10, -30]]]
        ]);
        const dy = tf.tensor1d([-1, -1]);
        const axis = [1, 2, 3];
        const gradients = tf.grad(v => tf.min(v, axis))(x, dy);
        expectArraysClose(await gradients.data(), [0, -1, -1, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, -1]);
        expect(gradients.shape).toEqual([2, 2, 2, 2]);
    });
    it('min gradient: ties, 4D, axes=[2, 3], keepDims=true', async () => {
        const x = tf.tensor4d([
            [[[0, -20], [-20, -20]], [[10, 30], [10, 30]]],
            [[[0, 20], [20, 20]], [[-10, -30], [-10, -30]]]
        ]);
        const dy = tf.tensor4d([[[[-1]], [[-2]]], [[[-3]], [[-4]]]]);
        const axis = [2, 3];
        const keepDims = true;
        const gradients = tf.grad(v => tf.min(v, axis, keepDims))(x, dy);
        expectArraysClose(await gradients.data(), [0, -1, -1, -1, -2, 0, -2, 0, -3, 0, 0, 0, 0, -4, 0, -4]);
        expect(gradients.shape).toEqual([2, 2, 2, 2]);
    });
    it('throws error for string tensor', () => {
        expect(() => tf.min(['a']))
            .toThrowError(/Argument 'x' passed to 'min' must be numeric tensor/);
    });
});
//# sourceMappingURL=data:application/json;base64,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