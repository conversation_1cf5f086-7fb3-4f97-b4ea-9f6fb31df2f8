#!/usr/bin/env node

/**
 * ملف تشغيل سريع لنظام التداول الهجين المتقدم
 * Quick Start Script for Advanced Hybrid Trading System
 */

const TradingSystem = require('./main');
const path = require('path');
const fs = require('fs');

// إعداد متغيرات البيئة
require('dotenv').config();

class QuickStart {
    constructor() {
        this.system = null;
        this.config = this.loadConfiguration();
    }

    /**
     * تحميل الإعدادات
     */
    loadConfiguration() {
        const configPath = path.join(__dirname, 'config.json');
        let config = {};

        // محاولة تحميل ملف الإعدادات
        try {
            if (fs.existsSync(configPath)) {
                const configData = fs.readFileSync(configPath, 'utf8');
                config = JSON.parse(configData);
                console.log('✅ Configuration loaded from config.json');
            }
        } catch (error) {
            console.log('⚠️ Could not load config.json, using defaults');
        }

        // دمج مع متغيرات البيئة
        return {
            quotex: {
                headless: false, // دائماً مرئي للتسجيل اليدوي
                userDataDir: process.env.QUOTEX_USER_DATA_DIR || './user_data'
            },
            autoTrade: process.env.AUTO_TRADE === 'true' || config.autoTrade || false,
            webPort: parseInt(process.env.PORT) || config.webPort || 3000,
            webHost: process.env.HOST || config.webHost || 'localhost',
            dataDir: process.env.DATA_DIR || config.dataDir || './data',
            logLevel: process.env.LOG_LEVEL || config.logLevel || 'info'
        };
    }

    /**
     * التحقق من المتطلبات
     */
    checkRequirements() {
        console.log('🔍 Checking system requirements...');

        // التحقق من Node.js version
        const nodeVersion = process.version;
        const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
        
        if (majorVersion < 14) {
            throw new Error(`Node.js 14+ required, current version: ${nodeVersion}`);
        }
        console.log(`✅ Node.js version: ${nodeVersion}`);

        // لا حاجة للتحقق من بيانات تسجيل الدخول - سيتم التسجيل يدوياً
        console.log('✅ Manual login mode enabled - browser will open for authentication');

        // التحقق من المجلدات المطلوبة
        const requiredDirs = [
            this.config.dataDir,
            path.join(this.config.dataDir, 'historical'),
            path.join(this.config.dataDir, 'live'),
            path.join(this.config.dataDir, 'analysis')
        ];

        requiredDirs.forEach(dir => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
                console.log(`📁 Created directory: ${dir}`);
            }
        });

        console.log('✅ All requirements satisfied');
    }

    /**
     * عرض معلومات النظام
     */
    displaySystemInfo() {
        console.log('\n' + '='.repeat(60));
        console.log('🚀 ADVANCED HYBRID TRADING SYSTEM');
        console.log('='.repeat(60));
        console.log(`👤 Login Mode: Manual (Browser will open)`);
        console.log(`🌐 Web Interface: http://${this.config.webHost}:${this.config.webPort}`);
        console.log(`🤖 Auto Trading: ${this.config.autoTrade ? 'ENABLED' : 'DISABLED'}`);
        console.log(`💾 Data Directory: ${this.config.dataDir}`);
        console.log(`🖥️  Browser Mode: Visible (for manual login)`);
        console.log('='.repeat(60));
        console.log('');
    }

    /**
     * بدء النظام
     */
    async start() {
        try {
            console.log('🚀 Starting Advanced Hybrid Trading System...\n');
            console.log('🔍 Current working directory:', process.cwd());
            console.log('🔍 Node.js version:', process.version);
            console.log('🔍 Process arguments:', process.argv);

            // التحقق من المتطلبات
            console.log('🔍 Checking requirements...');
            this.checkRequirements();
            console.log('✅ Requirements check completed');

            // عرض معلومات النظام
            console.log('📋 Displaying system info...');
            this.displaySystemInfo();
            console.log('✅ System info displayed');

            // إنشاء النظام
            console.log('🏗️ Creating TradingSystem instance...');
            this.system = new TradingSystem();
            console.log('✅ TradingSystem instance created');

            // تخصيص الإعدادات
            this.system.config.quotex = this.config.quotex;
            this.system.config.webInterface.port = this.config.webPort;
            this.system.config.webInterface.host = this.config.webHost;

            // تهيئة النظام
            await this.system.initialize();

            // بدء النظام
            await this.system.start();

            // عرض رسالة النجاح
            this.displaySuccessMessage();

            // إعداد معالجات الإيقاف
            this.setupShutdownHandlers();

        } catch (error) {
            console.error('\n❌ Failed to start system:', error.message);
            console.error('\nFor help, check the documentation or run with --help');
            process.exit(1);
        }
    }

    /**
     * عرض رسالة النجاح
     */
    displaySuccessMessage() {
        console.log('\n' + '🎉'.repeat(20));
        console.log('✅ SYSTEM STARTED SUCCESSFULLY!');
        console.log('🎉'.repeat(20));
        console.log('');
        console.log('🌐 Web Interface: http://' + this.config.webHost + ':' + this.config.webPort);
        console.log('📊 Monitor your trades and signals in real-time');
        console.log('🤖 Hybrid AI strategy is analyzing markets');
        console.log('💾 All data is being saved automatically');
        console.log('');
        console.log('Press Ctrl+C to stop the system');
        console.log('');
    }

    /**
     * إعداد معالجات الإيقاف
     */
    setupShutdownHandlers() {
        const shutdown = async (signal) => {
            console.log(`\n🛑 Received ${signal}, shutting down gracefully...`);
            
            if (this.system) {
                await this.system.shutdown();
            } else {
                process.exit(0);
            }
        };

        process.on('SIGINT', () => shutdown('SIGINT'));
        process.on('SIGTERM', () => shutdown('SIGTERM'));
        
        process.on('uncaughtException', (error) => {
            console.error('❌ Uncaught Exception:', error);
            if (this.system) {
                this.system.shutdown().catch(() => process.exit(1));
            } else {
                process.exit(1);
            }
        });

        process.on('unhandledRejection', (reason, promise) => {
            console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
        });
    }

    /**
     * عرض المساعدة
     */
    static showHelp() {
        console.log(`
🚀 Advanced Hybrid Trading System - Quick Start

USAGE:
  node start.js [options]

ENVIRONMENT VARIABLES:
  AUTO_TRADE            Enable automatic trading (default: false)
  PORT                  Web interface port (default: 3000)
  HOST                  Web interface host (default: localhost)
  DATA_DIR              Data storage directory (default: ./data)
  QUOTEX_USER_DATA_DIR  Browser user data directory (default: ./user_data)

EXAMPLES:
  # Basic start (browser will open for manual login)
  node start.js

  # Start with auto trading enabled
  AUTO_TRADE=true node start.js

  # Start with custom port
  PORT=8080 node start.js

  # Start with custom user data directory
  QUOTEX_USER_DATA_DIR=./my_browser_data node start.js

CONFIG FILE:
  You can also create a config.json file:
  {
    "autoTrade": false,
    "webPort": 3000,
    "webHost": "localhost",
    "dataDir": "./data"
  }

LOGIN:
  The system will open a browser window for manual login.
  Simply login to your Quotex account in the browser and the system will automatically detect the session.

For more information, visit: https://github.com/your-repo/quotex-hybrid-trading
        `);
    }
}

// معالجة معاملات سطر الأوامر
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
    QuickStart.showHelp();
    process.exit(0);
}

// بدء النظام
const quickStart = new QuickStart();
quickStart.start().catch(error => {
    console.error('❌ Startup failed:', error);
    process.exit(1);
});
