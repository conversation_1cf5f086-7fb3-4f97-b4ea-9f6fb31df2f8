# M(ulti-platform)ANGLE effort

Starting in early 2014, the ANGLE team has begun work on refactoring the code
with the goal of supporting translation to desktop OpenGL. The new purpose of
ANGLE will be provide a consistent OpenGL ES and EGL context on as many
platforms as possible.

The design doc is available [here]
(https://docs.google.com/document/d/17mxRfzXuEWyvGM3t2KqVY4svvfRj_GzysOEpmnDpqeo/edit?usp=sharing).
