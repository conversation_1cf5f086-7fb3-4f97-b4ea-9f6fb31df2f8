/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
/// <amd-module name="@tensorflow/tfjs-core/dist/ops/ops" />
export { abs } from './abs';
export { acos } from './acos';
export { acosh } from './acosh';
export { add } from './add';
export { addN } from './add_n';
export { all } from './all';
export { any } from './any';
export { argMax } from './arg_max';
export { argMin } from './arg_min';
export { asin } from './asin';
export { asinh } from './asinh';
export { atan } from './atan';
export { atan2 } from './atan2';
export { atanh } from './atanh';
export { avgPool } from './avg_pool';
export { avgPool3d } from './avg_pool_3d';
export { basicLSTMCell } from './basic_lstm_cell';
export { batchToSpaceND } from './batch_to_space_nd';
export { batchNorm } from './batchnorm';
export { batchNorm2d } from './batchnorm2d';
export { batchNorm3d } from './batchnorm3d';
export { batchNorm4d } from './batchnorm4d';
export { bincount } from './bincount';
export { bitwiseAnd } from './bitwise_and';
export { broadcastArgs } from './broadcast_args';
export { broadcastTo } from './broadcast_to';
export { buffer } from './buffer';
export { cast } from './cast';
export { ceil } from './ceil';
export { clipByValue } from './clip_by_value';
export { clone } from './clone';
export { complex } from './complex';
export { concat } from './concat';
export { concat1d } from './concat_1d';
export { concat2d } from './concat_2d';
export { concat3d } from './concat_3d';
export { concat4d } from './concat_4d';
export { conv1d } from './conv1d';
export { conv2d } from './conv2d';
export { conv2dTranspose } from './conv2d_transpose';
export { conv3d } from './conv3d';
export { conv3dTranspose } from './conv3d_transpose';
export { cos } from './cos';
export { cosh } from './cosh';
export { cumprod } from './cumprod';
export { cumsum } from './cumsum';
export { denseBincount } from './dense_bincount';
export { depthToSpace } from './depth_to_space';
export { depthwiseConv2d } from './depthwise_conv2d';
export { diag } from './diag';
export { dilation2d } from './dilation2d';
export { div } from './div';
export { divNoNan } from './div_no_nan';
export { dot } from './dot';
export { einsum } from './einsum';
export { elu } from './elu';
export { ensureShape } from './ensure_shape';
export { equal } from './equal';
export { erf } from './erf';
export { euclideanNorm } from './euclidean_norm';
export { exp } from './exp';
export { expandDims } from './expand_dims';
export { expm1 } from './expm1';
export { eye } from './eye';
export { fill } from './fill';
export { floor } from './floor';
export { floorDiv } from './floorDiv';
export { gather } from './gather';
export { greater } from './greater';
export { greaterEqual } from './greater_equal';
export { imag } from './imag';
export { isFinite } from './is_finite';
export { isInf } from './is_inf';
export { isNaN } from './is_nan';
export { leakyRelu } from './leaky_relu';
export { less } from './less';
export { lessEqual } from './less_equal';
export { linspace } from './linspace';
export { localResponseNormalization } from './local_response_normalization';
export { log } from './log';
export { log1p } from './log1p';
export { logSigmoid } from './log_sigmoid';
export { logSoftmax } from './log_softmax';
export { logSumExp } from './log_sum_exp';
export { logicalAnd } from './logical_and';
export { logicalNot } from './logical_not';
export { logicalOr } from './logical_or';
export { logicalXor } from './logical_xor';
export { lowerBound } from './lower_bound';
export { matMul } from './mat_mul';
export { max } from './max';
export { maxPool } from './max_pool';
export { maxPool3d } from './max_pool_3d';
export { maxPoolWithArgmax } from './max_pool_with_argmax';
export { maximum } from './maximum';
export { mean } from './mean';
export { meshgrid } from './meshgrid';
export { min } from './min';
export { minimum } from './minimum';
export { mirrorPad } from './mirror_pad';
export { mod } from './mod';
export { moments } from './moments';
export { mul } from './mul';
export { LSTMCellFunc, multiRNNCell } from './multi_rnn_cell';
export { multinomial } from './multinomial';
export { neg } from './neg';
export { notEqual } from './not_equal';
export { oneHot } from './one_hot';
export { ones } from './ones';
export { onesLike } from './ones_like';
export { outerProduct } from './outer_product';
export { pad } from './pad';
export { pad1d } from './pad1d';
export { pad2d } from './pad2d';
export { pad3d } from './pad3d';
export { pad4d } from './pad4d';
export { pool } from './pool';
export { pow } from './pow';
export { prelu } from './prelu';
export { print } from './print';
export { prod } from './prod';
export { raggedGather } from './ragged_gather';
export { raggedRange } from './ragged_range';
export { raggedTensorToTensor } from './ragged_tensor_to_tensor';
export { rand } from './rand';
export { randomGamma } from './random_gamma';
export { randomNormal } from './random_normal';
export { randomStandardNormal } from './random_standard_normal';
export { randomUniform } from './random_uniform';
export { randomUniformInt } from './random_uniform_int';
export { range } from './range';
export { real } from './real';
export { reciprocal } from './reciprocal';
export { relu } from './relu';
export { relu6 } from './relu6';
export { reshape } from './reshape';
export { reverse } from './reverse';
export { reverse1d } from './reverse_1d';
export { reverse2d } from './reverse_2d';
export { reverse3d } from './reverse_3d';
export { reverse4d } from './reverse_4d';
export { round } from './round';
export { rsqrt } from './rsqrt';
export { scalar } from './scalar';
export { selu } from './selu';
export { separableConv2d } from './separable_conv2d';
export { setdiff1dAsync } from './setdiff1d_async';
export { sigmoid } from './sigmoid';
export { sign } from './sign';
export { sin } from './sin';
export { sinh } from './sinh';
export { slice } from './slice';
export { slice1d } from './slice1d';
export { slice2d } from './slice2d';
export { slice3d } from './slice3d';
export { slice4d } from './slice4d';
export { softmax } from './softmax';
export { softplus } from './softplus';
export { spaceToBatchND } from './space_to_batch_nd';
export { fft } from './spectral/fft';
export { ifft } from './spectral/ifft';
export { irfft } from './spectral/irfft';
export { rfft } from './spectral/rfft';
export { split } from './split';
export { sqrt } from './sqrt';
export { square } from './square';
export { squaredDifference } from './squared_difference';
export { squeeze } from './squeeze';
export { stack } from './stack';
export { step } from './step';
export { stridedSlice } from './strided_slice';
export { sub } from './sub';
export { sum } from './sum';
export { tan } from './tan';
export { tanh } from './tanh';
export { tensor } from './tensor';
export { tensor1d } from './tensor1d';
export { tensor2d } from './tensor2d';
export { tensor3d } from './tensor3d';
export { tensor4d } from './tensor4d';
export { tensor5d } from './tensor5d';
export { tensor6d } from './tensor6d';
export { tensorScatterUpdate } from './tensor_scatter_update';
export { tile } from './tile';
export { topk } from './topk';
export { truncatedNormal } from './truncated_normal';
export { unique } from './unique';
export { unsortedSegmentSum } from './unsorted_segment_sum';
export { unstack } from './unstack';
export { upperBound } from './upper_bound';
export { variable } from './variable';
export { where } from './where';
export { whereAsync } from './where_async';
export { zeros } from './zeros';
export { zerosLike } from './zeros_like';
export * from './boolean_mask';
export * from './transpose';
export * from './norm';
export * from './moving_average';
export * from './scatter_nd';
export * from './search_sorted';
export * from './sparse_to_dense';
export * from './gather_nd';
export * from './dropout';
export * from './signal_ops_util';
export * from './in_top_k';
export { op, OP_SCOPE_SUFFIX } from './operation';
declare const spectral: {
    fft: (input: import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>) => import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>;
    ifft: (input: import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>) => import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>;
    rfft: (input: import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>, fftLength?: number) => import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>;
    irfft: (input: import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>) => import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>;
};
import * as fused from './fused_ops';
declare const signal: {
    hammingWindow: (windowLength: number) => import("@tensorflow/tfjs-core/dist/tensor").Tensor1D;
    hannWindow: (windowLength: number) => import("@tensorflow/tfjs-core/dist/tensor").Tensor1D;
    frame: (signal: import("@tensorflow/tfjs-core/dist/tensor").Tensor1D, frameLength: number, frameStep: number, padEnd?: boolean, padValue?: number) => import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>;
    stft: (signal: import("@tensorflow/tfjs-core/dist/tensor").Tensor1D, frameLength: number, frameStep: number, fftLength?: number, windowFn?: (length: number) => import("@tensorflow/tfjs-core/dist/tensor").Tensor1D) => import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>;
};
declare const image: {
    flipLeftRight: (image: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor4D) => import("@tensorflow/tfjs-core/dist/tensor").Tensor4D;
    grayscaleToRGB: <T extends import("@tensorflow/tfjs-core/dist/tensor").Tensor2D | import("@tensorflow/tfjs-core/dist/tensor").Tensor3D | import("@tensorflow/tfjs-core/dist/tensor").Tensor4D | import("@tensorflow/tfjs-core/dist/tensor").Tensor5D | import("@tensorflow/tfjs-core/dist/tensor").Tensor6D>(image: import("@tensorflow/tfjs-core/dist/types").TensorLike | T) => T;
    resizeNearestNeighbor: <T_1 extends import("@tensorflow/tfjs-core/dist/tensor").Tensor3D | import("@tensorflow/tfjs-core/dist/tensor").Tensor4D>(images: import("@tensorflow/tfjs-core/dist/types").TensorLike | T_1, size: [number, number], alignCorners?: boolean, halfPixelCenters?: boolean) => T_1;
    resizeBilinear: <T_2 extends import("@tensorflow/tfjs-core/dist/tensor").Tensor3D | import("@tensorflow/tfjs-core/dist/tensor").Tensor4D>(images: import("@tensorflow/tfjs-core/dist/types").TensorLike | T_2, size: [number, number], alignCorners?: boolean, halfPixelCenters?: boolean) => T_2;
    rgbToGrayscale: <T_3 extends import("@tensorflow/tfjs-core/dist/tensor").Tensor2D | import("@tensorflow/tfjs-core/dist/tensor").Tensor3D | import("@tensorflow/tfjs-core/dist/tensor").Tensor4D | import("@tensorflow/tfjs-core/dist/tensor").Tensor5D | import("@tensorflow/tfjs-core/dist/tensor").Tensor6D>(image: import("@tensorflow/tfjs-core/dist/types").TensorLike | T_3) => T_3;
    rotateWithOffset: (image: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor4D, radians: number, fillValue?: number | [number, number, number], center?: number | [number, number]) => import("@tensorflow/tfjs-core/dist/tensor").Tensor4D;
    cropAndResize: (image: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor4D, boxes: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor2D, boxInd: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor1D, cropSize: [number, number], method?: "bilinear" | "nearest", extrapolationValue?: number) => import("@tensorflow/tfjs-core/dist/tensor").Tensor4D;
    nonMaxSuppression: (boxes: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor2D, scores: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor1D, maxOutputSize: number, iouThreshold?: number, scoreThreshold?: number) => import("@tensorflow/tfjs-core/dist/tensor").Tensor1D;
    nonMaxSuppressionAsync: (boxes: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor2D, scores: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor1D, maxOutputSize: number, iouThreshold?: number, scoreThreshold?: number) => Promise<import("@tensorflow/tfjs-core/dist/tensor").Tensor1D>;
    nonMaxSuppressionWithScore: (boxes: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor2D, scores: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor1D, maxOutputSize: number, iouThreshold?: number, scoreThreshold?: number, softNmsSigma?: number) => import("@tensorflow/tfjs-core/dist/tensor_types").NamedTensorMap;
    nonMaxSuppressionWithScoreAsync: (boxes: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor2D, scores: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor1D, maxOutputSize: number, iouThreshold?: number, scoreThreshold?: number, softNmsSigma?: number) => Promise<import("@tensorflow/tfjs-core/dist/tensor_types").NamedTensorMap>;
    nonMaxSuppressionPadded: (boxes: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor2D, scores: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor1D, maxOutputSize: number, iouThreshold?: number, scoreThreshold?: number, padToMaxOutputSize?: boolean) => import("@tensorflow/tfjs-core/dist/tensor_types").NamedTensorMap;
    nonMaxSuppressionPaddedAsync: (boxes: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor2D, scores: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor1D, maxOutputSize: number, iouThreshold?: number, scoreThreshold?: number, padToMaxOutputSize?: boolean) => Promise<import("@tensorflow/tfjs-core/dist/tensor_types").NamedTensorMap>;
    threshold: (image: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor3D, method?: string, inverted?: boolean, threshValue?: number) => import("@tensorflow/tfjs-core/dist/tensor").Tensor3D;
    transform: (image: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor4D, transforms: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor2D, interpolation?: "bilinear" | "nearest", fillMode?: "reflect" | "nearest" | "constant" | "wrap", fillValue?: number, outputShape?: [number, number]) => import("@tensorflow/tfjs-core/dist/tensor").Tensor4D;
};
declare const linalg: {
    bandPart: <T extends import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>>(a: import("@tensorflow/tfjs-core/dist/types").TensorLike | T, numLower: number | import("@tensorflow/tfjs-core/dist/tensor").Scalar, numUpper: number | import("@tensorflow/tfjs-core/dist/tensor").Scalar) => T;
    gramSchmidt: (xs: import("@tensorflow/tfjs-core/dist/tensor").Tensor2D | import("@tensorflow/tfjs-core/dist/tensor").Tensor1D[]) => import("@tensorflow/tfjs-core/dist/tensor").Tensor2D | import("@tensorflow/tfjs-core/dist/tensor").Tensor1D[];
    qr: (x: import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>, fullMatrices?: boolean) => [import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>, import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>];
};
declare const losses: {
    absoluteDifference: <T extends import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>, O extends import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>>(labels: import("@tensorflow/tfjs-core/dist/types").TensorLike | T, predictions: import("@tensorflow/tfjs-core/dist/types").TensorLike | T, weights?: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>, reduction?: import("@tensorflow/tfjs-core/dist/base").Reduction) => O;
    computeWeightedLoss: <T_1 extends import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>, O_1 extends import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>>(losses: import("@tensorflow/tfjs-core/dist/types").TensorLike | T_1, weights?: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>, reduction?: import("@tensorflow/tfjs-core/dist/base").Reduction) => O_1;
    cosineDistance: <T_2 extends import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>, O_2 extends import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>>(labels: import("@tensorflow/tfjs-core/dist/types").TensorLike | T_2, predictions: import("@tensorflow/tfjs-core/dist/types").TensorLike | T_2, axis: number, weights?: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>, reduction?: import("@tensorflow/tfjs-core/dist/base").Reduction) => O_2;
    hingeLoss: <T_3 extends import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>, O_3 extends import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>>(labels: import("@tensorflow/tfjs-core/dist/types").TensorLike | T_3, predictions: import("@tensorflow/tfjs-core/dist/types").TensorLike | T_3, weights?: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>, reduction?: import("@tensorflow/tfjs-core/dist/base").Reduction) => O_3;
    huberLoss: <T_4 extends import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>, O_4 extends import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>>(labels: import("@tensorflow/tfjs-core/dist/types").TensorLike | T_4, predictions: import("@tensorflow/tfjs-core/dist/types").TensorLike | T_4, weights?: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>, delta?: number, reduction?: import("@tensorflow/tfjs-core/dist/base").Reduction) => O_4;
    logLoss: <T_5 extends import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>, O_5 extends import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>>(labels: import("@tensorflow/tfjs-core/dist/types").TensorLike | T_5, predictions: import("@tensorflow/tfjs-core/dist/types").TensorLike | T_5, weights?: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>, epsilon?: number, reduction?: import("@tensorflow/tfjs-core/dist/base").Reduction) => O_5;
    meanSquaredError: <T_6 extends import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>, O_6 extends import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>>(labels: import("@tensorflow/tfjs-core/dist/types").TensorLike | T_6, predictions: import("@tensorflow/tfjs-core/dist/types").TensorLike | T_6, weights?: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>, reduction?: import("@tensorflow/tfjs-core/dist/base").Reduction) => O_6;
    sigmoidCrossEntropy: <T_7 extends import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>, O_7 extends import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>>(multiClassLabels: import("@tensorflow/tfjs-core/dist/types").TensorLike | T_7, logits: import("@tensorflow/tfjs-core/dist/types").TensorLike | T_7, weights?: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>, labelSmoothing?: number, reduction?: import("@tensorflow/tfjs-core/dist/base").Reduction) => O_7;
    softmaxCrossEntropy: <T_8 extends import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>, O_8 extends import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>>(onehotLabels: import("@tensorflow/tfjs-core/dist/types").TensorLike | T_8, logits: import("@tensorflow/tfjs-core/dist/types").TensorLike | T_8, weights?: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>, labelSmoothing?: number, reduction?: import("@tensorflow/tfjs-core/dist/base").Reduction) => O_8;
};
declare const sparse: {
    sparseFillEmptyRows: (indices: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor2D, values: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor1D, denseShape: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor1D, defaultValue: import("@tensorflow/tfjs-core/dist/types").ScalarLike | import("@tensorflow/tfjs-core/dist/tensor").Scalar) => import("@tensorflow/tfjs-core/dist/tensor_types").NamedTensorMap;
    sparseReshape: (inputIndices: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor2D, inputShape: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor1D, newShape: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor1D) => import("@tensorflow/tfjs-core/dist/tensor_types").NamedTensorMap;
    sparseSegmentMean: (data: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>, indices: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor1D, segmentIds: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor1D) => import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>;
    sparseSegmentSum: (data: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>, indices: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor1D, segmentIds: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor1D) => import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>;
};
declare const string: {
    stringNGrams: (data: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor1D, dataSplits: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>, separator: string, nGramWidths: number[], leftPad: string, rightPad: string, padWidth: number, preserveShortSequences: boolean) => import("@tensorflow/tfjs-core/dist/tensor_types").NamedTensorMap;
    stringSplit: (input: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor1D, delimiter: import("@tensorflow/tfjs-core/dist/types").ScalarLike | import("@tensorflow/tfjs-core/dist/tensor").Scalar, skipEmpty?: boolean) => import("@tensorflow/tfjs-core/dist/tensor_types").NamedTensorMap;
    stringToHashBucketFast: (input: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>, numBuckets: number) => import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>;
    staticRegexReplace: (input: import("@tensorflow/tfjs-core/dist/types").TensorLike | import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>, pattern: string, rewrite: string, replaceGlobal?: boolean) => import("@tensorflow/tfjs-core/dist/tensor").Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>;
};
export { image, linalg, losses, spectral, fused, signal, sparse, string };
