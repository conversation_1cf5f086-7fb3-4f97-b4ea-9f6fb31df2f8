/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
// Modularized ops.
export { abs } from './abs';
export { acos } from './acos';
export { acosh } from './acosh';
export { add } from './add';
export { addN } from './add_n';
export { all } from './all';
export { any } from './any';
export { argMax } from './arg_max';
export { argMin } from './arg_min';
export { asin } from './asin';
export { asinh } from './asinh';
export { atan } from './atan';
export { atan2 } from './atan2';
export { atanh } from './atanh';
export { avgPool } from './avg_pool';
export { avgPool3d } from './avg_pool_3d';
export { basicLSTMCell } from './basic_lstm_cell';
export { batchToSpaceND } from './batch_to_space_nd';
export { batchNorm } from './batchnorm';
export { batchNorm2d } from './batchnorm2d';
export { batchNorm3d } from './batchnorm3d';
export { batchNorm4d } from './batchnorm4d';
export { bincount } from './bincount';
export { bitwiseAnd } from './bitwise_and';
export { broadcastArgs } from './broadcast_args';
export { broadcastTo } from './broadcast_to';
export { buffer } from './buffer';
export { cast } from './cast';
export { ceil } from './ceil';
export { clipByValue } from './clip_by_value';
export { clone } from './clone';
export { complex } from './complex';
export { concat } from './concat';
export { concat1d } from './concat_1d';
export { concat2d } from './concat_2d';
export { concat3d } from './concat_3d';
export { concat4d } from './concat_4d';
export { conv1d } from './conv1d';
export { conv2d } from './conv2d';
export { conv2dTranspose } from './conv2d_transpose';
export { conv3d } from './conv3d';
export { conv3dTranspose } from './conv3d_transpose';
export { cos } from './cos';
export { cosh } from './cosh';
export { cumprod } from './cumprod';
export { cumsum } from './cumsum';
export { denseBincount } from './dense_bincount';
export { depthToSpace } from './depth_to_space';
export { depthwiseConv2d } from './depthwise_conv2d';
export { diag } from './diag';
export { dilation2d } from './dilation2d';
export { div } from './div';
export { divNoNan } from './div_no_nan';
export { dot } from './dot';
export { einsum } from './einsum';
export { elu } from './elu';
export { ensureShape } from './ensure_shape';
export { equal } from './equal';
export { erf } from './erf';
export { euclideanNorm } from './euclidean_norm';
export { exp } from './exp';
export { expandDims } from './expand_dims';
export { expm1 } from './expm1';
export { eye } from './eye';
export { fill } from './fill';
export { floor } from './floor';
export { floorDiv } from './floorDiv';
export { gather } from './gather';
export { greater } from './greater';
export { greaterEqual } from './greater_equal';
export { imag } from './imag';
export { isFinite } from './is_finite';
export { isInf } from './is_inf';
export { isNaN } from './is_nan';
export { leakyRelu } from './leaky_relu';
export { less } from './less';
export { lessEqual } from './less_equal';
export { linspace } from './linspace';
export { localResponseNormalization } from './local_response_normalization';
export { log } from './log';
export { log1p } from './log1p';
export { logSigmoid } from './log_sigmoid';
export { logSoftmax } from './log_softmax';
export { logSumExp } from './log_sum_exp';
export { logicalAnd } from './logical_and';
export { logicalNot } from './logical_not';
export { logicalOr } from './logical_or';
export { logicalXor } from './logical_xor';
export { lowerBound } from './lower_bound';
export { matMul } from './mat_mul';
export { max } from './max';
export { maxPool } from './max_pool';
export { maxPool3d } from './max_pool_3d';
export { maxPoolWithArgmax } from './max_pool_with_argmax';
export { maximum } from './maximum';
export { mean } from './mean';
export { meshgrid } from './meshgrid';
export { min } from './min';
export { minimum } from './minimum';
export { mirrorPad } from './mirror_pad';
export { mod } from './mod';
export { moments } from './moments';
export { mul } from './mul';
export { multiRNNCell } from './multi_rnn_cell';
export { multinomial } from './multinomial';
export { neg } from './neg';
export { notEqual } from './not_equal';
export { oneHot } from './one_hot';
export { ones } from './ones';
export { onesLike } from './ones_like';
export { outerProduct } from './outer_product';
export { pad } from './pad';
export { pad1d } from './pad1d';
export { pad2d } from './pad2d';
export { pad3d } from './pad3d';
export { pad4d } from './pad4d';
export { pool } from './pool';
export { pow } from './pow';
export { prelu } from './prelu';
export { print } from './print';
export { prod } from './prod';
export { raggedGather } from './ragged_gather';
export { raggedRange } from './ragged_range';
export { raggedTensorToTensor } from './ragged_tensor_to_tensor';
export { rand } from './rand';
export { randomGamma } from './random_gamma';
export { randomNormal } from './random_normal';
export { randomStandardNormal } from './random_standard_normal';
export { randomUniform } from './random_uniform';
export { randomUniformInt } from './random_uniform_int';
export { range } from './range';
export { real } from './real';
export { reciprocal } from './reciprocal';
export { relu } from './relu';
export { relu6 } from './relu6';
export { reshape } from './reshape';
export { reverse } from './reverse';
export { reverse1d } from './reverse_1d';
export { reverse2d } from './reverse_2d';
export { reverse3d } from './reverse_3d';
export { reverse4d } from './reverse_4d';
export { round } from './round';
export { rsqrt } from './rsqrt';
export { scalar } from './scalar';
export { selu } from './selu';
export { separableConv2d } from './separable_conv2d';
export { setdiff1dAsync } from './setdiff1d_async';
export { sigmoid } from './sigmoid';
export { sign } from './sign';
export { sin } from './sin';
export { sinh } from './sinh';
export { slice } from './slice';
export { slice1d } from './slice1d';
export { slice2d } from './slice2d';
export { slice3d } from './slice3d';
export { slice4d } from './slice4d';
export { softmax } from './softmax';
export { softplus } from './softplus';
export { spaceToBatchND } from './space_to_batch_nd';
export { fft } from './spectral/fft';
export { ifft } from './spectral/ifft';
export { irfft } from './spectral/irfft';
export { rfft } from './spectral/rfft';
export { split } from './split';
export { sqrt } from './sqrt';
export { square } from './square';
export { squaredDifference } from './squared_difference';
export { squeeze } from './squeeze';
export { stack } from './stack';
export { step } from './step';
export { stridedSlice } from './strided_slice';
export { sub } from './sub';
export { sum } from './sum';
export { tan } from './tan';
export { tanh } from './tanh';
export { tensor } from './tensor';
export { tensor1d } from './tensor1d';
export { tensor2d } from './tensor2d';
export { tensor3d } from './tensor3d';
export { tensor4d } from './tensor4d';
export { tensor5d } from './tensor5d';
export { tensor6d } from './tensor6d';
export { tensorScatterUpdate } from './tensor_scatter_update';
export { tile } from './tile';
export { topk } from './topk';
export { truncatedNormal } from './truncated_normal';
export { unique } from './unique';
export { unsortedSegmentSum } from './unsorted_segment_sum';
export { unstack } from './unstack';
export { upperBound } from './upper_bound';
export { variable } from './variable';
export { where } from './where';
export { whereAsync } from './where_async';
export { zeros } from './zeros';
export { zerosLike } from './zeros_like';
export * from './boolean_mask';
export * from './transpose';
export * from './norm';
export * from './moving_average';
export * from './scatter_nd';
export * from './search_sorted';
export * from './sparse_to_dense';
export * from './gather_nd';
export * from './dropout';
export * from './signal_ops_util';
export * from './in_top_k';
export { op, OP_SCOPE_SUFFIX } from './operation';
import { rfft } from './spectral/rfft';
import { fft } from './spectral/fft';
import { ifft } from './spectral/ifft';
import { irfft } from './spectral/irfft';
const spectral = {
    fft,
    ifft,
    rfft,
    irfft
};
import * as fused from './fused_ops';
import { hammingWindow } from './signal/hamming_window';
import { hannWindow } from './signal/hann_window';
import { frame } from './signal/frame';
import { stft } from './signal/stft';
const signal = {
    hammingWindow,
    hannWindow,
    frame,
    stft,
};
// Image Ops namespace
import { cropAndResize } from './image/crop_and_resize';
import { flipLeftRight } from './image/flip_left_right';
import { grayscaleToRGB } from './image/grayscale_to_rgb';
import { rgbToGrayscale } from './image/rgb_to_grayscale';
import { rotateWithOffset } from './image/rotate_with_offset';
import { nonMaxSuppression } from './image/non_max_suppression';
import { nonMaxSuppressionAsync } from './image/non_max_suppression_async';
import { nonMaxSuppressionWithScore } from './image/non_max_suppression_with_score';
import { nonMaxSuppressionWithScoreAsync } from './image/non_max_suppression_with_score_async';
import { nonMaxSuppressionPadded } from './image/non_max_suppression_padded';
import { nonMaxSuppressionPaddedAsync } from './image/non_max_suppression_padded_async';
import { resizeBilinear } from './image/resize_bilinear';
import { resizeNearestNeighbor } from './image/resize_nearest_neighbor';
import { threshold } from './image/threshold';
import { transform } from './image/transform';
const image = {
    flipLeftRight,
    grayscaleToRGB,
    resizeNearestNeighbor,
    resizeBilinear,
    rgbToGrayscale,
    rotateWithOffset,
    cropAndResize,
    nonMaxSuppression,
    nonMaxSuppressionAsync,
    nonMaxSuppressionWithScore,
    nonMaxSuppressionWithScoreAsync,
    nonMaxSuppressionPadded,
    nonMaxSuppressionPaddedAsync,
    threshold,
    transform
};
// linalg namespace
import { bandPart } from './linalg/band_part';
import { gramSchmidt } from './linalg/gram_schmidt';
import { qr } from './linalg/qr';
const linalg = {
    bandPart,
    gramSchmidt,
    qr
};
// losses namespace;
import { absoluteDifference } from './losses/absolute_difference';
import { computeWeightedLoss } from './losses/compute_weighted_loss';
import { cosineDistance } from './losses/cosine_distance';
import { hingeLoss } from './losses/hinge_loss';
import { huberLoss } from './losses/huber_loss';
import { logLoss } from './losses/log_loss';
import { meanSquaredError } from './losses/mean_squared_error';
import { sigmoidCrossEntropy } from './losses/sigmoid_cross_entropy';
import { softmaxCrossEntropy } from './losses/softmax_cross_entropy';
const losses = {
    absoluteDifference,
    computeWeightedLoss,
    cosineDistance,
    hingeLoss,
    huberLoss,
    logLoss,
    meanSquaredError,
    sigmoidCrossEntropy,
    softmaxCrossEntropy
};
import { sparseFillEmptyRows } from './sparse/sparse_fill_empty_rows';
import { sparseReshape } from './sparse/sparse_reshape';
import { sparseSegmentMean } from './sparse/sparse_segment_mean';
import { sparseSegmentSum } from './sparse/sparse_segment_sum';
const sparse = {
    sparseFillEmptyRows,
    sparseReshape,
    sparseSegmentMean,
    sparseSegmentSum
};
import { stringNGrams } from './string/string_n_grams';
import { stringSplit } from './string/string_split';
import { stringToHashBucketFast } from './string/string_to_hash_bucket_fast';
import { staticRegexReplace } from './string/static_regex_replace';
// tslint:disable-next-line:variable-name
const string = {
    stringNGrams,
    stringSplit,
    stringToHashBucketFast,
    staticRegexReplace,
};
// Second level exports.
export { image, linalg, losses, spectral, fused, signal, sparse, string };
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoib3BzLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vLi4vdGZqcy1jb3JlL3NyYy9vcHMvb3BzLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7Ozs7Ozs7Ozs7R0FlRztBQUVILG1CQUFtQjtBQUNuQixPQUFPLEVBQUMsR0FBRyxFQUFDLE1BQU0sT0FBTyxDQUFDO0FBQzFCLE9BQU8sRUFBQyxJQUFJLEVBQUMsTUFBTSxRQUFRLENBQUM7QUFDNUIsT0FBTyxFQUFDLEtBQUssRUFBQyxNQUFNLFNBQVMsQ0FBQztBQUM5QixPQUFPLEVBQUMsR0FBRyxFQUFDLE1BQU0sT0FBTyxDQUFDO0FBQzFCLE9BQU8sRUFBQyxJQUFJLEVBQUMsTUFBTSxTQUFTLENBQUM7QUFDN0IsT0FBTyxFQUFDLEdBQUcsRUFBQyxNQUFNLE9BQU8sQ0FBQztBQUMxQixPQUFPLEVBQUMsR0FBRyxFQUFDLE1BQU0sT0FBTyxDQUFDO0FBQzFCLE9BQU8sRUFBQyxNQUFNLEVBQUMsTUFBTSxXQUFXLENBQUM7QUFDakMsT0FBTyxFQUFDLE1BQU0sRUFBQyxNQUFNLFdBQVcsQ0FBQztBQUNqQyxPQUFPLEVBQUMsSUFBSSxFQUFDLE1BQU0sUUFBUSxDQUFDO0FBQzVCLE9BQU8sRUFBQyxLQUFLLEVBQUMsTUFBTSxTQUFTLENBQUM7QUFDOUIsT0FBTyxFQUFDLElBQUksRUFBQyxNQUFNLFFBQVEsQ0FBQztBQUM1QixPQUFPLEVBQUMsS0FBSyxFQUFDLE1BQU0sU0FBUyxDQUFDO0FBQzlCLE9BQU8sRUFBQyxLQUFLLEVBQUMsTUFBTSxTQUFTLENBQUM7QUFDOUIsT0FBTyxFQUFDLE9BQU8sRUFBQyxNQUFNLFlBQVksQ0FBQztBQUNuQyxPQUFPLEVBQUMsU0FBUyxFQUFDLE1BQU0sZUFBZSxDQUFDO0FBQ3hDLE9BQU8sRUFBQyxhQUFhLEVBQUMsTUFBTSxtQkFBbUIsQ0FBQztBQUNoRCxPQUFPLEVBQUMsY0FBYyxFQUFDLE1BQU0scUJBQXFCLENBQUM7QUFDbkQsT0FBTyxFQUFDLFNBQVMsRUFBQyxNQUFNLGFBQWEsQ0FBQztBQUN0QyxPQUFPLEVBQUMsV0FBVyxFQUFDLE1BQU0sZUFBZSxDQUFDO0FBQzFDLE9BQU8sRUFBQyxXQUFXLEVBQUMsTUFBTSxlQUFlLENBQUM7QUFDMUMsT0FBTyxFQUFDLFdBQVcsRUFBQyxNQUFNLGVBQWUsQ0FBQztBQUMxQyxPQUFPLEVBQUMsUUFBUSxFQUFDLE1BQU0sWUFBWSxDQUFDO0FBQ3BDLE9BQU8sRUFBQyxVQUFVLEVBQUMsTUFBTSxlQUFlLENBQUM7QUFDekMsT0FBTyxFQUFDLGFBQWEsRUFBQyxNQUFNLGtCQUFrQixDQUFDO0FBQy9DLE9BQU8sRUFBQyxXQUFXLEVBQUMsTUFBTSxnQkFBZ0IsQ0FBQztBQUMzQyxPQUFPLEVBQUMsTUFBTSxFQUFDLE1BQU0sVUFBVSxDQUFDO0FBQ2hDLE9BQU8sRUFBQyxJQUFJLEVBQUMsTUFBTSxRQUFRLENBQUM7QUFDNUIsT0FBTyxFQUFDLElBQUksRUFBQyxNQUFNLFFBQVEsQ0FBQztBQUM1QixPQUFPLEVBQUMsV0FBVyxFQUFDLE1BQU0saUJBQWlCLENBQUM7QUFDNUMsT0FBTyxFQUFDLEtBQUssRUFBQyxNQUFNLFNBQVMsQ0FBQztBQUM5QixPQUFPLEVBQUMsT0FBTyxFQUFDLE1BQU0sV0FBVyxDQUFDO0FBQ2xDLE9BQU8sRUFBQyxNQUFNLEVBQUMsTUFBTSxVQUFVLENBQUM7QUFDaEMsT0FBTyxFQUFDLFFBQVEsRUFBQyxNQUFNLGFBQWEsQ0FBQztBQUNyQyxPQUFPLEVBQUMsUUFBUSxFQUFDLE1BQU0sYUFBYSxDQUFDO0FBQ3JDLE9BQU8sRUFBQyxRQUFRLEVBQUMsTUFBTSxhQUFhLENBQUM7QUFDckMsT0FBTyxFQUFDLFFBQVEsRUFBQyxNQUFNLGFBQWEsQ0FBQztBQUNyQyxPQUFPLEVBQUMsTUFBTSxFQUFDLE1BQU0sVUFBVSxDQUFDO0FBQ2hDLE9BQU8sRUFBQyxNQUFNLEVBQUMsTUFBTSxVQUFVLENBQUM7QUFDaEMsT0FBTyxFQUFDLGVBQWUsRUFBQyxNQUFNLG9CQUFvQixDQUFDO0FBQ25ELE9BQU8sRUFBQyxNQUFNLEVBQUMsTUFBTSxVQUFVLENBQUM7QUFDaEMsT0FBTyxFQUFDLGVBQWUsRUFBQyxNQUFNLG9CQUFvQixDQUFDO0FBQ25ELE9BQU8sRUFBQyxHQUFHLEVBQUMsTUFBTSxPQUFPLENBQUM7QUFDMUIsT0FBTyxFQUFDLElBQUksRUFBQyxNQUFNLFFBQVEsQ0FBQztBQUM1QixPQUFPLEVBQUMsT0FBTyxFQUFDLE1BQU0sV0FBVyxDQUFDO0FBQ2xDLE9BQU8sRUFBQyxNQUFNLEVBQUMsTUFBTSxVQUFVLENBQUM7QUFDaEMsT0FBTyxFQUFDLGFBQWEsRUFBQyxNQUFNLGtCQUFrQixDQUFDO0FBQy9DLE9BQU8sRUFBQyxZQUFZLEVBQUMsTUFBTSxrQkFBa0IsQ0FBQztBQUM5QyxPQUFPLEVBQUMsZUFBZSxFQUFDLE1BQU0sb0JBQW9CLENBQUM7QUFDbkQsT0FBTyxFQUFDLElBQUksRUFBQyxNQUFNLFFBQVEsQ0FBQztBQUM1QixPQUFPLEVBQUMsVUFBVSxFQUFDLE1BQU0sY0FBYyxDQUFDO0FBQ3hDLE9BQU8sRUFBQyxHQUFHLEVBQUMsTUFBTSxPQUFPLENBQUM7QUFDMUIsT0FBTyxFQUFDLFFBQVEsRUFBQyxNQUFNLGNBQWMsQ0FBQztBQUN0QyxPQUFPLEVBQUMsR0FBRyxFQUFDLE1BQU0sT0FBTyxDQUFDO0FBQzFCLE9BQU8sRUFBQyxNQUFNLEVBQUMsTUFBTSxVQUFVLENBQUM7QUFDaEMsT0FBTyxFQUFDLEdBQUcsRUFBQyxNQUFNLE9BQU8sQ0FBQztBQUMxQixPQUFPLEVBQUMsV0FBVyxFQUFDLE1BQU0sZ0JBQWdCLENBQUM7QUFDM0MsT0FBTyxFQUFDLEtBQUssRUFBQyxNQUFNLFNBQVMsQ0FBQztBQUM5QixPQUFPLEVBQUMsR0FBRyxFQUFDLE1BQU0sT0FBTyxDQUFDO0FBQzFCLE9BQU8sRUFBQyxhQUFhLEVBQUMsTUFBTSxrQkFBa0IsQ0FBQztBQUMvQyxPQUFPLEVBQUMsR0FBRyxFQUFDLE1BQU0sT0FBTyxDQUFDO0FBQzFCLE9BQU8sRUFBQyxVQUFVLEVBQUMsTUFBTSxlQUFlLENBQUM7QUFDekMsT0FBTyxFQUFDLEtBQUssRUFBQyxNQUFNLFNBQVMsQ0FBQztBQUM5QixPQUFPLEVBQUMsR0FBRyxFQUFDLE1BQU0sT0FBTyxDQUFDO0FBQzFCLE9BQU8sRUFBQyxJQUFJLEVBQUMsTUFBTSxRQUFRLENBQUM7QUFDNUIsT0FBTyxFQUFDLEtBQUssRUFBQyxNQUFNLFNBQVMsQ0FBQztBQUM5QixPQUFPLEVBQUMsUUFBUSxFQUFDLE1BQU0sWUFBWSxDQUFDO0FBQ3BDLE9BQU8sRUFBQyxNQUFNLEVBQUMsTUFBTSxVQUFVLENBQUM7QUFDaEMsT0FBTyxFQUFDLE9BQU8sRUFBQyxNQUFNLFdBQVcsQ0FBQztBQUNsQyxPQUFPLEVBQUMsWUFBWSxFQUFDLE1BQU0saUJBQWlCLENBQUM7QUFDN0MsT0FBTyxFQUFDLElBQUksRUFBQyxNQUFNLFFBQVEsQ0FBQztBQUM1QixPQUFPLEVBQUMsUUFBUSxFQUFDLE1BQU0sYUFBYSxDQUFDO0FBQ3JDLE9BQU8sRUFBQyxLQUFLLEVBQUMsTUFBTSxVQUFVLENBQUM7QUFDL0IsT0FBTyxFQUFDLEtBQUssRUFBQyxNQUFNLFVBQVUsQ0FBQztBQUMvQixPQUFPLEVBQUMsU0FBUyxFQUFDLE1BQU0sY0FBYyxDQUFDO0FBQ3ZDLE9BQU8sRUFBQyxJQUFJLEVBQUMsTUFBTSxRQUFRLENBQUM7QUFDNUIsT0FBTyxFQUFDLFNBQVMsRUFBQyxNQUFNLGNBQWMsQ0FBQztBQUN2QyxPQUFPLEVBQUMsUUFBUSxFQUFDLE1BQU0sWUFBWSxDQUFDO0FBQ3BDLE9BQU8sRUFBQywwQkFBMEIsRUFBQyxNQUFNLGdDQUFnQyxDQUFDO0FBQzFFLE9BQU8sRUFBQyxHQUFHLEVBQUMsTUFBTSxPQUFPLENBQUM7QUFDMUIsT0FBTyxFQUFDLEtBQUssRUFBQyxNQUFNLFNBQVMsQ0FBQztBQUM5QixPQUFPLEVBQUMsVUFBVSxFQUFDLE1BQU0sZUFBZSxDQUFDO0FBQ3pDLE9BQU8sRUFBQyxVQUFVLEVBQUMsTUFBTSxlQUFlLENBQUM7QUFDekMsT0FBTyxFQUFDLFNBQVMsRUFBQyxNQUFNLGVBQWUsQ0FBQztBQUN4QyxPQUFPLEVBQUMsVUFBVSxFQUFDLE1BQU0sZUFBZSxDQUFDO0FBQ3pDLE9BQU8sRUFBQyxVQUFVLEVBQUMsTUFBTSxlQUFlLENBQUM7QUFDekMsT0FBTyxFQUFDLFNBQVMsRUFBQyxNQUFNLGNBQWMsQ0FBQztBQUN2QyxPQUFPLEVBQUMsVUFBVSxFQUFDLE1BQU0sZUFBZSxDQUFDO0FBQ3pDLE9BQU8sRUFBQyxVQUFVLEVBQUMsTUFBTSxlQUFlLENBQUM7QUFDekMsT0FBTyxFQUFDLE1BQU0sRUFBQyxNQUFNLFdBQVcsQ0FBQztBQUNqQyxPQUFPLEVBQUMsR0FBRyxFQUFDLE1BQU0sT0FBTyxDQUFDO0FBQzFCLE9BQU8sRUFBQyxPQUFPLEVBQUMsTUFBTSxZQUFZLENBQUM7QUFDbkMsT0FBTyxFQUFDLFNBQVMsRUFBQyxNQUFNLGVBQWUsQ0FBQztBQUN4QyxPQUFPLEVBQUMsaUJBQWlCLEVBQUMsTUFBTSx3QkFBd0IsQ0FBQztBQUN6RCxPQUFPLEVBQUMsT0FBTyxFQUFDLE1BQU0sV0FBVyxDQUFDO0FBQ2xDLE9BQU8sRUFBQyxJQUFJLEVBQUMsTUFBTSxRQUFRLENBQUM7QUFDNUIsT0FBTyxFQUFDLFFBQVEsRUFBQyxNQUFNLFlBQVksQ0FBQztBQUNwQyxPQUFPLEVBQUMsR0FBRyxFQUFDLE1BQU0sT0FBTyxDQUFDO0FBQzFCLE9BQU8sRUFBQyxPQUFPLEVBQUMsTUFBTSxXQUFXLENBQUM7QUFDbEMsT0FBTyxFQUFDLFNBQVMsRUFBQyxNQUFNLGNBQWMsQ0FBQztBQUN2QyxPQUFPLEVBQUMsR0FBRyxFQUFDLE1BQU0sT0FBTyxDQUFDO0FBQzFCLE9BQU8sRUFBQyxPQUFPLEVBQUMsTUFBTSxXQUFXLENBQUM7QUFDbEMsT0FBTyxFQUFDLEdBQUcsRUFBQyxNQUFNLE9BQU8sQ0FBQztBQUMxQixPQUFPLEVBQWUsWUFBWSxFQUFDLE1BQU0sa0JBQWtCLENBQUM7QUFDNUQsT0FBTyxFQUFDLFdBQVcsRUFBQyxNQUFNLGVBQWUsQ0FBQztBQUMxQyxPQUFPLEVBQUMsR0FBRyxFQUFDLE1BQU0sT0FBTyxDQUFDO0FBQzFCLE9BQU8sRUFBQyxRQUFRLEVBQUMsTUFBTSxhQUFhLENBQUM7QUFDckMsT0FBTyxFQUFDLE1BQU0sRUFBQyxNQUFNLFdBQVcsQ0FBQztBQUNqQyxPQUFPLEVBQUMsSUFBSSxFQUFDLE1BQU0sUUFBUSxDQUFDO0FBQzVCLE9BQU8sRUFBQyxRQUFRLEVBQUMsTUFBTSxhQUFhLENBQUM7QUFDckMsT0FBTyxFQUFDLFlBQVksRUFBQyxNQUFNLGlCQUFpQixDQUFDO0FBQzdDLE9BQU8sRUFBQyxHQUFHLEVBQUMsTUFBTSxPQUFPLENBQUM7QUFDMUIsT0FBTyxFQUFDLEtBQUssRUFBQyxNQUFNLFNBQVMsQ0FBQztBQUM5QixPQUFPLEVBQUMsS0FBSyxFQUFDLE1BQU0sU0FBUyxDQUFDO0FBQzlCLE9BQU8sRUFBQyxLQUFLLEVBQUMsTUFBTSxTQUFTLENBQUM7QUFDOUIsT0FBTyxFQUFDLEtBQUssRUFBQyxNQUFNLFNBQVMsQ0FBQztBQUM5QixPQUFPLEVBQUMsSUFBSSxFQUFDLE1BQU0sUUFBUSxDQUFDO0FBQzVCLE9BQU8sRUFBQyxHQUFHLEVBQUMsTUFBTSxPQUFPLENBQUM7QUFDMUIsT0FBTyxFQUFDLEtBQUssRUFBQyxNQUFNLFNBQVMsQ0FBQztBQUM5QixPQUFPLEVBQUMsS0FBSyxFQUFDLE1BQU0sU0FBUyxDQUFDO0FBQzlCLE9BQU8sRUFBQyxJQUFJLEVBQUMsTUFBTSxRQUFRLENBQUM7QUFDNUIsT0FBTyxFQUFDLFlBQVksRUFBQyxNQUFNLGlCQUFpQixDQUFDO0FBQzdDLE9BQU8sRUFBQyxXQUFXLEVBQUMsTUFBTSxnQkFBZ0IsQ0FBQztBQUMzQyxPQUFPLEVBQUMsb0JBQW9CLEVBQUMsTUFBTSwyQkFBMkIsQ0FBQztBQUMvRCxPQUFPLEVBQUMsSUFBSSxFQUFDLE1BQU0sUUFBUSxDQUFDO0FBQzVCLE9BQU8sRUFBQyxXQUFXLEVBQUMsTUFBTSxnQkFBZ0IsQ0FBQztBQUMzQyxPQUFPLEVBQUMsWUFBWSxFQUFDLE1BQU0saUJBQWlCLENBQUM7QUFDN0MsT0FBTyxFQUFDLG9CQUFvQixFQUFDLE1BQU0sMEJBQTBCLENBQUM7QUFDOUQsT0FBTyxFQUFDLGFBQWEsRUFBQyxNQUFNLGtCQUFrQixDQUFDO0FBQy9DLE9BQU8sRUFBQyxnQkFBZ0IsRUFBQyxNQUFNLHNCQUFzQixDQUFDO0FBQ3RELE9BQU8sRUFBQyxLQUFLLEVBQUMsTUFBTSxTQUFTLENBQUM7QUFDOUIsT0FBTyxFQUFDLElBQUksRUFBQyxNQUFNLFFBQVEsQ0FBQztBQUM1QixPQUFPLEVBQUMsVUFBVSxFQUFDLE1BQU0sY0FBYyxDQUFDO0FBQ3hDLE9BQU8sRUFBQyxJQUFJLEVBQUMsTUFBTSxRQUFRLENBQUM7QUFDNUIsT0FBTyxFQUFDLEtBQUssRUFBQyxNQUFNLFNBQVMsQ0FBQztBQUM5QixPQUFPLEVBQUMsT0FBTyxFQUFDLE1BQU0sV0FBVyxDQUFDO0FBQ2xDLE9BQU8sRUFBQyxPQUFPLEVBQUMsTUFBTSxXQUFXLENBQUM7QUFDbEMsT0FBTyxFQUFDLFNBQVMsRUFBQyxNQUFNLGNBQWMsQ0FBQztBQUN2QyxPQUFPLEVBQUMsU0FBUyxFQUFDLE1BQU0sY0FBYyxDQUFDO0FBQ3ZDLE9BQU8sRUFBQyxTQUFTLEVBQUMsTUFBTSxjQUFjLENBQUM7QUFDdkMsT0FBTyxFQUFDLFNBQVMsRUFBQyxNQUFNLGNBQWMsQ0FBQztBQUN2QyxPQUFPLEVBQUMsS0FBSyxFQUFDLE1BQU0sU0FBUyxDQUFDO0FBQzlCLE9BQU8sRUFBQyxLQUFLLEVBQUMsTUFBTSxTQUFTLENBQUM7QUFDOUIsT0FBTyxFQUFDLE1BQU0sRUFBQyxNQUFNLFVBQVUsQ0FBQztBQUNoQyxPQUFPLEVBQUMsSUFBSSxFQUFDLE1BQU0sUUFBUSxDQUFDO0FBQzVCLE9BQU8sRUFBQyxlQUFlLEVBQUMsTUFBTSxvQkFBb0IsQ0FBQztBQUNuRCxPQUFPLEVBQUMsY0FBYyxFQUFDLE1BQU0sbUJBQW1CLENBQUM7QUFDakQsT0FBTyxFQUFDLE9BQU8sRUFBQyxNQUFNLFdBQVcsQ0FBQztBQUNsQyxPQUFPLEVBQUMsSUFBSSxFQUFDLE1BQU0sUUFBUSxDQUFDO0FBQzVCLE9BQU8sRUFBQyxHQUFHLEVBQUMsTUFBTSxPQUFPLENBQUM7QUFDMUIsT0FBTyxFQUFDLElBQUksRUFBQyxNQUFNLFFBQVEsQ0FBQztBQUM1QixPQUFPLEVBQUMsS0FBSyxFQUFDLE1BQU0sU0FBUyxDQUFDO0FBQzlCLE9BQU8sRUFBQyxPQUFPLEVBQUMsTUFBTSxXQUFXLENBQUM7QUFDbEMsT0FBTyxFQUFDLE9BQU8sRUFBQyxNQUFNLFdBQVcsQ0FBQztBQUNsQyxPQUFPLEVBQUMsT0FBTyxFQUFDLE1BQU0sV0FBVyxDQUFDO0FBQ2xDLE9BQU8sRUFBQyxPQUFPLEVBQUMsTUFBTSxXQUFXLENBQUM7QUFDbEMsT0FBTyxFQUFDLE9BQU8sRUFBQyxNQUFNLFdBQVcsQ0FBQztBQUNsQyxPQUFPLEVBQUMsUUFBUSxFQUFDLE1BQU0sWUFBWSxDQUFDO0FBQ3BDLE9BQU8sRUFBQyxjQUFjLEVBQUMsTUFBTSxxQkFBcUIsQ0FBQztBQUNuRCxPQUFPLEVBQUMsR0FBRyxFQUFDLE1BQU0sZ0JBQWdCLENBQUM7QUFDbkMsT0FBTyxFQUFDLElBQUksRUFBQyxNQUFNLGlCQUFpQixDQUFDO0FBQ3JDLE9BQU8sRUFBQyxLQUFLLEVBQUMsTUFBTSxrQkFBa0IsQ0FBQztBQUN2QyxPQUFPLEVBQUMsSUFBSSxFQUFDLE1BQU0saUJBQWlCLENBQUM7QUFDckMsT0FBTyxFQUFDLEtBQUssRUFBQyxNQUFNLFNBQVMsQ0FBQztBQUM5QixPQUFPLEVBQUMsSUFBSSxFQUFDLE1BQU0sUUFBUSxDQUFDO0FBQzVCLE9BQU8sRUFBQyxNQUFNLEVBQUMsTUFBTSxVQUFVLENBQUM7QUFDaEMsT0FBTyxFQUFDLGlCQUFpQixFQUFDLE1BQU0sc0JBQXNCLENBQUM7QUFDdkQsT0FBTyxFQUFDLE9BQU8sRUFBQyxNQUFNLFdBQVcsQ0FBQztBQUNsQyxPQUFPLEVBQUMsS0FBSyxFQUFDLE1BQU0sU0FBUyxDQUFDO0FBQzlCLE9BQU8sRUFBQyxJQUFJLEVBQUMsTUFBTSxRQUFRLENBQUM7QUFDNUIsT0FBTyxFQUFDLFlBQVksRUFBQyxNQUFNLGlCQUFpQixDQUFDO0FBQzdDLE9BQU8sRUFBQyxHQUFHLEVBQUMsTUFBTSxPQUFPLENBQUM7QUFDMUIsT0FBTyxFQUFDLEdBQUcsRUFBQyxNQUFNLE9BQU8sQ0FBQztBQUMxQixPQUFPLEVBQUMsR0FBRyxFQUFDLE1BQU0sT0FBTyxDQUFDO0FBQzFCLE9BQU8sRUFBQyxJQUFJLEVBQUMsTUFBTSxRQUFRLENBQUM7QUFDNUIsT0FBTyxFQUFDLE1BQU0sRUFBQyxNQUFNLFVBQVUsQ0FBQztBQUNoQyxPQUFPLEVBQUMsUUFBUSxFQUFDLE1BQU0sWUFBWSxDQUFDO0FBQ3BDLE9BQU8sRUFBQyxRQUFRLEVBQUMsTUFBTSxZQUFZLENBQUM7QUFDcEMsT0FBTyxFQUFDLFFBQVEsRUFBQyxNQUFNLFlBQVksQ0FBQztBQUNwQyxPQUFPLEVBQUMsUUFBUSxFQUFDLE1BQU0sWUFBWSxDQUFDO0FBQ3BDLE9BQU8sRUFBQyxRQUFRLEVBQUMsTUFBTSxZQUFZLENBQUM7QUFDcEMsT0FBTyxFQUFDLFFBQVEsRUFBQyxNQUFNLFlBQVksQ0FBQztBQUNwQyxPQUFPLEVBQUMsbUJBQW1CLEVBQUMsTUFBTSx5QkFBeUIsQ0FBQztBQUM1RCxPQUFPLEVBQUMsSUFBSSxFQUFDLE1BQU0sUUFBUSxDQUFDO0FBQzVCLE9BQU8sRUFBQyxJQUFJLEVBQUMsTUFBTSxRQUFRLENBQUM7QUFDNUIsT0FBTyxFQUFDLGVBQWUsRUFBQyxNQUFNLG9CQUFvQixDQUFDO0FBQ25ELE9BQU8sRUFBQyxNQUFNLEVBQUMsTUFBTSxVQUFVLENBQUM7QUFDaEMsT0FBTyxFQUFDLGtCQUFrQixFQUFDLE1BQU0sd0JBQXdCLENBQUM7QUFDMUQsT0FBTyxFQUFDLE9BQU8sRUFBQyxNQUFNLFdBQVcsQ0FBQztBQUNsQyxPQUFPLEVBQUMsVUFBVSxFQUFDLE1BQU0sZUFBZSxDQUFDO0FBQ3pDLE9BQU8sRUFBQyxRQUFRLEVBQUMsTUFBTSxZQUFZLENBQUM7QUFDcEMsT0FBTyxFQUFDLEtBQUssRUFBQyxNQUFNLFNBQVMsQ0FBQztBQUM5QixPQUFPLEVBQUMsVUFBVSxFQUFDLE1BQU0sZUFBZSxDQUFDO0FBQ3pDLE9BQU8sRUFBQyxLQUFLLEVBQUMsTUFBTSxTQUFTLENBQUM7QUFDOUIsT0FBTyxFQUFDLFNBQVMsRUFBQyxNQUFNLGNBQWMsQ0FBQztBQUV2QyxjQUFjLGdCQUFnQixDQUFDO0FBQy9CLGNBQWMsYUFBYSxDQUFDO0FBQzVCLGNBQWMsUUFBUSxDQUFDO0FBQ3ZCLGNBQWMsa0JBQWtCLENBQUM7QUFDakMsY0FBYyxjQUFjLENBQUM7QUFDN0IsY0FBYyxpQkFBaUIsQ0FBQztBQUNoQyxjQUFjLG1CQUFtQixDQUFDO0FBQ2xDLGNBQWMsYUFBYSxDQUFDO0FBQzVCLGNBQWMsV0FBVyxDQUFDO0FBQzFCLGNBQWMsbUJBQW1CLENBQUM7QUFDbEMsY0FBYyxZQUFZLENBQUM7QUFFM0IsT0FBTyxFQUFDLEVBQUUsRUFBRSxlQUFlLEVBQUMsTUFBTSxhQUFhLENBQUM7QUFFaEQsT0FBTyxFQUFDLElBQUksRUFBQyxNQUFNLGlCQUFpQixDQUFDO0FBQ3JDLE9BQU8sRUFBQyxHQUFHLEVBQUMsTUFBTSxnQkFBZ0IsQ0FBQztBQUNuQyxPQUFPLEVBQUMsSUFBSSxFQUFDLE1BQU0saUJBQWlCLENBQUM7QUFDckMsT0FBTyxFQUFDLEtBQUssRUFBQyxNQUFNLGtCQUFrQixDQUFDO0FBQ3ZDLE1BQU0sUUFBUSxHQUFHO0lBQ2YsR0FBRztJQUNILElBQUk7SUFDSixJQUFJO0lBQ0osS0FBSztDQUNOLENBQUM7QUFFRixPQUFPLEtBQUssS0FBSyxNQUFNLGFBQWEsQ0FBQztBQUVyQyxPQUFPLEVBQUMsYUFBYSxFQUFDLE1BQU0seUJBQXlCLENBQUM7QUFDdEQsT0FBTyxFQUFDLFVBQVUsRUFBQyxNQUFNLHNCQUFzQixDQUFDO0FBQ2hELE9BQU8sRUFBQyxLQUFLLEVBQUMsTUFBTSxnQkFBZ0IsQ0FBQztBQUNyQyxPQUFPLEVBQUMsSUFBSSxFQUFDLE1BQU0sZUFBZSxDQUFDO0FBQ25DLE1BQU0sTUFBTSxHQUFHO0lBQ2IsYUFBYTtJQUNiLFVBQVU7SUFDVixLQUFLO0lBQ0wsSUFBSTtDQUNMLENBQUM7QUFFRixzQkFBc0I7QUFDdEIsT0FBTyxFQUFDLGFBQWEsRUFBQyxNQUFNLHlCQUF5QixDQUFDO0FBQ3RELE9BQU8sRUFBQyxhQUFhLEVBQUMsTUFBTSx5QkFBeUIsQ0FBQztBQUN0RCxPQUFPLEVBQUMsY0FBYyxFQUFDLE1BQU0sMEJBQTBCLENBQUM7QUFDeEQsT0FBTyxFQUFDLGNBQWMsRUFBQyxNQUFNLDBCQUEwQixDQUFDO0FBQ3hELE9BQU8sRUFBQyxnQkFBZ0IsRUFBQyxNQUFNLDRCQUE0QixDQUFDO0FBQzVELE9BQU8sRUFBQyxpQkFBaUIsRUFBQyxNQUFNLDZCQUE2QixDQUFDO0FBQzlELE9BQU8sRUFBQyxzQkFBc0IsRUFBQyxNQUFNLG1DQUFtQyxDQUFDO0FBQ3pFLE9BQU8sRUFBQywwQkFBMEIsRUFBQyxNQUFNLHdDQUF3QyxDQUFDO0FBQ2xGLE9BQU8sRUFBQywrQkFBK0IsRUFBQyxNQUFNLDhDQUE4QyxDQUFDO0FBQzdGLE9BQU8sRUFBQyx1QkFBdUIsRUFBQyxNQUFNLG9DQUFvQyxDQUFDO0FBQzNFLE9BQU8sRUFBQyw0QkFBNEIsRUFBQyxNQUFNLDBDQUEwQyxDQUFDO0FBQ3RGLE9BQU8sRUFBQyxjQUFjLEVBQUMsTUFBTSx5QkFBeUIsQ0FBQztBQUN2RCxPQUFPLEVBQUMscUJBQXFCLEVBQUMsTUFBTSxpQ0FBaUMsQ0FBQztBQUN0RSxPQUFPLEVBQUMsU0FBUyxFQUFDLE1BQU0sbUJBQW1CLENBQUM7QUFDNUMsT0FBTyxFQUFDLFNBQVMsRUFBQyxNQUFNLG1CQUFtQixDQUFDO0FBQzVDLE1BQU0sS0FBSyxHQUFHO0lBQ1osYUFBYTtJQUNiLGNBQWM7SUFDZCxxQkFBcUI7SUFDckIsY0FBYztJQUNkLGNBQWM7SUFDZCxnQkFBZ0I7SUFDaEIsYUFBYTtJQUNiLGlCQUFpQjtJQUNqQixzQkFBc0I7SUFDdEIsMEJBQTBCO0lBQzFCLCtCQUErQjtJQUMvQix1QkFBdUI7SUFDdkIsNEJBQTRCO0lBQzVCLFNBQVM7SUFDVCxTQUFTO0NBQ1YsQ0FBQztBQUVGLG1CQUFtQjtBQUNuQixPQUFPLEVBQUMsUUFBUSxFQUFDLE1BQU0sb0JBQW9CLENBQUM7QUFDNUMsT0FBTyxFQUFDLFdBQVcsRUFBQyxNQUFNLHVCQUF1QixDQUFDO0FBQ2xELE9BQU8sRUFBQyxFQUFFLEVBQUMsTUFBTSxhQUFhLENBQUM7QUFDL0IsTUFBTSxNQUFNLEdBQUc7SUFDYixRQUFRO0lBQ1IsV0FBVztJQUNYLEVBQUU7Q0FDSCxDQUFDO0FBRUYsb0JBQW9CO0FBQ3BCLE9BQU8sRUFBQyxrQkFBa0IsRUFBQyxNQUFNLDhCQUE4QixDQUFDO0FBQ2hFLE9BQU8sRUFBQyxtQkFBbUIsRUFBQyxNQUFNLGdDQUFnQyxDQUFDO0FBQ25FLE9BQU8sRUFBQyxjQUFjLEVBQUMsTUFBTSwwQkFBMEIsQ0FBQztBQUN4RCxPQUFPLEVBQUMsU0FBUyxFQUFDLE1BQU0scUJBQXFCLENBQUM7QUFDOUMsT0FBTyxFQUFDLFNBQVMsRUFBQyxNQUFNLHFCQUFxQixDQUFDO0FBQzlDLE9BQU8sRUFBQyxPQUFPLEVBQUMsTUFBTSxtQkFBbUIsQ0FBQztBQUMxQyxPQUFPLEVBQUMsZ0JBQWdCLEVBQUMsTUFBTSw2QkFBNkIsQ0FBQztBQUM3RCxPQUFPLEVBQUMsbUJBQW1CLEVBQUMsTUFBTSxnQ0FBZ0MsQ0FBQztBQUNuRSxPQUFPLEVBQUMsbUJBQW1CLEVBQUMsTUFBTSxnQ0FBZ0MsQ0FBQztBQUNuRSxNQUFNLE1BQU0sR0FBRztJQUNiLGtCQUFrQjtJQUNsQixtQkFBbUI7SUFDbkIsY0FBYztJQUNkLFNBQVM7SUFDVCxTQUFTO0lBQ1QsT0FBTztJQUNQLGdCQUFnQjtJQUNoQixtQkFBbUI7SUFDbkIsbUJBQW1CO0NBQ3BCLENBQUM7QUFFRixPQUFPLEVBQUMsbUJBQW1CLEVBQUMsTUFBTSxpQ0FBaUMsQ0FBQztBQUNwRSxPQUFPLEVBQUMsYUFBYSxFQUFDLE1BQU0seUJBQXlCLENBQUM7QUFDdEQsT0FBTyxFQUFDLGlCQUFpQixFQUFDLE1BQU0sOEJBQThCLENBQUM7QUFDL0QsT0FBTyxFQUFDLGdCQUFnQixFQUFDLE1BQU0sNkJBQTZCLENBQUM7QUFDN0QsTUFBTSxNQUFNLEdBQUc7SUFDYixtQkFBbUI7SUFDbkIsYUFBYTtJQUNiLGlCQUFpQjtJQUNqQixnQkFBZ0I7Q0FDakIsQ0FBQztBQUVGLE9BQU8sRUFBQyxZQUFZLEVBQUMsTUFBTSx5QkFBeUIsQ0FBQztBQUNyRCxPQUFPLEVBQUMsV0FBVyxFQUFDLE1BQU0sdUJBQXVCLENBQUM7QUFDbEQsT0FBTyxFQUFDLHNCQUFzQixFQUFDLE1BQU0scUNBQXFDLENBQUM7QUFDM0UsT0FBTyxFQUFDLGtCQUFrQixFQUFDLE1BQU0sK0JBQStCLENBQUM7QUFDakUseUNBQXlDO0FBQ3pDLE1BQU0sTUFBTSxHQUFHO0lBQ2IsWUFBWTtJQUNaLFdBQVc7SUFDWCxzQkFBc0I7SUFDdEIsa0JBQWtCO0NBQ25CLENBQUM7QUFFRix3QkFBd0I7QUFDeEIsT0FBTyxFQUFDLEtBQUssRUFBRSxNQUFNLEVBQUUsTUFBTSxFQUFFLFFBQVEsRUFBRSxLQUFLLEVBQUUsTUFBTSxFQUFFLE1BQU0sRUFBRSxNQUFNLEVBQUMsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCAyMDIwIEdvb2dsZSBMTEMuIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogaHR0cDovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gKi9cblxuLy8gTW9kdWxhcml6ZWQgb3BzLlxuZXhwb3J0IHthYnN9IGZyb20gJy4vYWJzJztcbmV4cG9ydCB7YWNvc30gZnJvbSAnLi9hY29zJztcbmV4cG9ydCB7YWNvc2h9IGZyb20gJy4vYWNvc2gnO1xuZXhwb3J0IHthZGR9IGZyb20gJy4vYWRkJztcbmV4cG9ydCB7YWRkTn0gZnJvbSAnLi9hZGRfbic7XG5leHBvcnQge2FsbH0gZnJvbSAnLi9hbGwnO1xuZXhwb3J0IHthbnl9IGZyb20gJy4vYW55JztcbmV4cG9ydCB7YXJnTWF4fSBmcm9tICcuL2FyZ19tYXgnO1xuZXhwb3J0IHthcmdNaW59IGZyb20gJy4vYXJnX21pbic7XG5leHBvcnQge2FzaW59IGZyb20gJy4vYXNpbic7XG5leHBvcnQge2FzaW5ofSBmcm9tICcuL2FzaW5oJztcbmV4cG9ydCB7YXRhbn0gZnJvbSAnLi9hdGFuJztcbmV4cG9ydCB7YXRhbjJ9IGZyb20gJy4vYXRhbjInO1xuZXhwb3J0IHthdGFuaH0gZnJvbSAnLi9hdGFuaCc7XG5leHBvcnQge2F2Z1Bvb2x9IGZyb20gJy4vYXZnX3Bvb2wnO1xuZXhwb3J0IHthdmdQb29sM2R9IGZyb20gJy4vYXZnX3Bvb2xfM2QnO1xuZXhwb3J0IHtiYXNpY0xTVE1DZWxsfSBmcm9tICcuL2Jhc2ljX2xzdG1fY2VsbCc7XG5leHBvcnQge2JhdGNoVG9TcGFjZU5EfSBmcm9tICcuL2JhdGNoX3RvX3NwYWNlX25kJztcbmV4cG9ydCB7YmF0Y2hOb3JtfSBmcm9tICcuL2JhdGNobm9ybSc7XG5leHBvcnQge2JhdGNoTm9ybTJkfSBmcm9tICcuL2JhdGNobm9ybTJkJztcbmV4cG9ydCB7YmF0Y2hOb3JtM2R9IGZyb20gJy4vYmF0Y2hub3JtM2QnO1xuZXhwb3J0IHtiYXRjaE5vcm00ZH0gZnJvbSAnLi9iYXRjaG5vcm00ZCc7XG5leHBvcnQge2JpbmNvdW50fSBmcm9tICcuL2JpbmNvdW50JztcbmV4cG9ydCB7Yml0d2lzZUFuZH0gZnJvbSAnLi9iaXR3aXNlX2FuZCc7XG5leHBvcnQge2Jyb2FkY2FzdEFyZ3N9IGZyb20gJy4vYnJvYWRjYXN0X2FyZ3MnO1xuZXhwb3J0IHticm9hZGNhc3RUb30gZnJvbSAnLi9icm9hZGNhc3RfdG8nO1xuZXhwb3J0IHtidWZmZXJ9IGZyb20gJy4vYnVmZmVyJztcbmV4cG9ydCB7Y2FzdH0gZnJvbSAnLi9jYXN0JztcbmV4cG9ydCB7Y2VpbH0gZnJvbSAnLi9jZWlsJztcbmV4cG9ydCB7Y2xpcEJ5VmFsdWV9IGZyb20gJy4vY2xpcF9ieV92YWx1ZSc7XG5leHBvcnQge2Nsb25lfSBmcm9tICcuL2Nsb25lJztcbmV4cG9ydCB7Y29tcGxleH0gZnJvbSAnLi9jb21wbGV4JztcbmV4cG9ydCB7Y29uY2F0fSBmcm9tICcuL2NvbmNhdCc7XG5leHBvcnQge2NvbmNhdDFkfSBmcm9tICcuL2NvbmNhdF8xZCc7XG5leHBvcnQge2NvbmNhdDJkfSBmcm9tICcuL2NvbmNhdF8yZCc7XG5leHBvcnQge2NvbmNhdDNkfSBmcm9tICcuL2NvbmNhdF8zZCc7XG5leHBvcnQge2NvbmNhdDRkfSBmcm9tICcuL2NvbmNhdF80ZCc7XG5leHBvcnQge2NvbnYxZH0gZnJvbSAnLi9jb252MWQnO1xuZXhwb3J0IHtjb252MmR9IGZyb20gJy4vY29udjJkJztcbmV4cG9ydCB7Y29udjJkVHJhbnNwb3NlfSBmcm9tICcuL2NvbnYyZF90cmFuc3Bvc2UnO1xuZXhwb3J0IHtjb252M2R9IGZyb20gJy4vY29udjNkJztcbmV4cG9ydCB7Y29udjNkVHJhbnNwb3NlfSBmcm9tICcuL2NvbnYzZF90cmFuc3Bvc2UnO1xuZXhwb3J0IHtjb3N9IGZyb20gJy4vY29zJztcbmV4cG9ydCB7Y29zaH0gZnJvbSAnLi9jb3NoJztcbmV4cG9ydCB7Y3VtcHJvZH0gZnJvbSAnLi9jdW1wcm9kJztcbmV4cG9ydCB7Y3Vtc3VtfSBmcm9tICcuL2N1bXN1bSc7XG5leHBvcnQge2RlbnNlQmluY291bnR9IGZyb20gJy4vZGVuc2VfYmluY291bnQnO1xuZXhwb3J0IHtkZXB0aFRvU3BhY2V9IGZyb20gJy4vZGVwdGhfdG9fc3BhY2UnO1xuZXhwb3J0IHtkZXB0aHdpc2VDb252MmR9IGZyb20gJy4vZGVwdGh3aXNlX2NvbnYyZCc7XG5leHBvcnQge2RpYWd9IGZyb20gJy4vZGlhZyc7XG5leHBvcnQge2RpbGF0aW9uMmR9IGZyb20gJy4vZGlsYXRpb24yZCc7XG5leHBvcnQge2Rpdn0gZnJvbSAnLi9kaXYnO1xuZXhwb3J0IHtkaXZOb05hbn0gZnJvbSAnLi9kaXZfbm9fbmFuJztcbmV4cG9ydCB7ZG90fSBmcm9tICcuL2RvdCc7XG5leHBvcnQge2VpbnN1bX0gZnJvbSAnLi9laW5zdW0nO1xuZXhwb3J0IHtlbHV9IGZyb20gJy4vZWx1JztcbmV4cG9ydCB7ZW5zdXJlU2hhcGV9IGZyb20gJy4vZW5zdXJlX3NoYXBlJztcbmV4cG9ydCB7ZXF1YWx9IGZyb20gJy4vZXF1YWwnO1xuZXhwb3J0IHtlcmZ9IGZyb20gJy4vZXJmJztcbmV4cG9ydCB7ZXVjbGlkZWFuTm9ybX0gZnJvbSAnLi9ldWNsaWRlYW5fbm9ybSc7XG5leHBvcnQge2V4cH0gZnJvbSAnLi9leHAnO1xuZXhwb3J0IHtleHBhbmREaW1zfSBmcm9tICcuL2V4cGFuZF9kaW1zJztcbmV4cG9ydCB7ZXhwbTF9IGZyb20gJy4vZXhwbTEnO1xuZXhwb3J0IHtleWV9IGZyb20gJy4vZXllJztcbmV4cG9ydCB7ZmlsbH0gZnJvbSAnLi9maWxsJztcbmV4cG9ydCB7Zmxvb3J9IGZyb20gJy4vZmxvb3InO1xuZXhwb3J0IHtmbG9vckRpdn0gZnJvbSAnLi9mbG9vckRpdic7XG5leHBvcnQge2dhdGhlcn0gZnJvbSAnLi9nYXRoZXInO1xuZXhwb3J0IHtncmVhdGVyfSBmcm9tICcuL2dyZWF0ZXInO1xuZXhwb3J0IHtncmVhdGVyRXF1YWx9IGZyb20gJy4vZ3JlYXRlcl9lcXVhbCc7XG5leHBvcnQge2ltYWd9IGZyb20gJy4vaW1hZyc7XG5leHBvcnQge2lzRmluaXRlfSBmcm9tICcuL2lzX2Zpbml0ZSc7XG5leHBvcnQge2lzSW5mfSBmcm9tICcuL2lzX2luZic7XG5leHBvcnQge2lzTmFOfSBmcm9tICcuL2lzX25hbic7XG5leHBvcnQge2xlYWt5UmVsdX0gZnJvbSAnLi9sZWFreV9yZWx1JztcbmV4cG9ydCB7bGVzc30gZnJvbSAnLi9sZXNzJztcbmV4cG9ydCB7bGVzc0VxdWFsfSBmcm9tICcuL2xlc3NfZXF1YWwnO1xuZXhwb3J0IHtsaW5zcGFjZX0gZnJvbSAnLi9saW5zcGFjZSc7XG5leHBvcnQge2xvY2FsUmVzcG9uc2VOb3JtYWxpemF0aW9ufSBmcm9tICcuL2xvY2FsX3Jlc3BvbnNlX25vcm1hbGl6YXRpb24nO1xuZXhwb3J0IHtsb2d9IGZyb20gJy4vbG9nJztcbmV4cG9ydCB7bG9nMXB9IGZyb20gJy4vbG9nMXAnO1xuZXhwb3J0IHtsb2dTaWdtb2lkfSBmcm9tICcuL2xvZ19zaWdtb2lkJztcbmV4cG9ydCB7bG9nU29mdG1heH0gZnJvbSAnLi9sb2dfc29mdG1heCc7XG5leHBvcnQge2xvZ1N1bUV4cH0gZnJvbSAnLi9sb2dfc3VtX2V4cCc7XG5leHBvcnQge2xvZ2ljYWxBbmR9IGZyb20gJy4vbG9naWNhbF9hbmQnO1xuZXhwb3J0IHtsb2dpY2FsTm90fSBmcm9tICcuL2xvZ2ljYWxfbm90JztcbmV4cG9ydCB7bG9naWNhbE9yfSBmcm9tICcuL2xvZ2ljYWxfb3InO1xuZXhwb3J0IHtsb2dpY2FsWG9yfSBmcm9tICcuL2xvZ2ljYWxfeG9yJztcbmV4cG9ydCB7bG93ZXJCb3VuZH0gZnJvbSAnLi9sb3dlcl9ib3VuZCc7XG5leHBvcnQge21hdE11bH0gZnJvbSAnLi9tYXRfbXVsJztcbmV4cG9ydCB7bWF4fSBmcm9tICcuL21heCc7XG5leHBvcnQge21heFBvb2x9IGZyb20gJy4vbWF4X3Bvb2wnO1xuZXhwb3J0IHttYXhQb29sM2R9IGZyb20gJy4vbWF4X3Bvb2xfM2QnO1xuZXhwb3J0IHttYXhQb29sV2l0aEFyZ21heH0gZnJvbSAnLi9tYXhfcG9vbF93aXRoX2FyZ21heCc7XG5leHBvcnQge21heGltdW19IGZyb20gJy4vbWF4aW11bSc7XG5leHBvcnQge21lYW59IGZyb20gJy4vbWVhbic7XG5leHBvcnQge21lc2hncmlkfSBmcm9tICcuL21lc2hncmlkJztcbmV4cG9ydCB7bWlufSBmcm9tICcuL21pbic7XG5leHBvcnQge21pbmltdW19IGZyb20gJy4vbWluaW11bSc7XG5leHBvcnQge21pcnJvclBhZH0gZnJvbSAnLi9taXJyb3JfcGFkJztcbmV4cG9ydCB7bW9kfSBmcm9tICcuL21vZCc7XG5leHBvcnQge21vbWVudHN9IGZyb20gJy4vbW9tZW50cyc7XG5leHBvcnQge211bH0gZnJvbSAnLi9tdWwnO1xuZXhwb3J0IHtMU1RNQ2VsbEZ1bmMsIG11bHRpUk5OQ2VsbH0gZnJvbSAnLi9tdWx0aV9ybm5fY2VsbCc7XG5leHBvcnQge211bHRpbm9taWFsfSBmcm9tICcuL211bHRpbm9taWFsJztcbmV4cG9ydCB7bmVnfSBmcm9tICcuL25lZyc7XG5leHBvcnQge25vdEVxdWFsfSBmcm9tICcuL25vdF9lcXVhbCc7XG5leHBvcnQge29uZUhvdH0gZnJvbSAnLi9vbmVfaG90JztcbmV4cG9ydCB7b25lc30gZnJvbSAnLi9vbmVzJztcbmV4cG9ydCB7b25lc0xpa2V9IGZyb20gJy4vb25lc19saWtlJztcbmV4cG9ydCB7b3V0ZXJQcm9kdWN0fSBmcm9tICcuL291dGVyX3Byb2R1Y3QnO1xuZXhwb3J0IHtwYWR9IGZyb20gJy4vcGFkJztcbmV4cG9ydCB7cGFkMWR9IGZyb20gJy4vcGFkMWQnO1xuZXhwb3J0IHtwYWQyZH0gZnJvbSAnLi9wYWQyZCc7XG5leHBvcnQge3BhZDNkfSBmcm9tICcuL3BhZDNkJztcbmV4cG9ydCB7cGFkNGR9IGZyb20gJy4vcGFkNGQnO1xuZXhwb3J0IHtwb29sfSBmcm9tICcuL3Bvb2wnO1xuZXhwb3J0IHtwb3d9IGZyb20gJy4vcG93JztcbmV4cG9ydCB7cHJlbHV9IGZyb20gJy4vcHJlbHUnO1xuZXhwb3J0IHtwcmludH0gZnJvbSAnLi9wcmludCc7XG5leHBvcnQge3Byb2R9IGZyb20gJy4vcHJvZCc7XG5leHBvcnQge3JhZ2dlZEdhdGhlcn0gZnJvbSAnLi9yYWdnZWRfZ2F0aGVyJztcbmV4cG9ydCB7cmFnZ2VkUmFuZ2V9IGZyb20gJy4vcmFnZ2VkX3JhbmdlJztcbmV4cG9ydCB7cmFnZ2VkVGVuc29yVG9UZW5zb3J9IGZyb20gJy4vcmFnZ2VkX3RlbnNvcl90b190ZW5zb3InO1xuZXhwb3J0IHtyYW5kfSBmcm9tICcuL3JhbmQnO1xuZXhwb3J0IHtyYW5kb21HYW1tYX0gZnJvbSAnLi9yYW5kb21fZ2FtbWEnO1xuZXhwb3J0IHtyYW5kb21Ob3JtYWx9IGZyb20gJy4vcmFuZG9tX25vcm1hbCc7XG5leHBvcnQge3JhbmRvbVN0YW5kYXJkTm9ybWFsfSBmcm9tICcuL3JhbmRvbV9zdGFuZGFyZF9ub3JtYWwnO1xuZXhwb3J0IHtyYW5kb21Vbmlmb3JtfSBmcm9tICcuL3JhbmRvbV91bmlmb3JtJztcbmV4cG9ydCB7cmFuZG9tVW5pZm9ybUludH0gZnJvbSAnLi9yYW5kb21fdW5pZm9ybV9pbnQnO1xuZXhwb3J0IHtyYW5nZX0gZnJvbSAnLi9yYW5nZSc7XG5leHBvcnQge3JlYWx9IGZyb20gJy4vcmVhbCc7XG5leHBvcnQge3JlY2lwcm9jYWx9IGZyb20gJy4vcmVjaXByb2NhbCc7XG5leHBvcnQge3JlbHV9IGZyb20gJy4vcmVsdSc7XG5leHBvcnQge3JlbHU2fSBmcm9tICcuL3JlbHU2JztcbmV4cG9ydCB7cmVzaGFwZX0gZnJvbSAnLi9yZXNoYXBlJztcbmV4cG9ydCB7cmV2ZXJzZX0gZnJvbSAnLi9yZXZlcnNlJztcbmV4cG9ydCB7cmV2ZXJzZTFkfSBmcm9tICcuL3JldmVyc2VfMWQnO1xuZXhwb3J0IHtyZXZlcnNlMmR9IGZyb20gJy4vcmV2ZXJzZV8yZCc7XG5leHBvcnQge3JldmVyc2UzZH0gZnJvbSAnLi9yZXZlcnNlXzNkJztcbmV4cG9ydCB7cmV2ZXJzZTRkfSBmcm9tICcuL3JldmVyc2VfNGQnO1xuZXhwb3J0IHtyb3VuZH0gZnJvbSAnLi9yb3VuZCc7XG5leHBvcnQge3JzcXJ0fSBmcm9tICcuL3JzcXJ0JztcbmV4cG9ydCB7c2NhbGFyfSBmcm9tICcuL3NjYWxhcic7XG5leHBvcnQge3NlbHV9IGZyb20gJy4vc2VsdSc7XG5leHBvcnQge3NlcGFyYWJsZUNvbnYyZH0gZnJvbSAnLi9zZXBhcmFibGVfY29udjJkJztcbmV4cG9ydCB7c2V0ZGlmZjFkQXN5bmN9IGZyb20gJy4vc2V0ZGlmZjFkX2FzeW5jJztcbmV4cG9ydCB7c2lnbW9pZH0gZnJvbSAnLi9zaWdtb2lkJztcbmV4cG9ydCB7c2lnbn0gZnJvbSAnLi9zaWduJztcbmV4cG9ydCB7c2lufSBmcm9tICcuL3Npbic7XG5leHBvcnQge3Npbmh9IGZyb20gJy4vc2luaCc7XG5leHBvcnQge3NsaWNlfSBmcm9tICcuL3NsaWNlJztcbmV4cG9ydCB7c2xpY2UxZH0gZnJvbSAnLi9zbGljZTFkJztcbmV4cG9ydCB7c2xpY2UyZH0gZnJvbSAnLi9zbGljZTJkJztcbmV4cG9ydCB7c2xpY2UzZH0gZnJvbSAnLi9zbGljZTNkJztcbmV4cG9ydCB7c2xpY2U0ZH0gZnJvbSAnLi9zbGljZTRkJztcbmV4cG9ydCB7c29mdG1heH0gZnJvbSAnLi9zb2Z0bWF4JztcbmV4cG9ydCB7c29mdHBsdXN9IGZyb20gJy4vc29mdHBsdXMnO1xuZXhwb3J0IHtzcGFjZVRvQmF0Y2hORH0gZnJvbSAnLi9zcGFjZV90b19iYXRjaF9uZCc7XG5leHBvcnQge2ZmdH0gZnJvbSAnLi9zcGVjdHJhbC9mZnQnO1xuZXhwb3J0IHtpZmZ0fSBmcm9tICcuL3NwZWN0cmFsL2lmZnQnO1xuZXhwb3J0IHtpcmZmdH0gZnJvbSAnLi9zcGVjdHJhbC9pcmZmdCc7XG5leHBvcnQge3JmZnR9IGZyb20gJy4vc3BlY3RyYWwvcmZmdCc7XG5leHBvcnQge3NwbGl0fSBmcm9tICcuL3NwbGl0JztcbmV4cG9ydCB7c3FydH0gZnJvbSAnLi9zcXJ0JztcbmV4cG9ydCB7c3F1YXJlfSBmcm9tICcuL3NxdWFyZSc7XG5leHBvcnQge3NxdWFyZWREaWZmZXJlbmNlfSBmcm9tICcuL3NxdWFyZWRfZGlmZmVyZW5jZSc7XG5leHBvcnQge3NxdWVlemV9IGZyb20gJy4vc3F1ZWV6ZSc7XG5leHBvcnQge3N0YWNrfSBmcm9tICcuL3N0YWNrJztcbmV4cG9ydCB7c3RlcH0gZnJvbSAnLi9zdGVwJztcbmV4cG9ydCB7c3RyaWRlZFNsaWNlfSBmcm9tICcuL3N0cmlkZWRfc2xpY2UnO1xuZXhwb3J0IHtzdWJ9IGZyb20gJy4vc3ViJztcbmV4cG9ydCB7c3VtfSBmcm9tICcuL3N1bSc7XG5leHBvcnQge3Rhbn0gZnJvbSAnLi90YW4nO1xuZXhwb3J0IHt0YW5ofSBmcm9tICcuL3RhbmgnO1xuZXhwb3J0IHt0ZW5zb3J9IGZyb20gJy4vdGVuc29yJztcbmV4cG9ydCB7dGVuc29yMWR9IGZyb20gJy4vdGVuc29yMWQnO1xuZXhwb3J0IHt0ZW5zb3IyZH0gZnJvbSAnLi90ZW5zb3IyZCc7XG5leHBvcnQge3RlbnNvcjNkfSBmcm9tICcuL3RlbnNvcjNkJztcbmV4cG9ydCB7dGVuc29yNGR9IGZyb20gJy4vdGVuc29yNGQnO1xuZXhwb3J0IHt0ZW5zb3I1ZH0gZnJvbSAnLi90ZW5zb3I1ZCc7XG5leHBvcnQge3RlbnNvcjZkfSBmcm9tICcuL3RlbnNvcjZkJztcbmV4cG9ydCB7dGVuc29yU2NhdHRlclVwZGF0ZX0gZnJvbSAnLi90ZW5zb3Jfc2NhdHRlcl91cGRhdGUnO1xuZXhwb3J0IHt0aWxlfSBmcm9tICcuL3RpbGUnO1xuZXhwb3J0IHt0b3BrfSBmcm9tICcuL3RvcGsnO1xuZXhwb3J0IHt0cnVuY2F0ZWROb3JtYWx9IGZyb20gJy4vdHJ1bmNhdGVkX25vcm1hbCc7XG5leHBvcnQge3VuaXF1ZX0gZnJvbSAnLi91bmlxdWUnO1xuZXhwb3J0IHt1bnNvcnRlZFNlZ21lbnRTdW19IGZyb20gJy4vdW5zb3J0ZWRfc2VnbWVudF9zdW0nO1xuZXhwb3J0IHt1bnN0YWNrfSBmcm9tICcuL3Vuc3RhY2snO1xuZXhwb3J0IHt1cHBlckJvdW5kfSBmcm9tICcuL3VwcGVyX2JvdW5kJztcbmV4cG9ydCB7dmFyaWFibGV9IGZyb20gJy4vdmFyaWFibGUnO1xuZXhwb3J0IHt3aGVyZX0gZnJvbSAnLi93aGVyZSc7XG5leHBvcnQge3doZXJlQXN5bmN9IGZyb20gJy4vd2hlcmVfYXN5bmMnO1xuZXhwb3J0IHt6ZXJvc30gZnJvbSAnLi96ZXJvcyc7XG5leHBvcnQge3plcm9zTGlrZX0gZnJvbSAnLi96ZXJvc19saWtlJztcblxuZXhwb3J0ICogZnJvbSAnLi9ib29sZWFuX21hc2snO1xuZXhwb3J0ICogZnJvbSAnLi90cmFuc3Bvc2UnO1xuZXhwb3J0ICogZnJvbSAnLi9ub3JtJztcbmV4cG9ydCAqIGZyb20gJy4vbW92aW5nX2F2ZXJhZ2UnO1xuZXhwb3J0ICogZnJvbSAnLi9zY2F0dGVyX25kJztcbmV4cG9ydCAqIGZyb20gJy4vc2VhcmNoX3NvcnRlZCc7XG5leHBvcnQgKiBmcm9tICcuL3NwYXJzZV90b19kZW5zZSc7XG5leHBvcnQgKiBmcm9tICcuL2dhdGhlcl9uZCc7XG5leHBvcnQgKiBmcm9tICcuL2Ryb3BvdXQnO1xuZXhwb3J0ICogZnJvbSAnLi9zaWduYWxfb3BzX3V0aWwnO1xuZXhwb3J0ICogZnJvbSAnLi9pbl90b3Bfayc7XG5cbmV4cG9ydCB7b3AsIE9QX1NDT1BFX1NVRkZJWH0gZnJvbSAnLi9vcGVyYXRpb24nO1xuXG5pbXBvcnQge3JmZnR9IGZyb20gJy4vc3BlY3RyYWwvcmZmdCc7XG5pbXBvcnQge2ZmdH0gZnJvbSAnLi9zcGVjdHJhbC9mZnQnO1xuaW1wb3J0IHtpZmZ0fSBmcm9tICcuL3NwZWN0cmFsL2lmZnQnO1xuaW1wb3J0IHtpcmZmdH0gZnJvbSAnLi9zcGVjdHJhbC9pcmZmdCc7XG5jb25zdCBzcGVjdHJhbCA9IHtcbiAgZmZ0LFxuICBpZmZ0LFxuICByZmZ0LFxuICBpcmZmdFxufTtcblxuaW1wb3J0ICogYXMgZnVzZWQgZnJvbSAnLi9mdXNlZF9vcHMnO1xuXG5pbXBvcnQge2hhbW1pbmdXaW5kb3d9IGZyb20gJy4vc2lnbmFsL2hhbW1pbmdfd2luZG93JztcbmltcG9ydCB7aGFubldpbmRvd30gZnJvbSAnLi9zaWduYWwvaGFubl93aW5kb3cnO1xuaW1wb3J0IHtmcmFtZX0gZnJvbSAnLi9zaWduYWwvZnJhbWUnO1xuaW1wb3J0IHtzdGZ0fSBmcm9tICcuL3NpZ25hbC9zdGZ0JztcbmNvbnN0IHNpZ25hbCA9IHtcbiAgaGFtbWluZ1dpbmRvdyxcbiAgaGFubldpbmRvdyxcbiAgZnJhbWUsXG4gIHN0ZnQsXG59O1xuXG4vLyBJbWFnZSBPcHMgbmFtZXNwYWNlXG5pbXBvcnQge2Nyb3BBbmRSZXNpemV9IGZyb20gJy4vaW1hZ2UvY3JvcF9hbmRfcmVzaXplJztcbmltcG9ydCB7ZmxpcExlZnRSaWdodH0gZnJvbSAnLi9pbWFnZS9mbGlwX2xlZnRfcmlnaHQnO1xuaW1wb3J0IHtncmF5c2NhbGVUb1JHQn0gZnJvbSAnLi9pbWFnZS9ncmF5c2NhbGVfdG9fcmdiJztcbmltcG9ydCB7cmdiVG9HcmF5c2NhbGV9IGZyb20gJy4vaW1hZ2UvcmdiX3RvX2dyYXlzY2FsZSc7XG5pbXBvcnQge3JvdGF0ZVdpdGhPZmZzZXR9IGZyb20gJy4vaW1hZ2Uvcm90YXRlX3dpdGhfb2Zmc2V0JztcbmltcG9ydCB7bm9uTWF4U3VwcHJlc3Npb259IGZyb20gJy4vaW1hZ2Uvbm9uX21heF9zdXBwcmVzc2lvbic7XG5pbXBvcnQge25vbk1heFN1cHByZXNzaW9uQXN5bmN9IGZyb20gJy4vaW1hZ2Uvbm9uX21heF9zdXBwcmVzc2lvbl9hc3luYyc7XG5pbXBvcnQge25vbk1heFN1cHByZXNzaW9uV2l0aFNjb3JlfSBmcm9tICcuL2ltYWdlL25vbl9tYXhfc3VwcHJlc3Npb25fd2l0aF9zY29yZSc7XG5pbXBvcnQge25vbk1heFN1cHByZXNzaW9uV2l0aFNjb3JlQXN5bmN9IGZyb20gJy4vaW1hZ2Uvbm9uX21heF9zdXBwcmVzc2lvbl93aXRoX3Njb3JlX2FzeW5jJztcbmltcG9ydCB7bm9uTWF4U3VwcHJlc3Npb25QYWRkZWR9IGZyb20gJy4vaW1hZ2Uvbm9uX21heF9zdXBwcmVzc2lvbl9wYWRkZWQnO1xuaW1wb3J0IHtub25NYXhTdXBwcmVzc2lvblBhZGRlZEFzeW5jfSBmcm9tICcuL2ltYWdlL25vbl9tYXhfc3VwcHJlc3Npb25fcGFkZGVkX2FzeW5jJztcbmltcG9ydCB7cmVzaXplQmlsaW5lYXJ9IGZyb20gJy4vaW1hZ2UvcmVzaXplX2JpbGluZWFyJztcbmltcG9ydCB7cmVzaXplTmVhcmVzdE5laWdoYm9yfSBmcm9tICcuL2ltYWdlL3Jlc2l6ZV9uZWFyZXN0X25laWdoYm9yJztcbmltcG9ydCB7dGhyZXNob2xkfSBmcm9tICcuL2ltYWdlL3RocmVzaG9sZCc7XG5pbXBvcnQge3RyYW5zZm9ybX0gZnJvbSAnLi9pbWFnZS90cmFuc2Zvcm0nO1xuY29uc3QgaW1hZ2UgPSB7XG4gIGZsaXBMZWZ0UmlnaHQsXG4gIGdyYXlzY2FsZVRvUkdCLFxuICByZXNpemVOZWFyZXN0TmVpZ2hib3IsXG4gIHJlc2l6ZUJpbGluZWFyLFxuICByZ2JUb0dyYXlzY2FsZSxcbiAgcm90YXRlV2l0aE9mZnNldCxcbiAgY3JvcEFuZFJlc2l6ZSxcbiAgbm9uTWF4U3VwcHJlc3Npb24sXG4gIG5vbk1heFN1cHByZXNzaW9uQXN5bmMsXG4gIG5vbk1heFN1cHByZXNzaW9uV2l0aFNjb3JlLFxuICBub25NYXhTdXBwcmVzc2lvbldpdGhTY29yZUFzeW5jLFxuICBub25NYXhTdXBwcmVzc2lvblBhZGRlZCxcbiAgbm9uTWF4U3VwcHJlc3Npb25QYWRkZWRBc3luYyxcbiAgdGhyZXNob2xkLFxuICB0cmFuc2Zvcm1cbn07XG5cbi8vIGxpbmFsZyBuYW1lc3BhY2VcbmltcG9ydCB7YmFuZFBhcnR9IGZyb20gJy4vbGluYWxnL2JhbmRfcGFydCc7XG5pbXBvcnQge2dyYW1TY2htaWR0fSBmcm9tICcuL2xpbmFsZy9ncmFtX3NjaG1pZHQnO1xuaW1wb3J0IHtxcn0gZnJvbSAnLi9saW5hbGcvcXInO1xuY29uc3QgbGluYWxnID0ge1xuICBiYW5kUGFydCxcbiAgZ3JhbVNjaG1pZHQsXG4gIHFyXG59O1xuXG4vLyBsb3NzZXMgbmFtZXNwYWNlO1xuaW1wb3J0IHthYnNvbHV0ZURpZmZlcmVuY2V9IGZyb20gJy4vbG9zc2VzL2Fic29sdXRlX2RpZmZlcmVuY2UnO1xuaW1wb3J0IHtjb21wdXRlV2VpZ2h0ZWRMb3NzfSBmcm9tICcuL2xvc3Nlcy9jb21wdXRlX3dlaWdodGVkX2xvc3MnO1xuaW1wb3J0IHtjb3NpbmVEaXN0YW5jZX0gZnJvbSAnLi9sb3NzZXMvY29zaW5lX2Rpc3RhbmNlJztcbmltcG9ydCB7aGluZ2VMb3NzfSBmcm9tICcuL2xvc3Nlcy9oaW5nZV9sb3NzJztcbmltcG9ydCB7aHViZXJMb3NzfSBmcm9tICcuL2xvc3Nlcy9odWJlcl9sb3NzJztcbmltcG9ydCB7bG9nTG9zc30gZnJvbSAnLi9sb3NzZXMvbG9nX2xvc3MnO1xuaW1wb3J0IHttZWFuU3F1YXJlZEVycm9yfSBmcm9tICcuL2xvc3Nlcy9tZWFuX3NxdWFyZWRfZXJyb3InO1xuaW1wb3J0IHtzaWdtb2lkQ3Jvc3NFbnRyb3B5fSBmcm9tICcuL2xvc3Nlcy9zaWdtb2lkX2Nyb3NzX2VudHJvcHknO1xuaW1wb3J0IHtzb2Z0bWF4Q3Jvc3NFbnRyb3B5fSBmcm9tICcuL2xvc3Nlcy9zb2Z0bWF4X2Nyb3NzX2VudHJvcHknO1xuY29uc3QgbG9zc2VzID0ge1xuICBhYnNvbHV0ZURpZmZlcmVuY2UsXG4gIGNvbXB1dGVXZWlnaHRlZExvc3MsXG4gIGNvc2luZURpc3RhbmNlLFxuICBoaW5nZUxvc3MsXG4gIGh1YmVyTG9zcyxcbiAgbG9nTG9zcyxcbiAgbWVhblNxdWFyZWRFcnJvcixcbiAgc2lnbW9pZENyb3NzRW50cm9weSxcbiAgc29mdG1heENyb3NzRW50cm9weVxufTtcblxuaW1wb3J0IHtzcGFyc2VGaWxsRW1wdHlSb3dzfSBmcm9tICcuL3NwYXJzZS9zcGFyc2VfZmlsbF9lbXB0eV9yb3dzJztcbmltcG9ydCB7c3BhcnNlUmVzaGFwZX0gZnJvbSAnLi9zcGFyc2Uvc3BhcnNlX3Jlc2hhcGUnO1xuaW1wb3J0IHtzcGFyc2VTZWdtZW50TWVhbn0gZnJvbSAnLi9zcGFyc2Uvc3BhcnNlX3NlZ21lbnRfbWVhbic7XG5pbXBvcnQge3NwYXJzZVNlZ21lbnRTdW19IGZyb20gJy4vc3BhcnNlL3NwYXJzZV9zZWdtZW50X3N1bSc7XG5jb25zdCBzcGFyc2UgPSB7XG4gIHNwYXJzZUZpbGxFbXB0eVJvd3MsXG4gIHNwYXJzZVJlc2hhcGUsXG4gIHNwYXJzZVNlZ21lbnRNZWFuLFxuICBzcGFyc2VTZWdtZW50U3VtXG59O1xuXG5pbXBvcnQge3N0cmluZ05HcmFtc30gZnJvbSAnLi9zdHJpbmcvc3RyaW5nX25fZ3JhbXMnO1xuaW1wb3J0IHtzdHJpbmdTcGxpdH0gZnJvbSAnLi9zdHJpbmcvc3RyaW5nX3NwbGl0JztcbmltcG9ydCB7c3RyaW5nVG9IYXNoQnVja2V0RmFzdH0gZnJvbSAnLi9zdHJpbmcvc3RyaW5nX3RvX2hhc2hfYnVja2V0X2Zhc3QnO1xuaW1wb3J0IHtzdGF0aWNSZWdleFJlcGxhY2V9IGZyb20gJy4vc3RyaW5nL3N0YXRpY19yZWdleF9yZXBsYWNlJztcbi8vIHRzbGludDpkaXNhYmxlLW5leHQtbGluZTp2YXJpYWJsZS1uYW1lXG5jb25zdCBzdHJpbmcgPSB7XG4gIHN0cmluZ05HcmFtcyxcbiAgc3RyaW5nU3BsaXQsXG4gIHN0cmluZ1RvSGFzaEJ1Y2tldEZhc3QsXG4gIHN0YXRpY1JlZ2V4UmVwbGFjZSxcbn07XG5cbi8vIFNlY29uZCBsZXZlbCBleHBvcnRzLlxuZXhwb3J0IHtpbWFnZSwgbGluYWxnLCBsb3NzZXMsIHNwZWN0cmFsLCBmdXNlZCwgc2lnbmFsLCBzcGFyc2UsIHN0cmluZ307XG4iXX0=