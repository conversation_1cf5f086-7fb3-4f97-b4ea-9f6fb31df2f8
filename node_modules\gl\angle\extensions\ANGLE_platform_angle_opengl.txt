Name

    ANGLE_platform_angle_opengl

Name Strings

    EGL_ANGLE_platform_angle_opengl

Contributors

    <PERSON>, <PERSON>, Google

Contacts

    <PERSON>, Google (geofflang 'at' chromium 'dot' org)

Status

    Draft

Version

    Version 3, 2014-11-26

Number

    EGL Extension XXX

Extension Type

    EGL client extension

Dependencies

    Requires ANGLE_platform_angle.

Overview

    This extension enables selection of OpenGL display types.

New Types

    None

New Procedures and Functions

    None

New Tokens

    Accepted as values for the EGL_PLATFORM_ANGLE_TYPE_ANGLE attribute:

        EGL_PLATFORM_ANGLE_TYPE_OPENGL_ANGLE               0x320D
        EGL_PLATFORM_ANGLE_TYPE_OPENGLES_ANGLE             0x320E

Additions to the EGL Specification

    None.

New Behavior

    To request a display that translates to OpenGL or OpenGL ES, the value of
    EGL_PLATFORM_ANGLE_TYPE_ANGLE should be:
      - EGL_PLATFORM_ANGLE_TYPE_OPENGL_ANGLE for an OpenGL display,
      - EGL_PLATFORM_ANGLE_TYPE_OPENGLES_ANGLE for a native OpenGL ES display.

    To request a specific maximum context version to use for the underlying
    API, EGL_PLATFORM_ANGLE_MAX_VERSION_MAJOR_ANGLE and
    EGL_PLATFORM_ANGLE_MAX_VERSION_MINOR_ANGLE can be used.

Issues

    None

Revision History

    Version 1, 2014-06-05 (Geoff Lang)
      - Initial draft
    Version 2, 2014-10-27 (Geoff <PERSON>)
      - Moved descriptions of platforms and major/minor versions from
        EGL_ANGLE_platform_angle spec to EGL_ANGLE_platform_angle_opengl.
    Version 3, 2014-11-26 (Geoff Lang)
      - Updated enum values.
