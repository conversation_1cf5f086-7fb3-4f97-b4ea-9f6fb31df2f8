Name

    EXT_color_buffer_float

Name Strings

    GL_EXT_color_buffer_float

Contributors

    OpenGL ES Working Group members

Contact

    Mark Callow, HI Corp. (callow.mark 'at' artspark.co.jp)

Notice

    ©2012 The Khronos Group Inc.

Status

    Complete

IP Status

    Graphics Properties Holdings (GPH, formerly SGI) owns US Patent
    #6,650,327, issued November 18, 2003. GPH believes this patent
    contains necessary IP for graphics systems implementing floating
    point (FP) rasterization and FP framebuffer capabilities.

    GPH will not grant Khronos royalty-free use of this IP for use
    in OpenGL ES, but will discuss licensing on RAND terms, on an
    individual basis with companies wishing to use this IP in the
    context of conformant OpenGL ES implementations. GPH does not
    plan to make any special exemption for open source
    implementations.

    See
    https://www.khronos.org/files/ip-disclosures/opengl/SGI%20IP%20Disclosure%20Mar05_clean.pdf
    for the full disclosure.

Version

    Date: January 11th, 2013
    Revision: 5

Number

    OpenGL ES Extension #137

Dependencies

    Requires OpenGL ES 3.0.

    Written based on the wording of the OpenGL ES 3.0.1 Specification
    (January 10th, 2013).

Overview

    This extension allows a variety of floating point formats to be
    rendered to via framebuffer objects.

New Procedures and Functions

    None

New Tokens

    None

Additions to Chapter 3 of the OpenGL ES 3.0 Specification
(Rasterization)

    3.8.3 Texture Image Specification, unnumbered subsection "Required
    Texture Formats", p. 126

    Change the first two bullet items to the following:

    - Texture and renderbuffer color formats (see section 4.4.2).
      - RGBA32F, RGBA32I, RGBA32UI, RGBA16F, RGBA16I, RGBA16UI,
        RGBA8, RGBA8I, RGBA8UI, SRGB8_ALPHA8, RGB10_A2, RGB10_-
        A2UI, RGBA4, and RGB5_A1.
      - RGB8 and RGB565.
      - R11F G11F B10F.
      - RG32F, RG32I, RG32UI, RG16F, RG16I, RG16UI, RG8, RG8I, and
        RG8UI.
      - R32F, R32I, R32UI, R16F, R16I, R16UI, R8, R8I, and R8UI.

    - Texture-only color formats:
      - RGBA8_SNORM.
      - RGB32F, RGB32I, and RGB32UI.
      - RGB16F, RGB16I, and RGB16UI.
      - RGB8_SNORM, RGB8I, RGB8UI, and SRGB8.
      - RGB9_E5.
      - RG8_SNORM.
      - R8_SNORM.

    Table 3.12, p. 128 & 129

    Convert the dash under 'Color-renderable' to a 'check' for the
    following internal formats: R16F, RG16F, RGBA16F, R32F, RG32F,
    RGBA32F and R11F_G11F_B10F.
    
Additions to Chapter 4 of the OpenGL ES 3.0 Specification (Per-Fragment
Operations and the Framebuffer)

    (changed lines marked with *; added lines marked with +)

    Chapter 4 Introduction, p. 167

    Paragraph 5, sentence 3, p 168, insert "floating point" as shown:
        "R, G, B, and A components may be represented as unsigned
 *      normalized fixed-point, floating point or signed or unsigned
        integer values; ..."    ^^^^^^^^^^^^^^

    4.1.7 Blending, p. 174

    Modify paragraphs 3 & 4:
    
 *      "If the color buffer is fixed-point, the components of the
    source and destination values and blend factors are clamped
 *  to [0; 1] prior to evaluating the blend equation. If the color
 +  buffer is floating-point, no clamping occurs. The resulting four
 +  values are sent to the next operation.
    
        Blending applies only if the color buffer has a fixed-point or
 *  or floating-point format. If the color buffer has an integer
 *  format, proceed to the next operation.  Furthermore, an
 +  INVALID_OPERATION error is generated by DrawArrays and the other
 +  drawing commands defined in section 2.8.3 if blending is enabled
 +  (see below) and any draw buffer has a 32-bit floating-point
 +  format."

    4.2.3 Clearing the Buffers, p. 183

    Modify second paragraph, inserting "floating point":

    "   void ClearColor(float r, float g, float b, float a);

 *  sets the clear value for fixed- and floating-point color buffers.
    ..."                            ^^^^^^^^^^^^^^^^^^

    4.3.1 Reading Pixels, p. 186

    In paragraph 4, beginning "Only two combinations of format
    and type are accepted ...", after the sentence ending "... type
    UNSIGNED_BYTE is accepted." insert the following sentence:
        "For floating-point rendering surfaces, the combination
        format RGBA and type FLOAT is accepted."

    4.3.1 unnumbered subsection "Obtaining Pixels from the Framebuffer",
    p. 188

    Modify penultimate paragraph, p189, "If format is an integer ..."

    "If format is an integer format and the color buffer is not an
    integer format; if the color buffer is an integer format and
 *  format is not an integer format; if format is an integer format
 *  and type is FLOAT, HALF_FLOAT, or UNSIGNED_INT_10F_11F_11F_REV;
 +  or if the color buffer is a floating-point format and type is
 +  not FLOAT, HALF FLOAT, or UNSIGNED_INT_10F_11F_11F_REV, the error
    INVALID_OPERATION occurs."

    4.3.1 unnumbered subsection "Conversion of RGBA values", p.190

    Sole paragraph, sentence 3, insert "or floating point" as shown:
 *      "For an integer or floating point color buffer, the elements
        are unmodified."^^^^^^^^^^^^^^^^^

    4.3.2 Copying Pixels, p192

    Modify first error condition, at bottom of p193, "The read buffer
    contains ..." to encompass floating-point buffers.

 *  "- The read buffer contains fixed-point or floating-point values
 *     and any draw buffer contains neither fixed-point nor
 *     floating-point values."

    4.4.2 Attaching Images to Framebuffer Objects, p. 197, unnumbered
    subsection "Required Renderbuffer Formats", p. 200

    In the last paragraph beginning "Implementations must support
    creation ...", modify the final phrase to

 *   "with the exception of signed and unsigned integer, RGBA16F,
 +   R32F, RG32F and RGBA32F formats.

Additions to Chapter 5 of the OpenGL ES 2.0 Specification (Special Functions)

    None

Additions to Chapter 6 of the OpenGL ES 2.0 Specification (State and State
Requests)

    6.1.15 Internal Format Queries, p. 237

    P. 238, paragraph 8 after "Since multisampling is not supported
    for signed and unsigned integer internal formats, the value of
    NUM_SAMPLE_COUNTS will be zero for such formats.", insert new
    one-sentence paragraph:

        "If <internalformat> is RGBA16F, R32F, RG32F, or RGBA32F, the
        value of NUM_SAMPLE_COUNTS may be zero, or else the maximum
        value in SAMPLES may be less than the value of MAX_SAMPLES."

New Implementation Dependent State

    None

Issues

Revision History

    Rev.  Date     Author     Changes
    ----  -------- ---------  -----------------------------------------
      1   10/16/12 markc      Initial version
      2   10/18/12 markc      Referenced preliminary version of OpenGL
                              ES 3.0.1 specification and updated page
                              numbers.
      3   11/21/12 markc      Corrected IP status.
      4   01/09/13 markc      Changed date of referenced OpenGL ES
                              3.0.1 specification. Made minor language
                              simplification.
      5   01/11/13 markc      Changed date to release version of
                              OpenGL ES 3.0.1 specification.
                              Clarified change to "Required
                              renderbuffer formats" section.

# vim:ai:ts=4:sts=4:sw=4:expandtab:textwidth=70
