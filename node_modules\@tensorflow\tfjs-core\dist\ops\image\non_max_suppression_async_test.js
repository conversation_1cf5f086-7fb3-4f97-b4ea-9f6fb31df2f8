/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../../index';
import { ALL_ENVS, describeWithFlags } from '../../jasmine_util';
import { expectArraysClose, expectArraysEqual } from '../../test_util';
describeWithFlags('nonMaxSuppressionAsync', ALL_ENVS, () => {
    describe('NonMaxSuppressionAsync basic', () => {
        it('select from three clusters', async () => {
            const boxes = tf.tensor2d([
                0, 0, 1, 1, 0, 0.1, 1, 1.1, 0, -0.1, 1, 0.9,
                0, 10, 1, 11, 0, 10.1, 1, 11.1, 0, 100, 1, 101
            ], [6, 4]);
            const scores = tf.tensor1d([0.9, 0.75, 0.6, 0.95, 0.5, 0.3]);
            const maxOutputSize = 3;
            const iouThreshold = 0.5;
            const scoreThreshold = 0;
            const indices = await tf.image.nonMaxSuppressionAsync(boxes, scores, maxOutputSize, iouThreshold, scoreThreshold);
            expect(indices.shape).toEqual([3]);
            expectArraysEqual(await indices.data(), [3, 0, 5]);
        });
        it('accepts a tensor-like object', async () => {
            const boxes = [[0, 0, 1, 1], [0, 1, 1, 2]];
            const scores = [1, 2];
            const indices = await tf.image.nonMaxSuppressionAsync(boxes, scores, 10);
            expect(indices.shape).toEqual([2]);
            expect(indices.dtype).toEqual('int32');
            expectArraysEqual(await indices.data(), [1, 0]);
        });
    });
    describe('NonMaxSuppressionWithScoreAsync', () => {
        it('select from three clusters with SoftNMS', async () => {
            const boxes = tf.tensor2d([
                0, 0, 1, 1, 0, 0.1, 1, 1.1, 0, -0.1, 1, 0.9,
                0, 10, 1, 11, 0, 10.1, 1, 11.1, 0, 100, 1, 101
            ], [6, 4]);
            const scores = tf.tensor1d([0.9, 0.75, 0.6, 0.95, 0.5, 0.3]);
            const maxOutputSize = 6;
            const iouThreshold = 1.0;
            const scoreThreshold = 0;
            const softNmsSigma = 0.5;
            const numTensorsBefore = tf.memory().numTensors;
            const { selectedIndices, selectedScores } = await tf.image.nonMaxSuppressionWithScoreAsync(boxes, scores, maxOutputSize, iouThreshold, scoreThreshold, softNmsSigma);
            const numTensorsAfter = tf.memory().numTensors;
            expectArraysEqual(await selectedIndices.data(), [3, 0, 1, 5, 4, 2]);
            expectArraysClose(await selectedScores.data(), [0.95, 0.9, 0.384, 0.3, 0.256, 0.197]);
            // The number of tensors should increase by the number of tensors
            // returned (i.e. selectedIndices and selectedScores).
            expect(numTensorsAfter).toEqual(numTensorsBefore + 2);
        });
    });
    describe('NonMaxSuppressionPaddedAsync', () => {
        it('select from three clusters with pad five.', async () => {
            const boxes = tf.tensor2d([
                0, 0, 1, 1, 0, 0.1, 1, 1.1, 0, -0.1, 1, 0.9,
                0, 10, 1, 11, 0, 10.1, 1, 11.1, 0, 100, 1, 101
            ], [6, 4]);
            const scores = tf.tensor1d([0.9, 0.75, 0.6, 0.95, 0.5, 0.3]);
            const maxOutputSize = 5;
            const iouThreshold = 0.5;
            const scoreThreshold = 0.0;
            const before = tf.memory().numTensors;
            const { selectedIndices, validOutputs } = await tf.image.nonMaxSuppressionPaddedAsync(boxes, scores, maxOutputSize, iouThreshold, scoreThreshold, true);
            const after = tf.memory().numTensors;
            expectArraysEqual(await selectedIndices.data(), [3, 0, 5, 0, 0]);
            expectArraysEqual(await validOutputs.data(), 3);
            // The number of tensors should increase by the number of tensors
            // returned (i.e. selectedIndices and selectedScores).
            expect(after).toEqual(before + 2);
        });
    });
});
//# sourceMappingURL=data:application/json;base64,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