/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../../index';
import { ALL_ENVS, describeWithFlags } from '../../jasmine_util';
import { expectArraysClose } from '../../test_util';
describeWithFlags('hannWindow', ALL_ENVS, () => {
    it('length=3', async () => {
        const ret = tf.signal.hannWindow(3);
        expectArraysClose(await ret.data(), [0, 1, 0]);
    });
    it('length=7', async () => {
        const ret = tf.signal.hannWindow(7);
        expectArraysClose(await ret.data(), [0, 0.25, 0.75, 1, 0.75, 0.25, 0]);
    });
    it('length=6', async () => {
        const ret = tf.signal.hannWindow(6);
        expectArraysClose(await ret.data(), [0., 0.25, 0.75, 1., 0.75, 0.25]);
    });
    it('length=20', async () => {
        const ret = tf.signal.hannWindow(20);
        expectArraysClose(await ret.data(), [
            0., 0.02447176, 0.09549153, 0.20610738, 0.34549153,
            0.5, 0.65450853, 0.79389274, 0.9045085, 0.97552824,
            1., 0.97552824, 0.9045085, 0.7938925, 0.65450835,
            0.5, 0.34549144, 0.20610726, 0.09549153, 0.02447173
        ]);
    });
});
//# sourceMappingURL=data:application/json;base64,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