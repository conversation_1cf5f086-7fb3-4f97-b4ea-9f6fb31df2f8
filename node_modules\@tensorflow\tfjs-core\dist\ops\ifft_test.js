/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../index';
import { ALL_ENVS, describeWithFlags } from '../jasmine_util';
import { expectArraysClose } from '../test_util';
describeWithFlags('1D IFFT', ALL_ENVS, () => {
    it('should return the same value with TensorFlow (2 elements)', async () => {
        const t1Real = tf.tensor1d([1, 2]);
        const t1Imag = tf.tensor1d([1, 1]);
        const t1 = tf.complex(t1Real, t1Imag);
        expectArraysClose(await tf.spectral.ifft(t1).data(), [1.5, 1, -0.5, 0]);
    });
    it('should calculate FFT from Tensor directly', async () => {
        const t1Real = tf.tensor1d([1, 2]);
        const t1Imag = tf.tensor1d([1, 1]);
        const t1 = tf.complex(t1Real, t1Imag);
        expectArraysClose(await t1.ifft().data(), [1.5, 1, -0.5, 0]);
    });
    it('should return the same value as TensorFlow (3 elements)', async () => {
        const t1Real = tf.tensor1d([1, 2, 3]);
        const t1Imag = tf.tensor1d([0, 0, 0]);
        const t1 = tf.complex(t1Real, t1Imag);
        expectArraysClose(await tf.spectral.ifft(t1).data(), [
            2, -3.9736431e-08, -0.49999997, -.28867507, -0.49999994, 2.8867519e-01
        ]);
    });
    it('should return the same value as TensorFlow with imaginary (3 elements)', async () => {
        const t1Real = tf.tensor1d([1, 2, 3]);
        const t1Imag = tf.tensor1d([1, 2, 3]);
        const t1 = tf.complex(t1Real, t1Imag);
        expectArraysClose(await tf.spectral.ifft(t1).data(), [2, 1.9999999, -0.21132492, -0.78867507, -0.7886752, -0.2113249]);
    });
    it('should return the same value as TensorFlow (negative 3 elements)', async () => {
        const t1Real = tf.tensor1d([-1, -2, -3]);
        const t1Imag = tf.tensor1d([-1, -2, -3]);
        const t1 = tf.complex(t1Real, t1Imag);
        expectArraysClose(await tf.spectral.ifft(t1).data(), [-2, -1.9999999, 0.21132492, 0.78867507, 0.7886752, 0.2113249]);
    });
    it('should return the same value with TensorFlow (4 elements)', async () => {
        const t1Real = tf.tensor1d([1, 2, 3, 4]);
        const t1Imag = tf.tensor1d([0, 0, 0, 0]);
        const t1 = tf.complex(t1Real, t1Imag);
        expectArraysClose(await tf.spectral.ifft(t1).data(), [2.5, 0, -0.5, -0.5, -0.5, 0, -0.5, 0.5]);
    });
    it('should return the same value as TensorFlow with imaginary (4 elements)', async () => {
        const t1Real = tf.tensor1d([1, 2, 3, 4]);
        const t1Imag = tf.tensor1d([1, 2, 3, 4]);
        const t1 = tf.complex(t1Real, t1Imag);
        expectArraysClose(await tf.spectral.ifft(t1).data(), [2.5, 2.5, 0, -1, -0.5, -0.5, -1, 0]);
    });
});
describeWithFlags('2D IFFT', ALL_ENVS, () => {
    it('2D: should return the same value as TensorFlow', async () => {
        const t1Real = tf.tensor2d([1, 2, 3, 4], [2, 2]);
        const t1Imag = tf.tensor2d([5, 6, 7, 8], [2, 2]);
        const t1 = tf.complex(t1Real, t1Imag);
        const y = tf.spectral.ifft(t1);
        expectArraysClose(await y.data(), [1.5, 5.5, -0.5, -0.5, 3.5, 7.5, -0.5, -0.5]);
        expect(y.shape).toEqual(t1Real.shape);
    });
    it('3D: should return the same value as TensorFlow', async () => {
        const t1Real = tf.tensor3d([1, 2, 3, 4, -1, -2, -3, -4], [2, 2, 2]);
        const t1Imag = tf.tensor3d([5, 6, 7, 8, -5, -6, -7, -8], [2, 2, 2]);
        const t1 = tf.complex(t1Real, t1Imag);
        const y = tf.spectral.ifft(t1);
        expectArraysClose(await y.data(), [
            1.5, 5.5, -0.5, -0.5, 3.5, 7.5, -0.5, -0.5, -1.5, -5.5, 0.5, 0.5, -3.5,
            -7.5, 0.5, 0.5
        ]);
        expect(y.shape).toEqual(t1Real.shape);
    });
});
//# sourceMappingURL=data:application/json;base64,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