export declare function getDefaultSearchPlaces(moduleName: string): Array<string>;
export declare function getDefaultSearchPlacesSync(moduleName: string): Array<string>;
export declare const globalConfigSearchPlaces: string[];
export declare const globalConfigSearchPlacesSync: string[];
export declare const metaSearchPlaces: string[];
export declare const defaultLoaders: Readonly<{
    readonly '.mjs': import("./types").Loader;
    readonly '.cjs': import("./types").Loader;
    readonly '.js': import("./types").Loader;
    readonly '.ts': import("./types").Loader;
    readonly '.json': import("./types").LoaderSync;
    readonly '.yaml': import("./types").LoaderSync;
    readonly '.yml': import("./types").LoaderSync;
    readonly noExt: import("./types").LoaderSync;
}>;
export declare const defaultLoadersSync: Readonly<{
    readonly '.cjs': import("./types").LoaderSync;
    readonly '.js': import("./types").LoaderSync;
    readonly '.ts': import("./types").LoaderSync;
    readonly '.json': import("./types").LoaderSync;
    readonly '.yaml': import("./types").LoaderSync;
    readonly '.yml': import("./types").LoaderSync;
    readonly noExt: import("./types").LoaderSync;
}>;
//# sourceMappingURL=defaults.d.ts.map