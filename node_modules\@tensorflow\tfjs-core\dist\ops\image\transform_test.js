/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../../index';
import { ALL_ENVS, describeWithFlags } from '../../jasmine_util';
import { expectArraysClose } from '../../test_util';
describeWithFlags('image.transform', ALL_ENVS, () => {
    it('extreme projective transform.', async () => {
        const images = tf.tensor4d([1, 0, 1, 0, 0, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0, 1], [1, 4, 4, 1]);
        const transform = tf.tensor2d([1, 0, 0, 0, 1, 0, -1, 0], [1, 8]);
        const transformedImages = tf.image.transform(images, transform, 'nearest', 'constant', 0).toInt();
        const transformedImagesData = await transformedImages.data();
        const expected = [1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0];
        expectArraysClose(transformedImagesData, expected);
    });
    it('static output shape.', async () => {
        const images = tf.tensor4d([1, 2, 3, 4], [1, 2, 2, 1]);
        const transform = tf.randomUniform([1, 8], -1, 1);
        const transformedImages = tf.image.transform(images, transform, 'nearest', 'constant', 0, [3, 5]);
        expectArraysClose(transformedImages.shape, [1, 3, 5, 1]);
    });
    it('fill=constant, interpolation=nearest.', async () => {
        const images = tf.tensor4d([1, 1, 1, 0, 0, 1, 0, 0, 1, 0, 1, 0, 1, 1, 0, 0], [1, 4, 4, 1]);
        const transform = tf.tensor2d([0, 0.5, 1, -1, 2, 3, 0, 0], [1, 8]);
        const transformedImages = tf.image.transform(images, transform);
        const transformedImagesData = await transformedImages.data();
        const expected = [1, 0, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0];
        expectArraysClose(transformedImagesData, expected);
    });
    it('fill=constant, interpolation=bilinear.', async () => {
        const images = tf.tensor4d([1, 1, 1, 0, 0, 1, 0, 0, 1, 0, 1, 0, 1, 1, 0, 0], [1, 4, 4, 1]);
        const transform = tf.tensor2d([0, 0.5, 1, -1, 2, 3, 0, 0], [1, 8]);
        const transformedImages = tf.image.transform(images, transform, 'bilinear');
        const transformedImagesData = await transformedImages.data();
        const expected = [1, 0, 1, 1, 0, 0, 0.5, 0.5, 0, 0, 0, 0, 0, 0, 0, 0];
        expectArraysClose(transformedImagesData, expected);
    });
    it('fill=reflect, interpolation=bilinear.', async () => {
        const images = tf.tensor4d([1, 1, 1, 0, 0, 1, 0, 0, 1, 0, 1, 0, 1, 1, 0, 0], [1, 4, 4, 1]);
        const transform = tf.tensor2d([0, 0.5, 1, -1, 2, 3, 0, 0], [1, 8]);
        const transformedImages = tf.image.transform(images, transform, 'bilinear', 'reflect');
        const transformedImagesData = await transformedImages.data();
        const expected = [1, 0, 1, 1, 0.5, 0.5, 0.5, 0.5, 1, 0, 1, 0, 0, 0.5, 0.5, 0];
        expectArraysClose(transformedImagesData, expected);
    });
    it('fill=wrap, interpolation=bilinear.', async () => {
        const images = tf.tensor4d([1, 1, 1, 0, 0, 1, 0, 0, 1, 0, 1, 0, 1, 1, 0, 0], [1, 4, 4, 1]);
        const transform = tf.tensor2d([0, 0.5, 1, -1, 2, 3, 0, 0], [1, 8]);
        const transformedImages = tf.image.transform(images, transform, 'bilinear', 'wrap');
        const transformedImagesData = await transformedImages.data();
        const expected = [1, 0, 1, 1, 0.5, 1, 0.5, 0.5, 1, 1, 0, 1, 0.5, 0.5, 0.5, 0.5];
        expectArraysClose(transformedImagesData, expected);
    });
    it('fill=nearest, interpolation=bilinear.', async () => {
        const images = tf.tensor4d([1, 1, 1, 0, 0, 1, 0, 0, 1, 0, 1, 0, 1, 1, 0, 0], [1, 4, 4, 1]);
        const transform = tf.tensor2d([0, 0.5, 1, -1, 2, 3, 0, 0], [1, 8]);
        const transformedImages = tf.image.transform(images, transform, 'bilinear', 'nearest');
        const transformedImagesData = await transformedImages.data();
        const expected = [1, 0, 1, 1, 0.5, 0.5, 0.5, 0.5, 0, 0, 0, 0, 0, 0, 0, 0];
        expectArraysClose(transformedImagesData, expected);
    });
    it('throws when input is int32.', async () => {
        const images = tf.tensor4d([1, 0, 1, 0, 0, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0, 1], [1, 4, 4, 1], 'int32');
        const transform = tf.tensor2d([1, 0, 0, 0, 1, 0, -1, 0], [1, 8]);
        expect(() => tf.image.transform(images, transform, 'nearest', 'constant', 0))
            .toThrowError(/Argument 'image' passed to 'transform' must be float32/);
    });
    it('throws when transforms is int32.', async () => {
        const images = tf.tensor4d([1, 0, 1, 0, 0, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0, 1], [1, 4, 4, 1]);
        const transform = tf.tensor2d([1, 0, 0, 0, 1, 0, -1, 0], [1, 8], 'int32');
        expect(() => tf.image.transform(images, transform, 'nearest', 'constant', 0))
            .toThrowError(/Argument 'transforms' passed to 'transform' must be float32/);
    });
});
//# sourceMappingURL=data:application/json;base64,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