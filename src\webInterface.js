/**
 * واجهة الويب المتقدمة لنظام التداول
 * Advanced Web Interface for Trading System
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const fs = require('fs');

class WebInterface {
    constructor(quotexAPI, smartTrading, liveDataManager, options = {}) {
        this.api = quotexAPI;
        this.smartTrading = smartTrading;
        this.liveDataManager = liveDataManager;

        // إعدادات الخادم
        this.settings = {
            port: options.port || 3000,
            host: options.host || 'localhost',
            enableAuth: options.enableAuth || false,
            staticPath: options.staticPath || path.join(__dirname, '../public'),
            ...options
        };

        // إعداد Express
        this.app = express();
        this.server = http.createServer(this.app);
        this.io = socketIo(this.server, {
            cors: {
                origin: "*",
                methods: ["GET", "POST"]
            }
        });

        // متغيرات الحالة
        this.connectedClients = new Set();
        this.isRunning = false;

        this.setupMiddleware();
        this.setupRoutes();
        this.setupSocketHandlers();
        this.setupEventListeners();
    }

    /**
     * إعداد Middleware
     */
    setupMiddleware() {
        // تمكين CORS
        this.app.use((req, res, next) => {
            res.header('Access-Control-Allow-Origin', '*');
            res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
            res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
            next();
        });

        // تحليل JSON
        this.app.use(express.json());
        this.app.use(express.urlencoded({ extended: true }));

        // ملفات ثابتة
        this.app.use(express.static(this.settings.staticPath));

        // تسجيل الطلبات
        this.app.use((req, res, next) => {
            console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
            next();
        });
    }

    /**
     * إعداد المسارات
     */
    setupRoutes() {
        // الصفحة الرئيسية
        this.app.get('/', (req, res) => {
            res.sendFile(path.join(this.settings.staticPath, 'index.html'));
        });

        // API للحصول على حالة النظام
        this.app.get('/api/status', (req, res) => {
            res.json(this.getSystemStatus());
        });

        // API للحصول على البيانات المباشرة
        this.app.get('/api/live-data/:instrumentId', (req, res) => {
            const instrumentId = parseInt(req.params.instrumentId);
            const data = this.liveDataManager.getLiveData(instrumentId);
            res.json(data || { error: 'No data available' });
        });

        // API للحصول على البيانات التاريخية
        this.app.get('/api/historical-data/:instrumentId', (req, res) => {
            const instrumentId = parseInt(req.params.instrumentId);
            const data = this.liveDataManager.getHistoricalData(instrumentId);
            res.json(data || { error: 'No data available' });
        });

        // API للحصول على البيانات المدمجة
        this.app.get('/api/combined-data/:instrumentId', (req, res) => {
            const instrumentId = parseInt(req.params.instrumentId);
            const data = this.liveDataManager.getCombinedData(instrumentId);
            res.json(data || { error: 'No data available' });
        });

        // API لتنفيذ صفقة
        this.app.post('/api/execute-trade', async (req, res) => {
            try {
                const { instrumentId, direction, amount } = req.body;

                if (!instrumentId || !direction) {
                    return res.status(400).json({ error: 'Missing required parameters' });
                }

                // الحصول على رصيد الحساب
                const balance = await this.api.getAccountBalance();

                // إنشاء إشارة وهمية للتنفيذ
                const signal = {
                    instrumentId: instrumentId,
                    direction: direction,
                    confidence: 0.8,
                    reasons: ['Manual trade execution'],
                    timestamp: new Date()
                };

                const result = await this.smartTrading.executeTrade(signal, balance.balance, amount);
                res.json(result);

            } catch (error) {
                console.error('❌ Trade execution error:', error);
                res.status(500).json({ error: error.message });
            }
        });

        // API للحصول على تقرير الأداء
        this.app.get('/api/performance', (req, res) => {
            const report = this.smartTrading.getPerformanceReport();
            res.json(report);
        });

        // API للحصول على الإعدادات
        this.app.get('/api/settings', (req, res) => {
            res.json({
                smartTrading: this.smartTrading.settings,
                liveDataManager: this.liveDataManager.settings
            });
        });

        // API للحصول على جميع الأزواج الـ70
        this.app.get('/api/trading-pairs', async (req, res) => {
            try {
                const instruments = this.api.getInstruments();
                const profitRates = this.api.getProfitRates();

                const pairs = instruments.map(instrument => ({
                    symbol: instrument.id || instrument.symbol,
                    name: instrument.name,
                    status: instrument.isActive ? 'active' : 'inactive',
                    payout: profitRates[instrument.id] || profitRates[instrument.symbol] || 0,
                    type: instrument.type || 'currency',
                    change24: instrument.change24 || 0
                }));

                res.json(pairs);
            } catch (error) {
                console.error('❌ Error fetching trading pairs:', error);
                res.status(500).json({ error: error.message });
            }
        });

        // API للحصول على الصفقات المفتوحة
        this.app.get('/api/open-trades', async (req, res) => {
            try {
                // هنا سنحتاج لتطوير نظام متابعة الصفقات
                const openTrades = []; // سيتم تطويرها لاحقاً
                res.json(openTrades);
            } catch (error) {
                console.error('❌ Error fetching open trades:', error);
                res.status(500).json({ error: error.message });
            }
        });

        // API للحصول على الصفقات المغلقة
        this.app.get('/api/closed-trades', async (req, res) => {
            try {
                // هنا سنحتاج لتطوير نظام متابعة الصفقات
                const closedTrades = []; // سيتم تطويرها لاحقاً
                res.json(closedTrades);
            } catch (error) {
                console.error('❌ Error fetching closed trades:', error);
                res.status(500).json({ error: error.message });
            }
        });

        // API لتحديث إعدادات التداول الآلي
        this.app.post('/api/auto-trading-settings', (req, res) => {
            try {
                const settings = req.body;
                // تحديث إعدادات التداول الآلي
                if (this.smartTrading && this.smartTrading.updateSettings) {
                    this.smartTrading.updateSettings(settings);
                }
                res.json({ success: true });
            } catch (error) {
                console.error('❌ Error updating auto trading settings:', error);
                res.status(500).json({ error: error.message });
            }
        });

        // API لتحديث الإعدادات
        this.app.post('/api/settings', (req, res) => {
            try {
                const { smartTrading, liveDataManager } = req.body;

                if (smartTrading) {
                    Object.assign(this.smartTrading.settings, smartTrading);
                }

                if (liveDataManager) {
                    Object.assign(this.liveDataManager.settings, liveDataManager);
                }

                res.json({ success: true, message: 'Settings updated successfully' });

                // إشعار العملاء بالتحديث
                this.io.emit('settingsUpdated', { smartTrading, liveDataManager });

            } catch (error) {
                console.error('❌ Settings update error:', error);
                res.status(500).json({ error: error.message });
            }
        });

        // API للاشتراك في أداة مالية
        this.app.post('/api/subscribe/:instrumentId', async (req, res) => {
            try {
                const instrumentId = parseInt(req.params.instrumentId);
                const { timeframe } = req.body;

                const success = await this.liveDataManager.subscribeToInstrument(instrumentId, timeframe);

                if (success) {
                    res.json({ success: true, message: `Subscribed to instrument ${instrumentId}` });
                } else {
                    res.status(400).json({ error: 'Failed to subscribe' });
                }

            } catch (error) {
                console.error('❌ Subscription error:', error);
                res.status(500).json({ error: error.message });
            }
        });

        // API لإلغاء الاشتراك
        this.app.delete('/api/subscribe/:instrumentId', (req, res) => {
            try {
                const instrumentId = parseInt(req.params.instrumentId);
                const { timeframe } = req.body;

                const success = this.liveDataManager.unsubscribeFromInstrument(instrumentId, timeframe);

                if (success) {
                    res.json({ success: true, message: `Unsubscribed from instrument ${instrumentId}` });
                } else {
                    res.status(400).json({ error: 'Failed to unsubscribe' });
                }

            } catch (error) {
                console.error('❌ Unsubscription error:', error);
                res.status(500).json({ error: error.message });
            }
        });
    }

    /**
     * إعداد معالجات Socket.IO
     */
    setupSocketHandlers() {
        this.io.on('connection', (socket) => {
            console.log(`🔌 Client connected: ${socket.id}`);
            this.connectedClients.add(socket.id);

            // إرسال حالة النظام عند الاتصال
            socket.emit('systemStatus', this.getSystemStatus());

            // معالج طلب البيانات المباشرة
            socket.on('requestLiveData', (instrumentId) => {
                const data = this.liveDataManager.getLiveData(instrumentId);
                socket.emit('liveDataResponse', { instrumentId, data });
            });

            // معالج طلب تحليل السوق
            socket.on('requestMarketAnalysis', async (instrumentId) => {
                try {
                    const combinedData = this.liveDataManager.getCombinedData(instrumentId);
                    if (combinedData && combinedData.candles.length > 0) {
                        const signal = await this.smartTrading.analyzeMarket(instrumentId, combinedData.candles);
                        socket.emit('marketAnalysisResponse', { instrumentId, signal });
                    } else {
                        socket.emit('marketAnalysisResponse', { instrumentId, error: 'Insufficient data' });
                    }
                } catch (error) {
                    socket.emit('marketAnalysisResponse', { instrumentId, error: error.message });
                }
            });

            // معالج تبديل التداول الآلي
            socket.on('toggleAutoTrading', (data) => {
                try {
                    if (this.smartTrading && this.smartTrading.toggleAutoTrading) {
                        this.smartTrading.toggleAutoTrading(data.enabled, data.settings);
                        this.io.emit('autoTradingStatusChanged', { enabled: data.enabled });
                    }
                } catch (error) {
                    socket.emit('error', { message: 'Failed to toggle auto trading' });
                }
            });

            // معالج تنفيذ صفقة
            socket.on('executeTrade', async (tradeData) => {
                try {
                    const result = await this.executeTradeViaSocket(tradeData);
                    socket.emit('tradeExecuted', result);
                    this.io.emit('tradeUpdate', result);
                } catch (error) {
                    socket.emit('tradeError', { error: error.message });
                }
            });

            // معالج طلب تحديث البيانات
            socket.on('requestDataUpdate', async () => {
                try {
                    // إرسال بيانات الأزواج
                    const instruments = this.api.getInstruments();
                    const profitRates = this.api.getProfitRates();
                    socket.emit('instrumentsUpdated', instruments);

                    // إرسال بيانات الحساب
                    const accountData = this.api.getAccountBalance();
                    socket.emit('accountUpdate', accountData);
                } catch (error) {
                    socket.emit('error', { message: 'Failed to update data' });
                }
            });

            // معالج تحديث الأدوات
            socket.on('refreshInstruments', async () => {
                try {
                    await this.api.loadInstruments();
                    const instruments = this.api.getInstruments();
                    this.io.emit('instrumentsUpdated', instruments);
                } catch (error) {
                    socket.emit('error', { message: 'Failed to refresh instruments' });
                }
            });

            // معالج إعادة تعيين الإحصائيات اليومية
            socket.on('resetDailyStats', () => {
                try {
                    if (this.smartTrading && this.smartTrading.resetDailyStats) {
                        this.smartTrading.resetDailyStats();
                        this.io.emit('dailyStatsReset');
                    }
                } catch (error) {
                    socket.emit('error', { message: 'Failed to reset daily stats' });
                }
            });

            // معالج تصدير بيانات التداول
            socket.on('exportTradingData', () => {
                try {
                    const data = this.generateTradingDataExport();
                    socket.emit('tradingDataExport', data);
                } catch (error) {
                    socket.emit('error', { message: 'Failed to export trading data' });
                }
            });

            // معالج انقطاع الاتصال
            socket.on('disconnect', () => {
                console.log(`🔌 Client disconnected: ${socket.id}`);
                this.connectedClients.delete(socket.id);
            });
        });
    }

    /**
     * تنفيذ صفقة عبر Socket
     */
    async executeTradeViaSocket(tradeData) {
        try {
            const { asset, amount, direction, duration, isManual } = tradeData;

            // التحقق من صحة البيانات
            if (!asset || !amount || !direction) {
                throw new Error('Missing required trade parameters');
            }

            // تنفيذ الصفقة باستخدام API
            const result = await this.api.placeTrade(asset, amount, direction, duration || 300);

            if (result.success) {
                // تسجيل الصفقة
                const tradeRecord = {
                    id: result.tradeId || Date.now(),
                    asset: asset,
                    amount: amount,
                    direction: direction,
                    duration: duration || 300,
                    isManual: isManual || false,
                    timestamp: new Date(),
                    status: 'open'
                };

                // إشعار جميع العملاء
                this.io.emit('newTradeOpened', tradeRecord);

                return {
                    success: true,
                    trade: tradeRecord,
                    message: 'Trade executed successfully'
                };
            } else {
                throw new Error(result.reason || 'Trade execution failed');
            }

        } catch (error) {
            console.error('❌ Error executing trade via socket:', error);
            throw error;
        }
    }

    /**
     * إنتاج تصدير بيانات التداول
     */
    generateTradingDataExport() {
        try {
            const data = {
                timestamp: new Date().toISOString(),
                account: this.api.getAccountBalance(),
                instruments: this.api.getInstruments(),
                profitRates: this.api.getProfitRates(),
                performance: this.smartTrading ? this.smartTrading.getPerformanceReport() : null,
                systemStatus: this.getSystemStatus()
            };

            return {
                filename: `trading_data_${new Date().toISOString().split('T')[0]}.json`,
                data: JSON.stringify(data, null, 2),
                contentType: 'application/json'
            };

        } catch (error) {
            console.error('❌ Error generating trading data export:', error);
            throw error;
        }
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // أحداث البيانات المباشرة
        this.liveDataManager.on('liveDataUpdate', (data) => {
            this.io.emit('liveDataUpdate', data);
        });

        this.liveDataManager.on('candleCompleted', (data) => {
            this.io.emit('candleCompleted', data);
        });

        this.liveDataManager.on('performanceStats', (stats) => {
            this.io.emit('performanceStats', stats);
        });

        // أحداث API
        this.api.on('authenticated', () => {
            this.io.emit('apiStatus', { status: 'connected', timestamp: new Date() });
        });

        this.api.on('disconnected', (code, reason) => {
            this.io.emit('apiStatus', { status: 'disconnected', code, reason, timestamp: new Date() });
        });

        this.api.on('error', (error) => {
            this.io.emit('apiError', { error: error.message, timestamp: new Date() });
        });
    }

    /**
     * الحصول على حالة النظام
     */
    getSystemStatus() {
        return {
            server: {
                isRunning: this.isRunning,
                port: this.settings.port,
                connectedClients: this.connectedClients.size,
                uptime: process.uptime()
            },
            api: {
                isConnected: this.api.isConnected,
                isAuthenticated: this.api.isAuthenticated
            },
            liveDataManager: this.liveDataManager ? this.liveDataManager.getStatus() : null,
            smartTrading: this.smartTrading ? this.smartTrading.getPerformanceReport() : null,
            memory: process.memoryUsage(),
            timestamp: new Date()
        };
    }

    /**
     * بدء الخادم
     */
    async start() {
        return new Promise((resolve, reject) => {
            try {
                this.server.listen(this.settings.port, this.settings.host, () => {
                    this.isRunning = true;
                    console.log(`🌐 Web interface started on http://${this.settings.host}:${this.settings.port}`);

                    // بدء إرسال التحديثات الدورية
                    this.startPeriodicUpdates();

                    resolve();
                });

                this.server.on('error', (error) => {
                    console.error('❌ Server error:', error);
                    reject(error);
                });

            } catch (error) {
                console.error('❌ Failed to start web interface:', error);
                reject(error);
            }
        });
    }

    /**
     * إيقاف الخادم
     */
    async stop() {
        return new Promise((resolve) => {
            if (this.updateInterval) {
                clearInterval(this.updateInterval);
                this.updateInterval = null;
            }

            this.server.close(() => {
                this.isRunning = false;
                console.log('🛑 Web interface stopped');
                resolve();
            });
        });
    }

    /**
     * بدء التحديثات الدورية
     */
    startPeriodicUpdates() {
        // إرسال حالة النظام كل 5 ثوان
        this.updateInterval = setInterval(() => {
            const status = this.getSystemStatus();
            this.io.emit('systemStatusUpdate', status);
        }, 5000);
    }

    /**
     * إرسال إشعار لجميع العملاء
     */
    broadcast(event, data) {
        this.io.emit(event, data);
    }

    /**
     * إرسال إشعار لعميل محدد
     */
    sendToClient(clientId, event, data) {
        const socket = this.io.sockets.sockets.get(clientId);
        if (socket) {
            socket.emit(event, data);
        }
    }

    /**
     * الحصول على معلومات الخادم
     */
    getServerInfo() {
        return {
            host: this.settings.host,
            port: this.settings.port,
            isRunning: this.isRunning,
            connectedClients: this.connectedClients.size,
            uptime: process.uptime(),
            version: require('../package.json').version
        };
    }
}

module.exports = WebInterface;