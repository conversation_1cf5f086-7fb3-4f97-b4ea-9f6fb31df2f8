/**
 * @license
 * Copyright 2017 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../index';
import { backend } from '../index';
import { ALL_ENVS, describeWithFlags } from '../jasmine_util';
import { expectArraysClose } from '../test_util';
describeWithFlags('stridedSlice', ALL_ENVS, () => {
    it('with ellipsisMask=1', async () => {
        const t = tf.tensor2d([
            [1, 2, 3, 4, 5],
            [2, 3, 4, 5, 6],
            [3, 4, 5, 6, 7],
            [4, 5, 6, 7, 8],
            [5, 6, 7, 8, 9],
            [6, 7, 8, 9, 10],
            [7, 8, 9, 10, 11],
            [8, 8, 9, 10, 11],
            [9, 8, 9, 10, 11],
            [10, 8, 9, 10, 11],
        ]);
        const begin = [0, 4];
        const end = [0, 5];
        const strides = [1, 1];
        const beginMask = 0;
        const endMask = 0;
        const ellipsisMask = 1;
        const output = t.stridedSlice(begin, end, strides, beginMask, endMask, ellipsisMask);
        expect(output.shape).toEqual([10, 1]);
        expectArraysClose(await output.data(), [5, 6, 7, 8, 9, 10, 11, 11, 11, 11]);
    });
    it('with ellipsisMask=1, begin / end masks and start / end normalization', async () => {
        const t = tf.randomNormal([1, 6, 2006, 4]);
        const output = tf.stridedSlice(t, [0, 0, 0], [0, 2004, 0], [1, 1, 1], 6, 4, 1);
        expect(output.shape).toEqual([1, 6, 2004, 4]);
    });
    it('with ellipsisMask=1 and start / end normalization', async () => {
        const t = tf.tensor3d([
            [[1, 1, 1], [2, 2, 2]], [[3, 3, 3], [4, 4, 4]], [[5, 5, 5], [6, 6, 6]]
        ]);
        const begin = [1, 0];
        const end = [2, 1];
        const strides = [1, 1];
        const beginMask = 0;
        const endMask = 0;
        const ellipsisMask = 1;
        const output = tf.stridedSlice(t, begin, end, strides, beginMask, endMask, ellipsisMask);
        expect(output.shape).toEqual([3, 2, 1]);
        expectArraysClose(await output.data(), [1, 2, 3, 4, 5, 6]);
    });
    it('with ellipsisMask=2', async () => {
        const t = tf.tensor3d([
            [[1, 1, 1], [2, 2, 2]], [[3, 3, 3], [4, 4, 4]], [[5, 5, 5], [6, 6, 6]]
        ]);
        const begin = [1, 0, 0];
        const end = [2, 1, 3];
        const strides = [1, 1, 1];
        const beginMask = 0;
        const endMask = 0;
        const ellipsisMask = 2;
        const output = tf.stridedSlice(t, begin, end, strides, beginMask, endMask, ellipsisMask);
        expect(output.shape).toEqual([1, 2, 3]);
        expectArraysClose(await output.data(), [3, 3, 3, 4, 4, 4]);
    });
    it('with ellipsisMask=2 and start / end normalization', async () => {
        const t = tf.tensor4d([
            [[[1, 1], [1, 1], [1, 1]], [[2, 2], [2, 2], [2, 2]]],
            [[[3, 3], [3, 3], [3, 3]], [[4, 4], [4, 4], [4, 4]]],
            [[[5, 5], [5, 5], [5, 5]], [[6, 6], [6, 6], [6, 6]]]
        ]);
        const begin = [1, 0, 0];
        const end = [2, 1, 1];
        const strides = [1, 1, 1];
        const beginMask = 0;
        const endMask = 0;
        const ellipsisMask = 2;
        const output = tf.stridedSlice(t, begin, end, strides, beginMask, endMask, ellipsisMask);
        expect(output.shape).toEqual([1, 2, 3, 1]);
        expectArraysClose(await output.data(), [3, 3, 3, 4, 4, 4]);
    });
    it('both ellipsis mask and newAxisMask are set', async () => {
        const tensor = tf.tensor1d([0, 1, 2, 3]);
        const result = tf.stridedSlice(tensor, [0], [3], [2], 0, 0, 1, 1);
        expectArraysClose(await result.data(), [0, 1, 2, 3]);
    });
    it('both ellipsis mask and shrinkAxisMask are set', async () => {
        const tensor = tf.tensor1d([0, 1, 2, 3]);
        const result = tf.stridedSlice(tensor, [0], [3], [2], 0, 0, 1, 0, 1);
        expectArraysClose(await result.data(), [0, 1, 2, 3]);
    });
    it('stridedSlice with first axis being new', async () => {
        // Python slice code: t[tf.newaxis,0:3]
        const t = tf.tensor1d([0, 1, 2, 3]);
        const begin = [0, 0];
        const end = [1, 3];
        const strides = [1, 2];
        const beginMask = 0;
        const endMask = 0;
        const ellipsisMask = 0;
        const newAxisMask = 1;
        const output = tf.stridedSlice(t, begin, end, strides, beginMask, endMask, ellipsisMask, newAxisMask);
        expect(output.shape).toEqual([1, 2]);
        expectArraysClose(await output.data(), [0, 2]);
    });
    it('strided slice with several new axes', async () => {
        // Python slice code: t[1:2,tf.newaxis,0:3,tf.newaxis,2:5]
        const t = tf.zeros([2, 3, 4, 5]);
        const begin = [1, 0, 0, 0, 2];
        const end = [2, 1, 3, 1, 5];
        const strides = [1, 1, 1, 1, 1];
        const beginMask = 0;
        const endMask = 0;
        const ellipsisMask = 0;
        const newAxisMask = 0b1010;
        const output = tf.stridedSlice(t, begin, end, strides, beginMask, endMask, ellipsisMask, newAxisMask);
        expect(output.shape).toEqual([1, 1, 3, 1, 2, 5]);
        expectArraysClose(await output.data(), new Array(30).fill(0));
    });
    it('strided slice with new axes and shrink axes', () => {
        // Python slice code: t[1:2,tf.newaxis,1,tf.newaxis,2,2:5]
        const t = tf.zeros([2, 3, 4, 5]);
        const begin = [1, 0, 1, 0, 2, 2];
        const end = [2, 1, 2, 1, 3, 5];
        const strides = [1, 1, 1, 1, 1, 1];
        const beginMask = 0;
        const endMask = 0;
        const ellipsisMask = 0;
        const newAxisMask = 0b1010;
        const shrinkAxisMask = 0b10100;
        const output = tf.stridedSlice(t, begin, end, strides, beginMask, endMask, ellipsisMask, newAxisMask, shrinkAxisMask);
        expect(output.shape).toEqual([1, 1, 1, 3]);
    });
    it('stridedSlice should support 1d tensor', async () => {
        const tensor = tf.tensor1d([0, 1, 2, 3]);
        const output = tf.stridedSlice(tensor, [0], [3], [2]);
        expect(output.shape).toEqual([2]);
        expectArraysClose(await output.data(), [0, 2]);
    });
    it('stridedSlice should support 1d tensor', async () => {
        const tensor = tf.tensor1d([0, 1, 2, 3]);
        const output = tf.stridedSlice(tensor, [0], [3], [2]);
        expect(output.shape).toEqual([2]);
        expectArraysClose(await output.data(), [0, 2]);
    });
    it('stridedSlice with 1d tensor should be used by tensor directly', async () => {
        const t = tf.tensor1d([0, 1, 2, 3]);
        const output = t.stridedSlice([0], [3], [2]);
        expect(output.shape).toEqual([2]);
        expectArraysClose(await output.data(), [0, 2]);
    });
    it('stridedSlice should support 1d tensor empty result', async () => {
        const tensor = tf.tensor1d([0, 1, 2, 3]);
        const output = tf.stridedSlice(tensor, [10], [3], [2]);
        expect(output.shape).toEqual([0]);
        expectArraysClose(await output.data(), []);
    });
    it('stridedSlice should support 1d tensor negative begin', async () => {
        const tensor = tf.tensor1d([0, 1, 2, 3]);
        const output = tf.stridedSlice(tensor, [-3], [3], [1]);
        expect(output.shape).toEqual([2]);
        expectArraysClose(await output.data(), [1, 2]);
    });
    it('stridedSlice should support 1d tensor out of range begin', async () => {
        const tensor = tf.tensor1d([0, 1, 2, 3]);
        const output = tf.stridedSlice(tensor, [-5], [3], [1]);
        expect(output.shape).toEqual([3]);
        expectArraysClose(await output.data(), [0, 1, 2]);
    });
    it('stridedSlice should support 1d tensor negative end', async () => {
        const tensor = tf.tensor1d([0, 1, 2, 3]);
        const output = tf.stridedSlice(tensor, [1], [-2], [1]);
        expect(output.shape).toEqual([1]);
        expectArraysClose(await output.data(), [1]);
    });
    it('stridedSlice should support 1d tensor out of range end', async () => {
        const tensor = tf.tensor1d([0, 1, 2, 3]);
        const output = tf.stridedSlice(tensor, [-3], [5], [1]);
        expect(output.shape).toEqual([3]);
        expectArraysClose(await output.data(), [1, 2, 3]);
    });
    it('stridedSlice should support 1d tensor begin mask', async () => {
        const tensor = tf.tensor1d([0, 1, 2, 3]);
        const output = tf.stridedSlice(tensor, [1], [3], [1], 1);
        expect(output.shape).toEqual([3]);
        expectArraysClose(await output.data(), [0, 1, 2]);
    });
    it('stridedSlice should support 1d tensor nagtive begin and stride', async () => {
        const tensor = tf.tensor1d([0, 1, 2, 3]);
        const output = tf.stridedSlice(tensor, [-2], [-3], [-1]);
        expect(output.shape).toEqual([1]);
        expectArraysClose(await output.data(), [2]);
    });
    it('stridedSlice should support 1d tensor' +
        ' out of range begin and negative stride', async () => {
        const tensor = tf.tensor1d([0, 1, 2, 3]);
        const output = tf.stridedSlice(tensor, [5], [-2], [-1]);
        expect(output.shape).toEqual([1]);
        expectArraysClose(await output.data(), [3]);
    });
    it('stridedSlice should support 1d tensor nagtive end and stride', async () => {
        const tensor = tf.tensor1d([0, 1, 2, 3]);
        const output = tf.stridedSlice(tensor, [2], [-4], [-1]);
        expect(output.shape).toEqual([2]);
        expectArraysClose(await output.data(), [2, 1]);
    });
    it('stridedSlice should support 1d tensor' +
        ' out of range end and negative stride', async () => {
        const tensor = tf.tensor1d([0, 1, 2, 3]);
        const output = tf.stridedSlice(tensor, [-3], [-5], [-1]);
        expect(output.shape).toEqual([2]);
        expectArraysClose(await output.data(), [1, 0]);
    });
    it('stridedSlice should support 1d tensor end mask', async () => {
        const tensor = tf.tensor1d([0, 1, 2, 3]);
        const output = tf.stridedSlice(tensor, [1], [3], [1], 0, 1);
        expect(output.shape).toEqual([3]);
        expectArraysClose(await output.data(), [1, 2, 3]);
    });
    it('stridedSlice should support 1d tensor shrink axis mask', async () => {
        const tensor = tf.tensor1d([0, 1, 2, 3]);
        const output = tf.stridedSlice(tensor, [1], [3], [1], 0, 0, 0, 0, 1);
        expect(output.shape).toEqual([]);
        expectArraysClose(await output.data(), [1]);
    });
    it('stridedSlice should support 1d tensor negative stride', async () => {
        const tensor = tf.tensor1d([0, 1, 2, 3]);
        const output = tf.stridedSlice(tensor, [-1], [-4], [-1]);
        expect(output.shape).toEqual([3]);
        expectArraysClose(await output.data(), [3, 2, 1]);
    });
    it('stridedSlice should support 1d tensor even length stride', async () => {
        const tensor = tf.tensor1d([0, 1, 2, 3]);
        const output = tf.stridedSlice(tensor, [0], [2], [2]);
        expect(output.shape).toEqual([1]);
        expectArraysClose(await output.data(), [0]);
    });
    it('stridedSlice should support 1d tensor odd length stride', async () => {
        const tensor = tf.tensor1d([0, 1, 2, 3]);
        const output = tf.stridedSlice(tensor, [0], [3], [2]);
        expect(output.shape).toEqual([2]);
        expectArraysClose(await output.data(), [0, 2]);
    });
    it('stridedSlice should support 2d tensor identity', async () => {
        const tensor = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const output = tf.stridedSlice(tensor, [0, 0], [2, 3], [1, 1]);
        expect(output.shape).toEqual([2, 3]);
        expectArraysClose(await output.data(), [1, 2, 3, 4, 5, 6]);
    });
    it('stridedSlice should support 2d tensor', async () => {
        const tensor = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const output = tf.stridedSlice(tensor, [1, 0], [2, 2], [1, 1]);
        expect(output.shape).toEqual([1, 2]);
        expectArraysClose(await output.data(), [4, 5]);
    });
    it('stridedSlice should support 2d tensor strides', async () => {
        const tensor = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const output = tf.stridedSlice(tensor, [0, 0], [2, 3], [2, 2]);
        expect(output.shape).toEqual([1, 2]);
        expectArraysClose(await output.data(), [1, 3]);
    });
    it('stridedSlice with 2d tensor should be used by tensor directly', async () => {
        const t = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const output = t.stridedSlice([1, 0], [2, 2], [1, 1]);
        expect(output.shape).toEqual([1, 2]);
        expectArraysClose(await output.data(), [4, 5]);
    });
    it('stridedSlice should support 2d tensor negative strides', async () => {
        const tensor = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const output = tf.stridedSlice(tensor, [1, -1], [2, -4], [2, -1]);
        expect(output.shape).toEqual([1, 3]);
        expectArraysClose(await output.data(), [6, 5, 4]);
    });
    it('stridedSlice should support 2d tensor begin mask', async () => {
        const tensor = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const output = tf.stridedSlice(tensor, [1, 0], [2, 2], [1, 1], 1);
        expect(output.shape).toEqual([2, 2]);
        expectArraysClose(await output.data(), [1, 2, 4, 5]);
    });
    it('stridedSlice should support 2d tensor shrink mask', async () => {
        const tensor = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const output = tf.stridedSlice(tensor, [1, 0], [2, 2], [1, 1], 0, 0, 0, 0, 1);
        expect(output.shape).toEqual([2]);
        expectArraysClose(await output.data(), [4, 5]);
    });
    it('stridedSlice should support 2d tensor end mask', async () => {
        const tensor = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const output = tf.stridedSlice(tensor, [1, 0], [2, 2], [1, 1], 0, 2);
        expect(output.shape).toEqual([1, 3]);
        expectArraysClose(await output.data(), [4, 5, 6]);
    });
    it('stridedSlice should support 2d tensor' +
        ' negative strides and begin mask', async () => {
        const tensor = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const output = tf.stridedSlice(tensor, [1, -2], [2, -4], [1, -1], 2);
        expect(output.shape).toEqual([1, 3]);
        expectArraysClose(await output.data(), [6, 5, 4]);
    });
    it('stridedSlice should support 2d tensor' +
        ' negative strides and end mask', async () => {
        const tensor = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const output = tf.stridedSlice(tensor, [1, -2], [2, -3], [1, -1], 0, 2);
        expect(output.shape).toEqual([1, 2]);
        expectArraysClose(await output.data(), [5, 4]);
    });
    it('stridedSlice should support 3d tensor identity', async () => {
        const tensor = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [2, 3, 2]);
        const output = tf.stridedSlice(tensor, [0, 0, 0], [2, 3, 2], [1, 1, 1]);
        expect(output.shape).toEqual([2, 3, 2]);
        expectArraysClose(await output.data(), [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]);
    });
    it('stridedSlice should support 3d tensor negative stride', async () => {
        const tensor = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [2, 3, 2]);
        const output = tf.stridedSlice(tensor, [-1, -1, -1], [-3, -4, -3], [-1, -1, -1]);
        expect(output.shape).toEqual([2, 3, 2]);
        expectArraysClose(await output.data(), [12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1]);
    });
    it('stridedSlice should support 3d tensor strided 2', async () => {
        const tensor = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [2, 3, 2]);
        const output = tf.stridedSlice(tensor, [0, 0, 0], [2, 3, 2], [2, 2, 2]);
        expect(output.shape).toEqual([1, 2, 1]);
        expectArraysClose(await output.data(), [1, 5]);
    });
    it('stridedSlice should support 3d tensor shrink mask', async () => {
        const tensor = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [2, 3, 2]);
        const output = tf.stridedSlice(tensor, [0, 0, 0], [2, 3, 2], [1, 1, 1], 0, 0, 0, 0, 1);
        expect(output.shape).toEqual([3, 2]);
        expectArraysClose(await output.data(), [1, 2, 3, 4, 5, 6]);
    });
    it('stridedSlice should support 3d with smaller length of begin array', async () => {
        const tensor = tf.tensor4d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [2, 3, 1, 2]);
        const output = tf.stridedSlice(tensor, [1, 0], [2, 3], [1, 1], 0, 0, 0, 0, 0);
        expect(output.shape).toEqual([1, 3, 1, 2]);
        expectArraysClose(await output.data(), [7, 8, 9, 10, 11, 12]);
    });
    it('stridedSlice should support 3d with smaller length of end array', async () => {
        const tensor = tf.tensor4d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [2, 3, 1, 2]);
        const output = tf.stridedSlice(tensor, [1, 0], [2, 3], [1, 1], 0, 0, 0, 0, 0);
        expect(output.shape).toEqual([1, 3, 1, 2]);
        expectArraysClose(await output.data(), [7, 8, 9, 10, 11, 12]);
    });
    it('stridedSlice should throw when passed a non-tensor', () => {
        expect(() => tf.stridedSlice({}, [0], [0], [1]))
            .toThrowError(/Argument 'x' passed to 'stridedSlice' must be a Tensor/);
    });
    it('stridedSlice should handle negative end with ellipsisMask', () => {
        const a = tf.ones([1, 240, 1, 10]);
        const output = tf.stridedSlice(a, [0, 0, 0], [0, -1, 0], [1, 1, 1], 3, 1, 4);
        expect(output.shape).toEqual([1, 239, 1, 10]);
    });
    it('stridedSlice should handle negative begin with ellipsis_mask', () => {
        const a = tf.ones([1, 36, 17, 3]);
        const output = tf.stridedSlice(a, [0, -1], [0, 0], [1, 1], 0, 2, 1, 0, 0);
        expect(output.shape).toEqual([1, 36, 17, 1]);
    });
    it('accepts a tensor-like object', async () => {
        const tensor = [0, 1, 2, 3];
        const output = tf.stridedSlice(tensor, [0], [3], [2]);
        expect(output.shape).toEqual([2]);
        expectArraysClose(await output.data(), [0, 2]);
    });
    it('accepts int32 tensor', async () => {
        if (backend() && backend().floatPrecision() === 32) {
            // TODO: Use skip() instead when it is implemented
            const tensor = tf.tensor2d([1, 2, 3, 4, 12345678, 6], [2, 3], 'int32');
            const output = tf.stridedSlice(tensor, [1, 0], [2, 2], [1, 1]);
            expect(output.shape).toEqual([1, 2]);
            expect(output.dtype).toEqual('int32');
            expectArraysClose(await output.data(), [4, 12345678]);
        }
    });
    it('ensure no memory leak', async () => {
        const numTensorsBefore = tf.memory().numTensors;
        const numDataIdBefore = tf.engine().backend.numDataIds();
        const tensor = tf.tensor1d([0, 1, 2, 3]);
        const output = tf.stridedSlice(tensor, [0], [3], [2]);
        expect(output.shape).toEqual([2]);
        expectArraysClose(await output.data(), [0, 2]);
        tensor.dispose();
        output.dispose();
        const numTensorsAfter = tf.memory().numTensors;
        const numDataIdAfter = tf.engine().backend.numDataIds();
        expect(numTensorsAfter).toBe(numTensorsBefore);
        expect(numDataIdAfter).toBe(numDataIdBefore);
    });
});
//# sourceMappingURL=data:application/json;base64,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