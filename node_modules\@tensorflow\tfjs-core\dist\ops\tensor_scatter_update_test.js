/**
 * @license
 * Copyright 2022 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../index';
import { ALL_ENVS, describeWithFlags } from '../jasmine_util';
import { expectArraysClose } from '../test_util';
describeWithFlags('tensorScatterUpdate', ALL_ENVS, () => {
    it('should work for 2d', async () => {
        const tensor = tf.ones([5, 3], 'int32');
        const indices = tf.tensor2d([0, 4, 2], [3, 1], 'int32');
        const updates = tf.tensor2d([100, 101, 102, 777, 778, 779, 1000, 1001, 1002], [3, 3], 'int32');
        const result = tf.tensorScatterUpdate(tensor, indices, updates);
        expect(result.shape).toEqual(tensor.shape);
        expect(result.dtype).toEqual(tensor.dtype);
        expectArraysClose(await result.data(), [100, 101, 102, 1, 1, 1, 1000, 1001, 1002, 1, 1, 1, 777, 778, 779]);
    });
    it('should work for simple 1d', async () => {
        const shape = [5];
        const tensor = tf.ones(shape);
        const indices = tf.tensor2d([3], [1, 1], 'int32');
        const updates = tf.tensor1d([101], 'float32');
        const result = tf.tensorScatterUpdate(tensor, indices, updates);
        expect(result.shape).toEqual(shape);
        expect(result.dtype).toEqual(updates.dtype);
        expectArraysClose(await result.data(), [1, 1, 1, 101, 1]);
    });
    it('should work for multiple 1d', async () => {
        const shape = [5];
        const indices = tf.tensor2d([0, 4, 2], [3, 1], 'int32');
        const updates = tf.tensor1d([100, 101, 102], 'float32');
        const tensor = tf.ones(shape);
        const result = tf.tensorScatterUpdate(tensor, indices, updates);
        expect(result.shape).toEqual(shape);
        expect(result.dtype).toEqual(updates.dtype);
        expectArraysClose(await result.data(), [100, 1, 102, 1, 101]);
    });
    it('should work for high rank updates', async () => {
        const indices = tf.tensor2d([0, 2], [2, 1], 'int32');
        const updates = tf.tensor3d([
            5, 5, 5, 5, 6, 6, 6, 6, 7, 7, 7, 7, 8, 8, 8, 8,
            5, 5, 5, 5, 6, 6, 6, 6, 7, 7, 7, 7, 8, 8, 8, 8
        ], [2, 4, 4], 'float32');
        const shape = [4, 4, 4];
        const tensor = tf.ones(shape);
        const result = tf.tensorScatterUpdate(tensor, indices, updates);
        expect(result.shape).toEqual(shape);
        expect(result.dtype).toEqual(updates.dtype);
        expectArraysClose(await result.data(), [
            5, 5, 5, 5, 6, 6, 6, 6, 7, 7, 7, 7, 8, 8, 8, 8, 1, 1, 1, 1, 1, 1,
            1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5, 5, 5, 5, 6, 6, 6, 6, 7, 7, 7, 7,
            8, 8, 8, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1
        ]);
    });
    it('should work for high rank indices', async () => {
        const indices = tf.tensor2d([0, 2, 0, 1], [2, 2], 'int32');
        const updates = tf.tensor1d([10, 20], 'float32');
        const shape = [3, 3];
        const tensor = tf.ones(shape);
        const result = tf.tensorScatterUpdate(tensor, indices, updates);
        expect(result.shape).toEqual(shape);
        expect(result.dtype).toEqual(updates.dtype);
        expectArraysClose(await result.data(), [1, 20, 10, 1, 1, 1, 1, 1, 1]);
    });
    it('should work for high rank indices and update', () => {
        const indices = tf.tensor2d([1, 1, 1, 1, 1, 1], [3, 2], 'int32');
        const updates = tf.ones([3, 256], 'float32');
        const shape = [2, 2, 256];
        const tensor = tf.ones(shape);
        const result = tf.tensorScatterUpdate(tensor, indices, updates);
        expect(result.shape).toEqual(shape);
        expect(result.dtype).toEqual(updates.dtype);
    });
    it('should work for tensorLike input', async () => {
        const indices = [[0], [4], [2]];
        const updates = [100, 101, 102];
        const shape = [5];
        const tensor = tf.ones(shape);
        const result = tf.tensorScatterUpdate(tensor, indices, updates);
        expect(result.shape).toEqual(shape);
        expect(result.dtype).toEqual('float32');
        expectArraysClose(await result.data(), [100, 1, 102, 1, 101]);
    });
    it('should throw error when indices type is not int32', () => {
        const indices = tf.tensor2d([0, 2, 1, 1], [2, 2], 'float32');
        const updates = tf.tensor1d([10, 20], 'float32');
        const shape = [3, 3];
        const tensor = tf.ones(shape);
        expect(() => tf.tensorScatterUpdate(tensor, indices, updates)).toThrow();
    });
    it('should throw error when tensor and updates type does not match', () => {
        const indices = tf.tensor2d([0, 2, 1, 1], [2, 2], 'float32');
        const updates = tf.tensor1d([10, 20], 'int32');
        const shape = [3, 3];
        const tensor = tf.ones(shape, 'float32');
        expect(() => tf.tensorScatterUpdate(tensor, indices, updates)).toThrow();
    });
    it('should throw error when indices and update mismatch', () => {
        const indices = tf.tensor2d([0, 4, 2], [3, 1], 'int32');
        const updates = tf.tensor2d([100, 101, 102, 103, 777, 778, 779, 780, 10000, 10001, 10002, 10004], [3, 4], 'float32');
        const shape = [5, 3];
        const tensor = tf.ones(shape);
        expect(() => tf.tensorScatterUpdate(tensor, indices, updates)).toThrow();
    });
    it('should throw error when indices and update count mismatch', () => {
        const indices = tf.tensor2d([0, 4, 2], [3, 1], 'int32');
        const updates = tf.tensor2d([100, 101, 102, 10000, 10001, 10002], [2, 3], 'float32');
        const shape = [5, 3];
        const tensor = tf.ones(shape);
        expect(() => tf.tensorScatterUpdate(tensor, indices, updates)).toThrow();
    });
    it('should throw error when indices are scalar', () => {
        const indices = tf.scalar(1, 'int32');
        const updates = tf.tensor2d([100, 101, 102, 10000, 10001, 10002], [2, 3], 'float32');
        const shape = [5, 3];
        const tensor = tf.ones(shape);
        expect(() => tf.tensorScatterUpdate(tensor, indices, updates)).toThrow();
    });
    it('should throw error when update is scalar', () => {
        const indices = tf.tensor2d([0, 4, 2], [3, 1], 'int32');
        const updates = tf.scalar(1, 'float32');
        const shape = [5, 3];
        const tensor = tf.ones(shape);
        expect(() => tf.tensorScatterUpdate(tensor, indices, updates)).toThrow();
    });
});
//# sourceMappingURL=data:application/json;base64,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