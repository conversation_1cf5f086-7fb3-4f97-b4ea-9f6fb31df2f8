{"version": 3, "sources": ["config/versionTemplate.txt", "lib/es6-promise/utils.js", "lib/es6-promise/asap.js", "lib/es6-promise/then.js", "lib/es6-promise/promise/resolve.js", "lib/es6-promise/-internal.js", "lib/es6-promise/enumerator.js", "lib/es6-promise/promise/all.js", "lib/es6-promise/promise/race.js", "lib/es6-promise/promise/reject.js", "lib/es6-promise/promise.js", "lib/es6-promise/polyfill.js", "lib/es6-promise.js", "lib/es6-promise.auto.js"], "sourcesContent": ["/*!\n * @overview es6-promise - a tiny implementation of Promises/A+.\n * @copyright Copyright (c) 2014 <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and contributors (Conversion to ES6 API by <PERSON>)\n * @license   Licensed under MIT license\n *            See https://raw.githubusercontent.com/stefanpenner/es6-promise/master/LICENSE\n * @version   v4.2.8+1e68dce6\n */\n", "export function objectOrFunction(x) {\n  var type = typeof x;\n  return x !== null && (type === 'object' || type === 'function');\n}\n\nexport function isFunction(x) {\n  return typeof x === 'function';\n}\n\nexport function isMaybeThenable(x) {\n  return x !== null && typeof x === 'object';\n}\n\nvar _isArray = void 0;\nif (Array.isArray) {\n  _isArray = Array.isArray;\n} else {\n  _isArray = function (x) {\n    return Object.prototype.toString.call(x) === '[object Array]';\n  };\n}\n\nexport var isArray = _isArray;", "var len = 0;\nvar vertxNext = void 0;\nvar customSchedulerFn = void 0;\n\nexport var asap = function asap(callback, arg) {\n  queue[len] = callback;\n  queue[len + 1] = arg;\n  len += 2;\n  if (len === 2) {\n    // If len is 2, that means that we need to schedule an async flush.\n    // If additional callbacks are queued before the queue is flushed, they\n    // will be processed by this flush that we are scheduling.\n    if (customSchedulerFn) {\n      customSchedulerFn(flush);\n    } else {\n      scheduleFlush();\n    }\n  }\n};\n\nexport function setScheduler(scheduleFn) {\n  customSchedulerFn = scheduleFn;\n}\n\nexport function setAsap(asapFn) {\n  asap = asapFn;\n}\n\nvar browserWindow = typeof window !== 'undefined' ? window : undefined;\nvar browserGlobal = browserWindow || {};\nvar BrowserMutationObserver = browserGlobal.MutationObserver || browserGlobal.WebKitMutationObserver;\nvar isNode = typeof self === 'undefined' && typeof process !== 'undefined' && {}.toString.call(process) === '[object process]';\n\n// test for web worker but not in IE10\nvar isWorker = typeof Uint8ClampedArray !== 'undefined' && typeof importScripts !== 'undefined' && typeof MessageChannel !== 'undefined';\n\n// node\nfunction useNextTick() {\n  // node version 0.10.x displays a deprecation warning when nextTick is used recursively\n  // see https://github.com/cujojs/when/issues/410 for details\n  return function () {\n    return process.nextTick(flush);\n  };\n}\n\n// vertx\nfunction useVertxTimer() {\n  if (typeof vertxNext !== 'undefined') {\n    return function () {\n      vertxNext(flush);\n    };\n  }\n\n  return useSetTimeout();\n}\n\nfunction useMutationObserver() {\n  var iterations = 0;\n  var observer = new BrowserMutationObserver(flush);\n  var node = document.createTextNode('');\n  observer.observe(node, { characterData: true });\n\n  return function () {\n    node.data = iterations = ++iterations % 2;\n  };\n}\n\n// web worker\nfunction useMessageChannel() {\n  var channel = new MessageChannel();\n  channel.port1.onmessage = flush;\n  return function () {\n    return channel.port2.postMessage(0);\n  };\n}\n\nfunction useSetTimeout() {\n  // Store setTimeout reference so es6-promise will be unaffected by\n  // other code modifying setTimeout (like sinon.useFakeTimers())\n  var globalSetTimeout = setTimeout;\n  return function () {\n    return globalSetTimeout(flush, 1);\n  };\n}\n\nvar queue = new Array(1000);\nfunction flush() {\n  for (var i = 0; i < len; i += 2) {\n    var callback = queue[i];\n    var arg = queue[i + 1];\n\n    callback(arg);\n\n    queue[i] = undefined;\n    queue[i + 1] = undefined;\n  }\n\n  len = 0;\n}\n\nfunction attemptVertx() {\n  try {\n    var vertx = Function('return this')().require('vertx');\n    vertxNext = vertx.runOnLoop || vertx.runOnContext;\n    return useVertxTimer();\n  } catch (e) {\n    return useSetTimeout();\n  }\n}\n\nvar scheduleFlush = void 0;\n// Decide what async method to use to triggering processing of queued callbacks:\nif (isNode) {\n  scheduleFlush = useNextTick();\n} else if (BrowserMutationObserver) {\n  scheduleFlush = useMutationObserver();\n} else if (isWorker) {\n  scheduleFlush = useMessageChannel();\n} else if (browserWindow === undefined && typeof require === 'function') {\n  scheduleFlush = attemptVertx();\n} else {\n  scheduleFlush = useSetTimeout();\n}", "import { invokeCallback, subscribe, FULFILLED, REJECTED, noop, makePromise, PROMISE_ID } from './-internal';\n\nimport { asap } from './asap';\n\nexport default function then(onFulfillment, onRejection) {\n  var parent = this;\n\n  var child = new this.constructor(noop);\n\n  if (child[PROMISE_ID] === undefined) {\n    makePromise(child);\n  }\n\n  var _state = parent._state;\n\n\n  if (_state) {\n    var callback = arguments[_state - 1];\n    asap(function () {\n      return invokeCallback(_state, child, callback, parent._result);\n    });\n  } else {\n    subscribe(parent, child, onFulfillment, onRejection);\n  }\n\n  return child;\n}", "import { noop, resolve as _resolve } from '../-internal';\n\n/**\n  `Promise.resolve` returns a promise that will become resolved with the\n  passed `value`. It is shorthand for the following:\n\n  ```javascript\n  let promise = new Promise(function(resolve, reject){\n    resolve(1);\n  });\n\n  promise.then(function(value){\n    // value === 1\n  });\n  ```\n\n  Instead of writing the above, your code now simply becomes the following:\n\n  ```javascript\n  let promise = Promise.resolve(1);\n\n  promise.then(function(value){\n    // value === 1\n  });\n  ```\n\n  @method resolve\n  @static\n  @param {Any} value value that the returned promise will be resolved with\n  Useful for tooling.\n  @return {Promise} a promise that will become fulfilled with the given\n  `value`\n*/\nexport default function resolve(object) {\n  /*jshint validthis:true */\n  var Constructor = this;\n\n  if (object && typeof object === 'object' && object.constructor === Constructor) {\n    return object;\n  }\n\n  var promise = new Constructor(noop);\n  _resolve(promise, object);\n  return promise;\n}", "import { objectOrFunction, isFunction } from './utils';\n\nimport { asap } from './asap';\n\nimport originalThen from './then';\nimport originalResolve from './promise/resolve';\n\nexport var PROMISE_ID = Math.random().toString(36).substring(2);\n\nfunction noop() {}\n\nvar PENDING = void 0;\nvar FULFILLED = 1;\nvar REJECTED = 2;\n\nfunction selfFulfillment() {\n  return new TypeError(\"You cannot resolve a promise with itself\");\n}\n\nfunction cannotReturnOwn() {\n  return new TypeError('A promises callback cannot return that same promise.');\n}\n\nfunction tryThen(then, value, fulfillmentHandler, rejectionHandler) {\n  try {\n    then.call(value, fulfillmentHandler, rejectionHandler);\n  } catch (e) {\n    return e;\n  }\n}\n\nfunction handleForeignThenable(promise, thenable, then) {\n  asap(function (promise) {\n    var sealed = false;\n    var error = tryThen(then, thenable, function (value) {\n      if (sealed) {\n        return;\n      }\n      sealed = true;\n      if (thenable !== value) {\n        resolve(promise, value);\n      } else {\n        fulfill(promise, value);\n      }\n    }, function (reason) {\n      if (sealed) {\n        return;\n      }\n      sealed = true;\n\n      reject(promise, reason);\n    }, 'Settle: ' + (promise._label || ' unknown promise'));\n\n    if (!sealed && error) {\n      sealed = true;\n      reject(promise, error);\n    }\n  }, promise);\n}\n\nfunction handleOwnThenable(promise, thenable) {\n  if (thenable._state === FULFILLED) {\n    fulfill(promise, thenable._result);\n  } else if (thenable._state === REJECTED) {\n    reject(promise, thenable._result);\n  } else {\n    subscribe(thenable, undefined, function (value) {\n      return resolve(promise, value);\n    }, function (reason) {\n      return reject(promise, reason);\n    });\n  }\n}\n\nfunction handleMaybeThenable(promise, maybeThenable, then) {\n  if (maybeThenable.constructor === promise.constructor && then === originalThen && maybeThenable.constructor.resolve === originalResolve) {\n    handleOwnThenable(promise, maybeThenable);\n  } else {\n    if (then === undefined) {\n      fulfill(promise, maybeThenable);\n    } else if (isFunction(then)) {\n      handleForeignThenable(promise, maybeThenable, then);\n    } else {\n      fulfill(promise, maybeThenable);\n    }\n  }\n}\n\nfunction resolve(promise, value) {\n  if (promise === value) {\n    reject(promise, selfFulfillment());\n  } else if (objectOrFunction(value)) {\n    var then = void 0;\n    try {\n      then = value.then;\n    } catch (error) {\n      reject(promise, error);\n      return;\n    }\n    handleMaybeThenable(promise, value, then);\n  } else {\n    fulfill(promise, value);\n  }\n}\n\nfunction publishRejection(promise) {\n  if (promise._onerror) {\n    promise._onerror(promise._result);\n  }\n\n  publish(promise);\n}\n\nfunction fulfill(promise, value) {\n  if (promise._state !== PENDING) {\n    return;\n  }\n\n  promise._result = value;\n  promise._state = FULFILLED;\n\n  if (promise._subscribers.length !== 0) {\n    asap(publish, promise);\n  }\n}\n\nfunction reject(promise, reason) {\n  if (promise._state !== PENDING) {\n    return;\n  }\n  promise._state = REJECTED;\n  promise._result = reason;\n\n  asap(publishRejection, promise);\n}\n\nfunction subscribe(parent, child, onFulfillment, onRejection) {\n  var _subscribers = parent._subscribers;\n  var length = _subscribers.length;\n\n\n  parent._onerror = null;\n\n  _subscribers[length] = child;\n  _subscribers[length + FULFILLED] = onFulfillment;\n  _subscribers[length + REJECTED] = onRejection;\n\n  if (length === 0 && parent._state) {\n    asap(publish, parent);\n  }\n}\n\nfunction publish(promise) {\n  var subscribers = promise._subscribers;\n  var settled = promise._state;\n\n  if (subscribers.length === 0) {\n    return;\n  }\n\n  var child = void 0,\n      callback = void 0,\n      detail = promise._result;\n\n  for (var i = 0; i < subscribers.length; i += 3) {\n    child = subscribers[i];\n    callback = subscribers[i + settled];\n\n    if (child) {\n      invokeCallback(settled, child, callback, detail);\n    } else {\n      callback(detail);\n    }\n  }\n\n  promise._subscribers.length = 0;\n}\n\nfunction invokeCallback(settled, promise, callback, detail) {\n  var hasCallback = isFunction(callback),\n      value = void 0,\n      error = void 0,\n      succeeded = true;\n\n  if (hasCallback) {\n    try {\n      value = callback(detail);\n    } catch (e) {\n      succeeded = false;\n      error = e;\n    }\n\n    if (promise === value) {\n      reject(promise, cannotReturnOwn());\n      return;\n    }\n  } else {\n    value = detail;\n  }\n\n  if (promise._state !== PENDING) {\n    // noop\n  } else if (hasCallback && succeeded) {\n    resolve(promise, value);\n  } else if (succeeded === false) {\n    reject(promise, error);\n  } else if (settled === FULFILLED) {\n    fulfill(promise, value);\n  } else if (settled === REJECTED) {\n    reject(promise, value);\n  }\n}\n\nfunction initializePromise(promise, resolver) {\n  try {\n    resolver(function resolvePromise(value) {\n      resolve(promise, value);\n    }, function rejectPromise(reason) {\n      reject(promise, reason);\n    });\n  } catch (e) {\n    reject(promise, e);\n  }\n}\n\nvar id = 0;\nfunction nextId() {\n  return id++;\n}\n\nfunction makePromise(promise) {\n  promise[PROMISE_ID] = id++;\n  promise._state = undefined;\n  promise._result = undefined;\n  promise._subscribers = [];\n}\n\nexport { nextId, makePromise, noop, resolve, reject, fulfill, subscribe, publish, publishRejection, initializePromise, invokeCallback, FULFILLED, REJECTED, PENDING, handleMaybeThenable };", "function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nimport { isArray, isMaybeThenable } from './utils';\nimport { noop, reject, fulfill, subscribe, FULFILLED, REJECTED, PENDING, handleMaybeThenable } from './-internal';\n\nimport then from './then';\nimport Promise from './promise';\nimport originalResolve from './promise/resolve';\nimport originalThen from './then';\nimport { makePromise, PROMISE_ID } from './-internal';\n\nfunction validationError() {\n  return new Error('Array Methods must be provided an Array');\n};\n\nvar Enumerator = function () {\n  function Enumerator(Constructor, input) {\n    this._instanceConstructor = Constructor;\n    this.promise = new Constructor(noop);\n\n    if (!this.promise[PROMISE_ID]) {\n      makePromise(this.promise);\n    }\n\n    if (isArray(input)) {\n      this.length = input.length;\n      this._remaining = input.length;\n\n      this._result = new Array(this.length);\n\n      if (this.length === 0) {\n        fulfill(this.promise, this._result);\n      } else {\n        this.length = this.length || 0;\n        this._enumerate(input);\n        if (this._remaining === 0) {\n          fulfill(this.promise, this._result);\n        }\n      }\n    } else {\n      reject(this.promise, validationError());\n    }\n  }\n\n  Enumerator.prototype._enumerate = function _enumerate(input) {\n    for (var i = 0; this._state === PENDING && i < input.length; i++) {\n      this._eachEntry(input[i], i);\n    }\n  };\n\n  Enumerator.prototype._eachEntry = function _eachEntry(entry, i) {\n    var c = this._instanceConstructor;\n    var resolve = c.resolve;\n\n\n    if (resolve === originalResolve) {\n      var _then = void 0;\n      var error = void 0;\n      var didError = false;\n      try {\n        _then = entry.then;\n      } catch (e) {\n        didError = true;\n        error = e;\n      }\n\n      if (_then === originalThen && entry._state !== PENDING) {\n        this._settledAt(entry._state, i, entry._result);\n      } else if (typeof _then !== 'function') {\n        this._remaining--;\n        this._result[i] = entry;\n      } else if (c === Promise) {\n        var promise = new c(noop);\n        if (didError) {\n          reject(promise, error);\n        } else {\n          handleMaybeThenable(promise, entry, _then);\n        }\n        this._willSettleAt(promise, i);\n      } else {\n        this._willSettleAt(new c(function (resolve) {\n          return resolve(entry);\n        }), i);\n      }\n    } else {\n      this._willSettleAt(resolve(entry), i);\n    }\n  };\n\n  Enumerator.prototype._settledAt = function _settledAt(state, i, value) {\n    var promise = this.promise;\n\n\n    if (promise._state === PENDING) {\n      this._remaining--;\n\n      if (state === REJECTED) {\n        reject(promise, value);\n      } else {\n        this._result[i] = value;\n      }\n    }\n\n    if (this._remaining === 0) {\n      fulfill(promise, this._result);\n    }\n  };\n\n  Enumerator.prototype._willSettleAt = function _willSettleAt(promise, i) {\n    var enumerator = this;\n\n    subscribe(promise, undefined, function (value) {\n      return enumerator._settledAt(FULFILLED, i, value);\n    }, function (reason) {\n      return enumerator._settledAt(REJECTED, i, reason);\n    });\n  };\n\n  return Enumerator;\n}();\n\nexport default Enumerator;\n;", "import Enumerator from '../enumerator';\n\n/**\n  `Promise.all` accepts an array of promises, and returns a new promise which\n  is fulfilled with an array of fulfillment values for the passed promises, or\n  rejected with the reason of the first passed promise to be rejected. It casts all\n  elements of the passed iterable to promises as it runs this algorithm.\n\n  Example:\n\n  ```javascript\n  let promise1 = resolve(1);\n  let promise2 = resolve(2);\n  let promise3 = resolve(3);\n  let promises = [ promise1, promise2, promise3 ];\n\n  Promise.all(promises).then(function(array){\n    // The array here would be [ 1, 2, 3 ];\n  });\n  ```\n\n  If any of the `promises` given to `all` are rejected, the first promise\n  that is rejected will be given as an argument to the returned promises's\n  rejection handler. For example:\n\n  Example:\n\n  ```javascript\n  let promise1 = resolve(1);\n  let promise2 = reject(new Error(\"2\"));\n  let promise3 = reject(new Error(\"3\"));\n  let promises = [ promise1, promise2, promise3 ];\n\n  Promise.all(promises).then(function(array){\n    // Code here never runs because there are rejected promises!\n  }, function(error) {\n    // error.message === \"2\"\n  });\n  ```\n\n  @method all\n  @static\n  @param {Array} entries array of promises\n  @param {String} label optional string for labeling the promise.\n  Useful for tooling.\n  @return {Promise} promise that is fulfilled when all `promises` have been\n  fulfilled, or rejected if any of them become rejected.\n  @static\n*/\nexport default function all(entries) {\n  return new Enumerator(this, entries).promise;\n}", "import { isArray } from \"../utils\";\n\n/**\n  `Promise.race` returns a new promise which is settled in the same way as the\n  first passed promise to settle.\n\n  Example:\n\n  ```javascript\n  let promise1 = new Promise(function(resolve, reject){\n    setTimeout(function(){\n      resolve('promise 1');\n    }, 200);\n  });\n\n  let promise2 = new Promise(function(resolve, reject){\n    setTimeout(function(){\n      resolve('promise 2');\n    }, 100);\n  });\n\n  Promise.race([promise1, promise2]).then(function(result){\n    // result === 'promise 2' because it was resolved before promise1\n    // was resolved.\n  });\n  ```\n\n  `Promise.race` is deterministic in that only the state of the first\n  settled promise matters. For example, even if other promises given to the\n  `promises` array argument are resolved, but the first settled promise has\n  become rejected before the other promises became fulfilled, the returned\n  promise will become rejected:\n\n  ```javascript\n  let promise1 = new Promise(function(resolve, reject){\n    setTimeout(function(){\n      resolve('promise 1');\n    }, 200);\n  });\n\n  let promise2 = new Promise(function(resolve, reject){\n    setTimeout(function(){\n      reject(new Error('promise 2'));\n    }, 100);\n  });\n\n  Promise.race([promise1, promise2]).then(function(result){\n    // Code here never runs\n  }, function(reason){\n    // reason.message === 'promise 2' because promise 2 became rejected before\n    // promise 1 became fulfilled\n  });\n  ```\n\n  An example real-world use case is implementing timeouts:\n\n  ```javascript\n  Promise.race([ajax('foo.json'), timeout(5000)])\n  ```\n\n  @method race\n  @static\n  @param {Array} promises array of promises to observe\n  Useful for tooling.\n  @return {Promise} a promise which settles in the same way as the first passed\n  promise to settle.\n*/\nexport default function race(entries) {\n  /*jshint validthis:true */\n  var Constructor = this;\n\n  if (!isArray(entries)) {\n    return new Constructor(function (_, reject) {\n      return reject(new TypeError('You must pass an array to race.'));\n    });\n  } else {\n    return new Constructor(function (resolve, reject) {\n      var length = entries.length;\n      for (var i = 0; i < length; i++) {\n        Constructor.resolve(entries[i]).then(resolve, reject);\n      }\n    });\n  }\n}", "import { noop, reject as _reject } from '../-internal';\n\n/**\n  `Promise.reject` returns a promise rejected with the passed `reason`.\n  It is shorthand for the following:\n\n  ```javascript\n  let promise = new Promise(function(resolve, reject){\n    reject(new Error('WHOOPS'));\n  });\n\n  promise.then(function(value){\n    // Code here doesn't run because the promise is rejected!\n  }, function(reason){\n    // reason.message === 'WHOOPS'\n  });\n  ```\n\n  Instead of writing the above, your code now simply becomes the following:\n\n  ```javascript\n  let promise = Promise.reject(new Error('WHOOPS'));\n\n  promise.then(function(value){\n    // Code here doesn't run because the promise is rejected!\n  }, function(reason){\n    // reason.message === 'WHOOPS'\n  });\n  ```\n\n  @method reject\n  @static\n  @param {Any} reason value that the returned promise will be rejected with.\n  Useful for tooling.\n  @return {Promise} a promise rejected with the given `reason`.\n*/\nexport default function reject(reason) {\n  /*jshint validthis:true */\n  var Constructor = this;\n  var promise = new Constructor(noop);\n  _reject(promise, reason);\n  return promise;\n}", "function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nimport { isFunction } from './utils';\nimport { noop, nextId, PROMISE_ID, initializePromise } from './-internal';\nimport { asap, setAsap, setScheduler } from './asap';\n\nimport all from './promise/all';\nimport race from './promise/race';\nimport Resolve from './promise/resolve';\nimport Reject from './promise/reject';\nimport then from './then';\n\nfunction needsResolver() {\n  throw new TypeError('You must pass a resolver function as the first argument to the promise constructor');\n}\n\nfunction needsNew() {\n  throw new TypeError(\"Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.\");\n}\n\n/**\n  Promise objects represent the eventual result of an asynchronous operation. The\n  primary way of interacting with a promise is through its `then` method, which\n  registers callbacks to receive either a promise's eventual value or the reason\n  why the promise cannot be fulfilled.\n\n  Terminology\n  -----------\n\n  - `promise` is an object or function with a `then` method whose behavior conforms to this specification.\n  - `thenable` is an object or function that defines a `then` method.\n  - `value` is any legal JavaScript value (including undefined, a thenable, or a promise).\n  - `exception` is a value that is thrown using the throw statement.\n  - `reason` is a value that indicates why a promise was rejected.\n  - `settled` the final resting state of a promise, fulfilled or rejected.\n\n  A promise can be in one of three states: pending, fulfilled, or rejected.\n\n  Promises that are fulfilled have a fulfillment value and are in the fulfilled\n  state.  Promises that are rejected have a rejection reason and are in the\n  rejected state.  A fulfillment value is never a thenable.\n\n  Promises can also be said to *resolve* a value.  If this value is also a\n  promise, then the original promise's settled state will match the value's\n  settled state.  So a promise that *resolves* a promise that rejects will\n  itself reject, and a promise that *resolves* a promise that fulfills will\n  itself fulfill.\n\n\n  Basic Usage:\n  ------------\n\n  ```js\n  let promise = new Promise(function(resolve, reject) {\n    // on success\n    resolve(value);\n\n    // on failure\n    reject(reason);\n  });\n\n  promise.then(function(value) {\n    // on fulfillment\n  }, function(reason) {\n    // on rejection\n  });\n  ```\n\n  Advanced Usage:\n  ---------------\n\n  Promises shine when abstracting away asynchronous interactions such as\n  `XMLHttpRequest`s.\n\n  ```js\n  function getJSON(url) {\n    return new Promise(function(resolve, reject){\n      let xhr = new XMLHttpRequest();\n\n      xhr.open('GET', url);\n      xhr.onreadystatechange = handler;\n      xhr.responseType = 'json';\n      xhr.setRequestHeader('Accept', 'application/json');\n      xhr.send();\n\n      function handler() {\n        if (this.readyState === this.DONE) {\n          if (this.status === 200) {\n            resolve(this.response);\n          } else {\n            reject(new Error('getJSON: `' + url + '` failed with status: [' + this.status + ']'));\n          }\n        }\n      };\n    });\n  }\n\n  getJSON('/posts.json').then(function(json) {\n    // on fulfillment\n  }, function(reason) {\n    // on rejection\n  });\n  ```\n\n  Unlike callbacks, promises are great composable primitives.\n\n  ```js\n  Promise.all([\n    getJSON('/posts'),\n    getJSON('/comments')\n  ]).then(function(values){\n    values[0] // => postsJSON\n    values[1] // => commentsJSON\n\n    return values;\n  });\n  ```\n\n  @class Promise\n  @param {Function} resolver\n  Useful for tooling.\n  @constructor\n*/\n\nvar Promise = function () {\n  function Promise(resolver) {\n    this[PROMISE_ID] = nextId();\n    this._result = this._state = undefined;\n    this._subscribers = [];\n\n    if (noop !== resolver) {\n      typeof resolver !== 'function' && needsResolver();\n      this instanceof Promise ? initializePromise(this, resolver) : needsNew();\n    }\n  }\n\n  /**\n  The primary way of interacting with a promise is through its `then` method,\n  which registers callbacks to receive either a promise's eventual value or the\n  reason why the promise cannot be fulfilled.\n   ```js\n  findUser().then(function(user){\n    // user is available\n  }, function(reason){\n    // user is unavailable, and you are given the reason why\n  });\n  ```\n   Chaining\n  --------\n   The return value of `then` is itself a promise.  This second, 'downstream'\n  promise is resolved with the return value of the first promise's fulfillment\n  or rejection handler, or rejected if the handler throws an exception.\n   ```js\n  findUser().then(function (user) {\n    return user.name;\n  }, function (reason) {\n    return 'default name';\n  }).then(function (userName) {\n    // If `findUser` fulfilled, `userName` will be the user's name, otherwise it\n    // will be `'default name'`\n  });\n   findUser().then(function (user) {\n    throw new Error('Found user, but still unhappy');\n  }, function (reason) {\n    throw new Error('`findUser` rejected and we're unhappy');\n  }).then(function (value) {\n    // never reached\n  }, function (reason) {\n    // if `findUser` fulfilled, `reason` will be 'Found user, but still unhappy'.\n    // If `findUser` rejected, `reason` will be '`findUser` rejected and we're unhappy'.\n  });\n  ```\n  If the downstream promise does not specify a rejection handler, rejection reasons will be propagated further downstream.\n   ```js\n  findUser().then(function (user) {\n    throw new PedagogicalException('Upstream error');\n  }).then(function (value) {\n    // never reached\n  }).then(function (value) {\n    // never reached\n  }, function (reason) {\n    // The `PedgagocialException` is propagated all the way down to here\n  });\n  ```\n   Assimilation\n  ------------\n   Sometimes the value you want to propagate to a downstream promise can only be\n  retrieved asynchronously. This can be achieved by returning a promise in the\n  fulfillment or rejection handler. The downstream promise will then be pending\n  until the returned promise is settled. This is called *assimilation*.\n   ```js\n  findUser().then(function (user) {\n    return findCommentsByAuthor(user);\n  }).then(function (comments) {\n    // The user's comments are now available\n  });\n  ```\n   If the assimliated promise rejects, then the downstream promise will also reject.\n   ```js\n  findUser().then(function (user) {\n    return findCommentsByAuthor(user);\n  }).then(function (comments) {\n    // If `findCommentsByAuthor` fulfills, we'll have the value here\n  }, function (reason) {\n    // If `findCommentsByAuthor` rejects, we'll have the reason here\n  });\n  ```\n   Simple Example\n  --------------\n   Synchronous Example\n   ```javascript\n  let result;\n   try {\n    result = findResult();\n    // success\n  } catch(reason) {\n    // failure\n  }\n  ```\n   Errback Example\n   ```js\n  findResult(function(result, err){\n    if (err) {\n      // failure\n    } else {\n      // success\n    }\n  });\n  ```\n   Promise Example;\n   ```javascript\n  findResult().then(function(result){\n    // success\n  }, function(reason){\n    // failure\n  });\n  ```\n   Advanced Example\n  --------------\n   Synchronous Example\n   ```javascript\n  let author, books;\n   try {\n    author = findAuthor();\n    books  = findBooksByAuthor(author);\n    // success\n  } catch(reason) {\n    // failure\n  }\n  ```\n   Errback Example\n   ```js\n   function foundBooks(books) {\n   }\n   function failure(reason) {\n   }\n   findAuthor(function(author, err){\n    if (err) {\n      failure(err);\n      // failure\n    } else {\n      try {\n        findBoooksByAuthor(author, function(books, err) {\n          if (err) {\n            failure(err);\n          } else {\n            try {\n              foundBooks(books);\n            } catch(reason) {\n              failure(reason);\n            }\n          }\n        });\n      } catch(error) {\n        failure(err);\n      }\n      // success\n    }\n  });\n  ```\n   Promise Example;\n   ```javascript\n  findAuthor().\n    then(findBooksByAuthor).\n    then(function(books){\n      // found books\n  }).catch(function(reason){\n    // something went wrong\n  });\n  ```\n   @method then\n  @param {Function} onFulfilled\n  @param {Function} onRejected\n  Useful for tooling.\n  @return {Promise}\n  */\n\n  /**\n  `catch` is simply sugar for `then(undefined, onRejection)` which makes it the same\n  as the catch block of a try/catch statement.\n  ```js\n  function findAuthor(){\n  throw new Error('couldn't find that author');\n  }\n  // synchronous\n  try {\n  findAuthor();\n  } catch(reason) {\n  // something went wrong\n  }\n  // async with promises\n  findAuthor().catch(function(reason){\n  // something went wrong\n  });\n  ```\n  @method catch\n  @param {Function} onRejection\n  Useful for tooling.\n  @return {Promise}\n  */\n\n\n  Promise.prototype.catch = function _catch(onRejection) {\n    return this.then(null, onRejection);\n  };\n\n  /**\n    `finally` will be invoked regardless of the promise's fate just as native\n    try/catch/finally behaves\n  \n    Synchronous example:\n  \n    ```js\n    findAuthor() {\n      if (Math.random() > 0.5) {\n        throw new Error();\n      }\n      return new Author();\n    }\n  \n    try {\n      return findAuthor(); // succeed or fail\n    } catch(error) {\n      return findOtherAuther();\n    } finally {\n      // always runs\n      // doesn't affect the return value\n    }\n    ```\n  \n    Asynchronous example:\n  \n    ```js\n    findAuthor().catch(function(reason){\n      return findOtherAuther();\n    }).finally(function(){\n      // author was either found, or not\n    });\n    ```\n  \n    @method finally\n    @param {Function} callback\n    @return {Promise}\n  */\n\n\n  Promise.prototype.finally = function _finally(callback) {\n    var promise = this;\n    var constructor = promise.constructor;\n\n    if (isFunction(callback)) {\n      return promise.then(function (value) {\n        return constructor.resolve(callback()).then(function () {\n          return value;\n        });\n      }, function (reason) {\n        return constructor.resolve(callback()).then(function () {\n          throw reason;\n        });\n      });\n    }\n\n    return promise.then(callback, callback);\n  };\n\n  return Promise;\n}();\n\nPromise.prototype.then = then;\nexport default Promise;\nPromise.all = all;\nPromise.race = race;\nPromise.resolve = Resolve;\nPromise.reject = Reject;\nPromise._setScheduler = setScheduler;\nPromise._setAsap = setAsap;\nPromise._asap = asap;", "/*global self*/\nimport Promise from './promise';\n\nexport default function polyfill() {\n  var local = void 0;\n\n  if (typeof global !== 'undefined') {\n    local = global;\n  } else if (typeof self !== 'undefined') {\n    local = self;\n  } else {\n    try {\n      local = Function('return this')();\n    } catch (e) {\n      throw new Error('polyfill failed because global object is unavailable in this environment');\n    }\n  }\n\n  var P = local.Promise;\n\n  if (P) {\n    var promiseToString = null;\n    try {\n      promiseToString = Object.prototype.toString.call(P.resolve());\n    } catch (e) {\n      // silently ignored\n    }\n\n    if (promiseToString === '[object Promise]' && !P.cast) {\n      return;\n    }\n  }\n\n  local.Promise = Promise;\n}", "import Promise from './es6-promise/promise';\nimport polyfill from './es6-promise/polyfill';\n\n// Strange compat..\nPromise.polyfill = polyfill;\nPromise.Promise = Promise;\nexport default Promise;", "import Promise from './es6-promise';\nPromise.polyfill();\nexport default Promise;"], "names": ["resolve", "_resolve", "then", "originalThen", "originalResolve", "Promise", "reject", "_reject", "Resolve", "Reject"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNO,SAAS,gBAAgB,CAAC,CAAC,EAAE;EAClC,IAAI,IAAI,GAAG,OAAO,CAAC,CAAC;EACpB,OAAO,CAAC,KAAK,IAAI,KAAK,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,UAAU,CAAC,CAAC;CACjE;;AAED,AAAO,SAAS,UAAU,CAAC,CAAC,EAAE;EAC5B,OAAO,OAAO,CAAC,KAAK,UAAU,CAAC;CAChC;;AAED,AAEC;;AAED,IAAI,QAAQ,GAAG,KAAK,CAAC,CAAC;AACtB,IAAI,KAAK,CAAC,OAAO,EAAE;EACjB,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC;CAC1B,MAAM;EACL,QAAQ,GAAG,UAAU,CAAC,EAAE;IACtB,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,gBAAgB,CAAC;GAC/D,CAAC;CACH;;AAED,AAAO,IAAI,OAAO,GAAG,QAAQ;;ACtB7B,IAAI,GAAG,GAAG,CAAC,CAAC;AACZ,IAAI,SAAS,GAAG,KAAK,CAAC,CAAC;AACvB,IAAI,iBAAiB,GAAG,KAAK,CAAC,CAAC;;AAE/B,AAAO,IAAI,IAAI,GAAG,SAAS,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAE;EAC7C,KAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;EACtB,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;EACrB,GAAG,IAAI,CAAC,CAAC;EACT,IAAI,GAAG,KAAK,CAAC,EAAE;;;;IAIb,IAAI,iBAAiB,EAAE;MACrB,iBAAiB,CAAC,KAAK,CAAC,CAAC;KAC1B,MAAM;MACL,aAAa,EAAE,CAAC;KACjB;GACF;CACF,CAAC;;AAEF,AAAO,SAAS,YAAY,CAAC,UAAU,EAAE;EACvC,iBAAiB,GAAG,UAAU,CAAC;CAChC;;AAED,AAAO,SAAS,OAAO,CAAC,MAAM,EAAE;EAC9B,IAAI,GAAG,MAAM,CAAC;CACf;;AAED,IAAI,aAAa,GAAG,OAAO,MAAM,KAAK,WAAW,GAAG,MAAM,GAAG,SAAS,CAAC;AACvE,IAAI,aAAa,GAAG,aAAa,IAAI,EAAE,CAAC;AACxC,IAAI,uBAAuB,GAAG,aAAa,CAAC,gBAAgB,IAAI,aAAa,CAAC,sBAAsB,CAAC;AACrG,IAAI,MAAM,GAAG,OAAO,IAAI,KAAK,WAAW,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,kBAAkB,CAAC;;;AAG/H,IAAI,QAAQ,GAAG,OAAO,iBAAiB,KAAK,WAAW,IAAI,OAAO,aAAa,KAAK,WAAW,IAAI,OAAO,cAAc,KAAK,WAAW,CAAC;;;AAGzI,SAAS,WAAW,GAAG;;;EAGrB,OAAO,YAAY;IACjB,OAAO,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;GAChC,CAAC;CACH;;;AAGD,SAAS,aAAa,GAAG;EACvB,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE;IACpC,OAAO,YAAY;MACjB,SAAS,CAAC,KAAK,CAAC,CAAC;KAClB,CAAC;GACH;;EAED,OAAO,aAAa,EAAE,CAAC;CACxB;;AAED,SAAS,mBAAmB,GAAG;EAC7B,IAAI,UAAU,GAAG,CAAC,CAAC;EACnB,IAAI,QAAQ,GAAG,IAAI,uBAAuB,CAAC,KAAK,CAAC,CAAC;EAClD,IAAI,IAAI,GAAG,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;EACvC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;;EAEhD,OAAO,YAAY;IACjB,IAAI,CAAC,IAAI,GAAG,UAAU,GAAG,EAAE,UAAU,GAAG,CAAC,CAAC;GAC3C,CAAC;CACH;;;AAGD,SAAS,iBAAiB,GAAG;EAC3B,IAAI,OAAO,GAAG,IAAI,cAAc,EAAE,CAAC;EACnC,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC;EAChC,OAAO,YAAY;IACjB,OAAO,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;GACrC,CAAC;CACH;;AAED,SAAS,aAAa,GAAG;;;EAGvB,IAAI,gBAAgB,GAAG,UAAU,CAAC;EAClC,OAAO,YAAY;IACjB,OAAO,gBAAgB,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;GACnC,CAAC;CACH;;AAED,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC;AAC5B,SAAS,KAAK,GAAG;EACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;IAC/B,IAAI,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACxB,IAAI,GAAG,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;;IAEvB,QAAQ,CAAC,GAAG,CAAC,CAAC;;IAEd,KAAK,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;IACrB,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC;GAC1B;;EAED,GAAG,GAAG,CAAC,CAAC;CACT;;AAED,SAAS,YAAY,GAAG;EACtB,IAAI;IACF,IAAI,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IACvD,SAAS,GAAG,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,YAAY,CAAC;IAClD,OAAO,aAAa,EAAE,CAAC;GACxB,CAAC,OAAO,CAAC,EAAE;IACV,OAAO,aAAa,EAAE,CAAC;GACxB;CACF;;AAED,IAAI,aAAa,GAAG,KAAK,CAAC,CAAC;;AAE3B,IAAI,MAAM,EAAE;EACV,aAAa,GAAG,WAAW,EAAE,CAAC;CAC/B,MAAM,IAAI,uBAAuB,EAAE;EAClC,aAAa,GAAG,mBAAmB,EAAE,CAAC;CACvC,MAAM,IAAI,QAAQ,EAAE;EACnB,aAAa,GAAG,iBAAiB,EAAE,CAAC;CACrC,MAAM,IAAI,aAAa,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;EACvE,aAAa,GAAG,YAAY,EAAE,CAAC;CAChC,MAAM;EACL,aAAa,GAAG,aAAa,EAAE,CAAC;;;CACjC,DCtHc,SAAS,IAAI,CAAC,aAAa,EAAE,WAAW,EAAE;EACvD,IAAI,MAAM,GAAG,IAAI,CAAC;;EAElB,IAAI,KAAK,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;;EAEvC,IAAI,KAAK,CAAC,UAAU,CAAC,KAAK,SAAS,EAAE;IACnC,WAAW,CAAC,KAAK,CAAC,CAAC;GACpB;;EAED,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;;;EAG3B,IAAI,MAAM,EAAE;IACV,IAAI,QAAQ,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACrC,IAAI,CAAC,YAAY;MACf,OAAO,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;KAChE,CAAC,CAAC;GACJ,MAAM;IACL,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;GACtD;;EAED,OAAO,KAAK,CAAC;;;CACd,DCxBD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA,AAAe,SAASA,SAAO,CAAC,MAAM,EAAE;;EAEtC,IAAI,WAAW,GAAG,IAAI,CAAC;;EAEvB,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,CAAC,WAAW,KAAK,WAAW,EAAE;IAC9E,OAAO,MAAM,CAAC;GACf;;EAED,IAAI,OAAO,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC;EACpCC,OAAQ,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;EAC1B,OAAO,OAAO,CAAC;;;CAChB,DCrCM,IAAI,UAAU,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;;AAEhE,SAAS,IAAI,GAAG,EAAE;;AAElB,IAAI,OAAO,GAAG,KAAK,CAAC,CAAC;AACrB,IAAI,SAAS,GAAG,CAAC,CAAC;AAClB,IAAI,QAAQ,GAAG,CAAC,CAAC;;AAEjB,SAAS,eAAe,GAAG;EACzB,OAAO,IAAI,SAAS,CAAC,0CAA0C,CAAC,CAAC;CAClE;;AAED,SAAS,eAAe,GAAG;EACzB,OAAO,IAAI,SAAS,CAAC,sDAAsD,CAAC,CAAC;CAC9E;;AAED,SAAS,OAAO,CAACC,OAAI,EAAE,KAAK,EAAE,kBAAkB,EAAE,gBAAgB,EAAE;EAClE,IAAI;IACFA,OAAI,CAAC,IAAI,CAAC,KAAK,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;GACxD,CAAC,OAAO,CAAC,EAAE;IACV,OAAO,CAAC,CAAC;GACV;CACF;;AAED,SAAS,qBAAqB,CAAC,OAAO,EAAE,QAAQ,EAAEA,OAAI,EAAE;EACtD,IAAI,CAAC,UAAU,OAAO,EAAE;IACtB,IAAI,MAAM,GAAG,KAAK,CAAC;IACnB,IAAI,KAAK,GAAG,OAAO,CAACA,OAAI,EAAE,QAAQ,EAAE,UAAU,KAAK,EAAE;MACnD,IAAI,MAAM,EAAE;QACV,OAAO;OACR;MACD,MAAM,GAAG,IAAI,CAAC;MACd,IAAI,QAAQ,KAAK,KAAK,EAAE;QACtB,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;OACzB,MAAM;QACL,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;OACzB;KACF,EAAE,UAAU,MAAM,EAAE;MACnB,IAAI,MAAM,EAAE;QACV,OAAO;OACR;MACD,MAAM,GAAG,IAAI,CAAC;;MAEd,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;KACzB,EAAE,UAAU,IAAI,OAAO,CAAC,MAAM,IAAI,kBAAkB,CAAC,CAAC,CAAC;;IAExD,IAAI,CAAC,MAAM,IAAI,KAAK,EAAE;MACpB,MAAM,GAAG,IAAI,CAAC;MACd,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;KACxB;GACF,EAAE,OAAO,CAAC,CAAC;CACb;;AAED,SAAS,iBAAiB,CAAC,OAAO,EAAE,QAAQ,EAAE;EAC5C,IAAI,QAAQ,CAAC,MAAM,KAAK,SAAS,EAAE;IACjC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;GACpC,MAAM,IAAI,QAAQ,CAAC,MAAM,KAAK,QAAQ,EAAE;IACvC,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;GACnC,MAAM;IACL,SAAS,CAAC,QAAQ,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE;MAC9C,OAAO,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;KAChC,EAAE,UAAU,MAAM,EAAE;MACnB,OAAO,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;KAChC,CAAC,CAAC;GACJ;CACF;;AAED,SAAS,mBAAmB,CAAC,OAAO,EAAE,aAAa,EAAEA,OAAI,EAAE;EACzD,IAAI,aAAa,CAAC,WAAW,KAAK,OAAO,CAAC,WAAW,IAAIA,OAAI,KAAKC,IAAY,IAAI,aAAa,CAAC,WAAW,CAAC,OAAO,KAAKC,SAAe,EAAE;IACvI,iBAAiB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;GAC3C,MAAM;IACL,IAAIF,OAAI,KAAK,SAAS,EAAE;MACtB,OAAO,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;KACjC,MAAM,IAAI,UAAU,CAACA,OAAI,CAAC,EAAE;MAC3B,qBAAqB,CAAC,OAAO,EAAE,aAAa,EAAEA,OAAI,CAAC,CAAC;KACrD,MAAM;MACL,OAAO,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;KACjC;GACF;CACF;;AAED,SAAS,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE;EAC/B,IAAI,OAAO,KAAK,KAAK,EAAE;IACrB,MAAM,CAAC,OAAO,EAAE,eAAe,EAAE,CAAC,CAAC;GACpC,MAAM,IAAI,gBAAgB,CAAC,KAAK,CAAC,EAAE;IAClC,IAAIA,OAAI,GAAG,KAAK,CAAC,CAAC;IAClB,IAAI;MACFA,OAAI,GAAG,KAAK,CAAC,IAAI,CAAC;KACnB,CAAC,OAAO,KAAK,EAAE;MACd,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;MACvB,OAAO;KACR;IACD,mBAAmB,CAAC,OAAO,EAAE,KAAK,EAAEA,OAAI,CAAC,CAAC;GAC3C,MAAM;IACL,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;GACzB;CACF;;AAED,SAAS,gBAAgB,CAAC,OAAO,EAAE;EACjC,IAAI,OAAO,CAAC,QAAQ,EAAE;IACpB,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;GACnC;;EAED,OAAO,CAAC,OAAO,CAAC,CAAC;CAClB;;AAED,SAAS,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE;EAC/B,IAAI,OAAO,CAAC,MAAM,KAAK,OAAO,EAAE;IAC9B,OAAO;GACR;;EAED,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC;EACxB,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;;EAE3B,IAAI,OAAO,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;IACrC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;GACxB;CACF;;AAED,SAAS,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE;EAC/B,IAAI,OAAO,CAAC,MAAM,KAAK,OAAO,EAAE;IAC9B,OAAO;GACR;EACD,OAAO,CAAC,MAAM,GAAG,QAAQ,CAAC;EAC1B,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC;;EAEzB,IAAI,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;CACjC;;AAED,SAAS,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,WAAW,EAAE;EAC5D,IAAI,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;EACvC,IAAI,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;;;EAGjC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC;;EAEvB,YAAY,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;EAC7B,YAAY,CAAC,MAAM,GAAG,SAAS,CAAC,GAAG,aAAa,CAAC;EACjD,YAAY,CAAC,MAAM,GAAG,QAAQ,CAAC,GAAG,WAAW,CAAC;;EAE9C,IAAI,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,EAAE;IACjC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;GACvB;CACF;;AAED,SAAS,OAAO,CAAC,OAAO,EAAE;EACxB,IAAI,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;EACvC,IAAI,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;;EAE7B,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;IAC5B,OAAO;GACR;;EAED,IAAI,KAAK,GAAG,KAAK,CAAC;MACd,QAAQ,GAAG,KAAK,CAAC;MACjB,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;;EAE7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;IAC9C,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;IACvB,QAAQ,GAAG,WAAW,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC;;IAEpC,IAAI,KAAK,EAAE;MACT,cAAc,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;KAClD,MAAM;MACL,QAAQ,CAAC,MAAM,CAAC,CAAC;KAClB;GACF;;EAED,OAAO,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;CACjC;;AAED,SAAS,cAAc,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE;EAC1D,IAAI,WAAW,GAAG,UAAU,CAAC,QAAQ,CAAC;MAClC,KAAK,GAAG,KAAK,CAAC;MACd,KAAK,GAAG,KAAK,CAAC;MACd,SAAS,GAAG,IAAI,CAAC;;EAErB,IAAI,WAAW,EAAE;IACf,IAAI;MACF,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;KAC1B,CAAC,OAAO,CAAC,EAAE;MACV,SAAS,GAAG,KAAK,CAAC;MAClB,KAAK,GAAG,CAAC,CAAC;KACX;;IAED,IAAI,OAAO,KAAK,KAAK,EAAE;MACrB,MAAM,CAAC,OAAO,EAAE,eAAe,EAAE,CAAC,CAAC;MACnC,OAAO;KACR;GACF,MAAM;IACL,KAAK,GAAG,MAAM,CAAC;GAChB;;EAED,IAAI,OAAO,CAAC,MAAM,KAAK,OAAO,EAAE;;GAE/B,MAAM,IAAI,WAAW,IAAI,SAAS,EAAE;IACnC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;GACzB,MAAM,IAAI,SAAS,KAAK,KAAK,EAAE;IAC9B,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;GACxB,MAAM,IAAI,OAAO,KAAK,SAAS,EAAE;IAChC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;GACzB,MAAM,IAAI,OAAO,KAAK,QAAQ,EAAE;IAC/B,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;GACxB;CACF;;AAED,SAAS,iBAAiB,CAAC,OAAO,EAAE,QAAQ,EAAE;EAC5C,IAAI;IACF,QAAQ,CAAC,SAAS,cAAc,CAAC,KAAK,EAAE;MACtC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;KACzB,EAAE,SAAS,aAAa,CAAC,MAAM,EAAE;MAChC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;KACzB,CAAC,CAAC;GACJ,CAAC,OAAO,CAAC,EAAE;IACV,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;GACpB;CACF;;AAED,IAAI,EAAE,GAAG,CAAC,CAAC;AACX,SAAS,MAAM,GAAG;EAChB,OAAO,EAAE,EAAE,CAAC;CACb;;AAED,SAAS,WAAW,CAAC,OAAO,EAAE;EAC5B,OAAO,CAAC,UAAU,CAAC,GAAG,EAAE,EAAE,CAAC;EAC3B,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;EAC3B,OAAO,CAAC,OAAO,GAAG,SAAS,CAAC;EAC5B,OAAO,CAAC,YAAY,GAAG,EAAE,CAAC;CAC3B;;AChOD,SAAS,eAAe,GAAG;EACzB,OAAO,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;CAC7D,AAAC;;AAEF,IAAI,UAAU,GAAG,YAAY;EAC3B,SAAS,UAAU,CAAC,WAAW,EAAE,KAAK,EAAE;IACtC,IAAI,CAAC,oBAAoB,GAAG,WAAW,CAAC;IACxC,IAAI,CAAC,OAAO,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC;;IAErC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;MAC7B,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KAC3B;;IAED,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;MAClB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;MAC3B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC;;MAE/B,IAAI,CAAC,OAAO,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;;MAEtC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QACrB,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;OACrC,MAAM;QACL,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;QAC/B,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACvB,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,EAAE;UACzB,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;SACrC;OACF;KACF,MAAM;MACL,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,eAAe,EAAE,CAAC,CAAC;KACzC;GACF;;EAED,UAAU,CAAC,SAAS,CAAC,UAAU,GAAG,SAAS,UAAU,CAAC,KAAK,EAAE;IAC3D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,KAAK,OAAO,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;MAChE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KAC9B;GACF,CAAC;;EAEF,UAAU,CAAC,SAAS,CAAC,UAAU,GAAG,SAAS,UAAU,CAAC,KAAK,EAAE,CAAC,EAAE;IAC9D,IAAI,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;IAClC,IAAIF,UAAO,GAAG,CAAC,CAAC,OAAO,CAAC;;;IAGxB,IAAIA,UAAO,KAAKI,SAAe,EAAE;MAC/B,IAAI,KAAK,GAAG,KAAK,CAAC,CAAC;MACnB,IAAI,KAAK,GAAG,KAAK,CAAC,CAAC;MACnB,IAAI,QAAQ,GAAG,KAAK,CAAC;MACrB,IAAI;QACF,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;OACpB,CAAC,OAAO,CAAC,EAAE;QACV,QAAQ,GAAG,IAAI,CAAC;QAChB,KAAK,GAAG,CAAC,CAAC;OACX;;MAED,IAAI,KAAK,KAAKD,IAAY,IAAI,KAAK,CAAC,MAAM,KAAK,OAAO,EAAE;QACtD,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;OACjD,MAAM,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;QACtC,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;OACzB,MAAM,IAAI,CAAC,KAAKE,SAAO,EAAE;QACxB,IAAI,OAAO,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,QAAQ,EAAE;UACZ,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;SACxB,MAAM;UACL,mBAAmB,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;SAC5C;QACD,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;OAChC,MAAM;QACL,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,UAAUL,UAAO,EAAE;UAC1C,OAAOA,UAAO,CAAC,KAAK,CAAC,CAAC;SACvB,CAAC,EAAE,CAAC,CAAC,CAAC;OACR;KACF,MAAM;MACL,IAAI,CAAC,aAAa,CAACA,UAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;KACvC;GACF,CAAC;;EAEF,UAAU,CAAC,SAAS,CAAC,UAAU,GAAG,SAAS,UAAU,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE;IACrE,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;;;IAG3B,IAAI,OAAO,CAAC,MAAM,KAAK,OAAO,EAAE;MAC9B,IAAI,CAAC,UAAU,EAAE,CAAC;;MAElB,IAAI,KAAK,KAAK,QAAQ,EAAE;QACtB,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;OACxB,MAAM;QACL,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;OACzB;KACF;;IAED,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,EAAE;MACzB,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;KAChC;GACF,CAAC;;EAEF,UAAU,CAAC,SAAS,CAAC,aAAa,GAAG,SAAS,aAAa,CAAC,OAAO,EAAE,CAAC,EAAE;IACtE,IAAI,UAAU,GAAG,IAAI,CAAC;;IAEtB,SAAS,CAAC,OAAO,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE;MAC7C,OAAO,UAAU,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;KACnD,EAAE,UAAU,MAAM,EAAE;MACnB,OAAO,UAAU,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;KACnD,CAAC,CAAC;GACJ,CAAC;;EAEF,OAAO,UAAU,CAAC;CACnB,EAAE;;ACrHH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CA,AAAe,SAAS,GAAG,CAAC,OAAO,EAAE;EACnC,OAAO,IAAI,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC;;;CAC9C,DCjDD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiEA,AAAe,SAAS,IAAI,CAAC,OAAO,EAAE;;EAEpC,IAAI,WAAW,GAAG,IAAI,CAAC;;EAEvB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;IACrB,OAAO,IAAI,WAAW,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE;MAC1C,OAAO,MAAM,CAAC,IAAI,SAAS,CAAC,iCAAiC,CAAC,CAAC,CAAC;KACjE,CAAC,CAAC;GACJ,MAAM;IACL,OAAO,IAAI,WAAW,CAAC,UAAU,OAAO,EAAE,MAAM,EAAE;MAChD,IAAI,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;MAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;QAC/B,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;OACvD;KACF,CAAC,CAAC;GACJ;;;CACF,DCjFD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,AAAe,SAASM,QAAM,CAAC,MAAM,EAAE;;EAErC,IAAI,WAAW,GAAG,IAAI,CAAC;EACvB,IAAI,OAAO,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC;EACpCC,MAAO,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;EACzB,OAAO,OAAO,CAAC;;;CAChB,DC9BD,SAAS,aAAa,GAAG;EACvB,MAAM,IAAI,SAAS,CAAC,oFAAoF,CAAC,CAAC;CAC3G;;AAED,SAAS,QAAQ,GAAG;EAClB,MAAM,IAAI,SAAS,CAAC,uHAAuH,CAAC,CAAC;CAC9I;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0GD,IAAIF,SAAO,GAAG,YAAY;EACxB,SAAS,OAAO,CAAC,QAAQ,EAAE;IACzB,IAAI,CAAC,UAAU,CAAC,GAAG,MAAM,EAAE,CAAC;IAC5B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;IACvC,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;;IAEvB,IAAI,IAAI,KAAK,QAAQ,EAAE;MACrB,OAAO,QAAQ,KAAK,UAAU,IAAI,aAAa,EAAE,CAAC;MAClD,IAAI,YAAY,OAAO,GAAG,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,QAAQ,EAAE,CAAC;KAC1E;GACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4LD,OAAO,CAAC,SAAS,CAAC,KAAK,GAAG,SAAS,MAAM,CAAC,WAAW,EAAE;IACrD,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;GACrC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA0CF,OAAO,CAAC,SAAS,CAAC,OAAO,GAAG,SAAS,QAAQ,CAAC,QAAQ,EAAE;IACtD,IAAI,OAAO,GAAG,IAAI,CAAC;IACnB,IAAI,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;;IAEtC,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE;MACxB,OAAO,OAAO,CAAC,IAAI,CAAC,UAAU,KAAK,EAAE;QACnC,OAAO,WAAW,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY;UACtD,OAAO,KAAK,CAAC;SACd,CAAC,CAAC;OACJ,EAAE,UAAU,MAAM,EAAE;QACnB,OAAO,WAAW,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY;UACtD,MAAM,MAAM,CAAC;SACd,CAAC,CAAC;OACJ,CAAC,CAAC;KACJ;;IAED,OAAO,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;GACzC,CAAC;;EAEF,OAAO,OAAO,CAAC;CAChB,EAAE,CAAC;;AAEJA,SAAO,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC;AAC9B,AACAA,SAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAClBA,SAAO,CAAC,IAAI,GAAG,IAAI,CAAC;AACpBA,SAAO,CAAC,OAAO,GAAGG,SAAO,CAAC;AAC1BH,SAAO,CAAC,MAAM,GAAGI,QAAM,CAAC;AACxBJ,SAAO,CAAC,aAAa,GAAG,YAAY,CAAC;AACrCA,SAAO,CAAC,QAAQ,GAAG,OAAO,CAAC;AAC3BA,SAAO,CAAC,KAAK,GAAG,IAAI;;AC5YpB;AACA,AAEe,SAAS,QAAQ,GAAG;EACjC,IAAI,KAAK,GAAG,KAAK,CAAC,CAAC;;EAEnB,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;IACjC,KAAK,GAAG,MAAM,CAAC;GAChB,MAAM,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;IACtC,KAAK,GAAG,IAAI,CAAC;GACd,MAAM;IACL,IAAI;MACF,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;KACnC,CAAC,OAAO,CAAC,EAAE;MACV,MAAM,IAAI,KAAK,CAAC,0EAA0E,CAAC,CAAC;KAC7F;GACF;;EAED,IAAI,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC;;EAEtB,IAAI,CAAC,EAAE;IACL,IAAI,eAAe,GAAG,IAAI,CAAC;IAC3B,IAAI;MACF,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;KAC/D,CAAC,OAAO,CAAC,EAAE;;KAEX;;IAED,IAAI,eAAe,KAAK,kBAAkB,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;MACrD,OAAO;KACR;GACF;;EAED,KAAK,CAAC,OAAO,GAAGA,SAAO,CAAC;;;CACzB,DC/BD;AACAA,SAAO,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC5BA,SAAO,CAAC,OAAO,GAAGA,SAAO,CAAC;;ACJ1BA,SAAO,CAAC,QAAQ,EAAE,CAAC;;;;;;;;", "file": "es6-promise.auto.js"}