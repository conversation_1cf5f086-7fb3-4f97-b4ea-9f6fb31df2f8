/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { sizeFromShape } from '../../util';
/**
 * Generates sparse reshape multiple negative 1 output dimension error message.
 *
 * @param dim1 The first dimension with a negative 1 value.
 * @param dim2 The second dimension with a negative 1 value.
 */
export function getSparseReshapeMultipleNegativeOneOutputDimErrorMessage(dim1, dim2) {
    return `only one output dimension may be -1, not both ${dim1} and ${dim2}`;
}
/**
 * Generates sparse reshape negative output dimension error message.
 *
 * @param dim The dimension with a negative value.
 * @param value The negative value.
 */
export function getSparseReshapeNegativeOutputDimErrorMessage(dim, value) {
    return `size ${dim} must be non-negative, not ${value}`;
}
/**
 * Generates sparse reshape empty tensor zero output dimension error message.
 *
 */
export function getSparseReshapeEmptyTensorZeroOutputDimErrorMessage() {
    return 'reshape cannot infer the missing input size for an empty tensor ' +
        'unless all specified input sizes are non-zero';
}
/**
 * Generates sparse reshape input output multiple mismatch error message.
 *
 * @param inputShape the input shape.
 * @param outputShape the requested output shape.
 */
export function getSparseReshapeInputOutputMultipleErrorMessage(inputShape, outputShape) {
    const inputSize = sizeFromShape(inputShape);
    const outputSize = sizeFromShape(outputShape);
    return `Input to reshape is a SparseTensor with ${inputSize}
  dense values, but the requested shape requires a multiple of ${outputSize}. inputShape=${inputShape} outputShape= ${outputShape}`;
}
/**
 * Generates sparse reshape input output inequality error message.
 *
 * @param inputShape the input shape.
 * @param outputShape the requested output shape.
 */
export function getSparseReshapeInputOutputMismatchErrorMessage(inputShape, outputShape) {
    const inputSize = sizeFromShape(inputShape);
    const outputSize = sizeFromShape(outputShape);
    return `Input to reshape is a tensor with ${inputSize} dense values, but the requested shape has ${outputSize}. inputShape=${inputShape} outputShape=${outputShape}`;
}
//# sourceMappingURL=data:application/json;base64,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