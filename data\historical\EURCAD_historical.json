{"symbol": "EURCAD", "timeframe": 300, "lastUpdate": "2025-07-07T02:42:27.802Z", "candlesCount": 501, "candles": [{"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T08:58:39.958Z", "timeframe": 300, "open": 1.1848069339372098, "high": 1.1851026452940527, "low": 1.1844055832061395, "close": 1.1846000256394944, "volume": 289, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T09:03:39.958Z", "timeframe": 300, "open": 1.1102525637148855, "high": 1.1102623261399498, "low": 1.1096218740076715, "close": 1.1099201504633402, "volume": 357, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T09:08:39.958Z", "timeframe": 300, "open": 1.1437841904839354, "high": 1.1447688367809847, "low": 1.142905743377409, "close": 1.1439032278152668, "volume": 930, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T09:13:39.958Z", "timeframe": 300, "open": 1.1112280289980432, "high": 1.1117312486360988, "low": 1.110617357477705, "close": 1.111617574199947, "volume": 394, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T09:18:39.958Z", "timeframe": 300, "open": 1.1995872299246637, "high": 1.2000530695788716, "low": 1.198965244454864, "close": 1.2000530695788716, "volume": 341, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T09:23:39.958Z", "timeframe": 300, "open": 1.143916746729839, "high": 1.1446612161280447, "low": 1.1430019418964652, "close": 1.1439942176021285, "volume": 236, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T09:28:39.958Z", "timeframe": 300, "open": 1.1445749492942314, "high": 1.1454515949414756, "low": 1.1441439994384057, "close": 1.1441439994384057, "volume": 279, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T09:33:39.958Z", "timeframe": 300, "open": 1.1710845772955343, "high": 1.1712956294366894, "low": 1.170154829424415, "close": 1.1709505362868409, "volume": 817, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T09:38:39.958Z", "timeframe": 300, "open": 1.153466372327039, "high": 1.153628789739718, "low": 1.1528137126572087, "close": 1.153628789739718, "volume": 132, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T09:43:39.958Z", "timeframe": 300, "open": 1.1413673851721282, "high": 1.1423615375537837, "low": 1.140481382199024, "close": 1.1417538148523414, "volume": 781, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T09:48:39.958Z", "timeframe": 300, "open": 1.1915372091711733, "high": 1.1920264405458012, "low": 1.1912917372569967, "close": 1.1913795399940321, "volume": 491, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T09:53:39.958Z", "timeframe": 300, "open": 1.1935329182343626, "high": 1.1941199004982883, "low": 1.192672720851849, "close": 1.1939081680571646, "volume": 1097, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T09:58:39.958Z", "timeframe": 300, "open": 1.1778231125494876, "high": 1.1786572759683542, "low": 1.1773640849726683, "close": 1.1782662197155356, "volume": 1029, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T10:03:39.958Z", "timeframe": 300, "open": 1.140648227530347, "high": 1.141018687314384, "low": 1.1404986530872612, "close": 1.141018687314384, "volume": 909, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T10:08:39.958Z", "timeframe": 300, "open": 1.1606867735582445, "high": 1.1616449279540322, "low": 1.1605354920483897, "close": 1.1605997108092911, "volume": 103, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T10:13:39.958Z", "timeframe": 300, "open": 1.114365950855752, "high": 1.1149897549162726, "low": 1.1139008766405196, "close": 1.1139008766405196, "volume": 155, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T10:18:39.958Z", "timeframe": 300, "open": 1.1754746053321738, "high": 1.1760735823420574, "low": 1.1753802797597976, "close": 1.1755033087862017, "volume": 274, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T10:23:39.958Z", "timeframe": 300, "open": 1.1054571348281343, "high": 1.105938411973456, "low": 1.104543442028998, "close": 1.105938411973456, "volume": 379, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T10:28:39.958Z", "timeframe": 300, "open": 1.170510161332915, "high": 1.1710678803834722, "low": 1.1697885709913858, "close": 1.1703307447231859, "volume": 385, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T10:33:39.958Z", "timeframe": 300, "open": 1.1245694774182202, "high": 1.1249131724776844, "low": 1.1241323522089688, "close": 1.1245233354716102, "volume": 405, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T10:38:39.958Z", "timeframe": 300, "open": 1.177928411839668, "high": 1.1782465276109788, "low": 1.177653058814487, "close": 1.1782465276109788, "volume": 265, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T10:43:39.958Z", "timeframe": 300, "open": 1.1394048765216844, "high": 1.1397304281647032, "low": 1.13902265239849, "close": 1.1394039866603314, "volume": 118, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T10:48:39.958Z", "timeframe": 300, "open": 1.1641132101175635, "high": 1.164194143545871, "low": 1.1638792176908486, "close": 1.1638792176908486, "volume": 782, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T10:53:39.958Z", "timeframe": 300, "open": 1.109040295962565, "high": 1.1099898487717046, "low": 1.1084624263722542, "close": 1.1093204507454992, "volume": 189, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T10:58:39.958Z", "timeframe": 300, "open": 1.1614182746205899, "high": 1.161818826411233, "low": 1.1611600820147185, "close": 1.161818826411233, "volume": 665, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T11:03:39.958Z", "timeframe": 300, "open": 1.1321452515262442, "high": 1.1323796872041365, "low": 1.1314067836026436, "close": 1.1323796872041365, "volume": 567, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T11:08:39.958Z", "timeframe": 300, "open": 1.1301926914101517, "high": 1.1306675807199906, "low": 1.129774383584317, "close": 1.1304171683044921, "volume": 916, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T11:13:39.958Z", "timeframe": 300, "open": 1.1641263938370645, "high": 1.164761564873472, "low": 1.1638726782892137, "close": 1.1642796402720852, "volume": 444, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T11:18:39.958Z", "timeframe": 300, "open": 1.1717186604102756, "high": 1.1724433084492956, "low": 1.170718946757526, "close": 1.1712571338150868, "volume": 114, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T11:23:39.958Z", "timeframe": 300, "open": 1.1131562372565726, "high": 1.114103798736934, "low": 1.1123348078840618, "close": 1.1127324248911137, "volume": 548, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T11:28:39.958Z", "timeframe": 300, "open": 1.1674164987092028, "high": 1.167810519353471, "low": 1.1671809045821828, "close": 1.1671809045821828, "volume": 712, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T11:33:39.958Z", "timeframe": 300, "open": 1.1790520677966432, "high": 1.1795408330568358, "low": 1.1780934332700992, "close": 1.1790792325010473, "volume": 622, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T11:38:39.958Z", "timeframe": 300, "open": 1.1831003885689597, "high": 1.183802668361501, "low": 1.1823486780763004, "close": 1.1830355855781147, "volume": 168, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T11:43:39.958Z", "timeframe": 300, "open": 1.118155888487852, "high": 1.1184422708604986, "low": 1.1177270995823838, "close": 1.1177486633797673, "volume": 401, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T11:48:39.958Z", "timeframe": 300, "open": 1.123373048056065, "high": 1.1233950744102366, "low": 1.1229799489441736, "close": 1.1232222204360662, "volume": 206, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.961Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T11:53:39.958Z", "timeframe": 300, "open": 1.1944404014801457, "high": 1.1951746141007005, "low": 1.1935456174249, "close": 1.1944401549707204, "volume": 756, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T11:58:39.958Z", "timeframe": 300, "open": 1.1851061231515032, "high": 1.1858379960428476, "low": 1.1842060507534167, "close": 1.1852583799959762, "volume": 260, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T12:03:39.958Z", "timeframe": 300, "open": 1.185699741010642, "high": 1.1864984119275097, "low": 1.1848147047854438, "close": 1.1857466353984012, "volume": 987, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T12:08:39.958Z", "timeframe": 300, "open": 1.139964187578923, "high": 1.140335259141981, "low": 1.1399618212329217, "close": 1.140335259141981, "volume": 625, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T12:13:39.958Z", "timeframe": 300, "open": 1.182730466674264, "high": 1.1829188059386622, "low": 1.181762366468532, "close": 1.1829188059386622, "volume": 381, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T12:18:39.958Z", "timeframe": 300, "open": 1.199122786796284, "high": 1.1996276426921308, "low": 1.1991088013415176, "close": 1.1994123371345538, "volume": 923, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T12:23:39.958Z", "timeframe": 300, "open": 1.1584429091499744, "high": 1.1590946328783183, "low": 1.1577934976404811, "close": 1.1579560337368517, "volume": 841, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T12:28:39.958Z", "timeframe": 300, "open": 1.1766613421408036, "high": 1.177194486846703, "low": 1.1765012781315085, "close": 1.1765012781315085, "volume": 857, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T12:33:39.958Z", "timeframe": 300, "open": 1.1363954281370896, "high": 1.137140709134018, "low": 1.1356826986431994, "close": 1.1368070760388982, "volume": 864, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T12:38:39.958Z", "timeframe": 300, "open": 1.118042969931241, "high": 1.1182927397168905, "low": 1.1174837046013089, "close": 1.1179661155665566, "volume": 758, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T12:43:39.958Z", "timeframe": 300, "open": 1.1617278618190496, "high": 1.1622900530341207, "low": 1.16154940884598, "close": 1.16154940884598, "volume": 120, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T12:48:39.958Z", "timeframe": 300, "open": 1.1204247520905457, "high": 1.1207029199893983, "low": 1.1204039221195197, "close": 1.1204941155447332, "volume": 624, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T12:53:39.958Z", "timeframe": 300, "open": 1.191543733969355, "high": 1.192474020318219, "low": 1.1911319343685005, "close": 1.191731448094972, "volume": 825, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T12:58:39.958Z", "timeframe": 300, "open": 1.1142739468839837, "high": 1.1144591905735919, "low": 1.1137503153643857, "close": 1.1139413401528748, "volume": 1030, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T13:03:39.958Z", "timeframe": 300, "open": 1.1691502864416259, "high": 1.1700604182200873, "low": 1.168987938595675, "close": 1.1690347131113537, "volume": 826, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T13:08:39.958Z", "timeframe": 300, "open": 1.1788840421671705, "high": 1.1796114928705437, "low": 1.1779914555078579, "close": 1.178430028978567, "volume": 879, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T13:13:39.958Z", "timeframe": 300, "open": 1.173537784113609, "high": 1.174478667338397, "low": 1.1728598116920574, "close": 1.1732334784927883, "volume": 676, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T13:18:39.958Z", "timeframe": 300, "open": 1.1447040537276918, "high": 1.1450344591666068, "low": 1.144347836403967, "close": 1.1449891234053762, "volume": 1019, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T13:23:39.958Z", "timeframe": 300, "open": 1.1748199177987257, "high": 1.1751988377424722, "low": 1.1744519655107981, "close": 1.1751988377424722, "volume": 853, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T13:28:39.958Z", "timeframe": 300, "open": 1.1164118533862688, "high": 1.1167252175022597, "low": 1.1158019462480602, "close": 1.1165742927798754, "volume": 985, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T13:33:39.958Z", "timeframe": 300, "open": 1.1012223754142088, "high": 1.1013655248818952, "low": 1.100813521608034, "close": 1.1013642302854467, "volume": 900, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T13:38:39.958Z", "timeframe": 300, "open": 1.1243829409999855, "high": 1.1252867872740335, "low": 1.1241158833886906, "close": 1.124166611296706, "volume": 553, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T13:43:39.958Z", "timeframe": 300, "open": 1.133559887552971, "high": 1.1342228641198386, "low": 1.13316521079623, "close": 1.1335904129148022, "volume": 708, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T13:48:39.958Z", "timeframe": 300, "open": 1.1960836757851536, "high": 1.1963683194533348, "low": 1.1954100644937775, "close": 1.196106171696344, "volume": 499, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T13:53:39.958Z", "timeframe": 300, "open": 1.1159071778491225, "high": 1.116370798466768, "low": 1.115479944837974, "close": 1.116370798466768, "volume": 908, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T13:58:39.958Z", "timeframe": 300, "open": 1.1407047190517858, "high": 1.1411650844454158, "low": 1.140114955226455, "close": 1.1409715436746635, "volume": 438, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T14:03:39.958Z", "timeframe": 300, "open": 1.1098188822975303, "high": 1.110169361314643, "low": 1.10938330583669, "close": 1.1101198018280654, "volume": 119, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T14:08:39.958Z", "timeframe": 300, "open": 1.1046622366655257, "high": 1.1047279498475555, "low": 1.1042525742092433, "close": 1.1042525742092433, "volume": 533, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T14:13:39.958Z", "timeframe": 300, "open": 1.1085146597574496, "high": 1.1090446397004958, "low": 1.1076826030826323, "close": 1.1082098268683827, "volume": 833, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T14:18:39.958Z", "timeframe": 300, "open": 1.1737863327758442, "high": 1.1740539226148576, "low": 1.1732405036641935, "close": 1.1737359115063142, "volume": 650, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T14:23:39.958Z", "timeframe": 300, "open": 1.133443828414351, "high": 1.1339581317832321, "low": 1.1327316390527111, "close": 1.1336894319213369, "volume": 137, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T14:28:39.958Z", "timeframe": 300, "open": 1.1033671587343434, "high": 1.1038162661964759, "low": 1.1023901698820906, "close": 1.1029720903953872, "volume": 841, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T14:33:39.958Z", "timeframe": 300, "open": 1.1509588139635087, "high": 1.1515421961726038, "low": 1.1506812919582963, "close": 1.1512442630042141, "volume": 343, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T14:38:39.958Z", "timeframe": 300, "open": 1.16016338379968, "high": 1.1606729460714724, "low": 1.1592990258787625, "close": 1.160177598298878, "volume": 448, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T14:43:39.958Z", "timeframe": 300, "open": 1.1073844526135628, "high": 1.1076955807550868, "low": 1.106758447799857, "close": 1.1069672518074012, "volume": 305, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T14:48:39.958Z", "timeframe": 300, "open": 1.1212603222574986, "high": 1.1218524111456005, "low": 1.1210502595654532, "close": 1.1216564935028708, "volume": 114, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T14:53:39.958Z", "timeframe": 300, "open": 1.1526550099291857, "high": 1.1534701653560706, "low": 1.151869330633862, "close": 1.1526023255527311, "volume": 745, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T14:58:39.958Z", "timeframe": 300, "open": 1.1235544314585104, "high": 1.1245311445661652, "low": 1.1234013612613416, "close": 1.1234103414645966, "volume": 329, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T15:03:39.958Z", "timeframe": 300, "open": 1.122845153314384, "high": 1.123776696656535, "low": 1.1227192327107713, "close": 1.1230465119763882, "volume": 1020, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T15:08:39.958Z", "timeframe": 300, "open": 1.1325296884061211, "high": 1.133341847523028, "low": 1.1320361928361002, "close": 1.1320629885345266, "volume": 376, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T15:13:39.958Z", "timeframe": 300, "open": 1.1101546209167414, "high": 1.1106790084966403, "low": 1.109722937502471, "close": 1.1101312680044515, "volume": 813, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T15:18:39.958Z", "timeframe": 300, "open": 1.1301014960562903, "high": 1.130422633802008, "low": 1.129816057836195, "close": 1.130344377788834, "volume": 873, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T15:23:39.958Z", "timeframe": 300, "open": 1.1605128632327926, "high": 1.1612609780437968, "low": 1.160088509848874, "close": 1.160088509848874, "volume": 938, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T15:28:39.958Z", "timeframe": 300, "open": 1.1473539559635637, "high": 1.1478805153357317, "low": 1.1469391445210666, "close": 1.1475322944024664, "volume": 652, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T15:33:39.958Z", "timeframe": 300, "open": 1.1280597019938903, "high": 1.1289576664936536, "low": 1.1271306509150885, "close": 1.128402608811684, "volume": 611, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T15:38:39.958Z", "timeframe": 300, "open": 1.1333561658997926, "high": 1.1333861046945501, "low": 1.1330146869533386, "close": 1.133053420961147, "volume": 437, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T15:43:39.958Z", "timeframe": 300, "open": 1.1518992767110203, "high": 1.1526248595989272, "low": 1.151381440768946, "close": 1.1523471805599845, "volume": 141, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T15:48:39.958Z", "timeframe": 300, "open": 1.109625707594251, "high": 1.1098607659233521, "low": 1.108736935252337, "close": 1.1095713712465123, "volume": 206, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T15:53:39.958Z", "timeframe": 300, "open": 1.1669158072255041, "high": 1.1672512591739297, "low": 1.166622480760641, "close": 1.166622480760641, "volume": 776, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T15:58:39.958Z", "timeframe": 300, "open": 1.1971509859581162, "high": 1.1978923205046823, "low": 1.1963968208430846, "close": 1.1974514361267583, "volume": 992, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T16:03:39.958Z", "timeframe": 300, "open": 1.1010584254753983, "high": 1.1015232318607748, "low": 1.100962507484607, "close": 1.1012198074088362, "volume": 586, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T16:08:39.958Z", "timeframe": 300, "open": 1.1573159966438966, "high": 1.1582360704545087, "low": 1.157101035391968, "close": 1.1574816942738766, "volume": 785, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T16:13:39.958Z", "timeframe": 300, "open": 1.1487433728662388, "high": 1.1495351333225556, "low": 1.1483557992863287, "close": 1.1485690255823773, "volume": 944, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T16:18:39.958Z", "timeframe": 300, "open": 1.1192879167616845, "high": 1.1193508968143457, "low": 1.118916670484115, "close": 1.1189618046569105, "volume": 917, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T16:23:39.958Z", "timeframe": 300, "open": 1.1387314155191963, "high": 1.1393144291911892, "low": 1.1377909835636217, "close": 1.1382354924045093, "volume": 313, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T16:28:39.958Z", "timeframe": 300, "open": 1.1620134909191422, "high": 1.162472219964494, "low": 1.1611963416039106, "close": 1.162472219964494, "volume": 912, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T16:33:39.958Z", "timeframe": 300, "open": 1.1200806508800605, "high": 1.1202545273816287, "low": 1.1197189021532825, "close": 1.119734194869857, "volume": 867, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T16:38:39.958Z", "timeframe": 300, "open": 1.1350313273455495, "high": 1.1355376982608885, "low": 1.1343884411257195, "close": 1.1355002814156587, "volume": 280, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T16:43:39.958Z", "timeframe": 300, "open": 1.1118897292940573, "high": 1.1126661057755385, "low": 1.1115602751640736, "close": 1.1117981466354034, "volume": 275, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T16:48:39.958Z", "timeframe": 300, "open": 1.1491283205184328, "high": 1.1498557435805699, "low": 1.1487603654774041, "close": 1.1491715049697342, "volume": 902, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T16:53:39.958Z", "timeframe": 300, "open": 1.158192429811088, "high": 1.1585027507951622, "low": 1.1575374645910061, "close": 1.1583936780225823, "volume": 892, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T16:58:39.958Z", "timeframe": 300, "open": 1.1829347571833728, "high": 1.1839119079684817, "low": 1.1823048048051765, "close": 1.183098356549003, "volume": 566, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T17:03:39.958Z", "timeframe": 300, "open": 1.1656710799102277, "high": 1.1664722857923098, "low": 1.1650906011295001, "close": 1.1657156679782252, "volume": 251, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T17:08:39.958Z", "timeframe": 300, "open": 1.1331495401031466, "high": 1.1334178751714699, "low": 1.1327182427845917, "close": 1.1334178751714699, "volume": 544, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T17:13:39.958Z", "timeframe": 300, "open": 1.1812482350805802, "high": 1.1815643016745174, "low": 1.1809330947365047, "close": 1.1810057227658506, "volume": 320, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T17:18:39.958Z", "timeframe": 300, "open": 1.1575553884165528, "high": 1.1577262218016613, "low": 1.1566752388779797, "close": 1.1577262218016613, "volume": 998, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T17:23:39.958Z", "timeframe": 300, "open": 1.1150602986603801, "high": 1.1151503325304066, "low": 1.1140934993089904, "close": 1.1149558033164895, "volume": 400, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T17:28:39.958Z", "timeframe": 300, "open": 1.15291680911109, "high": 1.1536062094841004, "low": 1.1523619003917007, "close": 1.152561766189436, "volume": 382, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T17:33:39.958Z", "timeframe": 300, "open": 1.1112931304466, "high": 1.1116243818296154, "low": 1.110693083952684, "close": 1.1114217695996855, "volume": 422, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T17:38:39.958Z", "timeframe": 300, "open": 1.1067435728321802, "high": 1.1069743459794366, "low": 1.1064089514623539, "close": 1.1068350689392725, "volume": 338, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T17:43:39.958Z", "timeframe": 300, "open": 1.1895883120032675, "high": 1.1901699011419946, "low": 1.1888544510586845, "close": 1.1895546121743148, "volume": 879, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T17:48:39.958Z", "timeframe": 300, "open": 1.1244206971780597, "high": 1.1251585334519667, "low": 1.1236734595864406, "close": 1.124252863413315, "volume": 428, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T17:53:39.958Z", "timeframe": 300, "open": 1.1022809507772262, "high": 1.102939595211934, "low": 1.1015398200596893, "close": 1.102007752313814, "volume": 813, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T17:58:39.958Z", "timeframe": 300, "open": 1.101337403793276, "high": 1.1019590686949736, "low": 1.1006492860300716, "close": 1.1014855578782548, "volume": 133, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T18:03:39.958Z", "timeframe": 300, "open": 1.1042835909719377, "high": 1.1049156103294795, "low": 1.103808027228543, "close": 1.1039293356520108, "volume": 419, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T18:08:39.958Z", "timeframe": 300, "open": 1.1211693556434732, "high": 1.121641773847153, "low": 1.1208783613547253, "close": 1.120908041973894, "volume": 455, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T18:13:39.958Z", "timeframe": 300, "open": 1.1484074606547492, "high": 1.1493364369284713, "low": 1.1482931765512516, "close": 1.1483526271836269, "volume": 393, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T18:18:39.958Z", "timeframe": 300, "open": 1.1263831133046736, "high": 1.1264151560467894, "low": 1.12591465930347, "close": 1.12591465930347, "volume": 704, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T18:23:39.958Z", "timeframe": 300, "open": 1.1875353270598001, "high": 1.18816085573572, "low": 1.187358193925501, "close": 1.1875842764215745, "volume": 138, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T18:28:39.958Z", "timeframe": 300, "open": 1.1582794435482866, "high": 1.1585746168167312, "low": 1.1579240745001624, "close": 1.1579240745001624, "volume": 199, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T18:33:39.958Z", "timeframe": 300, "open": 1.1507462828214188, "high": 1.1514495047593192, "low": 1.1503910926263845, "close": 1.1503910926263845, "volume": 458, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T18:38:39.958Z", "timeframe": 300, "open": 1.1815243802246358, "high": 1.1817393309930877, "low": 1.1813534585653256, "close": 1.181562600699624, "volume": 217, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T18:43:39.958Z", "timeframe": 300, "open": 1.1560451161443153, "high": 1.1565979622319167, "low": 1.1558657248574802, "close": 1.1562586328671025, "volume": 867, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T18:48:39.958Z", "timeframe": 300, "open": 1.1132401386182786, "high": 1.113379485191216, "low": 1.1125774372890915, "close": 1.1130750735531159, "volume": 624, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T18:53:39.958Z", "timeframe": 300, "open": 1.1162641880171218, "high": 1.1170487823626114, "low": 1.1154137446274783, "close": 1.11636368027276, "volume": 617, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T18:58:39.958Z", "timeframe": 300, "open": 1.1985168561835593, "high": 1.1994514110693235, "low": 1.198092304994778, "close": 1.1985515042496246, "volume": 123, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T19:03:39.958Z", "timeframe": 300, "open": 1.1760384237605737, "high": 1.176341457176372, "low": 1.1754688756161196, "close": 1.175884406612406, "volume": 985, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T19:08:39.958Z", "timeframe": 300, "open": 1.1035867297763782, "high": 1.103654143223167, "low": 1.1031627223061693, "close": 1.1034278743579886, "volume": 784, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T19:13:39.958Z", "timeframe": 300, "open": 1.1808198835005763, "high": 1.1811674631256297, "low": 1.1806667382240277, "close": 1.1807555542416068, "volume": 695, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T19:18:39.958Z", "timeframe": 300, "open": 1.1941254857903418, "high": 1.194502039210376, "low": 1.1937612294317883, "close": 1.1943905049209786, "volume": 117, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T19:23:39.958Z", "timeframe": 300, "open": 1.1528072506448304, "high": 1.1529322290216895, "low": 1.1518718486726078, "close": 1.1524583611552435, "volume": 689, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T19:28:39.958Z", "timeframe": 300, "open": 1.19602359852132, "high": 1.1960534796883098, "low": 1.1952799511488905, "close": 1.1956919080398924, "volume": 219, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T19:33:39.958Z", "timeframe": 300, "open": 1.1712534250681772, "high": 1.1715971506298415, "low": 1.170861113313555, "close": 1.1715476076038447, "volume": 374, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T19:38:39.958Z", "timeframe": 300, "open": 1.1847583452958135, "high": 1.1856247211309863, "low": 1.1840342964743638, "close": 1.1845070984091364, "volume": 377, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T19:43:39.958Z", "timeframe": 300, "open": 1.1683999399787441, "high": 1.1692652321946473, "low": 1.1682531431156598, "close": 1.1688844852115248, "volume": 894, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T19:48:39.958Z", "timeframe": 300, "open": 1.1299591479715698, "high": 1.1300863901378213, "low": 1.129851829633865, "close": 1.1299913686573448, "volume": 537, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T19:53:39.958Z", "timeframe": 300, "open": 1.1938349369965016, "high": 1.1938704925423131, "low": 1.193374200298556, "close": 1.1936678957142521, "volume": 526, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T19:58:39.958Z", "timeframe": 300, "open": 1.1464408315141743, "high": 1.1473854875184508, "low": 1.1458691604951303, "close": 1.1459826039287486, "volume": 705, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T20:03:39.958Z", "timeframe": 300, "open": 1.1961732793409958, "high": 1.1964607730638566, "low": 1.1959680273083289, "close": 1.1959680273083289, "volume": 1019, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T20:08:39.958Z", "timeframe": 300, "open": 1.1500375047416933, "high": 1.1504316361710134, "low": 1.1491879460020005, "close": 1.1500410338421365, "volume": 598, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T20:13:39.958Z", "timeframe": 300, "open": 1.1507006465734233, "high": 1.1515607141953774, "low": 1.1498528762974658, "close": 1.1511091288780673, "volume": 591, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T20:18:39.958Z", "timeframe": 300, "open": 1.1197205382960282, "high": 1.1204090329782954, "low": 1.1192339593552996, "close": 1.1196327182007084, "volume": 783, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T20:23:39.958Z", "timeframe": 300, "open": 1.1449201191054614, "high": 1.1459065325998883, "low": 1.143994402726958, "close": 1.1451693332311463, "volume": 396, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T20:28:39.958Z", "timeframe": 300, "open": 1.129645809528414, "high": 1.130269949269468, "low": 1.1287876449722862, "close": 1.1298332637425226, "volume": 714, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T20:33:39.958Z", "timeframe": 300, "open": 1.1014522456129763, "high": 1.1018078685927448, "low": 1.101315911136749, "close": 1.1015044442561615, "volume": 132, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T20:38:39.958Z", "timeframe": 300, "open": 1.120681449113527, "high": 1.1208811399395964, "low": 1.1205183359748032, "close": 1.1207432947239295, "volume": 908, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T20:43:39.958Z", "timeframe": 300, "open": 1.1465327646483061, "high": 1.147531876810973, "low": 1.1460716398710535, "close": 1.146823487288372, "volume": 1019, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T20:48:39.958Z", "timeframe": 300, "open": 1.15694192279216, "high": 1.157736789317614, "low": 1.1564328451421673, "close": 1.1572712573628985, "volume": 116, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T20:53:39.958Z", "timeframe": 300, "open": 1.129984767578167, "high": 1.1302692610246232, "low": 1.129773472587429, "close": 1.129773472587429, "volume": 288, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T20:58:39.958Z", "timeframe": 300, "open": 1.1367998499247012, "high": 1.1377776787347609, "low": 1.135989041215827, "close": 1.1365476830624257, "volume": 779, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T21:03:39.958Z", "timeframe": 300, "open": 1.1664104249561473, "high": 1.1671294213318915, "low": 1.1658728073194413, "close": 1.1663221087953501, "volume": 705, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T21:08:39.958Z", "timeframe": 300, "open": 1.123313901212433, "high": 1.1241016184844885, "low": 1.1224593353345917, "close": 1.1234671568782693, "volume": 561, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T21:13:39.958Z", "timeframe": 300, "open": 1.1308832719206556, "high": 1.1310844234156179, "low": 1.1303893076052538, "close": 1.1305737991799343, "volume": 318, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T21:18:39.958Z", "timeframe": 300, "open": 1.1568563054626315, "high": 1.1569618721540638, "low": 1.1567730359125945, "close": 1.1569618721540638, "volume": 652, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T21:23:39.958Z", "timeframe": 300, "open": 1.15648148688122, "high": 1.1571058701529977, "low": 1.1558449885875233, "close": 1.1560277016065525, "volume": 267, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T21:28:39.958Z", "timeframe": 300, "open": 1.14389491460749, "high": 1.1444585107257141, "low": 1.1436089109372185, "close": 1.1443902169455196, "volume": 994, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T21:33:39.958Z", "timeframe": 300, "open": 1.1300502340870067, "high": 1.1302481953705446, "low": 1.129071224277626, "close": 1.1297286441279815, "volume": 902, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T21:38:39.958Z", "timeframe": 300, "open": 1.1440560169606746, "high": 1.144740762161977, "low": 1.1435204316361045, "close": 1.1444994454583561, "volume": 944, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T21:43:39.958Z", "timeframe": 300, "open": 1.1227836498809114, "high": 1.1234548829732538, "low": 1.1222565236769375, "close": 1.1226267081666639, "volume": 1086, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T21:48:39.958Z", "timeframe": 300, "open": 1.1222060629881043, "high": 1.1231836991158888, "low": 1.1213724365874427, "close": 1.1225646723016627, "volume": 105, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T21:53:39.958Z", "timeframe": 300, "open": 1.1802906047189272, "high": 1.1811933784772835, "low": 1.1798917890514091, "close": 1.1801637402700416, "volume": 786, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T21:58:39.958Z", "timeframe": 300, "open": 1.1514242941333324, "high": 1.152159758773465, "low": 1.1506063814436882, "close": 1.1511670777184353, "volume": 969, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T22:03:39.958Z", "timeframe": 300, "open": 1.1471440386039247, "high": 1.1477886754748239, "low": 1.1465673459863883, "close": 1.147473123582644, "volume": 595, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T22:08:39.958Z", "timeframe": 300, "open": 1.1779186061403508, "high": 1.178095578358872, "low": 1.1777071168227133, "close": 1.178095578358872, "volume": 105, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T22:13:39.958Z", "timeframe": 300, "open": 1.1916454586448115, "high": 1.1926393849416224, "low": 1.1910080929796278, "close": 1.1913347922537159, "volume": 481, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T22:18:39.958Z", "timeframe": 300, "open": 1.1565666884694596, "high": 1.1571676404282407, "low": 1.1563923346139249, "close": 1.1564069034234166, "volume": 750, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T22:23:39.958Z", "timeframe": 300, "open": 1.1745752371616254, "high": 1.1751731966211723, "low": 1.1745584907432147, "close": 1.1747538925703678, "volume": 347, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T22:28:39.958Z", "timeframe": 300, "open": 1.197729258649256, "high": 1.1981884210826874, "low": 1.19733615664124, "close": 1.1981884210826874, "volume": 884, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T22:33:39.958Z", "timeframe": 300, "open": 1.148099136130409, "high": 1.148830979142035, "low": 1.147512641405974, "close": 1.1482719680835634, "volume": 304, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T22:38:39.958Z", "timeframe": 300, "open": 1.1342130876985697, "high": 1.1348228084054603, "low": 1.1339953985437956, "close": 1.1343978827228545, "volume": 325, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T22:43:39.958Z", "timeframe": 300, "open": 1.1045521690259017, "high": 1.1049374352482448, "low": 1.104056883384593, "close": 1.104081122352307, "volume": 275, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T22:48:39.958Z", "timeframe": 300, "open": 1.1269186548632886, "high": 1.127097968436339, "low": 1.1264796199126406, "close": 1.1268660199307319, "volume": 604, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T22:53:39.958Z", "timeframe": 300, "open": 1.1648097436149216, "high": 1.1653755319678447, "low": 1.1644235052364167, "close": 1.1644235052364167, "volume": 781, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T22:58:39.958Z", "timeframe": 300, "open": 1.1149297187725373, "high": 1.1159132697325094, "low": 1.114119440705813, "close": 1.1154275287543918, "volume": 273, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T23:03:39.958Z", "timeframe": 300, "open": 1.1994114420098743, "high": 1.2002645122729076, "low": 1.1985633982118602, "close": 1.1990557925570133, "volume": 447, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T23:08:39.958Z", "timeframe": 300, "open": 1.1883639410035705, "high": 1.1891638237098086, "low": 1.1880209921979392, "close": 1.1880209921979392, "volume": 519, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T23:13:39.958Z", "timeframe": 300, "open": 1.1424990437736418, "high": 1.1428832916084124, "low": 1.141906887007949, "close": 1.1428832916084124, "volume": 257, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T23:18:39.958Z", "timeframe": 300, "open": 1.1914516583290573, "high": 1.1924311454168992, "low": 1.191362379389658, "close": 1.1916303762112648, "volume": 218, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T23:23:39.958Z", "timeframe": 300, "open": 1.196606770379869, "high": 1.1974548126519546, "low": 1.1958988703793922, "close": 1.1967705586714066, "volume": 609, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T23:28:39.958Z", "timeframe": 300, "open": 1.1312702713353386, "high": 1.1322442095821328, "low": 1.1307869171175777, "close": 1.1307869171175777, "volume": 444, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T23:33:39.958Z", "timeframe": 300, "open": 1.1906405294519276, "high": 1.1915195629360638, "low": 1.190595076331311, "close": 1.190595076331311, "volume": 738, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T23:38:39.958Z", "timeframe": 300, "open": 1.1352890347605273, "high": 1.1356418079472206, "low": 1.1351268062386508, "close": 1.1356418079472206, "volume": 589, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T23:43:39.958Z", "timeframe": 300, "open": 1.1164469903815888, "high": 1.117088346367089, "low": 1.1155307446357554, "close": 1.116112644253326, "volume": 763, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T23:48:39.958Z", "timeframe": 300, "open": 1.1315586188478808, "high": 1.1319554905219325, "low": 1.1312070988668554, "close": 1.1319554905219325, "volume": 413, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T23:53:39.958Z", "timeframe": 300, "open": 1.1000027860425587, "high": 1.1006960633872147, "low": 1.0997827272990972, "close": 1.10046927541639, "volume": 284, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-05T23:58:39.958Z", "timeframe": 300, "open": 1.1116600009407753, "high": 1.111845540437945, "low": 1.1110020030128627, "close": 1.111845540437945, "volume": 628, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T00:03:39.958Z", "timeframe": 300, "open": 1.1719699211447365, "high": 1.1725495457580506, "low": 1.1713245166227106, "close": 1.171616556686812, "volume": 337, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T00:08:39.958Z", "timeframe": 300, "open": 1.1513102960948722, "high": 1.1516880579204967, "low": 1.1508905881369265, "close": 1.1515720201835722, "volume": 341, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T00:13:39.958Z", "timeframe": 300, "open": 1.1346744733960126, "high": 1.1354451388779319, "low": 1.1341874101216458, "close": 1.134220702603056, "volume": 1058, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T00:18:39.958Z", "timeframe": 300, "open": 1.1692687310268537, "high": 1.1693762317437417, "low": 1.1688936502364626, "close": 1.1688936502364626, "volume": 454, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T00:23:39.958Z", "timeframe": 300, "open": 1.1415133379112734, "high": 1.1424141747170964, "low": 1.1408651497779188, "close": 1.1411067774104682, "volume": 281, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T00:28:39.958Z", "timeframe": 300, "open": 1.1190222625271242, "high": 1.1195660637853893, "low": 1.11894296442586, "close": 1.1191104362788011, "volume": 671, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T00:33:39.958Z", "timeframe": 300, "open": 1.124506249762483, "high": 1.1248553516587338, "low": 1.1240654570718893, "close": 1.1244915936162916, "volume": 769, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T00:38:39.958Z", "timeframe": 300, "open": 1.123438083163161, "high": 1.1238296617123678, "low": 1.122546865843821, "close": 1.1237804052494695, "volume": 326, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T00:43:39.958Z", "timeframe": 300, "open": 1.1314406721923158, "high": 1.1318934264637128, "low": 1.1312014165113138, "close": 1.1315083301247408, "volume": 344, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T00:48:39.958Z", "timeframe": 300, "open": 1.1282515156875126, "high": 1.1286053193629175, "low": 1.1275840271398727, "close": 1.1286053193629175, "volume": 740, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T00:53:39.958Z", "timeframe": 300, "open": 1.1383345497456985, "high": 1.1384668981490949, "low": 1.1378746456456528, "close": 1.138293989992738, "volume": 707, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T00:58:39.958Z", "timeframe": 300, "open": 1.1374749504938033, "high": 1.1377125324587285, "low": 1.136993687730529, "close": 1.136993687730529, "volume": 167, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T01:03:39.958Z", "timeframe": 300, "open": 1.1214234487681813, "high": 1.121795990371216, "low": 1.1207617887223806, "close": 1.1216592307108504, "volume": 846, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T01:08:39.958Z", "timeframe": 300, "open": 1.1330570977118535, "high": 1.1340283961769635, "low": 1.1327856691372868, "close": 1.1335562282300802, "volume": 142, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T01:13:39.958Z", "timeframe": 300, "open": 1.1732103265118923, "high": 1.1736857471992683, "low": 1.17293577303199, "close": 1.173290905753333, "volume": 368, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T01:18:39.958Z", "timeframe": 300, "open": 1.1287488651445696, "high": 1.1292871211362685, "low": 1.1279779542831152, "close": 1.1290082381607434, "volume": 139, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T01:23:39.958Z", "timeframe": 300, "open": 1.1409196451591317, "high": 1.1409482631845673, "low": 1.1404756011510984, "close": 1.1409401617874146, "volume": 612, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T01:28:39.958Z", "timeframe": 300, "open": 1.1250417026908996, "high": 1.1256406572913602, "low": 1.1247998645931485, "close": 1.1247998645931485, "volume": 102, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T01:33:39.958Z", "timeframe": 300, "open": 1.1699628823290906, "high": 1.170736063465014, "low": 1.1697955764590484, "close": 1.1704199606789707, "volume": 870, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T01:38:39.958Z", "timeframe": 300, "open": 1.186612497902904, "high": 1.186795666101093, "low": 1.1862987828458902, "close": 1.186520555960847, "volume": 386, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T01:43:39.958Z", "timeframe": 300, "open": 1.1772473918525106, "high": 1.1777256921418713, "low": 1.1770939382940684, "close": 1.1777256921418713, "volume": 830, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T01:48:39.958Z", "timeframe": 300, "open": 1.148844805247617, "high": 1.1493358175499977, "low": 1.1485174086967458, "close": 1.1487492754873427, "volume": 188, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T01:53:39.958Z", "timeframe": 300, "open": 1.1631408788539261, "high": 1.163198097492047, "low": 1.162796384872851, "close": 1.1630041508104518, "volume": 595, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T01:58:39.958Z", "timeframe": 300, "open": 1.122962321008768, "high": 1.123334267594638, "low": 1.1227320169617494, "close": 1.1232023308943762, "volume": 1006, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T02:03:39.958Z", "timeframe": 300, "open": 1.1730354301483517, "high": 1.1737067498682294, "low": 1.1721363606048703, "close": 1.172939638709075, "volume": 1052, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T02:08:39.958Z", "timeframe": 300, "open": 1.1984858919175083, "high": 1.1990572584202892, "low": 1.1980279614345164, "close": 1.1980279614345164, "volume": 743, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T02:13:39.958Z", "timeframe": 300, "open": 1.1603174234008529, "high": 1.1609511866678544, "low": 1.1601411428923523, "close": 1.16035178607826, "volume": 677, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T02:18:39.958Z", "timeframe": 300, "open": 1.1850828976809769, "high": 1.18584280999706, "low": 1.1846532518489397, "close": 1.1850468230247828, "volume": 166, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T02:23:39.958Z", "timeframe": 300, "open": 1.1805373509378065, "high": 1.1806173567974483, "low": 1.1802074467921813, "close": 1.1804021564114835, "volume": 1046, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T02:28:39.958Z", "timeframe": 300, "open": 1.1091892328461692, "high": 1.1100988147518769, "low": 1.1087102726859268, "close": 1.1087102726859268, "volume": 428, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T02:33:39.958Z", "timeframe": 300, "open": 1.1779681156836739, "high": 1.178546620249573, "low": 1.1774212278242024, "close": 1.1783086003992977, "volume": 118, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T02:38:39.958Z", "timeframe": 300, "open": 1.1302077844163718, "high": 1.1309403227525288, "low": 1.129859204631743, "close": 1.129859204631743, "volume": 357, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T02:43:39.958Z", "timeframe": 300, "open": 1.1380127671358313, "high": 1.1384088698647554, "low": 1.1376327391257255, "close": 1.1384088698647554, "volume": 707, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T02:48:39.958Z", "timeframe": 300, "open": 1.1526165958710077, "high": 1.153436561223133, "low": 1.1517854637833194, "close": 1.1530191409515882, "volume": 278, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T02:53:39.958Z", "timeframe": 300, "open": 1.151004293939839, "high": 1.1518750944879612, "low": 1.1505757161359316, "close": 1.150926939493512, "volume": 765, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T02:58:39.958Z", "timeframe": 300, "open": 1.1904263273409041, "high": 1.1906110922487687, "low": 1.1900726181708954, "close": 1.1904783137933557, "volume": 653, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T03:03:39.958Z", "timeframe": 300, "open": 1.1762257786315138, "high": 1.176791343367383, "low": 1.1753947998173202, "close": 1.1759533362302195, "volume": 836, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T03:08:39.958Z", "timeframe": 300, "open": 1.1594973935941975, "high": 1.1604811184118309, "low": 1.1588284967064082, "close": 1.159239535368625, "volume": 812, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T03:13:39.958Z", "timeframe": 300, "open": 1.1044940054458796, "high": 1.105161914482776, "low": 1.103818440316012, "close": 1.1049082964607804, "volume": 1021, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T03:18:39.958Z", "timeframe": 300, "open": 1.1958284849561494, "high": 1.1962374150744295, "low": 1.1954661600322964, "close": 1.1961085857729414, "volume": 257, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T03:23:39.958Z", "timeframe": 300, "open": 1.1659968051669594, "high": 1.1667768266291747, "low": 1.1650876132569634, "close": 1.1661497252381303, "volume": 742, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T03:28:39.958Z", "timeframe": 300, "open": 1.1524847212223095, "high": 1.1527980865350578, "low": 1.1522436028421523, "close": 1.1527980865350578, "volume": 263, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T03:33:39.958Z", "timeframe": 300, "open": 1.1624673890944552, "high": 1.1631956657106106, "low": 1.161863760489263, "close": 1.1621500068846213, "volume": 881, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T03:38:39.958Z", "timeframe": 300, "open": 1.1761967318966071, "high": 1.177028664903229, "low": 1.175293518283492, "close": 1.176325032870332, "volume": 862, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T03:43:39.958Z", "timeframe": 300, "open": 1.1813532078573081, "high": 1.182340282701708, "low": 1.181056674386676, "close": 1.181056674386676, "volume": 828, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T03:48:39.958Z", "timeframe": 300, "open": 1.1288579199242366, "high": 1.1292610958409108, "low": 1.1284255593838495, "close": 1.1292610958409108, "volume": 596, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T03:53:39.958Z", "timeframe": 300, "open": 1.1953548879566172, "high": 1.1959228462892533, "low": 1.194753418083851, "close": 1.195384826959539, "volume": 485, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T03:58:39.958Z", "timeframe": 300, "open": 1.1790604017371686, "high": 1.1792323218601868, "low": 1.1789431377083508, "close": 1.1789544789594497, "volume": 204, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T04:03:39.958Z", "timeframe": 300, "open": 1.1795531118996139, "high": 1.1802946551240214, "low": 1.1793237161166683, "close": 1.1795398561823656, "volume": 315, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T04:08:39.958Z", "timeframe": 300, "open": 1.1772103852611635, "high": 1.1776891952772344, "low": 1.1771847034406244, "close": 1.177677304271451, "volume": 897, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T04:13:39.958Z", "timeframe": 300, "open": 1.1479342848564629, "high": 1.1481978408553262, "low": 1.1475111925705903, "close": 1.1480552126306987, "volume": 457, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T04:18:39.958Z", "timeframe": 300, "open": 1.188581468520568, "high": 1.1890471886193836, "low": 1.1879674691178395, "close": 1.1890471886193836, "volume": 342, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T04:23:39.958Z", "timeframe": 300, "open": 1.1569636148906, "high": 1.1571543207399209, "low": 1.1562370427220723, "close": 1.1570816213681532, "volume": 116, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T04:28:39.958Z", "timeframe": 300, "open": 1.15369255631456, "high": 1.1538239692586716, "low": 1.1529563192558274, "close": 1.153766347944766, "volume": 599, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T04:33:39.958Z", "timeframe": 300, "open": 1.1428948581633913, "high": 1.142997536108572, "low": 1.1420151658963664, "close": 1.1425419937632866, "volume": 769, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T04:38:39.958Z", "timeframe": 300, "open": 1.1397673835598752, "high": 1.1401797789957695, "low": 1.139386066131676, "close": 1.1395477092482094, "volume": 635, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T04:43:39.958Z", "timeframe": 300, "open": 1.1653037239779085, "high": 1.1659085358497439, "low": 1.1648483145361028, "close": 1.1649383961187147, "volume": 356, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T04:48:39.958Z", "timeframe": 300, "open": 1.1903984608369735, "high": 1.1909610403751665, "low": 1.1901807662858348, "close": 1.1904020728967588, "volume": 689, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T04:53:39.958Z", "timeframe": 300, "open": 1.1937321659223614, "high": 1.1945852603374343, "low": 1.1932517693762081, "close": 1.1934921748395815, "volume": 271, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T04:58:39.958Z", "timeframe": 300, "open": 1.1103773643459962, "high": 1.1110661054940962, "low": 1.1101759066549315, "close": 1.1102864404310677, "volume": 193, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T05:03:39.958Z", "timeframe": 300, "open": 1.1343194145946305, "high": 1.1350586093440207, "low": 1.1341187770095773, "close": 1.1341187770095773, "volume": 823, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T05:08:39.958Z", "timeframe": 300, "open": 1.1628687659974406, "high": 1.1629909092690984, "low": 1.162425111952228, "close": 1.162425111952228, "volume": 401, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T05:13:39.958Z", "timeframe": 300, "open": 1.1035599504770968, "high": 1.103618100088123, "low": 1.1029173437849442, "close": 1.1034374850802247, "volume": 1046, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T05:18:39.958Z", "timeframe": 300, "open": 1.1956795620771676, "high": 1.1966085442062013, "low": 1.1951208496440053, "close": 1.1959485095173954, "volume": 821, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T05:23:39.958Z", "timeframe": 300, "open": 1.1794250216029822, "high": 1.1803802988703829, "low": 1.1787824766648995, "close": 1.1790830174791447, "volume": 836, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T05:28:39.958Z", "timeframe": 300, "open": 1.1403765195816287, "high": 1.1407076456229084, "low": 1.140096495118219, "close": 1.1406310223336316, "volume": 1004, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T05:33:39.958Z", "timeframe": 300, "open": 1.1360503492408094, "high": 1.136664902233457, "low": 1.1353631637824622, "close": 1.136207714992701, "volume": 686, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T05:38:39.958Z", "timeframe": 300, "open": 1.1291566307150092, "high": 1.1295999735469942, "low": 1.1283336342765284, "close": 1.1295999735469942, "volume": 1025, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T05:43:39.958Z", "timeframe": 300, "open": 1.1186773323659278, "high": 1.1191087554736456, "low": 1.1179995567945429, "close": 1.1191087554736456, "volume": 366, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T05:48:39.958Z", "timeframe": 300, "open": 1.1451708179666824, "high": 1.1456062806019458, "low": 1.144300955796509, "close": 1.1454723002254992, "volume": 389, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T05:53:39.958Z", "timeframe": 300, "open": 1.1851727290643463, "high": 1.1858020234826157, "low": 1.1847584494762597, "close": 1.1848019403606824, "volume": 168, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T05:58:39.958Z", "timeframe": 300, "open": 1.1638609597429692, "high": 1.1643731473663346, "low": 1.1631523031241033, "close": 1.1640219598380857, "volume": 647, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T06:03:39.958Z", "timeframe": 300, "open": 1.1731262876753543, "high": 1.173942513550503, "low": 1.173013018409964, "close": 1.1734525686415451, "volume": 463, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T06:08:39.958Z", "timeframe": 300, "open": 1.107134516391937, "high": 1.1077401504649307, "low": 1.1069587531172358, "close": 1.1069587531172358, "volume": 248, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T06:13:39.958Z", "timeframe": 300, "open": 1.1716516584904864, "high": 1.1720671013114772, "low": 1.1709157707984883, "close": 1.1711885078548188, "volume": 875, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T06:18:39.958Z", "timeframe": 300, "open": 1.1131283394309146, "high": 1.1131963951754495, "low": 1.1126643139007697, "close": 1.1128184235712242, "volume": 1076, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T06:23:39.958Z", "timeframe": 300, "open": 1.1719535404174697, "high": 1.1725656743816721, "low": 1.1714578298832492, "close": 1.1719002921982995, "volume": 216, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T06:28:39.958Z", "timeframe": 300, "open": 1.1645007585425584, "high": 1.1652569739329603, "low": 1.1635333082129273, "close": 1.164104549204198, "volume": 639, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T06:33:39.958Z", "timeframe": 300, "open": 1.1580131771133046, "high": 1.1582089395299995, "low": 1.1575899154810765, "close": 1.1575899154810765, "volume": 681, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T06:38:39.958Z", "timeframe": 300, "open": 1.179823664352533, "high": 1.1806077356267108, "low": 1.1795644546867243, "close": 1.1798555312619077, "volume": 471, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T06:43:39.958Z", "timeframe": 300, "open": 1.171511821455964, "high": 1.1722060125959588, "low": 1.1707689112233215, "close": 1.17144816886613, "volume": 727, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T06:48:39.958Z", "timeframe": 300, "open": 1.1687074944806108, "high": 1.1687321203822423, "low": 1.1685738640206644, "close": 1.1686267072164107, "volume": 955, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T06:53:39.958Z", "timeframe": 300, "open": 1.1241786903557147, "high": 1.1250643232303226, "low": 1.1234582118921819, "close": 1.1246343946491044, "volume": 121, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T06:58:39.958Z", "timeframe": 300, "open": 1.1703251870917355, "high": 1.1704966878464933, "low": 1.1698496585462308, "close": 1.1698496585462308, "volume": 384, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T07:03:39.958Z", "timeframe": 300, "open": 1.1112818279435226, "high": 1.1116373411655898, "low": 1.1103250079344904, "close": 1.1114404640938271, "volume": 233, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T07:08:39.958Z", "timeframe": 300, "open": 1.1399511328232073, "high": 1.1407332394072427, "low": 1.1392196078690155, "close": 1.139487763987402, "volume": 959, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T07:13:39.958Z", "timeframe": 300, "open": 1.1641078763404553, "high": 1.1642973323602914, "low": 1.1633283733139141, "close": 1.1636757376470865, "volume": 1058, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T07:18:39.958Z", "timeframe": 300, "open": 1.102910290813303, "high": 1.1039058602868173, "low": 1.1022723763518105, "close": 1.1029960200287747, "volume": 480, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T07:23:39.958Z", "timeframe": 300, "open": 1.1976149207637332, "high": 1.1981091539898125, "low": 1.1972197239923976, "close": 1.1981091539898125, "volume": 548, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T07:28:39.958Z", "timeframe": 300, "open": 1.1876197388453653, "high": 1.188284941966309, "low": 1.1866555972693575, "close": 1.1872872486947723, "volume": 711, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T07:33:39.958Z", "timeframe": 300, "open": 1.1778491550061445, "high": 1.178642128353264, "low": 1.1777052975081448, "close": 1.178001543851164, "volume": 804, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T07:38:39.958Z", "timeframe": 300, "open": 1.1541244697668278, "high": 1.1546580490820422, "low": 1.1535713133240986, "close": 1.1539714730320358, "volume": 767, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T07:43:39.958Z", "timeframe": 300, "open": 1.187959183157002, "high": 1.188391087454518, "low": 1.18763577528729, "close": 1.188391087454518, "volume": 912, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T07:48:39.958Z", "timeframe": 300, "open": 1.1644841028149224, "high": 1.164972735815643, "low": 1.1641433369908891, "close": 1.164972735815643, "volume": 352, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T07:53:39.958Z", "timeframe": 300, "open": 1.191660587071322, "high": 1.1918178440928768, "low": 1.1906993709545515, "close": 1.191266814561155, "volume": 1035, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T07:58:39.958Z", "timeframe": 300, "open": 1.1410059644080028, "high": 1.1415825579360241, "low": 1.1407800158643844, "close": 1.1410760164634755, "volume": 155, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T08:03:39.958Z", "timeframe": 300, "open": 1.14544341469092, "high": 1.1461802850723655, "low": 1.1453161132347156, "close": 1.1453161132347156, "volume": 1021, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T08:08:39.958Z", "timeframe": 300, "open": 1.1926897686805948, "high": 1.1929652857523525, "low": 1.1917757701804537, "close": 1.1925580073612256, "volume": 872, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T08:13:39.958Z", "timeframe": 300, "open": 1.1657237911468064, "high": 1.166004729314903, "low": 1.1650676118042418, "close": 1.1656463422479908, "volume": 373, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T08:18:39.958Z", "timeframe": 300, "open": 1.1081661492020967, "high": 1.1084968425285275, "low": 1.107820464025871, "close": 1.1084968425285275, "volume": 179, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T08:23:39.958Z", "timeframe": 300, "open": 1.1279591623415253, "high": 1.1284265246431457, "low": 1.12762457756752, "close": 1.12762457756752, "volume": 849, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T08:28:39.958Z", "timeframe": 300, "open": 1.175352374709803, "high": 1.1762760711771834, "low": 1.1747395665193106, "close": 1.1750158930609778, "volume": 794, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T08:33:39.958Z", "timeframe": 300, "open": 1.1492897498528034, "high": 1.1498206972139648, "low": 1.1483333878376596, "close": 1.1491417391412173, "volume": 1028, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T08:38:39.958Z", "timeframe": 300, "open": 1.1642072925284133, "high": 1.1643850122875778, "low": 1.1638166385997049, "close": 1.1638166385997049, "volume": 260, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T08:43:39.958Z", "timeframe": 300, "open": 1.1571662619953544, "high": 1.1577022213817652, "low": 1.1568729565608444, "close": 1.1575010961051524, "volume": 963, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T08:48:39.958Z", "timeframe": 300, "open": 1.111765719515001, "high": 1.1124335956887017, "low": 1.1113056544178574, "close": 1.1118222619493938, "volume": 771, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T08:53:39.958Z", "timeframe": 300, "open": 1.1998451336237557, "high": 1.2007756170532906, "low": 1.199319053541113, "close": 1.2001139210880398, "volume": 139, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T08:58:39.958Z", "timeframe": 300, "open": 1.1113818267324072, "high": 1.1114543924098386, "low": 1.1110798026944189, "close": 1.1113502066062573, "volume": 699, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T09:03:39.958Z", "timeframe": 300, "open": 1.1726356904746202, "high": 1.173436632052443, "low": 1.1717573681619509, "close": 1.1731028482977786, "volume": 1010, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T09:08:39.958Z", "timeframe": 300, "open": 1.114257827132364, "high": 1.1147393203329736, "low": 1.1137997965310946, "close": 1.1139185844538304, "volume": 251, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T09:13:39.958Z", "timeframe": 300, "open": 1.1671419383170563, "high": 1.167708003220534, "low": 1.166566938071829, "close": 1.1667246193875747, "volume": 580, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T09:18:39.958Z", "timeframe": 300, "open": 1.1427510978346156, "high": 1.1429135366146441, "low": 1.142363902488705, "close": 1.142363902488705, "volume": 689, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T09:23:39.958Z", "timeframe": 300, "open": 1.107510788405824, "high": 1.1077280271527576, "low": 1.1074916582855832, "close": 1.1077280271527576, "volume": 422, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T09:28:39.958Z", "timeframe": 300, "open": 1.1665697253974803, "high": 1.1670043309473617, "low": 1.1663803067289331, "close": 1.1666609818708744, "volume": 1038, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T09:33:39.958Z", "timeframe": 300, "open": 1.1226953549808332, "high": 1.1231286380288872, "low": 1.1219910344454267, "close": 1.1225171375142722, "volume": 409, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T09:38:39.958Z", "timeframe": 300, "open": 1.1382500348982152, "high": 1.1387453700377335, "low": 1.1374940539452771, "close": 1.1384546648014875, "volume": 233, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T09:43:39.958Z", "timeframe": 300, "open": 1.1094241923656745, "high": 1.110148948787154, "low": 1.109019707969714, "close": 1.109019707969714, "volume": 1037, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T09:48:39.958Z", "timeframe": 300, "open": 1.1529290966227623, "high": 1.153782430534166, "low": 1.1521234893545396, "close": 1.1529965032198617, "volume": 190, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T09:53:39.958Z", "timeframe": 300, "open": 1.1530362010789408, "high": 1.1535405863202157, "low": 1.1526609760831281, "close": 1.1527958538888377, "volume": 684, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T09:58:39.958Z", "timeframe": 300, "open": 1.1250515412425321, "high": 1.1260276133361897, "low": 1.1246461977256954, "close": 1.125473097889439, "volume": 372, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T10:03:39.958Z", "timeframe": 300, "open": 1.1227487394812836, "high": 1.1227646893841114, "low": 1.1219850015654185, "close": 1.122615961385073, "volume": 481, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T10:08:39.958Z", "timeframe": 300, "open": 1.1655931066951697, "high": 1.1663279284293648, "low": 1.1645937891813234, "close": 1.165690799882726, "volume": 487, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T10:13:39.958Z", "timeframe": 300, "open": 1.117786763226547, "high": 1.1183135583493529, "low": 1.1172907503201517, "close": 1.1178421009489827, "volume": 826, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T10:18:39.958Z", "timeframe": 300, "open": 1.1300502412806521, "high": 1.1309402176727714, "low": 1.129387682602744, "close": 1.1299154255985564, "volume": 549, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T10:23:39.958Z", "timeframe": 300, "open": 1.1462697785397657, "high": 1.1468092753860222, "low": 1.1458053134531692, "close": 1.145837005894881, "volume": 1044, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T10:28:39.958Z", "timeframe": 300, "open": 1.1250694588192887, "high": 1.1252209073779444, "low": 1.1242414521097979, "close": 1.1249242987029968, "volume": 230, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T10:33:39.958Z", "timeframe": 300, "open": 1.1718102192725357, "high": 1.1722904276071395, "low": 1.1708348574228693, "close": 1.1722904276071395, "volume": 305, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T10:38:39.958Z", "timeframe": 300, "open": 1.1594740550041942, "high": 1.159895662710432, "low": 1.15866752163876, "close": 1.1597695295847759, "volume": 596, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T10:43:39.958Z", "timeframe": 300, "open": 1.1038322775339604, "high": 1.1042355689992953, "low": 1.103166626101782, "close": 1.1042355689992953, "volume": 622, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T10:48:39.958Z", "timeframe": 300, "open": 1.1839274728791875, "high": 1.184214499654268, "low": 1.1839049616826844, "close": 1.1839896359654756, "volume": 932, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T10:53:39.958Z", "timeframe": 300, "open": 1.1528656824351442, "high": 1.1537368567172446, "low": 1.1519555981052927, "close": 1.1531274136969951, "volume": 999, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T10:58:39.958Z", "timeframe": 300, "open": 1.1811651458495, "high": 1.1820536629659497, "low": 1.1808372029420213, "close": 1.1808575725914445, "volume": 166, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T11:03:39.958Z", "timeframe": 300, "open": 1.1387289913122725, "high": 1.1395928986117947, "low": 1.1385159574533432, "close": 1.1388631145528518, "volume": 980, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T11:08:39.958Z", "timeframe": 300, "open": 1.1003405364182608, "high": 1.101206393570574, "low": 1.1000783644799836, "close": 1.1005793186022663, "volume": 456, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T11:13:39.958Z", "timeframe": 300, "open": 1.1351678935825587, "high": 1.135546413520795, "low": 1.1348421492621477, "close": 1.1352640342853264, "volume": 1012, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T11:18:39.958Z", "timeframe": 300, "open": 1.1485651737724778, "high": 1.1486852592297812, "low": 1.1479053930512995, "close": 1.1486852592297812, "volume": 207, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T11:23:39.958Z", "timeframe": 300, "open": 1.1275476904288975, "high": 1.1279505955628104, "low": 1.1267980581158528, "close": 1.1275522232847934, "volume": 615, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T11:28:39.958Z", "timeframe": 300, "open": 1.151854056189986, "high": 1.1524939475329004, "low": 1.1513819410212627, "close": 1.1513819410212627, "volume": 320, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T11:33:39.958Z", "timeframe": 300, "open": 1.147487643676381, "high": 1.1484439144554714, "low": 1.1465179692728864, "close": 1.1473745199539938, "volume": 1070, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T11:38:39.958Z", "timeframe": 300, "open": 1.1378587720703297, "high": 1.1383554132180418, "low": 1.137394612034189, "close": 1.137394612034189, "volume": 259, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T11:43:39.958Z", "timeframe": 300, "open": 1.1965065579394776, "high": 1.1974045841945367, "low": 1.1955236064281811, "close": 1.1969973949043409, "volume": 382, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T11:48:39.958Z", "timeframe": 300, "open": 1.1242996519271846, "high": 1.1248737340507518, "low": 1.1234610474199507, "close": 1.124072883911147, "volume": 152, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T11:53:39.958Z", "timeframe": 300, "open": 1.1408253327470144, "high": 1.1417671541527121, "low": 1.1398752046188958, "close": 1.1404345474225503, "volume": 553, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T11:58:39.958Z", "timeframe": 300, "open": 1.1815353363417356, "high": 1.1816431011516604, "low": 1.1805385496039182, "close": 1.1811924662015252, "volume": 939, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T12:03:39.958Z", "timeframe": 300, "open": 1.1554492169424784, "high": 1.1556545255504875, "low": 1.154683327477868, "close": 1.1550626154858512, "volume": 818, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T12:08:39.958Z", "timeframe": 300, "open": 1.1073281612325212, "high": 1.1080460959689937, "low": 1.1064076784411783, "close": 1.107557508092183, "volume": 955, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T12:13:39.958Z", "timeframe": 300, "open": 1.1056756786601263, "high": 1.1066656642644206, "low": 1.1054724868098278, "close": 1.105616552846337, "volume": 1087, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T12:18:39.958Z", "timeframe": 300, "open": 1.1583987009483836, "high": 1.1589119686937532, "low": 1.1575561043274478, "close": 1.1580520878334903, "volume": 939, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T12:23:39.958Z", "timeframe": 300, "open": 1.1317023828921373, "high": 1.132082099793371, "low": 1.1311527801392802, "close": 1.132082099793371, "volume": 403, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T12:28:39.958Z", "timeframe": 300, "open": 1.186398988296819, "high": 1.1868244954471006, "low": 1.1862429005860233, "close": 1.1868244954471006, "volume": 538, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T12:33:39.958Z", "timeframe": 300, "open": 1.1317211604483433, "high": 1.132035612553087, "low": 1.1310222602611295, "close": 1.1313131722526184, "volume": 548, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T12:38:39.958Z", "timeframe": 300, "open": 1.1506297456402343, "high": 1.1510052236188246, "low": 1.1497167629341472, "close": 1.1502631375772494, "volume": 585, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T12:43:39.958Z", "timeframe": 300, "open": 1.1431103132789469, "high": 1.1437650513130637, "low": 1.142195189322891, "close": 1.1431430191547702, "volume": 223, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T12:48:39.958Z", "timeframe": 300, "open": 1.1811278384329187, "high": 1.1815579654760988, "low": 1.1802371401161904, "close": 1.1815579654760988, "volume": 367, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T12:53:39.958Z", "timeframe": 300, "open": 1.1045359090442324, "high": 1.1053081994578118, "low": 1.1037686707801408, "close": 1.1045993432361543, "volume": 804, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T12:58:39.958Z", "timeframe": 300, "open": 1.1644771397732598, "high": 1.1649913087001413, "low": 1.163505245763113, "close": 1.1644995250123182, "volume": 960, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T13:03:39.958Z", "timeframe": 300, "open": 1.175931389420874, "high": 1.1766338314326321, "low": 1.1755701572380322, "close": 1.1762417347282599, "volume": 403, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T13:08:39.958Z", "timeframe": 300, "open": 1.17889315239659, "high": 1.1789260806901698, "low": 1.1783683275054668, "close": 1.1787487857547074, "volume": 652, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T13:13:39.958Z", "timeframe": 300, "open": 1.169348602368904, "high": 1.1694638100566408, "low": 1.168784174845714, "close": 1.169200360591488, "volume": 409, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T13:18:39.958Z", "timeframe": 300, "open": 1.181009302770161, "high": 1.1811982355067772, "low": 1.1810079311679618, "close": 1.1811982355067772, "volume": 318, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T13:23:39.958Z", "timeframe": 300, "open": 1.176284769693718, "high": 1.1770546545475105, "low": 1.1753991040512053, "close": 1.1767550091987957, "volume": 102, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T13:28:39.958Z", "timeframe": 300, "open": 1.1303755963788622, "high": 1.1312357837840137, "low": 1.1301332950057263, "close": 1.1307656683678593, "volume": 1005, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T13:33:39.958Z", "timeframe": 300, "open": 1.1676673967956583, "high": 1.168633934001866, "low": 1.1669935060900591, "close": 1.1677484098564581, "volume": 186, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T13:38:39.958Z", "timeframe": 300, "open": 1.1087228844349482, "high": 1.1090038406173703, "low": 1.1083863213210727, "close": 1.108794808467439, "volume": 145, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T13:43:39.958Z", "timeframe": 300, "open": 1.1080093926405052, "high": 1.1082986090793403, "low": 1.1071844374687245, "close": 1.1076185448992493, "volume": 489, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T13:48:39.958Z", "timeframe": 300, "open": 1.1595958933606025, "high": 1.1605231553196005, "low": 1.1588311997771479, "close": 1.1600451647851024, "volume": 880, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T13:53:39.958Z", "timeframe": 300, "open": 1.166816075135321, "high": 1.1670792393201226, "low": 1.1666073997669923, "close": 1.1670274174403528, "volume": 126, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T13:58:39.958Z", "timeframe": 300, "open": 1.1023294890878614, "high": 1.1027028459501769, "low": 1.1013355065771775, "close": 1.1025227383212075, "volume": 604, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T14:03:39.958Z", "timeframe": 300, "open": 1.1903139453110287, "high": 1.1906091702372308, "low": 1.1897625876685096, "close": 1.1901097834943055, "volume": 314, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T14:08:39.958Z", "timeframe": 300, "open": 1.1391029488612765, "high": 1.1397679121034845, "low": 1.1386481853620525, "close": 1.1390607709063039, "volume": 729, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T14:13:39.958Z", "timeframe": 300, "open": 1.1277160593678581, "high": 1.1283487813013993, "low": 1.1274321692771416, "close": 1.1277019895225158, "volume": 872, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T14:18:39.958Z", "timeframe": 300, "open": 1.149265298606258, "high": 1.1495234500540992, "low": 1.148674777754488, "close": 1.1495234500540992, "volume": 814, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T14:23:39.958Z", "timeframe": 300, "open": 1.1337591544975747, "high": 1.1346128908060105, "low": 1.133307546793647, "close": 1.13341116548228, "volume": 938, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T14:28:39.958Z", "timeframe": 300, "open": 1.15541985620116, "high": 1.1557395843782297, "low": 1.1548990625229034, "close": 1.1557395843782297, "volume": 419, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T14:33:39.958Z", "timeframe": 300, "open": 1.1842999517001798, "high": 1.185106775296737, "low": 1.1839994842883408, "close": 1.1843340280382788, "volume": 261, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T14:38:39.958Z", "timeframe": 300, "open": 1.1852327404437473, "high": 1.185554686782075, "low": 1.1843676821012048, "close": 1.185461304254264, "volume": 521, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T14:43:39.958Z", "timeframe": 300, "open": 1.1998011968167595, "high": 1.2000276695136836, "low": 1.1991197613020719, "close": 1.1994612415760102, "volume": 226, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T14:48:39.958Z", "timeframe": 300, "open": 1.1281723887030288, "high": 1.1291056523060243, "low": 1.1274285892435547, "close": 1.1286672435919896, "volume": 573, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T14:53:39.958Z", "timeframe": 300, "open": 1.1766081960645995, "high": 1.1769059765714187, "low": 1.1764444428011709, "close": 1.1764444428011709, "volume": 564, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T14:58:39.958Z", "timeframe": 300, "open": 1.1674719995076481, "high": 1.1678704529417994, "low": 1.1672027007302286, "close": 1.1678704529417994, "volume": 381, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T15:03:39.958Z", "timeframe": 300, "open": 1.146499667640296, "high": 1.1474079143774742, "low": 1.1459441727275188, "close": 1.146914864601547, "volume": 699, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T15:08:39.958Z", "timeframe": 300, "open": 1.1796911328660629, "high": 1.1799224809551354, "low": 1.179040690227758, "close": 1.1792638804016682, "volume": 365, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T15:13:39.958Z", "timeframe": 300, "open": 1.1982533297082307, "high": 1.1986468009321094, "low": 1.198031855300713, "close": 1.198031855300713, "volume": 966, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T15:18:39.958Z", "timeframe": 300, "open": 1.134792933041656, "high": 1.1352493422501924, "low": 1.1342648868309362, "close": 1.134871950252297, "volume": 209, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T15:23:39.958Z", "timeframe": 300, "open": 1.1563329588666726, "high": 1.156824991722163, "low": 1.1561770138251313, "close": 1.156824991722163, "volume": 741, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T15:28:39.958Z", "timeframe": 300, "open": 1.1341951615307604, "high": 1.1346955906188614, "low": 1.1333072945322669, "close": 1.133704915280074, "volume": 134, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T15:33:39.958Z", "timeframe": 300, "open": 1.142175273655565, "high": 1.1430312952647725, "low": 1.1412360291167252, "close": 1.1425444172560648, "volume": 634, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T15:38:39.958Z", "timeframe": 300, "open": 1.1604330265005884, "high": 1.1609110276668355, "low": 1.1595029117475255, "close": 1.1605394525083954, "volume": 1070, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T15:43:39.958Z", "timeframe": 300, "open": 1.1531208383183762, "high": 1.1536130112759213, "low": 1.1529157902542482, "close": 1.1529157902542482, "volume": 888, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T15:48:39.958Z", "timeframe": 300, "open": 1.1929406456513028, "high": 1.1931632158456105, "low": 1.1920169430269667, "close": 1.1929874254475297, "volume": 779, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T15:53:39.958Z", "timeframe": 300, "open": 1.1026686922076283, "high": 1.103503358280287, "low": 1.1024083428660212, "close": 1.102780887347238, "volume": 369, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T15:58:39.958Z", "timeframe": 300, "open": 1.1929562235406264, "high": 1.1936678523496174, "low": 1.1923321122737625, "close": 1.1933144069182116, "volume": 389, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T16:03:39.958Z", "timeframe": 300, "open": 1.1564770819085943, "high": 1.1569624697080039, "low": 1.1564672641188245, "close": 1.1569624697080039, "volume": 585, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T16:08:39.958Z", "timeframe": 300, "open": 1.1119520499620876, "high": 1.1124109433914326, "low": 1.1115096478763151, "close": 1.1115096478763151, "volume": 634, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T16:13:39.958Z", "timeframe": 300, "open": 1.193041961281283, "high": 1.193052404777033, "low": 1.1926680009202488, "close": 1.1926680009202488, "volume": 716, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T16:18:39.958Z", "timeframe": 300, "open": 1.1981443199629038, "high": 1.198286992771, "low": 1.1976642318903685, "close": 1.198247909589351, "volume": 529, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T16:23:39.958Z", "timeframe": 300, "open": 1.131382461229102, "high": 1.1323754408466793, "low": 1.1304638453788036, "close": 1.130987766312163, "volume": 418, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T16:28:39.958Z", "timeframe": 300, "open": 1.188063701008602, "high": 1.1885265725333114, "low": 1.1874683053171924, "close": 1.1882929067756296, "volume": 713, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T16:33:39.958Z", "timeframe": 300, "open": 1.1119901445330402, "high": 1.1126752308042915, "low": 1.1113571844426957, "close": 1.1122776812458484, "volume": 428, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T16:38:39.958Z", "timeframe": 300, "open": 1.1785603801055158, "high": 1.1788765177536469, "low": 1.177587107617596, "close": 1.1786981912468295, "volume": 392, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T16:43:39.958Z", "timeframe": 300, "open": 1.1626952444090652, "high": 1.163504140729745, "low": 1.1622129760386883, "close": 1.1622129760386883, "volume": 549, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T16:48:39.958Z", "timeframe": 300, "open": 1.1344616673071883, "high": 1.1350248772855365, "low": 1.1336281468810214, "close": 1.1341266097428586, "volume": 824, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T16:53:39.958Z", "timeframe": 300, "open": 1.102795250176734, "high": 1.1032446008677967, "low": 1.10216709770715, "close": 1.1030276728155464, "volume": 780, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T16:58:39.958Z", "timeframe": 300, "open": 1.1390165026397452, "high": 1.1393033343189616, "low": 1.138517483334895, "close": 1.1390014722905852, "volume": 934, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T17:03:39.958Z", "timeframe": 300, "open": 1.1395599818306212, "high": 1.1400309127386692, "low": 1.139489892723137, "close": 1.1398138062627783, "volume": 660, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T17:08:39.958Z", "timeframe": 300, "open": 1.1623492457008224, "high": 1.1627815129370134, "low": 1.1618328835010345, "close": 1.1627815129370134, "volume": 400, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T17:13:39.958Z", "timeframe": 300, "open": 1.1354090925727962, "high": 1.1357231887353727, "low": 1.1349158241873734, "close": 1.1357231887353727, "volume": 646, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T17:18:39.958Z", "timeframe": 300, "open": 1.1220741794948028, "high": 1.1221350976754905, "low": 1.121755313074855, "close": 1.1220664288794369, "volume": 506, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T17:23:39.958Z", "timeframe": 300, "open": 1.119637955927905, "high": 1.1205647337336289, "low": 1.1192851747315087, "close": 1.1192851747315087, "volume": 392, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T17:28:39.958Z", "timeframe": 300, "open": 1.1260035774630814, "high": 1.1263121991412843, "low": 1.1252086693237224, "close": 1.1260147772981353, "volume": 570, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T17:33:39.958Z", "timeframe": 300, "open": 1.1149127347640109, "high": 1.1153475048941384, "low": 1.1144998432415767, "close": 1.1149681856247935, "volume": 198, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T17:38:39.958Z", "timeframe": 300, "open": 1.1091386258784186, "high": 1.1095717793143367, "low": 1.108987109754133, "close": 1.1090616731995215, "volume": 994, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T17:43:39.958Z", "timeframe": 300, "open": 1.1122018050191134, "high": 1.112828585571764, "low": 1.1115207273056529, "close": 1.1123277826630111, "volume": 1082, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T17:48:39.958Z", "timeframe": 300, "open": 1.1166269593526232, "high": 1.1174887814872672, "low": 1.1161431727035929, "close": 1.1162115490364795, "volume": 542, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T17:53:39.958Z", "timeframe": 300, "open": 1.1910272306562861, "high": 1.1917662350471143, "low": 1.1907176134817665, "close": 1.1908506121550426, "volume": 845, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T17:58:39.958Z", "timeframe": 300, "open": 1.172832349983428, "high": 1.173739359099165, "low": 1.172664873750047, "close": 1.173073168900845, "volume": 284, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T18:03:39.958Z", "timeframe": 300, "open": 1.1564754010383715, "high": 1.1567319052209724, "low": 1.155701048088796, "close": 1.1567319052209724, "volume": 520, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T18:08:39.958Z", "timeframe": 300, "open": 1.1476420024362246, "high": 1.1482207810041323, "low": 1.1475542121760158, "close": 1.1475542121760158, "volume": 512, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T18:13:39.958Z", "timeframe": 300, "open": 1.1246196657410352, "high": 1.125099967857186, "low": 1.1241619164109438, "close": 1.1241619164109438, "volume": 707, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T18:18:39.958Z", "timeframe": 300, "open": 1.111696953174499, "high": 1.112009688539407, "low": 1.1110339117394548, "close": 1.112009688539407, "volume": 344, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T18:23:39.958Z", "timeframe": 300, "open": 1.179458715300461, "high": 1.179957781717656, "low": 1.1789404092611742, "close": 1.1794688961882094, "volume": 283, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T18:28:39.958Z", "timeframe": 300, "open": 1.1597278977483556, "high": 1.1604189208629831, "low": 1.159005948699637, "close": 1.1601849224403473, "volume": 785, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T18:33:39.958Z", "timeframe": 300, "open": 1.191302665426196, "high": 1.1916934445546667, "low": 1.1910741326490708, "close": 1.1916934445546667, "volume": 707, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T18:38:39.958Z", "timeframe": 300, "open": 1.1875129297164233, "high": 1.187772755449452, "low": 1.1870409365260135, "close": 1.1873512429890187, "volume": 153, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T18:43:39.958Z", "timeframe": 300, "open": 1.122937612306478, "high": 1.123672440247303, "low": 1.1221110171988058, "close": 1.123414059243546, "volume": 277, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T18:48:39.958Z", "timeframe": 300, "open": 1.1165691851995394, "high": 1.116743208378923, "low": 1.1157122737018588, "close": 1.116734204993871, "volume": 529, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T18:53:39.958Z", "timeframe": 300, "open": 1.1143517361654252, "high": 1.1146569392489867, "low": 1.113593895095887, "close": 1.1142382814579144, "volume": 781, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T18:58:39.958Z", "timeframe": 300, "open": 1.123406135637862, "high": 1.1240256632213936, "low": 1.1227186797403816, "close": 1.1237128415894946, "volume": 217, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T19:03:39.958Z", "timeframe": 300, "open": 1.116417228925065, "high": 1.1171637955238416, "low": 1.1162262193262815, "close": 1.1163821189250018, "volume": 673, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T19:08:39.958Z", "timeframe": 300, "open": 1.1538196017975966, "high": 1.153979428338881, "low": 1.152934010616785, "close": 1.153665343614798, "volume": 714, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T19:13:39.958Z", "timeframe": 300, "open": 1.14831072978809, "high": 1.148361959494099, "low": 1.1481431090481102, "close": 1.1481431090481102, "volume": 345, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T19:18:39.958Z", "timeframe": 300, "open": 1.146377445634222, "high": 1.1472649672194253, "low": 1.145553054355539, "close": 1.1463681573593651, "volume": 962, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.962Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T19:23:39.958Z", "timeframe": 300, "open": 1.1523793596416116, "high": 1.152867292222275, "low": 1.1514110081521622, "close": 1.1524116802617739, "volume": 634, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T19:28:39.958Z", "timeframe": 300, "open": 1.1960452909300063, "high": 1.1969075462209129, "low": 1.1952163852295627, "close": 1.1960492614978633, "volume": 374, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T19:33:39.958Z", "timeframe": 300, "open": 1.1387915449263388, "high": 1.1395937756310368, "low": 1.1386466086576366, "close": 1.139149029410314, "volume": 362, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T19:38:39.958Z", "timeframe": 300, "open": 1.15899266078259, "high": 1.1597478925304618, "low": 1.1582688004074189, "close": 1.1585944703298878, "volume": 582, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T19:43:39.958Z", "timeframe": 300, "open": 1.1501294121980012, "high": 1.1506888247318545, "low": 1.1497426296205557, "close": 1.1502825116506132, "volume": 399, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T19:48:39.958Z", "timeframe": 300, "open": 1.1967829359862239, "high": 1.1976860639379334, "low": 1.1965588814800754, "close": 1.1965588814800754, "volume": 210, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T19:53:39.958Z", "timeframe": 300, "open": 1.184787749788721, "high": 1.1855556394731206, "low": 1.1844139352942984, "close": 1.1847018755819312, "volume": 1063, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T19:58:39.958Z", "timeframe": 300, "open": 1.1918520148719474, "high": 1.1919731156565758, "low": 1.1911945548546223, "close": 1.1913623141624443, "volume": 889, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T20:03:39.958Z", "timeframe": 300, "open": 1.1614931385023892, "high": 1.161927624902591, "low": 1.1608595395312657, "close": 1.161927624902591, "volume": 686, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T20:08:39.958Z", "timeframe": 300, "open": 1.1819965083546937, "high": 1.1829294238826202, "low": 1.181385845699053, "close": 1.1822442811442138, "volume": 1054, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T20:13:39.958Z", "timeframe": 300, "open": 1.1844051429628228, "high": 1.184729207753402, "low": 1.1837594989911107, "close": 1.1842950048540737, "volume": 1050, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T20:18:39.958Z", "timeframe": 300, "open": 1.1700151102494, "high": 1.1704841891740891, "low": 1.1691897516088188, "close": 1.1700553931102942, "volume": 875, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T20:23:39.958Z", "timeframe": 300, "open": 1.1202964722747055, "high": 1.1207454477816572, "low": 1.1200554576168316, "close": 1.120462338756408, "volume": 173, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T20:28:39.958Z", "timeframe": 300, "open": 1.1835019639377296, "high": 1.1838362206550637, "low": 1.1833981802335205, "close": 1.1833981802335205, "volume": 957, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T20:33:39.958Z", "timeframe": 300, "open": 1.1424052813280399, "high": 1.1425857703264317, "low": 1.141566872296279, "close": 1.1425857703264317, "volume": 449, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T20:38:39.958Z", "timeframe": 300, "open": 1.1064894307308102, "high": 1.1069834921890453, "low": 1.1063549944772126, "close": 1.1069834921890453, "volume": 275, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T20:43:39.958Z", "timeframe": 300, "open": 1.171612112494182, "high": 1.1724687517904544, "low": 1.1709618686407717, "close": 1.1714249138762958, "volume": 619, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T20:48:39.958Z", "timeframe": 300, "open": 1.1150568359746396, "high": 1.1152596823310896, "low": 1.1141019716062877, "close": 1.1150772460665572, "volume": 405, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T20:53:39.958Z", "timeframe": 300, "open": 1.1136576284640105, "high": 1.113877564397394, "low": 1.113398678069924, "close": 1.1134813458387585, "volume": 822, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T20:58:39.958Z", "timeframe": 300, "open": 1.161849188290646, "high": 1.1622310441693435, "low": 1.1616703883250787, "close": 1.1622055815791035, "volume": 1077, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T21:03:39.958Z", "timeframe": 300, "open": 1.1507822231863507, "high": 1.1512646238031945, "low": 1.150521488171411, "close": 1.1512646238031945, "volume": 725, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T21:08:39.958Z", "timeframe": 300, "open": 1.1533964851814498, "high": 1.1542951383080342, "low": 1.1525272960754112, "close": 1.1529390433981288, "volume": 121, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T21:13:39.958Z", "timeframe": 300, "open": 1.174945303064727, "high": 1.175149578143297, "low": 1.1747258603750623, "close": 1.175137587975375, "volume": 133, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T21:18:39.958Z", "timeframe": 300, "open": 1.1485335442000415, "high": 1.1494173702872819, "low": 1.147559355824244, "close": 1.1481894710519698, "volume": 658, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T21:23:39.958Z", "timeframe": 300, "open": 1.1302306639234998, "high": 1.1305728490128717, "low": 1.1293938975851514, "close": 1.1297403810917057, "volume": 480, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T21:28:39.958Z", "timeframe": 300, "open": 1.1911470716199253, "high": 1.1916556577932877, "low": 1.190671275626967, "close": 1.190875545373365, "volume": 108, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T21:33:39.958Z", "timeframe": 300, "open": 1.1553284610532377, "high": 1.1557612770676282, "low": 1.1552395668100444, "close": 1.1557081990978035, "volume": 1029, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T21:38:39.958Z", "timeframe": 300, "open": 1.1442349730935317, "high": 1.1449911457939734, "low": 1.1437640872869945, "close": 1.1437640872869945, "volume": 1046, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T21:43:39.958Z", "timeframe": 300, "open": 1.1990318063919325, "high": 1.199526316499362, "low": 1.1986591313453998, "close": 1.1986591313453998, "volume": 180, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T21:48:39.958Z", "timeframe": 300, "open": 1.1277432852978309, "high": 1.1279921998283624, "low": 1.1267640406474548, "close": 1.1273616901460277, "volume": 602, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T21:53:39.958Z", "timeframe": 300, "open": 1.1336827133305531, "high": 1.133927086270197, "low": 1.133349577351617, "close": 1.133927086270197, "volume": 126, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T21:58:39.958Z", "timeframe": 300, "open": 1.1162655540517006, "high": 1.1170081575404625, "low": 1.1156984550734528, "close": 1.1164693209602228, "volume": 406, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T22:03:39.958Z", "timeframe": 300, "open": 1.1571003366440804, "high": 1.1572443537409625, "low": 1.1563913750363997, "close": 1.1570479415253139, "volume": 182, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T22:08:39.958Z", "timeframe": 300, "open": 1.1981626226015882, "high": 1.1983509394080165, "low": 1.1974487594086445, "close": 1.1980081323537413, "volume": 247, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T22:13:39.958Z", "timeframe": 300, "open": 1.1891126704190569, "high": 1.1899845652698238, "low": 1.1883546358353612, "close": 1.1893339139968795, "volume": 735, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T22:18:39.958Z", "timeframe": 300, "open": 1.1165431283615006, "high": 1.1170152769939903, "low": 1.1161734138512416, "close": 1.1170152769939903, "volume": 469, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T22:23:39.958Z", "timeframe": 300, "open": 1.1804168035990896, "high": 1.1806366138069646, "low": 1.1796950657014347, "close": 1.180400475142902, "volume": 467, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T22:28:39.958Z", "timeframe": 300, "open": 1.1804200042112882, "high": 1.1809167292847516, "low": 1.1802623334029942, "close": 1.1808061267610204, "volume": 317, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T22:33:39.958Z", "timeframe": 300, "open": 1.1800349605063063, "high": 1.1809820677064151, "low": 1.179525519757826, "close": 1.1796849096203703, "volume": 775, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T22:38:39.958Z", "timeframe": 300, "open": 1.1011730019194674, "high": 1.1015768983450553, "low": 1.1003381611813159, "close": 1.1015757952476304, "volume": 119, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T22:43:39.958Z", "timeframe": 300, "open": 1.150779871739458, "high": 1.1511523274319049, "low": 1.150764904944462, "close": 1.1511523274319049, "volume": 134, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T22:48:39.958Z", "timeframe": 300, "open": 1.1361736004771736, "high": 1.1371473125253173, "low": 1.135343847979003, "close": 1.1362355624423202, "volume": 585, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T22:53:39.958Z", "timeframe": 300, "open": 1.1804558186967773, "high": 1.180620176431475, "low": 1.1795760246919993, "close": 1.1800167745512373, "volume": 489, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T22:58:39.958Z", "timeframe": 300, "open": 1.1274293799991035, "high": 1.1279024439880545, "low": 1.1267304068734174, "close": 1.1276743458454104, "volume": 604, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T23:03:39.958Z", "timeframe": 300, "open": 1.1871267185076122, "high": 1.1879482890884834, "low": 1.186777999355841, "close": 1.186777999355841, "volume": 655, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T23:08:39.958Z", "timeframe": 300, "open": 1.1868314559966495, "high": 1.1875519214071857, "low": 1.186384972848968, "close": 1.186384972848968, "volume": 986, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T23:13:39.958Z", "timeframe": 300, "open": 1.1513923695692767, "high": 1.15224736776832, "low": 1.1507681171573934, "close": 1.1512664646640938, "volume": 136, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T23:18:39.958Z", "timeframe": 300, "open": 1.192369827386659, "high": 1.1927911515015062, "low": 1.192133989472361, "close": 1.1927911515015062, "volume": 998, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T23:23:39.958Z", "timeframe": 300, "open": 1.182538257472611, "high": 1.183201600140636, "low": 1.1816979462221908, "close": 1.1827252608248562, "volume": 396, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T23:28:39.958Z", "timeframe": 300, "open": 1.1482297494667386, "high": 1.1485587951669505, "low": 1.1478504687001054, "close": 1.1478504687001054, "volume": 749, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T23:33:39.958Z", "timeframe": 300, "open": 1.1400877579163138, "high": 1.1402903585086548, "low": 1.1397119147241157, "close": 1.140135204867496, "volume": 985, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T23:38:39.958Z", "timeframe": 300, "open": 1.1671788665196043, "high": 1.1678139853523466, "low": 1.1666159173867605, "close": 1.1667496691042045, "volume": 585, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T23:43:39.958Z", "timeframe": 300, "open": 1.1553248034646895, "high": 1.1557513570254085, "low": 1.1545814839091082, "close": 1.1556367542808308, "volume": 1048, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T23:48:39.958Z", "timeframe": 300, "open": 1.1950886345481098, "high": 1.1955014273986104, "low": 1.1943622315337459, "close": 1.1952383173633123, "volume": 357, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T23:53:39.958Z", "timeframe": 300, "open": 1.1627970967208647, "high": 1.163041542387415, "low": 1.1622510286384866, "close": 1.1623027494708535, "volume": 104, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-06T23:58:39.958Z", "timeframe": 300, "open": 1.1602554273816121, "high": 1.1610188029433326, "low": 1.1593488349300045, "close": 1.1598466792597504, "volume": 392, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T00:03:39.958Z", "timeframe": 300, "open": 1.1977741915721727, "high": 1.1984207625029604, "low": 1.1971307547847836, "close": 1.197379349794936, "volume": 531, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T00:08:39.958Z", "timeframe": 300, "open": 1.1384662400377366, "high": 1.1393655684683175, "low": 1.137504954851239, "close": 1.138346988243242, "volume": 1079, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T00:13:39.958Z", "timeframe": 300, "open": 1.1034325429894005, "high": 1.1039800876467607, "low": 1.1034118928998358, "close": 1.1036820811979973, "volume": 210, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T00:18:39.958Z", "timeframe": 300, "open": 1.1438261488220616, "high": 1.1445170183754207, "low": 1.1434889205728964, "close": 1.1441270770773748, "volume": 356, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T00:23:39.958Z", "timeframe": 300, "open": 1.152462375673481, "high": 1.1532523491772793, "low": 1.1523448920192374, "close": 1.1529346770582645, "volume": 238, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T00:28:39.958Z", "timeframe": 300, "open": 1.1647837340069436, "high": 1.1656277882731274, "low": 1.1644339015744718, "close": 1.1651750230500504, "volume": 923, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T00:33:39.958Z", "timeframe": 300, "open": 1.1836181983604352, "high": 1.1842779841881859, "low": 1.1831321780262367, "close": 1.1831321780262367, "volume": 318, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T00:38:39.958Z", "timeframe": 300, "open": 1.1027660751424715, "high": 1.1031556118004473, "low": 1.1021401608348844, "close": 1.1027185499379104, "volume": 555, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T00:43:39.958Z", "timeframe": 300, "open": 1.1203352117626721, "high": 1.12113451822061, "low": 1.1198227345456038, "close": 1.1206553474298258, "volume": 392, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T00:48:39.958Z", "timeframe": 300, "open": 1.1066124862058855, "high": 1.1075194273450941, "low": 1.1061257900728887, "close": 1.1061257900728887, "volume": 337, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T00:53:39.958Z", "timeframe": 300, "open": 1.1624646380684405, "high": 1.1628544697212297, "low": 1.1623535634308426, "close": 1.1628544697212297, "volume": 1005, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T00:58:39.958Z", "timeframe": 300, "open": 1.1132737296410338, "high": 1.1142341712544122, "low": 1.1122929952707532, "close": 1.1133487676708376, "volume": 810, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T01:03:39.958Z", "timeframe": 300, "open": 1.139668753899141, "high": 1.1404402044285133, "low": 1.138820505718284, "close": 1.139221348449644, "volume": 983, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T01:08:39.958Z", "timeframe": 300, "open": 1.197294880006126, "high": 1.1979460151814973, "low": 1.1963498060819466, "close": 1.197392058323558, "volume": 953, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T01:13:39.958Z", "timeframe": 300, "open": 1.1321525169417221, "high": 1.132292030062838, "low": 1.1318668403339778, "close": 1.132292030062838, "volume": 271, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T01:18:39.958Z", "timeframe": 300, "open": 1.1119429181791158, "high": 1.1121847690361042, "low": 1.111911628462178, "close": 1.112111318063931, "volume": 782, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T01:23:39.958Z", "timeframe": 300, "open": 1.1002292754728984, "high": 1.101026719504682, "low": 1.0996009804776607, "close": 1.0999056929735436, "volume": 403, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T01:28:39.958Z", "timeframe": 300, "open": 1.132345857570514, "high": 1.1327826160584895, "low": 1.132281839796886, "close": 1.1327826160584895, "volume": 193, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T01:33:39.958Z", "timeframe": 300, "open": 1.1587216071058382, "high": 1.1592284887007243, "low": 1.1584909738262459, "close": 1.1587685963338599, "volume": 643, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T01:38:39.958Z", "timeframe": 300, "open": 1.1451842600767685, "high": 1.1458158694389264, "low": 1.1448466228290364, "close": 1.1451926934736794, "volume": 404, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T01:43:39.958Z", "timeframe": 300, "open": 1.1678092405990634, "high": 1.168125703437434, "low": 1.1673910711492348, "close": 1.1680681299129818, "volume": 1050, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T01:48:39.958Z", "timeframe": 300, "open": 1.171150893300021, "high": 1.1715654236147688, "low": 1.1704937147729297, "close": 1.1715654236147688, "volume": 335, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T01:53:39.958Z", "timeframe": 300, "open": 1.1314833703845157, "high": 1.1324592986412994, "low": 1.1307446169370607, "close": 1.1319079283872056, "volume": 1063, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T01:58:39.958Z", "timeframe": 300, "open": 1.113811769767329, "high": 1.1142341517317507, "low": 1.1132323140511766, "close": 1.1136988333429483, "volume": 666, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T02:03:39.958Z", "timeframe": 300, "open": 1.105705225350084, "high": 1.105873968568083, "low": 1.1051094030469066, "close": 1.1052678799215745, "volume": 671, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T02:08:39.958Z", "timeframe": 300, "open": 1.1300894952935678, "high": 1.1306496664583086, "low": 1.1292655286635045, "close": 1.1302274739660154, "volume": 512, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T02:13:39.958Z", "timeframe": 300, "open": 1.1828446164226751, "high": 1.1830431947279418, "low": 1.1820639001072422, "close": 1.1828885300842766, "volume": 886, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T02:18:39.958Z", "timeframe": 300, "open": 1.1473431429865948, "high": 1.1477383928103386, "low": 1.1468604572532182, "close": 1.1477304066411476, "volume": 237, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T02:23:39.958Z", "timeframe": 300, "open": 1.1452119553254592, "high": 1.1456372398584977, "low": 1.1452046057009264, "close": 1.1453799284593085, "volume": 867, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T02:28:39.958Z", "timeframe": 300, "open": 1.1220376532517606, "high": 1.122099188842665, "low": 1.1211748337089797, "close": 1.1220084247636124, "volume": 247, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T02:33:39.958Z", "timeframe": 300, "open": 1.157632706250592, "high": 1.1583591631385475, "low": 1.1566870966994935, "close": 1.1576992370570163, "volume": 894, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:38:39.963Z"}}, {"assetSymbol": "EURCAD", "assetName": "EUR/CAD", "timestamp": "2025-07-07T02:37:26.626Z", "timeframe": 300, "open": 1.1439214396660413, "high": 1.144453970123938, "low": 1.1436788820480972, "close": 1.1439895327457175, "volume": 276, "isComplete": true, "metadata": {"source": "quotex_api", "fetched": "2025-07-07T02:42:26.857Z"}}], "metadata": {"source": "historical_data_manager", "version": "1.0"}}