/**
 * @license
 * Copyright 2022 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../index';
import { ALL_ENVS, describeWithFlags } from '../jasmine_util';
import { expectArraysClose, expectArraysEqual } from '../test_util';
describeWithFlags('raggedTensorToTensor ', ALL_ENVS, () => {
    it('RaggedTensorToTensor', async () => {
        const shape = [4, 4];
        const values = [.1, .2, .3, .4, .5, .6, .7, .8, .9];
        const defaultValue = 1.5;
        const rowPartitionTensors = [
            tf.scalar(4, 'int32'), tf.tensor1d([0, 0, 0, 2, 2, 2, 2, 3, 3], 'int32')
        ];
        const rowPartitionTypes = ['FIRST_DIM_SIZE', 'VALUE_ROWIDS'];
        const result = tf.raggedTensorToTensor(shape, values, defaultValue, rowPartitionTensors, rowPartitionTypes);
        expectArraysEqual(result.shape, shape);
        expectArraysClose(await result.data(), [
            .1, .2, .3, 1.5, 1.5, 1.5, 1.5, 1.5, .4, .5, .6, .7, .8, .9, 1.5, 1.5
        ]);
    });
    it('RaggedTensorToTensorRowSplits', async () => {
        const shape = [4, 4];
        const values = [.1, .2, .3, .4, .5, .6, .7, .8, .9];
        const defaultValue = 1.5;
        const rowPartitionTensors = [tf.tensor1d([0, 3, 3, 7, 9], 'int32')];
        const rowPartitionTypes = ['ROW_SPLITS'];
        const result = tf.raggedTensorToTensor(shape, values, defaultValue, rowPartitionTensors, rowPartitionTypes);
        expectArraysEqual(result.shape, shape);
        expectArraysClose(await result.data(), [
            .1, .2, .3, 1.5, 1.5, 1.5, 1.5, 1.5, .4, .5, .6, .7, .8, .9, 1.5, 1.5
        ]);
    });
    it('RaggedTensorToTensor3DParams', async () => {
        const shape = [5, 2, 3];
        const values = [.1, .2, .3, .4, .5, .6, .7, .8, .9];
        const defaultValue = 1.5;
        const rowPartitionTensors = [
            tf.scalar(5, 'int32'), tf.tensor1d([0, 1, 1, 3, 3, 4], 'int32'),
            tf.tensor1d([1, 1, 2, 3, 3, 4, 4, 4, 5], 'int32')
        ];
        const rowPartitionTypes = ['FIRST_DIM_SIZE', 'VALUE_ROWIDS', 'VALUE_ROWIDS'];
        const result = tf.raggedTensorToTensor(shape, values, defaultValue, rowPartitionTensors, rowPartitionTypes);
        expectArraysEqual(result.shape, shape);
        expectArraysClose(await result.data(), [
            1.5, 1.5, 1.5, 1.5, 1.5, 1.5, .1, .2, 1.5, .3, 1.5, 1.5, 1.5, 1.5, 1.5,
            1.5, 1.5, 1.5, .4, .5, 1.5, .6, .7, .8, .9, 1.5, 1.5, 1.5, 1.5, 1.5
        ]);
    });
    it('RaggedTensorToTensor3DParamsRowSplits', async () => {
        const shape = [5, 2, 3];
        const values = [.1, .2, .3, .4, .5, .6, .7, .8, .9];
        const defaultValue = 1.5;
        const rowPartitionTensors = [
            tf.tensor1d([0, 1, 3, 3, 5, 6], 'int32'),
            tf.tensor1d([0, 0, 2, 3, 5, 8, 9], 'int32')
        ];
        const rowPartitionTypes = ['ROW_SPLITS', 'ROW_SPLITS'];
        const result = tf.raggedTensorToTensor(shape, values, defaultValue, rowPartitionTensors, rowPartitionTypes);
        expectArraysEqual(result.shape, shape);
        expectArraysClose(await result.data(), [
            1.5, 1.5, 1.5, 1.5, 1.5, 1.5, .1, .2, 1.5, .3, 1.5, 1.5, 1.5, 1.5, 1.5,
            1.5, 1.5, 1.5, .4, .5, 1.5, .6, .7, .8, .9, 1.5, 1.5, 1.5, 1.5, 1.5
        ]);
    });
    it('RaggedTensorToTensor3DParamsRowSplits2', async () => {
        const shape = [3, 2, 3];
        const values = [0, 1, 2, 3];
        const defaultValue = 5;
        const rowPartitionTensors = [
            tf.tensor1d([0, 2, 2, 3], 'int32'), tf.tensor1d([0, 3, 3, 4], 'int32')
        ];
        const rowPartitionTypes = ['ROW_SPLITS', 'ROW_SPLITS'];
        const result = tf.raggedTensorToTensor(shape, values, defaultValue, rowPartitionTensors, rowPartitionTypes);
        expectArraysEqual(result.shape, shape);
        expectArraysClose(await result.data(), [0, 1, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 3, 5, 5, 5, 5, 5]);
    });
    it('RaggedTensorToTensor4DParams', async () => {
        const shape = [4, 2, 3, 2];
        const values = [1, 2, 3, 4, 5, 6, 7, 8];
        const defaultValue = 15;
        const rowPartitionTensors = [
            tf.scalar(5, 'int32'), tf.tensor1d([0, 1, 1], 'int32'),
            tf.tensor1d([1, 1, 1, 2], 'int32'),
            tf.tensor1d([0, 0, 1, 1, 2, 2, 3, 3], 'int32')
        ];
        const rowPartitionTypes = ['FIRST_DIM_SIZE', 'VALUE_ROWIDS', 'VALUE_ROWIDS', 'VALUE_ROWIDS'];
        const result = tf.raggedTensorToTensor(shape, values, defaultValue, rowPartitionTensors, rowPartitionTypes);
        expectArraysEqual(result.shape, shape);
        expectArraysClose(await result.data(), [
            15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 1, 2, 3, 4,
            5, 6, 7, 8, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15,
            15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15
        ]);
    });
    it('RaggedTensorToTensor4DParamsRowSplit', async () => {
        const shape = [4, 2, 3, 2];
        const values = [1, 2, 3, 4, 5, 6, 7, 8];
        const defaultValue = 15;
        const rowPartitionTensors = [
            tf.tensor1d([0, 1, 3], 'int32'), tf.tensor1d([0, 0, 3, 4], 'int32'),
            tf.tensor1d([0, 2, 4, 6, 8], 'int32')
        ];
        const rowPartitionTypes = ['ROW_SPLITS', 'ROW_SPLITS', 'ROW_SPLITS'];
        const result = tf.raggedTensorToTensor(shape, values, defaultValue, rowPartitionTensors, rowPartitionTypes);
        expectArraysEqual(result.shape, shape);
        expectArraysClose(await result.data(), [
            15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 1, 2, 3, 4,
            5, 6, 7, 8, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15,
            15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15
        ]);
    });
    it('RaggedTensorToTensorContractExpanded', async () => {
        const shape = [3, 5];
        const values = [.1, .2, .3, .4, .5, .6, .7, .8, .9];
        const defaultValue = 1.5;
        const rowPartitionTensors = [
            tf.scalar(4, 'int32'),
            tf.tensor1d([0, 0, 0, 2, 2, 2, 2, 3, 3], 'int32'),
        ];
        const rowPartitionTypes = ['FIRST_DIM_SIZE', 'VALUE_ROWIDS'];
        const result = tf.raggedTensorToTensor(shape, values, defaultValue, rowPartitionTensors, rowPartitionTypes);
        expectArraysEqual(result.shape, shape);
        expectArraysClose(await result.data(), [
            .1, .2, .3, 1.5, 1.5,
            1.5, 1.5, 1.5, 1.5, 1.5,
            .4, .5, .6, .7, 1.5
        ]);
    });
    it('RaggedTensorToTensorContractExpandedDense', async () => {
        const shape = [3, 5, 2];
        const values = tf.tensor2d([
            .1, 1.1, .2, 1.2, .3, 1.3, .4, 1.4, .5, 1.5, .6, 1.6, .7, 1.7, .8,
            1.8, .9, 1.9
        ], [9, 2]);
        const defaultValue = 1.5;
        const rowPartitionTensors = [
            tf.scalar(4, 'int32'),
            tf.tensor1d([0, 0, 0, 2, 2, 2, 2, 3, 3], 'int32'),
        ];
        const rowPartitionTypes = ['FIRST_DIM_SIZE', 'VALUE_ROWIDS'];
        const result = tf.raggedTensorToTensor(shape, values, defaultValue, rowPartitionTensors, rowPartitionTypes);
        expectArraysEqual(result.shape, shape);
        expectArraysClose(await result.data(), [
            .1, 1.1, .2, 1.2, .3, 1.3, 1.5, 1.5, 1.5, 1.5,
            1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5,
            .4, 1.4, .5, 1.5, .6, 1.6, .7, 1.7, 1.5, 1.5
        ]);
    });
    it('RaggedTensorToTensorConstrained', async () => {
        const shape = [3, 3];
        const values = [.1, .2, .3, .4, .5, .6, .7, .8, .9];
        const defaultValue = 1.5;
        const rowPartitionTensors = [
            tf.scalar(4, 'int32'),
            tf.tensor1d([0, 0, 0, 2, 2, 2, 2, 3, 3], 'int32'),
        ];
        const rowPartitionTypes = ['FIRST_DIM_SIZE', 'VALUE_ROWIDS'];
        const result = tf.raggedTensorToTensor(shape, values, defaultValue, rowPartitionTensors, rowPartitionTypes);
        expectArraysEqual(result.shape, shape);
        expectArraysClose(await result.data(), [
            .1, .2, .3,
            1.5, 1.5, 1.5,
            .4, .5, .6
        ]);
    });
    it('RaggedTensorToTensor3DParamsConstrained', async () => {
        const shape = [4, 1, 2];
        const values = [.1, .2, .3, .4, .5, .6, .7, .8, .9];
        const defaultValue = 1.5;
        const rowPartitionTensors = [
            tf.scalar(5, 'int32'),
            tf.tensor1d([0, 1, 1, 3, 3, 4], 'int32'),
            tf.tensor1d([1, 1, 2, 3, 3, 4, 4, 4, 5], 'int32'),
        ];
        const rowPartitionTypes = ['FIRST_DIM_SIZE', 'VALUE_ROWIDS', 'VALUE_ROWIDS'];
        const result = tf.raggedTensorToTensor(shape, values, defaultValue, rowPartitionTensors, rowPartitionTypes);
        expectArraysEqual(result.shape, shape);
        expectArraysClose(await result.data(), [1.5, 1.5, .1, .2, 1.5, 1.5, .4, .5]);
    });
    it('RaggedTensorToTensor4DParamsConstrained', async () => {
        const shape = [2, 2, 2, 2];
        const values = [1, 2, 3, 4, 5, 6, 7, 8];
        const defaultValue = 15;
        const rowPartitionTensors = [
            tf.scalar(5, 'int32'),
            tf.tensor1d([0, 1, 1], 'int32'),
            tf.tensor1d([1, 1, 1, 2], 'int32'),
            tf.tensor1d([0, 0, 1, 1, 2, 2, 3, 3], 'int32'),
        ];
        const rowPartitionTypes = ['FIRST_DIM_SIZE', 'VALUE_ROWIDS', 'VALUE_ROWIDS', 'VALUE_ROWIDS'];
        const result = tf.raggedTensorToTensor(shape, values, defaultValue, rowPartitionTensors, rowPartitionTypes);
        expectArraysEqual(result.shape, shape);
        expectArraysClose(await result.data(), [
            15, 15, 15, 15,
            15, 15, 15, 15,
            1, 2, 3, 4,
            7, 8, 15, 15, //
        ]);
    });
    it('shape wrong dimensions', async () => {
        const shape = [10, 7, 10, 20];
        const values = [1, 2, 3, 4];
        const defaultValue = 15;
        const rowPartitionTensors = [
            tf.scalar(5, 'int32'),
            tf.tensor1d([0, 1, 1], 'int32'),
            tf.tensor1d([1, 1, 1, 2], 'int32'),
        ];
        const rowPartitionTypes = ['FIRST_DIM_SIZE', 'VALUE_ROWIDS', 'VALUE_ROWIDS'];
        expect(() => tf.raggedTensorToTensor(shape, values, defaultValue, rowPartitionTensors, rowPartitionTypes))
            .toThrowError(/are incompatible/);
    });
    it('does not have memory leak.', async () => {
        const beforeDataIds = tf.engine().backend.numDataIds();
        const rowPartitionTensors = [
            tf.scalar(4, 'int32'), tf.tensor1d([0, 0, 0, 2, 2, 2, 2, 3, 3], 'int32')
        ];
        const result = tf.raggedTensorToTensor([4, 4], [.1, .2, .3, .4, .5, .6, .7, .8, .9], 1.5, rowPartitionTensors, ['FIRST_DIM_SIZE', 'VALUE_ROWIDS']);
        await result.data();
        const afterResDataIds = tf.engine().backend.numDataIds();
        expect(afterResDataIds).toEqual(beforeDataIds + 3);
        result.dispose();
        rowPartitionTensors.map(tensor => tensor.dispose());
        const afterDisposeDataIds = tf.engine().backend.numDataIds();
        expect(afterDisposeDataIds).toEqual(beforeDataIds);
    });
});
//# sourceMappingURL=data:application/json;base64,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