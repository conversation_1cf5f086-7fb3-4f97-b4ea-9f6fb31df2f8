/**
 * @license
 * Copyright 2019 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { ALL_ENVS, describeWithFlags } from '../jasmine_util';
import { isSliceContinous } from './slice_util';
describeWithFlags('isSliceContinous', ALL_ENVS, () => {
    it('[] => []', () => {
        const shape = [];
        const size = [];
        const begin = [];
        expect(isSliceContinous(shape, begin, size)).toBeTruthy();
    });
    it('[5] sliced to [3]', () => {
        const shape = [5];
        const size = [3];
        const begin = [1];
        expect(isSliceContinous(shape, begin, size)).toBeTruthy();
    });
    it('[5, 3] sliced to [2, 3] skipping a row', () => {
        const shape = [5, 3];
        const size = [2, 3];
        const begin = [1, 0];
        expect(isSliceContinous(shape, begin, size)).toBeTruthy();
    });
    it('[5, 3] sliced to [5, 2] skipping a column', () => {
        const shape = [5, 3];
        const size = [5, 2];
        const begin = [0, 1];
        expect(isSliceContinous(shape, begin, size)).toBeFalsy();
    });
    it('[5, 3] sliced to [1, 2] skipping a row and column', () => {
        const shape = [5, 3];
        const size = [1, 2];
        const begin = [2, 1];
        expect(isSliceContinous(shape, begin, size)).toBeTruthy();
    });
    it('[1, 5, 3] sliced to [1, 2, 3], skipping middle axis', () => {
        const shape = [1, 5, 3];
        const size = [1, 2, 3];
        const begin = [0, 2, 0];
        expect(isSliceContinous(shape, begin, size)).toBeTruthy();
    });
    it('[2, 5, 3] sliced to [2, 2, 3], skipping middle axis', () => {
        const shape = [2, 5, 3];
        const size = [2, 2, 3];
        const begin = [0, 2, 0];
        expect(isSliceContinous(shape, begin, size)).toBeFalsy();
    });
});
//# sourceMappingURL=data:application/json;base64,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