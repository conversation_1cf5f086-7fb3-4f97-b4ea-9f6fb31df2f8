/**
 * @license
 * Copyright 2022 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
export var RowPartitionType;
(function (RowPartitionType) {
    RowPartitionType[RowPartitionType["FIRST_DIM_SIZE"] = 0] = "FIRST_DIM_SIZE";
    RowPartitionType[RowPartitionType["VALUE_ROWIDS"] = 1] = "VALUE_ROWIDS";
    RowPartitionType[RowPartitionType["ROW_LENGTHS"] = 2] = "ROW_LENGTHS";
    RowPartitionType[RowPartitionType["ROW_SPLITS"] = 3] = "ROW_SPLITS";
    RowPartitionType[RowPartitionType["ROW_LIMITS"] = 4] = "ROW_LIMITS";
    RowPartitionType[RowPartitionType["ROW_STARTS"] = 5] = "ROW_STARTS";
})(RowPartitionType || (RowPartitionType = {}));
export function combineRaggedTensorToTensorShapes(raggedRank, shape, valueShape) {
    // Test for consistency of valueShape and shape specified.
    // If shape is unspecified and valueShape is specified, then copy
    // over the size from the valueShape dimension.
    let outputShape = new Array();
    if (valueShape == null && shape == null) {
        return outputShape;
    }
    if (shape == null) {
        // Here, value_shape must be of known size.
        while (outputShape.length < raggedRank + valueShape.length) {
            outputShape.push(-1);
        }
    }
    else {
        outputShape = shape.slice();
    }
    if (valueShape == null) {
        return outputShape;
    }
    // At this point, valueShape and output_shape have known ranks.
    if (raggedRank + valueShape.length !== outputShape.length) {
        throw new Error(`rt input.shape and shape=${shape} are incompatible: rt input.rank = ${raggedRank +
            valueShape.length}, but shape.rank = ${outputShape.length}`);
    }
    for (let i = 1; i < valueShape.length; ++i) {
        const valueDim = valueShape[i];
        const outputShapeDimIndex = outputShape[outputShape.length - valueShape.length + i];
        const outputShapeDim = outputShape[outputShapeDimIndex];
        if (valueDim >= 0) {
            if (outputShapeDim >= 0) {
                if (outputShapeDim !== valueDim) {
                    throw new Error(`rt input.shape and shape=${shape} are incompatible: rt input.shape[${i + raggedRank}] = ${valueDim} but shape[${i + raggedRank}] = ${outputShapeDim}`);
                }
            }
            else {
                outputShape[outputShapeDimIndex] = valueDim;
            }
        }
    }
    return outputShape;
}
export function getRowPartitionTypesHelper(rowPartitionTypeStrings) {
    const stringToType = {
        'FIRST_DIM_SIZE': RowPartitionType.FIRST_DIM_SIZE,
        'VALUE_ROWIDS': RowPartitionType.VALUE_ROWIDS,
        'ROW_LENGTHS': RowPartitionType.ROW_LENGTHS,
        'ROW_SPLITS': RowPartitionType.ROW_SPLITS,
        'ROW_LIMITS': RowPartitionType.ROW_LIMITS,
        'ROW_STARTS': RowPartitionType.ROW_STARTS
    };
    const result = [];
    for (const typeStr of rowPartitionTypeStrings) {
        if (typeStr in stringToType) {
            result.push(stringToType[typeStr]);
        }
        else {
            break;
        }
    }
    return result;
}
export function getRaggedRank(rowPartitionTypes) {
    if (rowPartitionTypes.length === 0) {
        return 0;
    }
    if (rowPartitionTypes[0] === RowPartitionType.FIRST_DIM_SIZE) {
        return rowPartitionTypes.length - 1;
    }
    return rowPartitionTypes.length;
}
export function validateDefaultValueShape(defaultValueShape, valueShape) {
    if (defaultValueShape == null || valueShape == null) {
        return;
    }
    const defaultNDims = defaultValueShape.length;
    const valuesNDims = valueShape.length;
    if (defaultNDims >= valuesNDims) {
        throw new Error(`defaultValue.shape=${defaultValueShape} and ragged tensor flatValues.shape=${valueShape}, are incompatible: defaultValue.rank = ${defaultNDims} must be less than ragged tensor input flatValues.rank = ${valuesNDims})`);
    }
    for (let i = 0; i < Math.min(defaultNDims, valuesNDims - 1); ++i) {
        const defaultDim = defaultValueShape[i];
        const valueDim = valueShape[i + 1];
        if (defaultDim >= 0 && valueDim >= 0 && defaultDim !== 1 &&
            defaultDim !== valueDim) {
            throw new Error(`defaultValue.shape=${defaultValueShape}, and ragged tensor input flatValues.shape=${valueShape} are incompatible: defaultValue.shape[${i - defaultValueShape.length}] = ${defaultDim} but ragged tensor input.flatValues.shape[${i - defaultValueShape.length}] = ${valueDim}`);
        }
    }
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicmFnZ2VkX3RvX2RlbnNlX3V0aWwuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi90ZmpzLWNvcmUvc3JjL29wcy9yYWdnZWRfdG9fZGVuc2VfdXRpbC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7Ozs7Ozs7Ozs7O0dBZUc7QUFFSCxNQUFNLENBQU4sSUFBWSxnQkFPWDtBQVBELFdBQVksZ0JBQWdCO0lBQzFCLDJFQUFjLENBQUE7SUFDZCx1RUFBWSxDQUFBO0lBQ1oscUVBQVcsQ0FBQTtJQUNYLG1FQUFVLENBQUE7SUFDVixtRUFBVSxDQUFBO0lBQ1YsbUVBQVUsQ0FBQTtBQUNaLENBQUMsRUFQVyxnQkFBZ0IsS0FBaEIsZ0JBQWdCLFFBTzNCO0FBRUQsTUFBTSxVQUFVLGlDQUFpQyxDQUM3QyxVQUFrQixFQUFFLEtBQWUsRUFBRSxVQUFvQjtJQUMzRCwwREFBMEQ7SUFDMUQsaUVBQWlFO0lBQ2pFLCtDQUErQztJQUUvQyxJQUFJLFdBQVcsR0FBYSxJQUFJLEtBQUssRUFBRSxDQUFDO0lBQ3hDLElBQUksVUFBVSxJQUFJLElBQUksSUFBSSxLQUFLLElBQUksSUFBSSxFQUFFO1FBQ3ZDLE9BQU8sV0FBVyxDQUFDO0tBQ3BCO0lBRUQsSUFBSSxLQUFLLElBQUksSUFBSSxFQUFFO1FBQ2pCLDJDQUEyQztRQUMzQyxPQUFPLFdBQVcsQ0FBQyxNQUFNLEdBQUcsVUFBVSxHQUFHLFVBQVUsQ0FBQyxNQUFNLEVBQUU7WUFDMUQsV0FBVyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1NBQ3RCO0tBQ0Y7U0FBTTtRQUNMLFdBQVcsR0FBRyxLQUFLLENBQUMsS0FBSyxFQUFFLENBQUM7S0FDN0I7SUFDRCxJQUFJLFVBQVUsSUFBSSxJQUFJLEVBQUU7UUFDdEIsT0FBTyxXQUFXLENBQUM7S0FDcEI7SUFDRCwrREFBK0Q7SUFDL0QsSUFBSSxVQUFVLEdBQUcsVUFBVSxDQUFDLE1BQU0sS0FBSyxXQUFXLENBQUMsTUFBTSxFQUFFO1FBQ3pELE1BQU0sSUFBSSxLQUFLLENBQ1gsNEJBQTRCLEtBQUssc0NBQzdCLFVBQVU7WUFDVixVQUFVLENBQUMsTUFBTSxzQkFBc0IsV0FBVyxDQUFDLE1BQU0sRUFBRSxDQUFDLENBQUM7S0FDdEU7SUFFRCxLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsVUFBVSxDQUFDLE1BQU0sRUFBRSxFQUFFLENBQUMsRUFBRTtRQUMxQyxNQUFNLFFBQVEsR0FBRyxVQUFVLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDL0IsTUFBTSxtQkFBbUIsR0FDckIsV0FBVyxDQUFDLFdBQVcsQ0FBQyxNQUFNLEdBQUcsVUFBVSxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUMsQ0FBQztRQUM1RCxNQUFNLGNBQWMsR0FBRyxXQUFXLENBQUMsbUJBQW1CLENBQUMsQ0FBQztRQUV4RCxJQUFJLFFBQVEsSUFBSSxDQUFDLEVBQUU7WUFDakIsSUFBSSxjQUFjLElBQUksQ0FBQyxFQUFFO2dCQUN2QixJQUFJLGNBQWMsS0FBSyxRQUFRLEVBQUU7b0JBQy9CLE1BQU0sSUFBSSxLQUFLLENBQUMsNEJBQ1osS0FBSyxxQ0FBcUMsQ0FBQyxHQUFHLFVBQVUsT0FDeEQsUUFBUSxjQUFjLENBQUMsR0FBRyxVQUFVLE9BQU8sY0FBYyxFQUFFLENBQUMsQ0FBQztpQkFDbEU7YUFDRjtpQkFBTTtnQkFDTCxXQUFXLENBQUMsbUJBQW1CLENBQUMsR0FBRyxRQUFRLENBQUM7YUFDN0M7U0FDRjtLQUNGO0lBQ0QsT0FBTyxXQUFXLENBQUM7QUFDckIsQ0FBQztBQUVELE1BQU0sVUFBVSwwQkFBMEIsQ0FBQyx1QkFBaUM7SUFDMUUsTUFBTSxZQUFZLEdBQUc7UUFDbkIsZ0JBQWdCLEVBQUUsZ0JBQWdCLENBQUMsY0FBYztRQUNqRCxjQUFjLEVBQUUsZ0JBQWdCLENBQUMsWUFBWTtRQUM3QyxhQUFhLEVBQUUsZ0JBQWdCLENBQUMsV0FBVztRQUMzQyxZQUFZLEVBQUUsZ0JBQWdCLENBQUMsVUFBVTtRQUN6QyxZQUFZLEVBQUUsZ0JBQWdCLENBQUMsVUFBVTtRQUN6QyxZQUFZLEVBQUUsZ0JBQWdCLENBQUMsVUFBVTtLQUMxQyxDQUFDO0lBRUYsTUFBTSxNQUFNLEdBQXVCLEVBQUUsQ0FBQztJQUN0QyxLQUFLLE1BQU0sT0FBTyxJQUFJLHVCQUF1QixFQUFFO1FBQzdDLElBQUksT0FBTyxJQUFJLFlBQVksRUFBRTtZQUMzQixNQUFNLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxPQUFvQyxDQUFDLENBQUMsQ0FBQztTQUNqRTthQUFNO1lBQ0wsTUFBTTtTQUNQO0tBQ0Y7SUFFRCxPQUFPLE1BQU0sQ0FBQztBQUNoQixDQUFDO0FBRUQsTUFBTSxVQUFVLGFBQWEsQ0FBQyxpQkFBcUM7SUFDakUsSUFBSSxpQkFBaUIsQ0FBQyxNQUFNLEtBQUssQ0FBQyxFQUFFO1FBQ2xDLE9BQU8sQ0FBQyxDQUFDO0tBQ1Y7SUFDRCxJQUFJLGlCQUFpQixDQUFDLENBQUMsQ0FBQyxLQUFLLGdCQUFnQixDQUFDLGNBQWMsRUFBRTtRQUM1RCxPQUFPLGlCQUFpQixDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUM7S0FDckM7SUFDRCxPQUFPLGlCQUFpQixDQUFDLE1BQU0sQ0FBQztBQUNsQyxDQUFDO0FBRUQsTUFBTSxVQUFVLHlCQUF5QixDQUNyQyxpQkFBMkIsRUFBRSxVQUFvQjtJQUNuRCxJQUFJLGlCQUFpQixJQUFJLElBQUksSUFBSSxVQUFVLElBQUksSUFBSSxFQUFFO1FBQ25ELE9BQU87S0FDUjtJQUVELE1BQU0sWUFBWSxHQUFHLGlCQUFpQixDQUFDLE1BQU0sQ0FBQztJQUM5QyxNQUFNLFdBQVcsR0FBRyxVQUFVLENBQUMsTUFBTSxDQUFDO0lBQ3RDLElBQUksWUFBWSxJQUFJLFdBQVcsRUFBRTtRQUMvQixNQUFNLElBQUksS0FBSyxDQUFDLHNCQUNaLGlCQUFpQix1Q0FDakIsVUFBVSwyQ0FDVixZQUFZLDREQUNaLFdBQVcsR0FBRyxDQUFDLENBQUM7S0FDckI7SUFDRCxLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxZQUFZLEVBQUUsV0FBVyxHQUFHLENBQUMsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxFQUFFO1FBQ2hFLE1BQU0sVUFBVSxHQUFHLGlCQUFpQixDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3hDLE1BQU0sUUFBUSxHQUFHLFVBQVUsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUM7UUFDbkMsSUFBSSxVQUFVLElBQUksQ0FBQyxJQUFJLFFBQVEsSUFBSSxDQUFDLElBQUksVUFBVSxLQUFLLENBQUM7WUFDcEQsVUFBVSxLQUFLLFFBQVEsRUFBRTtZQUMzQixNQUFNLElBQUksS0FBSyxDQUFDLHNCQUNaLGlCQUFpQiw4Q0FDakIsVUFBVSx5Q0FDVixDQUFDLEdBQUcsaUJBQWlCLENBQUMsTUFBTSxPQUM1QixVQUFVLDZDQUNWLENBQUMsR0FBRyxpQkFBaUIsQ0FBQyxNQUFNLE9BQU8sUUFBUSxFQUFFLENBQUMsQ0FBQztTQUNwRDtLQUNGO0FBQ0gsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCAyMDIyIEdvb2dsZSBMTEMuIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogaHR0cDovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gKi9cblxuZXhwb3J0IGVudW0gUm93UGFydGl0aW9uVHlwZSB7XG4gIEZJUlNUX0RJTV9TSVpFLFxuICBWQUxVRV9ST1dJRFMsXG4gIFJPV19MRU5HVEhTLFxuICBST1dfU1BMSVRTLFxuICBST1dfTElNSVRTLFxuICBST1dfU1RBUlRTXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBjb21iaW5lUmFnZ2VkVGVuc29yVG9UZW5zb3JTaGFwZXMoXG4gICAgcmFnZ2VkUmFuazogbnVtYmVyLCBzaGFwZTogbnVtYmVyW10sIHZhbHVlU2hhcGU6IG51bWJlcltdKSB7XG4gIC8vIFRlc3QgZm9yIGNvbnNpc3RlbmN5IG9mIHZhbHVlU2hhcGUgYW5kIHNoYXBlIHNwZWNpZmllZC5cbiAgLy8gSWYgc2hhcGUgaXMgdW5zcGVjaWZpZWQgYW5kIHZhbHVlU2hhcGUgaXMgc3BlY2lmaWVkLCB0aGVuIGNvcHlcbiAgLy8gb3ZlciB0aGUgc2l6ZSBmcm9tIHRoZSB2YWx1ZVNoYXBlIGRpbWVuc2lvbi5cblxuICBsZXQgb3V0cHV0U2hhcGU6IG51bWJlcltdID0gbmV3IEFycmF5KCk7XG4gIGlmICh2YWx1ZVNoYXBlID09IG51bGwgJiYgc2hhcGUgPT0gbnVsbCkge1xuICAgIHJldHVybiBvdXRwdXRTaGFwZTtcbiAgfVxuXG4gIGlmIChzaGFwZSA9PSBudWxsKSB7XG4gICAgLy8gSGVyZSwgdmFsdWVfc2hhcGUgbXVzdCBiZSBvZiBrbm93biBzaXplLlxuICAgIHdoaWxlIChvdXRwdXRTaGFwZS5sZW5ndGggPCByYWdnZWRSYW5rICsgdmFsdWVTaGFwZS5sZW5ndGgpIHtcbiAgICAgIG91dHB1dFNoYXBlLnB1c2goLTEpO1xuICAgIH1cbiAgfSBlbHNlIHtcbiAgICBvdXRwdXRTaGFwZSA9IHNoYXBlLnNsaWNlKCk7XG4gIH1cbiAgaWYgKHZhbHVlU2hhcGUgPT0gbnVsbCkge1xuICAgIHJldHVybiBvdXRwdXRTaGFwZTtcbiAgfVxuICAvLyBBdCB0aGlzIHBvaW50LCB2YWx1ZVNoYXBlIGFuZCBvdXRwdXRfc2hhcGUgaGF2ZSBrbm93biByYW5rcy5cbiAgaWYgKHJhZ2dlZFJhbmsgKyB2YWx1ZVNoYXBlLmxlbmd0aCAhPT0gb3V0cHV0U2hhcGUubGVuZ3RoKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgICBgcnQgaW5wdXQuc2hhcGUgYW5kIHNoYXBlPSR7c2hhcGV9IGFyZSBpbmNvbXBhdGlibGU6IHJ0IGlucHV0LnJhbmsgPSAke1xuICAgICAgICAgICAgcmFnZ2VkUmFuayArXG4gICAgICAgICAgICB2YWx1ZVNoYXBlLmxlbmd0aH0sIGJ1dCBzaGFwZS5yYW5rID0gJHtvdXRwdXRTaGFwZS5sZW5ndGh9YCk7XG4gIH1cblxuICBmb3IgKGxldCBpID0gMTsgaSA8IHZhbHVlU2hhcGUubGVuZ3RoOyArK2kpIHtcbiAgICBjb25zdCB2YWx1ZURpbSA9IHZhbHVlU2hhcGVbaV07XG4gICAgY29uc3Qgb3V0cHV0U2hhcGVEaW1JbmRleCA9XG4gICAgICAgIG91dHB1dFNoYXBlW291dHB1dFNoYXBlLmxlbmd0aCAtIHZhbHVlU2hhcGUubGVuZ3RoICsgaV07XG4gICAgY29uc3Qgb3V0cHV0U2hhcGVEaW0gPSBvdXRwdXRTaGFwZVtvdXRwdXRTaGFwZURpbUluZGV4XTtcblxuICAgIGlmICh2YWx1ZURpbSA+PSAwKSB7XG4gICAgICBpZiAob3V0cHV0U2hhcGVEaW0gPj0gMCkge1xuICAgICAgICBpZiAob3V0cHV0U2hhcGVEaW0gIT09IHZhbHVlRGltKSB7XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBydCBpbnB1dC5zaGFwZSBhbmQgc2hhcGU9JHtcbiAgICAgICAgICAgICAgc2hhcGV9IGFyZSBpbmNvbXBhdGlibGU6IHJ0IGlucHV0LnNoYXBlWyR7aSArIHJhZ2dlZFJhbmt9XSA9ICR7XG4gICAgICAgICAgICAgIHZhbHVlRGltfSBidXQgc2hhcGVbJHtpICsgcmFnZ2VkUmFua31dID0gJHtvdXRwdXRTaGFwZURpbX1gKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgb3V0cHV0U2hhcGVbb3V0cHV0U2hhcGVEaW1JbmRleF0gPSB2YWx1ZURpbTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgcmV0dXJuIG91dHB1dFNoYXBlO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gZ2V0Um93UGFydGl0aW9uVHlwZXNIZWxwZXIocm93UGFydGl0aW9uVHlwZVN0cmluZ3M6IHN0cmluZ1tdKSB7XG4gIGNvbnN0IHN0cmluZ1RvVHlwZSA9IHtcbiAgICAnRklSU1RfRElNX1NJWkUnOiBSb3dQYXJ0aXRpb25UeXBlLkZJUlNUX0RJTV9TSVpFLFxuICAgICdWQUxVRV9ST1dJRFMnOiBSb3dQYXJ0aXRpb25UeXBlLlZBTFVFX1JPV0lEUyxcbiAgICAnUk9XX0xFTkdUSFMnOiBSb3dQYXJ0aXRpb25UeXBlLlJPV19MRU5HVEhTLFxuICAgICdST1dfU1BMSVRTJzogUm93UGFydGl0aW9uVHlwZS5ST1dfU1BMSVRTLFxuICAgICdST1dfTElNSVRTJzogUm93UGFydGl0aW9uVHlwZS5ST1dfTElNSVRTLFxuICAgICdST1dfU1RBUlRTJzogUm93UGFydGl0aW9uVHlwZS5ST1dfU1RBUlRTXG4gIH07XG5cbiAgY29uc3QgcmVzdWx0OiBSb3dQYXJ0aXRpb25UeXBlW10gPSBbXTtcbiAgZm9yIChjb25zdCB0eXBlU3RyIG9mIHJvd1BhcnRpdGlvblR5cGVTdHJpbmdzKSB7XG4gICAgaWYgKHR5cGVTdHIgaW4gc3RyaW5nVG9UeXBlKSB7XG4gICAgICByZXN1bHQucHVzaChzdHJpbmdUb1R5cGVbdHlwZVN0ciBhcyBrZXlvZiB0eXBlb2Ygc3RyaW5nVG9UeXBlXSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIGJyZWFrO1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiByZXN1bHQ7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBnZXRSYWdnZWRSYW5rKHJvd1BhcnRpdGlvblR5cGVzOiBSb3dQYXJ0aXRpb25UeXBlW10pIHtcbiAgaWYgKHJvd1BhcnRpdGlvblR5cGVzLmxlbmd0aCA9PT0gMCkge1xuICAgIHJldHVybiAwO1xuICB9XG4gIGlmIChyb3dQYXJ0aXRpb25UeXBlc1swXSA9PT0gUm93UGFydGl0aW9uVHlwZS5GSVJTVF9ESU1fU0laRSkge1xuICAgIHJldHVybiByb3dQYXJ0aXRpb25UeXBlcy5sZW5ndGggLSAxO1xuICB9XG4gIHJldHVybiByb3dQYXJ0aXRpb25UeXBlcy5sZW5ndGg7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiB2YWxpZGF0ZURlZmF1bHRWYWx1ZVNoYXBlKFxuICAgIGRlZmF1bHRWYWx1ZVNoYXBlOiBudW1iZXJbXSwgdmFsdWVTaGFwZTogbnVtYmVyW10pIHtcbiAgaWYgKGRlZmF1bHRWYWx1ZVNoYXBlID09IG51bGwgfHwgdmFsdWVTaGFwZSA9PSBudWxsKSB7XG4gICAgcmV0dXJuO1xuICB9XG5cbiAgY29uc3QgZGVmYXVsdE5EaW1zID0gZGVmYXVsdFZhbHVlU2hhcGUubGVuZ3RoO1xuICBjb25zdCB2YWx1ZXNORGltcyA9IHZhbHVlU2hhcGUubGVuZ3RoO1xuICBpZiAoZGVmYXVsdE5EaW1zID49IHZhbHVlc05EaW1zKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKGBkZWZhdWx0VmFsdWUuc2hhcGU9JHtcbiAgICAgICAgZGVmYXVsdFZhbHVlU2hhcGV9IGFuZCByYWdnZWQgdGVuc29yIGZsYXRWYWx1ZXMuc2hhcGU9JHtcbiAgICAgICAgdmFsdWVTaGFwZX0sIGFyZSBpbmNvbXBhdGlibGU6IGRlZmF1bHRWYWx1ZS5yYW5rID0gJHtcbiAgICAgICAgZGVmYXVsdE5EaW1zfSBtdXN0IGJlIGxlc3MgdGhhbiByYWdnZWQgdGVuc29yIGlucHV0IGZsYXRWYWx1ZXMucmFuayA9ICR7XG4gICAgICAgIHZhbHVlc05EaW1zfSlgKTtcbiAgfVxuICBmb3IgKGxldCBpID0gMDsgaSA8IE1hdGgubWluKGRlZmF1bHRORGltcywgdmFsdWVzTkRpbXMgLSAxKTsgKytpKSB7XG4gICAgY29uc3QgZGVmYXVsdERpbSA9IGRlZmF1bHRWYWx1ZVNoYXBlW2ldO1xuICAgIGNvbnN0IHZhbHVlRGltID0gdmFsdWVTaGFwZVtpICsgMV07XG4gICAgaWYgKGRlZmF1bHREaW0gPj0gMCAmJiB2YWx1ZURpbSA+PSAwICYmIGRlZmF1bHREaW0gIT09IDEgJiZcbiAgICAgICAgZGVmYXVsdERpbSAhPT0gdmFsdWVEaW0pIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgZGVmYXVsdFZhbHVlLnNoYXBlPSR7XG4gICAgICAgICAgZGVmYXVsdFZhbHVlU2hhcGV9LCBhbmQgcmFnZ2VkIHRlbnNvciBpbnB1dCBmbGF0VmFsdWVzLnNoYXBlPSR7XG4gICAgICAgICAgdmFsdWVTaGFwZX0gYXJlIGluY29tcGF0aWJsZTogZGVmYXVsdFZhbHVlLnNoYXBlWyR7XG4gICAgICAgICAgaSAtIGRlZmF1bHRWYWx1ZVNoYXBlLmxlbmd0aH1dID0gJHtcbiAgICAgICAgICBkZWZhdWx0RGltfSBidXQgcmFnZ2VkIHRlbnNvciBpbnB1dC5mbGF0VmFsdWVzLnNoYXBlWyR7XG4gICAgICAgICAgaSAtIGRlZmF1bHRWYWx1ZVNoYXBlLmxlbmd0aH1dID0gJHt2YWx1ZURpbX1gKTtcbiAgICB9XG4gIH1cbn1cbiJdfQ==