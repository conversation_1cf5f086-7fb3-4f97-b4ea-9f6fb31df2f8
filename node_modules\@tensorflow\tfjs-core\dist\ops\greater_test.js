/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../index';
import { ALL_ENVS, describeWithFlags } from '../jasmine_util';
import { expectArraysClose, expectArraysEqual } from '../test_util';
describeWithFlags('greater', ALL_ENVS, () => {
    it('Tensor1D - int32', async () => {
        let a = tf.tensor1d([1, 4, 5], 'int32');
        let b = tf.tensor1d([2, 3, 5], 'int32');
        let res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [0, 1, 0]);
        a = tf.tensor1d([2, 2, 2], 'int32');
        b = tf.tensor1d([2, 2, 2], 'int32');
        res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [0, 0, 0]);
        a = tf.tensor1d([3, 3], 'int32');
        b = tf.tensor1d([0, 0], 'int32');
        res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [1, 1]);
    });
    it('Tensor1D - float32', async () => {
        let a = tf.tensor1d([1.1, 4.1, 5.1], 'float32');
        let b = tf.tensor1d([2.2, 3.2, 5.1], 'float32');
        let res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [0, 1, 0]);
        a = tf.tensor1d([2.31, 2.31, 2.31], 'float32');
        b = tf.tensor1d([2.31, 2.31, 2.31], 'float32');
        res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [0, 0, 0]);
        a = tf.tensor1d([3.123, 3.321], 'float32');
        b = tf.tensor1d([0.45, 0.123], 'float32');
        res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [1, 1]);
    });
    it('upcasts when dtypes dont match', async () => {
        const a = [1.1, 4.1, 5.2];
        const b = [2.2, 3.2, 5.1];
        let res = tf.greater(tf.tensor(a, [3], 'float32'), tf.tensor(b, [3], 'int32'));
        expect(res.dtype).toBe('bool');
        expect(res.shape).toEqual([3]);
        expectArraysClose(await res.data(), [0, 1, 1]);
        res = tf.greater(tf.tensor(a, [3], 'int32'), tf.tensor(b, [3], 'bool'));
        expect(res.dtype).toBe('bool');
        expect(res.shape).toEqual([3]);
        expectArraysClose(await res.data(), [0, 1, 1]);
    });
    it('TensorLike', async () => {
        const a = [1.1, 4.1, 5.1];
        const b = [2.2, 3.2, 5.1];
        const res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [0, 1, 0]);
    });
    it('TensorLike Chained', async () => {
        const a = tf.tensor1d([1.1, 4.1, 5.1], 'float32');
        const b = [2.2, 3.2, 5.1];
        const res = a.greater(b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [0, 1, 0]);
    });
    it('mismatched Tensor1D shapes - int32', () => {
        const a = tf.tensor1d([1, 2], 'int32');
        const b = tf.tensor1d([1, 2, 3], 'int32');
        const f = () => {
            tf.greater(a, b);
        };
        expect(f).toThrowError();
    });
    it('mismatched Tensor1D shapes - float32', () => {
        const a = tf.tensor1d([1.1, 2.1], 'float32');
        const b = tf.tensor1d([1.1, 2.1, 3.1], 'float32');
        const f = () => {
            tf.greater(a, b);
        };
        expect(f).toThrowError();
    });
    it('NaNs in Tensor1D - float32', async () => {
        const a = tf.tensor1d([1.1, NaN, 2.1], 'float32');
        const b = tf.tensor1d([2.1, 3.1, NaN], 'float32');
        const res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [0, 0, 0]);
    });
    // Tensor2D:
    it('Tensor2D - int32', async () => {
        let a = tf.tensor2d([[1, 4, 5], [8, 9, 11]], [2, 3], 'int32');
        let b = tf.tensor2d([[2, 3, 6], [7, 10, 11]], [2, 3], 'int32');
        let res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [0, 1, 0, 1, 0, 0]);
        a = tf.tensor2d([[0, 0], [1, 1]], [2, 2], 'int32');
        b = tf.tensor2d([[0, 0], [1, 1]], [2, 2], 'int32');
        res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [0, 0, 0, 0]);
    });
    it('Tensor2D - float32', async () => {
        let a = tf.tensor2d([[1.1, 4.1, 5.1], [8.1, 9.1, 11.1]], [2, 3], 'float32');
        let b = tf.tensor2d([[2.1, 3.1, 6.1], [7.1, 10.1, 11.1]], [2, 3], 'float32');
        let res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [0, 1, 0, 1, 0, 0]);
        a = tf.tensor2d([[0.2, 0.2], [1.2, 1.2]], [2, 2], 'float32');
        b = tf.tensor2d([[0.2, 0.2], [1.2, 1.2]], [2, 2], 'float32');
        res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [0, 0, 0, 0]);
    });
    it('broadcasting Tensor2D shapes - int32', async () => {
        const a = tf.tensor2d([[3], [7]], [2, 1], 'int32');
        const b = tf.tensor2d([[2, 3, 4], [7, 8, 9]], [2, 3], 'int32');
        const res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [1, 0, 0, 0, 0, 0]);
    });
    it('broadcasting Tensor2D shapes - float32', async () => {
        const a = tf.tensor2d([[1.1], [7.1]], [2, 1], 'float32');
        const b = tf.tensor2d([[0.1, 1.1, 2.1], [7.1, 8.1, 9.1]], [2, 3], 'float32');
        const res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [1, 0, 0, 0, 0, 0]);
    });
    it('NaNs in Tensor2D - float32', async () => {
        const a = tf.tensor2d([[1.1, NaN], [0.1, NaN]], [2, 2], 'float32');
        const b = tf.tensor2d([[0.1, NaN], [1.1, NaN]], [2, 2], 'float32');
        const res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [1, 0, 0, 0]);
    });
    // Tensor3D:
    it('Tensor3D - int32', async () => {
        let a = tf.tensor3d([[[1], [4], [5]], [[8], [9], [11]]], [2, 3, 1], 'int32');
        let b = tf.tensor3d([[[2], [3], [6]], [[7], [10], [11]]], [2, 3, 1], 'int32');
        let res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [0, 1, 0, 1, 0, 0]);
        a = tf.tensor3d([[[0], [0], [0]], [[1], [1], [1]]], [2, 3, 1], 'int32');
        b = tf.tensor3d([[[0], [0], [0]], [[1], [1], [1]]], [2, 3, 1], 'int32');
        res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [0, 0, 0, 0, 0, 0]);
    });
    it('Tensor3D - float32', async () => {
        let a = tf.tensor3d([[[1.1], [4.1], [5.1]], [[8.1], [9.1], [11.1]]], [2, 3, 1], 'float32');
        let b = tf.tensor3d([[[2.1], [3.1], [6.1]], [[7.1], [10.1], [11.1]]], [2, 3, 1], 'float32');
        let res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [0, 1, 0, 1, 0, 0]);
        a = tf.tensor3d([[[0.1], [0.1], [0.1]], [[1.1], [1.1], [1.2]]], [2, 3, 1], 'float32');
        b = tf.tensor3d([[[0.1], [0.1], [0.1]], [[1.1], [1.1], [1.1]]], [2, 3, 1], 'float32');
        res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [0, 0, 0, 0, 0, 1]);
    });
    it('broadcasting Tensor3D shapes - int32', async () => {
        const a = tf.tensor3d([[[1, 0], [2, 3], [4, 5]], [[6, 7], [9, 8], [10, 11]]], [2, 3, 2], 'int32');
        const b = tf.tensor3d([[[1], [2], [3]], [[7], [10], [9]]], [2, 3, 1], 'int32');
        const res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1]);
    });
    it('broadcasting Tensor3D shapes - float32', async () => {
        const a = tf.tensor3d([
            [[1.1, 0.1], [2.1, 3.1], [4.1, 5.1]],
            [[6.1, 7.1], [9.1, 8.1], [10.1, 11.1]]
        ], [2, 3, 2], 'float32');
        const b = tf.tensor3d([[[1.1], [2.1], [3.1]], [[7.1], [10.1], [9.1]]], [2, 3, 1], 'float32');
        const res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1]);
    });
    it('NaNs in Tensor3D - float32', async () => {
        const a = tf.tensor3d([[[1.1], [NaN], [1.1]], [[0.1], [0.1], [0.1]]], [2, 3, 1], 'float32');
        const b = tf.tensor3d([[[0.1], [0.1], [1.1]], [[1.1], [0.1], [NaN]]], [2, 3, 1], 'float32');
        const res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [1, 0, 0, 0, 0, 0]);
    });
    // Tensor4D:
    it('Tensor4D - int32', async () => {
        let a = tf.tensor4d([1, 4, 5, 8], [2, 2, 1, 1], 'int32');
        let b = tf.tensor4d([2, 3, 6, 8], [2, 2, 1, 1], 'int32');
        let res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [0, 1, 0, 0]);
        a = tf.tensor4d([0, 1, 2, 3], [2, 2, 1, 1], 'int32');
        b = tf.tensor4d([0, 1, 2, 3], [2, 2, 1, 1], 'int32');
        res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [0, 0, 0, 0]);
        a = tf.tensor4d([2, 2, 2, 2], [2, 2, 1, 1], 'int32');
        b = tf.tensor4d([1, 1, 1, 1], [2, 2, 1, 1], 'int32');
        res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [1, 1, 1, 1]);
    });
    it('Tensor4D - float32', async () => {
        let a = tf.tensor4d([1.1, 4.1, 5.1, 8.1], [2, 2, 1, 1], 'float32');
        let b = tf.tensor4d([2.1, 3.1, 6.1, 8.1], [2, 2, 1, 1], 'float32');
        let res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [0, 1, 0, 0]);
        a = tf.tensor4d([0.1, 1.1, 2.2, 3.3], [2, 2, 1, 1], 'float32');
        b = tf.tensor4d([0.1, 1.1, 2.2, 3.3], [2, 2, 1, 1], 'float32');
        res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [0, 0, 0, 0]);
        a = tf.tensor4d([1.1, 1.1, 1.1, 1.1], [2, 2, 1, 1], 'float32');
        b = tf.tensor4d([0.1, 0.1, 0.1, 0.1], [2, 2, 1, 1], 'float32');
        res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [1, 1, 1, 1]);
    });
    it('broadcasting Tensor4D shapes - int32', async () => {
        const a = tf.tensor4d([1, 2, 5, 9], [2, 2, 1, 1], 'int32');
        const b = tf.tensor4d([[[[1, 2]], [[3, 4]]], [[[5, 6]], [[7, 8]]]], [2, 2, 1, 2], 'int32');
        const res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [0, 0, 0, 0, 0, 0, 1, 1]);
    });
    it('broadcasting Tensor4D shapes - float32', async () => {
        const a = tf.tensor4d([1.1, 2.1, 5.1, 9.1], [2, 2, 1, 1], 'float32');
        const b = tf.tensor4d([[[[1.1, 2.1]], [[3.1, 4.1]]], [[[5.1, 6.1]], [[7.1, 8.1]]]], [2, 2, 1, 2], 'float32');
        const res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [0, 0, 0, 0, 0, 0, 1, 1]);
    });
    it('NaNs in Tensor4D - float32', async () => {
        const a = tf.tensor4d([1.1, NaN, 0.1, 0.1], [2, 2, 1, 1], 'float32');
        const b = tf.tensor4d([0.1, 1.1, 1.1, NaN], [2, 2, 1, 1], 'float32');
        const res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [1, 0, 0, 0]);
    });
    it('throws when passed a as a non-tensor', () => {
        expect(() => tf.greater({}, tf.scalar(1)))
            .toThrowError(/Argument 'a' passed to 'greater' must be a Tensor/);
    });
    it('throws when passed b as a non-tensor', () => {
        expect(() => tf.greater(tf.scalar(1), {}))
            .toThrowError(/Argument 'b' passed to 'greater' must be a Tensor/);
    });
    it('accepts a tensor-like object', async () => {
        const a = [1, 4, 5];
        const b = [2, 3, 5];
        const res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expectArraysClose(await res.data(), [0, 1, 0]);
    });
    it('works with 0 sized tensors', async () => {
        const a = tf.tensor2d([], [0, 5]);
        const b = tf.tensor1d([1, 2, 3, 4, 5]);
        const res = tf.greater(a, b);
        expect(res.dtype).toBe('bool');
        expect(res.shape).toEqual([0, 5]);
        expectArraysClose(await res.data(), []);
    });
    it('should support string comparison', async () => {
        const tensorA = tf.tensor('a', [], 'string');
        const tensorB = tf.tensor(['a', 'b', ''], [3], 'string');
        const result = await tf.greater(tensorA, tensorB);
        expectArraysEqual(result.shape, [3]);
        expectArraysEqual(await result.data(), [0, 0, 1]);
    });
});
//# sourceMappingURL=data:application/json;base64,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