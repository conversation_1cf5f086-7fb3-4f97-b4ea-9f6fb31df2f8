/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../../index';
import { ALL_ENVS, describeWithFlags } from '../../jasmine_util';
import { expectArraysClose } from '../../test_util';
describeWithFlags('1D IRFFT', ALL_ENVS, () => {
    it('should return the same value with TensorFlow (2 elements)', async () => {
        const t1Real = tf.tensor1d([1, 2]);
        const t1Imag = tf.tensor1d([0, 0]);
        const t1 = tf.complex(t1Real, t1Imag);
        expectArraysClose(await tf.spectral.irfft(t1).data(), [1.5, -0.5]);
    });
    it('should calculate from the tensor directly', async () => {
        const t1Real = tf.tensor1d([1, 2]);
        const t1Imag = tf.tensor1d([0, 0]);
        const t1 = tf.complex(t1Real, t1Imag);
        expectArraysClose(await t1.irfft().data(), [1.5, -0.5]);
    });
    it('should return the same value with TensorFlow (5 elements)', async () => {
        const t1Real = tf.tensor1d([1, 2, 3, 4, 5]);
        const t1Imag = tf.tensor1d([0, 0, 0, 0, 0]);
        const t1 = tf.complex(t1Real, t1Imag);
        expectArraysClose(await tf.spectral.irfft(t1).data(), [3, -0.8535534, 0, -0.14644662, 0, -0.14644662, 0, -0.8535534]);
    });
    it('should return the same value with TensorFlow (5 elements) with imag', async () => {
        const t1Real = tf.tensor1d([1, 2, 3, 4, 5]);
        const t1Imag = tf.tensor1d([1, 2, 3, 4, 5]);
        const t1 = tf.complex(t1Real, t1Imag);
        expectArraysClose(await tf.spectral.irfft(t1).data(), [3, -2.6642137, 0.5, -0.45710677, 0, 0.16421354, -0.5, 0.95710677]);
    });
});
describeWithFlags('2D IRFFT', ALL_ENVS, () => {
    it('should return the same value with TensorFlow (2x2 elements)', async () => {
        const t1Real = tf.tensor2d([1, 2, 3, 4], [2, 2]);
        const t1Imag = tf.tensor2d([0, 0, 0, 0], [2, 2]);
        const t1 = tf.complex(t1Real, t1Imag);
        expectArraysClose(await tf.spectral.irfft(t1).data(), [1.5, -0.5, 3.5, -0.5]);
    });
    it('should return the same value with TensorFlow (2x3 elements)', async () => {
        const t1Real = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const t1Imag = tf.tensor2d([0, 0, 0, 0, 0, 0], [2, 3]);
        const t1 = tf.complex(t1Real, t1Imag);
        expectArraysClose(await tf.spectral.irfft(t1).data(), [2, -0.5, 0, -0.5, 5, -0.5, 0, -0.5]);
    });
    it('should return the same value with TensorFlow (2x3 elements) with imag', async () => {
        const t1Real = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const t1Imag = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const t1 = tf.complex(t1Real, t1Imag);
        expectArraysClose(await tf.spectral.irfft(t1).data(), [2, -1.5, 0, 0.5, 5, -3, 0, 2]);
    });
});
describeWithFlags('3D IRFFT', ALL_ENVS, () => {
    it('should return the same value with TensorFlow (2x2x2 elements)', async () => {
        const t1Real = tf.tensor3d([1, 2, 3, 4, 1, 2, 3, 4], [2, 2, 2]);
        const t1Imag = tf.tensor3d([0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 2]);
        const t1 = tf.complex(t1Real, t1Imag);
        const result = tf.spectral.irfft(t1);
        expect(result.shape).toEqual([2, 2, 2]);
        expectArraysClose(await result.data(), [1.5, -0.5, 3.5, -0.5, 1.5, -0.5, 3.5, -0.5]);
    });
    it('should return the same value with TensorFlow (2x2x3 elements)', async () => {
        const t1Real = tf.tensor3d([1, 2, 3, 4, 5, 6, 1, 2, 3, 4, 5, 6], [2, 2, 3]);
        const t1Imag = tf.tensor3d([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 3]);
        const t1 = tf.complex(t1Real, t1Imag);
        const result = tf.spectral.irfft(t1);
        expect(result.shape).toEqual([2, 2, 4]);
        expectArraysClose(await result.data(), [
            2, -0.5, 0, -0.5, 5, -0.5, 0, -0.5, 2, -0.5, 0, -0.5, 5, -0.5, 0, -0.5
        ]);
    });
    it('should return the same value with TensorFlow (2x2x3 elements) with imag', async () => {
        const t1Real = tf.tensor3d([1, 2, 3, 4, 5, 6, 1, 2, 3, 4, 5, 6], [2, 2, 3]);
        const t1Imag = tf.tensor3d([1, 2, 3, 4, 5, 6, 1, 2, 3, 4, 5, 6], [2, 2, 3]);
        const t1 = tf.complex(t1Real, t1Imag);
        const result = tf.spectral.irfft(t1);
        expect(result.shape).toEqual([2, 2, 4]);
        expectArraysClose(await result.data(), [2, -1.5, 0, 0.5, 5, -3, 0, 2, 2, -1.5, 0, 0.5, 5, -3, 0, 2]);
    });
});
//# sourceMappingURL=data:application/json;base64,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