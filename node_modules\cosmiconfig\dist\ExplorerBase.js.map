{"version": 3, "file": "ExplorerBase.js", "sourceRoot": "", "sources": ["../src/ExplorerBase.ts"], "names": [], "mappings": ";;;;;;AAAA,0DAAiC;AACjC,4CAAoB;AACpB,gDAAwB;AAUxB,uCAA8C;AAE9C;;GAEG;AACH,MAAsB,YAAY;IAGhC,kBAAkB,GAAG,KAAK,CAAC;IAER,MAAM,CAAI;IACV,SAAS,CAEb;IACI,WAAW,CAEf;IAEf,YAAmB,OAAoB;QACrC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC;QACtB,IAAI,OAAO,CAAC,KAAK,EAAE;YACjB,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;YAC3B,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;SAC9B;QACD,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED,IAAc,iBAAiB,CAAC,KAAc;QAC5C,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;IAClC,CAAC;IAED,eAAe;QACb,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAE3B,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,YAAY,EAAE;YACvC,MAAM,SAAS,GAAG,cAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACtC,MAAM,MAAM,GACV,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC;gBACzC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACjC,IAAI,MAAM,KAAK,SAAS,EAAE;gBACxB,MAAM,IAAI,KAAK,CACb,sBAAsB,uBAAuB,CAAC,KAAK,CAAC,GAAG,CACxD,CAAC;aACH;YACD,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;gBAChC,MAAM,IAAI,KAAK,CACb,cAAc,uBAAuB,CACnC,KAAK,CACN,gCAAgC,OAAO,MAAM,GAAG,CAClD,CAAC;aACH;SACF;IACH,CAAC;IAEM,cAAc;QACnB,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;SACxB;IACH,CAAC;IAEM,gBAAgB;QACrB,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;SAC1B;IACH,CAAC;IAEM,WAAW;QAChB,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAES,mBAAmB,CAC3B,QAAgB,EAChB,MAAc;QAEd,IAAI,MAAM,KAAK,IAAI,EAAE;YACnB,OAAO,IAAI,CAAC;SACb;QACD,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;SACvD;QACD,IACE,IAAI,CAAC,MAAM,CAAC,uCAAuC;YACnD,IAAI,CAAC,kBAAkB,EACvB;YACA,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;YACtE,MAAM,GAAG,IAAA,2BAAiB,EAAC,MAAM,EAAE,WAAW,CAAC,CAAC;SACjD;QACD,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;SACvD;QACD,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;IAC9B,CAAC;IAES,eAAe,CACvB,kBAA0B,EAC1B,OAAuB,EACvB,WAA0B;QAE1B,MAAM,aAAa,GAAG,cAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QACvD,KAAK,MAAM,UAAU,IAAI,OAAO,EAAE;YAChC,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;gBAClC,MAAM,IAAI,KAAK,CACb,GAAG,kBAAkB,0DAA0D,CAChF,CAAC;aACH;YACD,MAAM,QAAQ,GAAG,cAAI,CAAC,OAAO,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;YACzD,IAAI,QAAQ,KAAK,kBAAkB,EAAE;gBACnC,MAAM,IAAI,KAAK,CAAC,2BAA2B,kBAAkB,EAAE,CAAC,CAAC;aAClE;YACD,MAAM,GAAG,GAAG,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC1C,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;gBACd,MAAM,IAAI,KAAK,CACb;EACR,CAAC,GAAG,WAAW,EAAE,QAAQ,CAAC;qBACzB,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC;qBACrC,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC,IAAI,CAC5B,CAAC;aACH;SACF;IACH,CAAC;IAES,qBAAqB,CAC7B,GAAgB,EAChB,kBAAiC;QAEjC,OAAO,CACL,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CACnE,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,cAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;IAC/C,CAAC;IAES,kBAAkB;QAC1B,OAAO,IAAA,mBAAQ,EAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC;IACjE,CAAC;IAES,CAAC,aAAa,CAAC,QAAgB;QACvC,MAAM,OAAO,GAAG,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,YAAE,CAAC,OAAO,EAAE,CAAC,CAAC;QAClE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,cAAc,EAAE,KAAK,EAAE,CAAC;QAChD,IAAI,UAAU,GAAG,QAAQ,CAAC;QAC1B,OAAO,UAAU,KAAK,OAAO,EAAE;YAC7B,MAAM,SAAS,GAAG,cAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAC3C,qCAAqC;YACrC,IAAI,SAAS,KAAK,UAAU,EAAE;gBAC5B,wDAAwD;gBACxD,MAAM;aACP;YAED,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,cAAc,EAAE,KAAK,EAAE,CAAC;YACjD,UAAU,GAAG,SAAS,CAAC;SACxB;QAED,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,kBAAkB,EAAE,EAAE,cAAc,EAAE,IAAI,EAAE,CAAC;IAClE,CAAC;CACF;AApJD,oCAoJC;AAED;;GAEG;AACH,SAAgB,uBAAuB,CAAC,SAAkB;IACxD,uCAAuC;IACvC,OAAO,SAAS,CAAC,CAAC,CAAC,cAAc,SAAS,GAAG,CAAC,CAAC,CAAC,0BAA0B,CAAC;AAC7E,CAAC;AAHD,0DAGC"}