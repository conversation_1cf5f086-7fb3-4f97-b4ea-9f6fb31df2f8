/**
 * @license
 * Copyright 2017 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../index';
import { ALL_ENVS, describeWithFlags } from '../jasmine_util';
import { expectArraysClose } from '../test_util';
describeWithFlags('pool', ALL_ENVS, () => {
    // First test that tf.pool calls are consistent with maxPool/avgPool by
    // duplicating some maxPool/avgPool tests. The implementation code is the
    // same, so we don't need the same level of thoroughness here.
    it('max x=[1,1,1] f=[1,1] s=1 d=1 [0] => [0]', async () => {
        const x = tf.tensor3d([0], [1, 1, 1]);
        const windowShape = 1;
        const padding = 0;
        const result = tf.pool(x, windowShape, 'max', padding);
        expectArraysClose(await result.data(), [0]);
    });
    it('max x=[3,3,1] f=[2,2] s=1 d=1', async () => {
        // Feed forward.
        const x = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 9, 8], [3, 3, 1]);
        const windowShape = 2;
        const padding = 0;
        const dilationRate = undefined;
        const strides = undefined;
        const result = tf.pool(x, windowShape, 'max', padding, dilationRate, strides);
        expect(result.shape).toEqual([2, 2, 1]);
        expectArraysClose(await result.data(), [5, 6, 9, 9]);
    });
    it('max x=[4,4,1] f=[2,2] s=2 d=1', async () => {
        // Feed forward.
        const x = tf.tensor3d([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], [4, 4, 1]);
        const windowShape = 2;
        const padding = 0;
        const dilationRate = undefined;
        const strides = 2;
        const result = tf.pool(x, windowShape, 'max', padding, dilationRate, strides);
        expect(result.shape).toEqual([2, 2, 1]);
        expectArraysClose(await result.data(), [5, 7, 13, 15]);
    });
    it('max x=[2,2,1] f=[2,2] s=1 d=1 p=same', async () => {
        // Feed forward.
        const x = tf.tensor3d([1, 2, 3, 4], [2, 2, 1]);
        const windowShape = 2;
        const padding = 'same';
        const dilationRate = undefined;
        const strides = 1;
        const result = tf.pool(x, windowShape, 'max', padding, dilationRate, strides);
        expect(result.shape).toEqual([2, 2, 1]);
        expectArraysClose(await result.data(), [4, 4, 4, 4]);
    });
    it('max x=[3,3,1] f=[3,3] s=1 d=1 p=explicit', async () => {
        // Feed forward.
        const x = tf.tensor3d([0, 1, 2, 3, 4, 5, 6, 7, 8], [3, 3, 1]);
        const windowShape = 3;
        const padding = [[0, 0], [1, 2], [0, 1], [0, 0]];
        const dilationRate = undefined;
        const strides = 1;
        const result = tf.pool(x, windowShape, 'max', padding, dilationRate, strides);
        expect(result.shape).toEqual([4, 2, 1]);
        expectArraysClose(await result.data(), [5, 5, 8, 8, 8, 8, 8, 8]);
    });
    it('max x=[3,3,1] f=[3,3] s=3 d=1 p=explicit defualt dimRoundingMode', async () => {
        // Feed forward.
        const x = tf.tensor3d([0, 1, 2, 3, 4, 5, 6, 7, 8], [3, 3, 1]);
        const windowShape = 3;
        const padding = [[0, 0], [2, 2], [1, 1], [0, 0]];
        const dilationRate = undefined;
        const strides = 3;
        const result = tf.pool(x, windowShape, 'max', padding, dilationRate, strides);
        expect(result.shape).toEqual([2, 1, 1]);
    });
    it('max x=[3,3,1] f=[3,3] s=3 d=1 p=explicit dimRoundingMode=floor', async () => {
        // Feed forward.
        const x = tf.tensor3d([0, 1, 2, 3, 4, 5, 6, 7, 8], [3, 3, 1]);
        const windowShape = 3;
        const padding = [[0, 0], [2, 2], [1, 1], [0, 0]];
        const dilationRate = undefined;
        const strides = 3;
        const result = tf.pool(x, windowShape, 'max', padding, dilationRate, strides, 'floor');
        expect(result.shape).toEqual([2, 1, 1]);
    });
    it('max x=[3,3,1] f=[3,3] s=3 d=1 p=explicit dimRoundingMode=round', async () => {
        // Feed forward.
        const x = tf.tensor3d([0, 1, 2, 3, 4, 5, 6, 7, 8], [3, 3, 1]);
        const windowShape = 3;
        const padding = [[0, 0], [2, 2], [1, 1], [0, 0]];
        const dilationRate = undefined;
        const strides = 3;
        const result = tf.pool(x, windowShape, 'max', padding, dilationRate, strides, 'round');
        expect(result.shape).toEqual([2, 2, 1]);
    });
    it('max x=[3,3,1] f=[3,3] s=3 d=1 p=explicit dimRoundingMode=ceil', async () => {
        // Feed forward.
        const x = tf.tensor3d([0, 1, 2, 3, 4, 5, 6, 7, 8], [3, 3, 1]);
        const windowShape = 3;
        const padding = [[0, 0], [2, 2], [1, 1], [0, 0]];
        const dilationRate = undefined;
        const strides = 3;
        const result = tf.pool(x, windowShape, 'max', padding, dilationRate, strides, 'ceil');
        expect(result.shape).toEqual([3, 2, 1]);
    });
    it('max x=[2,2,3] f=[1,1] s=2 p=1 fractional outputs default rounding', async () => {
        // Feed forward.
        const a = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [2, 2, 3]);
        const windowShape = 1;
        const padding = 1;
        const dilationRate = undefined;
        const strides = 2;
        const result = tf.pool(a, windowShape, 'max', padding, dilationRate, strides);
        expect(result.shape).toEqual([2, 2, 3]);
        expectArraysClose(await result.data(), [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]);
    });
    it('avg x=[1,1,1] f=[1,1] s=1 d=1 [0] => [0]', async () => {
        const a = tf.tensor3d([0], [1, 1, 1]);
        const windowShape = 1;
        const padding = 0;
        const result = tf.pool(a, windowShape, 'avg', padding);
        expectArraysClose(await result.data(), [0]);
    });
    it('avg x=[3,3,1] f=[2,2] s=1 d=1', async () => {
        // Feed forward.
        const a = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 9, 8], [3, 3, 1]);
        const windowShape = 2;
        const padding = 0;
        const dilationRate = undefined;
        const strides = undefined;
        const result = tf.pool(a, windowShape, 'avg', padding, dilationRate, strides);
        expect(result.shape).toEqual([2, 2, 1]);
        expect(result.dtype).toBe('float32');
        expectArraysClose(await result.data(), [3, 4, 6.25, 7]);
    });
    it('avg x=[4,4,1] f=[2,2] s=2 d=1', async () => {
        // Feed forward.
        const a = tf.tensor3d([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], [4, 4, 1]);
        const windowShape = 2;
        const padding = 0;
        const dilationRate = undefined;
        const strides = 2;
        const result = tf.pool(a, windowShape, 'avg', padding, dilationRate, strides);
        expect(result.shape).toEqual([2, 2, 1]);
        expectArraysClose(await result.data(), [2.5, 4.5, 10.5, 12.5]);
    });
    it('avg x=[2,2,1] f=[2,2] s=1 p=same', async () => {
        // Feed forward.
        const a = tf.tensor3d([1, 2, 3, 4], [2, 2, 1]);
        const windowShape = 2;
        const padding = 'same';
        const dilationRate = undefined;
        const strides = 1;
        const result = tf.pool(a, windowShape, 'avg', padding, dilationRate, strides);
        expect(result.shape).toEqual([2, 2, 1]);
        expectArraysClose(await result.data(), [2.5, 3, 3.5, 4]);
    });
    it('avg x=[3,3,1] f=[3,3] s=1 d=1 p=explicit', async () => {
        // Feed forward.
        const x = tf.tensor3d([0, 1, 2, 3, 4, 5, 6, 7, 8], [3, 3, 1]);
        const windowShape = 3;
        const padding = [[0, 0], [1, 2], [0, 1], [0, 0]];
        const dilationRate = undefined;
        const strides = 1;
        const result = tf.pool(x, windowShape, 'avg', padding, dilationRate, strides);
        expect(result.shape).toEqual([4, 2, 1]);
        expectArraysClose(await result.data(), [2.5, 3, 4, 4.5, 5.5, 6, 7, 7.5]);
    });
    it('avg x=[3,3,1] f=[3,3] s=3 d=1 p=explicit defualt dimRoundingMode', async () => {
        // Feed forward.
        const x = tf.tensor3d([0, 1, 2, 3, 4, 5, 6, 7, 8], [3, 3, 1]);
        const windowShape = 3;
        const padding = [[0, 0], [2, 2], [1, 1], [0, 0]];
        const dilationRate = undefined;
        const strides = 3;
        const result = tf.pool(x, windowShape, 'avg', padding, dilationRate, strides);
        expect(result.shape).toEqual([2, 1, 1]);
    });
    it('avg x=[3,3,1] f=[3,3] s=3 d=1 p=explicit dimRoundingMode=floor', async () => {
        // Feed forward.
        const x = tf.tensor3d([0, 1, 2, 3, 4, 5, 6, 7, 8], [3, 3, 1]);
        const windowShape = 3;
        const padding = [[0, 0], [2, 2], [1, 1], [0, 0]];
        const dilationRate = undefined;
        const strides = 3;
        const result = tf.pool(x, windowShape, 'avg', padding, dilationRate, strides, 'floor');
        expect(result.shape).toEqual([2, 1, 1]);
    });
    it('avg x=[3,3,1] f=[3,3] s=3 d=1 p=explicit dimRoundingMode=round', async () => {
        // Feed forward.
        const x = tf.tensor3d([0, 1, 2, 3, 4, 5, 6, 7, 8], [3, 3, 1]);
        const windowShape = 3;
        const padding = [[0, 0], [2, 2], [1, 1], [0, 0]];
        const dilationRate = undefined;
        const strides = 3;
        const result = tf.pool(x, windowShape, 'avg', padding, dilationRate, strides, 'round');
        expect(result.shape).toEqual([2, 2, 1]);
    });
    it('avg x=[3,3,1] f=[3,3] s=3 d=1 p=explicit dimRoundingMode=ceil', async () => {
        // Feed forward.
        const x = tf.tensor3d([0, 1, 2, 3, 4, 5, 6, 7, 8], [3, 3, 1]);
        const windowShape = 3;
        const padding = [[0, 0], [2, 2], [1, 1], [0, 0]];
        const dilationRate = undefined;
        const strides = 3;
        const result = tf.pool(x, windowShape, 'avg', padding, dilationRate, strides, 'ceil');
        expect(result.shape).toEqual([3, 2, 1]);
    });
    it('avg x=[2,2,3] f=[1,1] s=2 p=1 fractional outputs default rounding', async () => {
        // Feed forward.
        const a = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [2, 2, 3]);
        const windowShape = 1;
        const padding = 1;
        const dilationRate = undefined;
        const strides = 2;
        const result = tf.pool(a, windowShape, 'avg', padding, dilationRate, strides);
        expect(result.shape).toEqual([2, 2, 3]);
        expectArraysClose(await result.data(), [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]);
    });
    // tf.pool supports dilation, unlike maxPool or avgPool
    it('max x=[4,3,1] f=[2,2] s=1 d=2', async () => {
        // Feed forward.
        const x = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16], [4, 4, 1]);
        const windowShape = 2;
        const padding = 0;
        const dilationRate = 2;
        const strides = undefined;
        const result = tf.pool(x, windowShape, 'max', padding, dilationRate, strides);
        expect(result.shape).toEqual([2, 2, 1]);
        expectArraysClose(await result.data(), [11, 12, 15, 16]);
    });
    it('max x=[2,4,4,1] f=[2,2] s=1 d=2', async () => {
        // Feed forward.
        const x = tf.tensor4d([
            1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16,
            1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 11, 13, 14, 16, 15
        ], [2, 4, 4, 1]);
        const windowShape = 2;
        const padding = 0;
        const dilationRate = 2;
        const strides = undefined;
        const result = tf.pool(x, windowShape, 'max', padding, dilationRate, strides);
        expect(result.shape).toEqual([2, 2, 2, 1]);
        expectArraysClose(await result.data(), [11, 12, 15, 16, 12, 11, 16, 15]);
    });
    it('avg x=[4,4,1] f=[2,2] s=1 d=2', async () => {
        // Feed forward.
        const a = tf.tensor3d([1, 3, 2, 4, 6, 5, 8, 7, 9, 10, 12, 11, 16, 15, 14, 13], [4, 4, 1]);
        const windowShape = 2;
        const padding = 0;
        const dilationRate = 2;
        const strides = undefined;
        const result = tf.pool(a, windowShape, 'avg', padding, dilationRate, strides);
        expect(result.shape).toEqual([2, 2, 1]);
        expect(result.dtype).toBe('float32');
        expectArraysClose(await result.data(), [6, 7, 11, 10]);
    });
    it('max throws when neither s=1 nor d=1', () => {
        // Feed forward.
        const x = tf.tensor3d([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], [4, 4, 1]);
        const windowShape = 2;
        const padding = 0;
        const dilationRate = 2;
        const strides = 2;
        expect(() => tf.pool(x, windowShape, 'max', padding, dilationRate, strides))
            .toThrowError();
    });
    it('avg throws when neither s=1 nor d=1', () => {
        // Feed forward.
        const x = tf.tensor3d([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], [4, 4, 1]);
        const windowShape = 2;
        const padding = 0;
        const dilationRate = 2;
        const strides = 2;
        expect(() => tf.pool(x, windowShape, 'avg', padding, dilationRate, strides))
            .toThrowError();
    });
});
describeWithFlags('poolBackprop', ALL_ENVS, () => {
    it('max gradients x=[3,3,1] f=[2,2] s=1 d=1 no dup max value', async () => {
        const dy = tf.tensor3d([1, 2, 3, 4], [2, 2, 1]);
        const x = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9], [3, 3, 1]);
        const expected = [0, 0, 0, 0, 1, 2, 0, 3, 4];
        const windowShape = 2;
        const padding = 0;
        const dilationRate = undefined;
        const strides = undefined;
        const dx = tf.grad((x) => x.pool(windowShape, 'max', padding, dilationRate, strides))(x, dy);
        expect(dx.shape).toEqual(x.shape);
        expectArraysClose(await dx.data(), expected);
    });
    it('max gradients x=[3,3,1] f=[2,2] s=1 d=2 no dup max value, test #1', async () => {
        const dy = tf.tensor3d([1, 2, 3, 4], [2, 2, 1]);
        const x = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16], [4, 4, 1]);
        const expected = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2, 0, 0, 3, 4];
        const windowShape = 2;
        const padding = 0;
        const dilationRate = 2;
        const strides = undefined;
        const dx = tf.grad((x) => x.pool(windowShape, 'max', padding, dilationRate, strides))(x, dy);
        expect(dx.shape).toEqual(x.shape);
        expectArraysClose(await dx.data(), expected);
    });
    it('max gradients x=[3,3,1] f=[2,2] s=1 d=2 no dup max value, test #2', async () => {
        const dy = tf.tensor3d([1, 2, 3, 4], [2, 2, 1]);
        const x = tf.tensor3d([9, 5, 8, 6, 3, 1, 2, 4, 7, 3, 6, 4, 11, 15, 10, 16], [4, 4, 1]);
        const expected = [1, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 4];
        const windowShape = 2;
        const padding = 0;
        const dilationRate = 2;
        const strides = undefined;
        const dx = tf.grad((x) => x.pool(windowShape, 'max', padding, dilationRate, strides))(x, dy);
        expect(dx.shape).toEqual(x.shape);
        expectArraysClose(await dx.data(), expected);
    });
    it('max gradient x=[3,3,1] f=[2,2] s=1 d=2 dup max value', async () => {
        const dy = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9], [3, 3, 1]);
        const x = tf.tensor3d([
            0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
        ], [5, 5, 1]);
        const expected = [
            0, 0, 0, 0, 0, 0, 5, 10, 0, 0, 0, 10, 20,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
        ];
        const windowShape = 2;
        const padding = 0;
        const dilationRate = 2;
        const strides = undefined;
        const dx = tf.grad((x) => x.pool(windowShape, 'max', padding, dilationRate, strides))(x, dy);
        expect(dx.shape).toEqual(x.shape);
        expectArraysClose(await dx.data(), expected);
    });
    it('avg gradient x=[4,4,1] f=[2,2] s=1 d=2', async () => {
        const x = tf.tensor3d([
            1, 3, 2, 4, 6, 5, 8, 7, 9, 10, 12, 11, 16,
            15, 14, 13, 17, 18, 19, 20, 21, 22, 23, 24, 25
        ], [5, 5, 1]);
        const dy = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9], [3, 3, 1]);
        const f = 1 / (2 * 2);
        const windowShape = 2;
        const padding = 0;
        const dilationRate = 2;
        const strides = undefined;
        const dx = tf.grad((x) => x.pool(windowShape, 'avg', padding, dilationRate, strides))(x, dy);
        expect(dx.shape).toEqual(x.shape);
        expectArraysClose(await dx.data(), [
            1 * f, 2 * f, 4 * f, 2 * f, 3 * f, 4 * f, 5 * f, 10 * f, 5 * f,
            6 * f, 8 * f, 10 * f, 20 * f, 10 * f, 12 * f, 4 * f, 5 * f, 10 * f,
            5 * f, 6 * f, 7 * f, 8 * f, 16 * f, 8 * f, 9 * f
        ]);
    });
});
//# sourceMappingURL=data:application/json;base64,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