/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
// Importing local_storage and indexed_db is necessary for the routers to be
// registered.
import './indexed_db';
import './local_storage';
import { browserFiles } from './browser_files';
import { browserHTTPRequest, http, isHTTPScheme } from './http';
import { concatenateArrayBuffers, decodeWeights, decodeWeightsStream, encodeWeights, getModelArtifactsForJSON, getModelArtifactsForJSONSync, getModelArtifactsInfoForJSON, getWeightSpecs } from './io_utils';
import { fromMemory, fromMemorySync, withSaveHandler, withSaveHandlerSync } from './passthrough';
import { getLoadHandlers, getSaveHandlers, registerLoadRouter, registerSaveRouter } from './router_registry';
import { loadWeights, weightsLoaderFactory } from './weights_loader';
import { CompositeArrayBuffer } from './composite_array_buffer';
export { copyModel, listModels, moveModel, removeModel } from './model_management';
export { browserFiles, browserHTTPRequest, CompositeArrayBuffer, concatenateArrayBuffers, decodeWeights, decodeWeightsStream, encodeWeights, fromMemory, fromMemorySync, getLoadHandlers, getModelArtifactsForJSON, getModelArtifactsForJSONSync, getModelArtifactsInfoForJSON, getSaveHandlers, getWeightSpecs, http, isHTTPScheme, loadWeights, registerLoadRouter, registerSaveRouter, weightsLoaderFactory, withSaveHandler, withSaveHandlerSync, };
//# sourceMappingURL=data:application/json;base64,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