/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../../index';
import { ALL_ENVS, describeWithFlags } from '../../jasmine_util';
import { expectArraysClose } from '../../test_util';
describeWithFlags('1D RFFT', ALL_ENVS, () => {
    it('should return the same value with TensorFlow (3 elements)', async () => {
        const t1Real = tf.tensor1d([1, 2, 3]);
        expectArraysClose(await tf.spectral.rfft(t1Real).data(), [6, 1.1920929e-07, -1.4999999, 8.6602521e-01]);
    });
    it('should calculate from tensor directly', async () => {
        const t1Real = tf.tensor1d([1, 2, 3]);
        expectArraysClose(await t1Real.rfft().data(), [6, 1.1920929e-07, -1.4999999, 8.6602521e-01]);
    });
    it('should return the same value with TensorFlow (6 elements)', async () => {
        const t1Real = tf.tensor1d([-3, -2, -1, 1, 2, 3]);
        expectArraysClose(await tf.spectral.rfft(t1Real).data(), [
            -5.8859587e-07, 1.1920929e-07, -3.9999995, 6.9282026e+00, -2.9999998,
            1.7320497, -4.0000000, -2.3841858e-07
        ]);
    });
    it('should return the same value without any fftLength', async () => {
        const t1Real = tf.tensor1d([-3, -2, -1, 1, 2, 3]);
        const fftLength = 6;
        expectArraysClose(await tf.spectral.rfft(t1Real, fftLength).data(), [
            -5.8859587e-07, 1.1920929e-07, -3.9999995, 6.9282026e+00, -2.9999998,
            1.7320497, -4.0000000, -2.3841858e-07
        ]);
    });
    it('should return the value with cropped input', async () => {
        const t1Real = tf.tensor1d([-3, -2, -1, 1, 2, 3]);
        const fftLength = 3;
        expectArraysClose(await tf.spectral.rfft(t1Real, fftLength).data(), [-6, 0.0, -1.5000002, 0.866]);
    });
    it('should return the value with padded input', async () => {
        const t1Real = tf.tensor1d([-3, -2, -1]);
        const fftLength = 4;
        expectArraysClose(await tf.spectral.rfft(t1Real, fftLength).data(), [-6, 0, -2, 2, -2, 0]);
    });
});
describeWithFlags('2D RFFT', ALL_ENVS, () => {
    it('should return the same value with TensorFlow (2x2 elements)', async () => {
        const t1Real = tf.tensor2d([1, 2, 3, 4], [2, 2]);
        expectArraysClose(await tf.spectral.rfft(t1Real).data(), [3, 0, -1, 0, 7, 0, -1, 0]);
    });
    it('should return the same value with TensorFlow (2x3 elements)', async () => {
        const t1Real = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        expectArraysClose(await tf.spectral.rfft(t1Real).data(), [
            6, 1.1920929e-07, -1.4999999, 8.6602521e-01, 15, -5.9604645e-08,
            -1.4999998, 8.6602545e-01
        ]);
    });
    it('should return the same value with TensorFlow (2x2x2 elements)', async () => {
        const t1Real = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8], [2, 2, 2]);
        expectArraysClose(await tf.spectral.rfft(t1Real).data(), [3, 0, -1, 0, 7, 0, -1, 0, 11, 0, -1, 0, 15, 0, -1, 0]);
    });
    it('should return the value with cropping', async () => {
        const t1Real = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const fftLength = 2;
        expectArraysClose(await tf.spectral.rfft(t1Real, fftLength).data(), [3, 0, -1, 0, 9, 0, -1, 0]);
    });
    it('should return the value with padding', async () => {
        const t1Real = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const fftLength = 4;
        expectArraysClose(await tf.spectral.rfft(t1Real, fftLength).data(), [6, 0, -2, -2, 2, 0, 15, 0, -2, -5, 5, 0]);
    });
});
//# sourceMappingURL=data:application/json;base64,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