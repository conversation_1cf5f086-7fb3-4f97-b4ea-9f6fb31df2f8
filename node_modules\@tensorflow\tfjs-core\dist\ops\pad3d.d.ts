/// <amd-module name="@tensorflow/tfjs-core/dist/ops/pad3d" />
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { Tensor3D } from '../tensor';
import { TensorLike } from '../types';
/**
 * Pads a `tf.Tensor3D` with a given value and paddings. See `pad` for details.
 */
declare function pad3d_(x: Tensor3D | TensorLike, paddings: [[number, number], [number, number], [number, number]], constantValue?: number): Tensor3D;
export declare const pad3d: typeof pad3d_;
export {};
