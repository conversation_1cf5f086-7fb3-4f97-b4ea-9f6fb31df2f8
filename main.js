/**
 * التطبيق الرئيسي لنظام التداول الذكي المتقدم
 * Main Application for Advanced Smart Trading System
 */

const QuotexTradingLibrary = require('./quotexLibrary');
const WebInterface = require('./src/webInterface');

class TradingSystem {
    constructor() {
        this.quotexLibrary = null;
        this.webInterface = null;
        this.isRunning = false;
        
        // إعدادات النظام
        this.config = {
            // إعدادات Quotex
            quotex: {
                headless: false, // دائماً مرئي للتسجيل اليدوي
                userDataDir: process.env.QUOTEX_USER_DATA_DIR || './user_data'
            },

            // إعدادات تخزين البيانات
            dataStorage: {
                dataDir: './data',
                historicalDir: './data/historical',
                liveDir: './data/live',
                analysisDir: './data/analysis',
                maxFileSize: 50 * 1024 * 1024, // 50MB
                compressionEnabled: true,
                backupEnabled: true
            },

            // إعدادات الاستراتيجية الهجينة
            hybridStrategy: {
                technical: {
                    minimum_signals: 2,
                    rsi_period: 5,
                    ema_periods: [5, 10, 21]
                },
                quantitative: {
                    zscore_threshold: 2.0,
                    probability_threshold: 0.7
                },
                behavioral: {
                    pattern_strength_min: 'medium'
                },
                ai: {
                    confidence_threshold: 0.8
                },
                decision: {
                    minimum_confidence: 0.8
                }
            },
            
            // إعدادات التداول الذكي
            smartTrading: {
                autoTrade: false,
                minConfidence: 0.7,
                maxTradesPerDay: 10,
                tradingHours: { start: 8, end: 18 },
                enabledStrategies: ['rsi', 'macd', 'bollinger', 'candlestick'],
                riskSettings: {
                    maxRiskPerTrade: 0.02,
                    maxDailyLoss: 0.05,
                    maxConsecutiveLosses: 3,
                    minWinRate: 0.6,
                    maxDrawdown: 0.10
                }
            },
            
            // إعدادات مدير البيانات المباشرة
            liveDataManager: {
                maxCandlesPerInstrument: 1000,
                updateInterval: 1000,
                enableCompression: true,
                enableCaching: true
            },
            
            // إعدادات الواجهة الويب
            webInterface: {
                port: process.env.PORT || 3000,
                host: process.env.HOST || 'localhost',
                enableAuth: false
            },

            // إعدادات محسن الأداء
            performanceOptimizer: {
                memoryThreshold: 100 * 1024 * 1024, // 100MB
                cpuThreshold: 80,
                latencyThreshold: 1000,
                cleanupInterval: 60000,
                optimizationInterval: 300000
            }
        };
    }

    /**
     * تهيئة النظام
     */
    async initialize() {
        try {
            console.log('🚀 Initializing Advanced Hybrid Trading System...');

            // تهيئة مكتبة Quotex المتكاملة
            console.log('📡 Initializing Quotex Trading Library...');
            this.quotexLibrary = new QuotexTradingLibrary({
                headless: this.config.quotex.headless,
                userDataDir: this.config.quotex.userDataDir,
                enableLiveStreaming: true,
                historicalCandles: 500,
                maxConcurrentTrades: 5,
                defaultTradeAmount: 10
            });

            // تهيئة المكتبة
            await this.quotexLibrary.initialize();

            // تهيئة الواجهة الويب
            console.log('🌐 Initializing Web Interface...');
            this.webInterface = new WebInterface(
                this.quotexLibrary.connector,
                this.quotexLibrary, // استخدام المكتبة كاملة
                this.quotexLibrary.liveDataStreamer, // liveDataManager
                this.config.webInterface
            );

            // إعداد معالجات الأحداث
            this.setupEventHandlers();

            console.log('✅ System initialization completed successfully');
            return true;

        } catch (error) {
            console.error('❌ System initialization failed:', error);
            throw error;
        }
    }

    /**
     * إعداد معالجات الأحداث
     */
    setupEventHandlers() {
        // معالجات مكتبة Quotex
        this.quotexLibrary.connector.on('connected', () => {
            console.log('🔐 Quotex platform connected successfully');
            this.startTradingOperations();
        });

        this.quotexLibrary.connector.on('authenticated', () => {
            console.log('✅ Quotex authentication successful');
        });

        this.quotexLibrary.connector.on('disconnected', () => {
            console.log('🔌 Quotex platform disconnected');
            this.handleQuotexDisconnection();
        });

        this.quotexLibrary.connector.on('error', (error) => {
            console.error('❌ Quotex error:', error.message);
            this.handleQuotexError(error);
        });

        // معالجات إضافية للبيانات
        this.quotexLibrary.connector.on('priceUpdate', (data) => {
            if (this.webInterface) {
                this.webInterface.broadcast('priceUpdate', data);
            }
        });

        this.quotexLibrary.connector.on('candleUpdate', (data) => {
            if (this.webInterface) {
                this.webInterface.broadcast('candleUpdate', data);
            }
        });

        this.quotexLibrary.connector.on('tradeResult', (data) => {
            if (this.webInterface) {
                this.webInterface.broadcast('tradeResult', data);
            }
        });



        // معالجة إشارات النظام
        process.on('SIGINT', () => {
            console.log('\n🛑 Received SIGINT, shutting down gracefully...');
            this.shutdown();
        });

        process.on('SIGTERM', () => {
            console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
            this.shutdown();
        });

        process.on('uncaughtException', (error) => {
            console.error('❌ Uncaught Exception:', error);
            this.shutdown();
        });

        process.on('unhandledRejection', (reason, promise) => {
            console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
        });
    }

    /**
     * بدء النظام
     */
    async start() {
        try {
            console.log('🚀 Starting Advanced Hybrid Trading System...');

            // تهيئة النظام
            await this.initialize();

            // الاتصال بمنصة Quotex
            console.log('📡 Connecting to Quotex platform...');
            console.log('👤 Browser will open for manual login...');

            // استخدام الاتصال اليدوي
            await this.quotexLibrary.connect();

            // بدء الواجهة الويب
            console.log('🌐 Starting Web Interface...');
            await this.webInterface.start();

            this.isRunning = true;

            console.log('✅ System started successfully!');
            console.log('🌐 Web interface available at: http://localhost:3000');

            return true;

        } catch (error) {
            console.error('❌ Failed to start system:', error);
            throw error;
        }
    }

    /**
     * بدء عمليات التداول
     */
    async startTradingOperations() {
        try {
            console.log('📈 Starting hybrid trading operations...');

            // الحصول على قائمة الأدوات
            const instruments = this.quotexLibrary.getTargetPairs();
            console.log(`📋 Found ${instruments.length} available instruments`);

            // بدء التحليل
            await this.quotexLibrary.startAnalysis();

            console.log('✅ Hybrid trading operations started successfully');

        } catch (error) {
            console.error('❌ Failed to start trading operations:', error);
        }
    }

    /**
     * إيقاف النظام
     */
    async shutdown() {
        try {
            console.log('🛑 Shutting down hybrid trading system...');

            this.isRunning = false;

            // إيقاف مكتبة Quotex
            if (this.quotexLibrary) {
                await this.quotexLibrary.disconnect();
            }

            // إيقاف الواجهة الويب
            if (this.webInterface) {
                await this.webInterface.stop();
            }

            console.log('✅ System shutdown completed');
            process.exit(0);

        } catch (error) {
            console.error('❌ Error during shutdown:', error);
            process.exit(1);
        }
    }

    /**
     * معالجة انقطاع Quotex
     */
    handleQuotexDisconnection() {
        console.log('🔄 Attempting to reconnect to Quotex...');
        this.isConnected = false;
        // سيتم إعادة الاتصال تلقائياً بواسطة QuotexConnector
    }

    /**
     * معالجة خطأ Quotex
     */
    handleQuotexError(error) {
        console.error('❌ Quotex Error:', error.message);

        // إشعار الواجهة الويب
        if (this.webInterface) {
            this.webInterface.broadcast('quotexError', {
                error: error.message,
                timestamp: new Date()
            });
        }
    }

    /**
     * الحصول على حالة النظام
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            isConnected: this.isConnected,
            libraryStatus: this.quotexLibrary ? this.quotexLibrary.getLibraryStatus() : null
        };
    }


}

module.exports = TradingSystem;
