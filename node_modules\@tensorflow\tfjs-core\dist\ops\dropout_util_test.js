/**
 * @license
 * Copyright 2019 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../index';
import { ALL_ENVS, describeWithFlags } from '../jasmine_util';
import { getNoiseShape } from './dropout_util';
describeWithFlags('getNoiseShape', ALL_ENVS, () => {
    it('x.shape == noiseShape', async () => {
        const x = tf.ones([2, 3]);
        const noiseShape = [2, 3];
        const shape = getNoiseShape(x, noiseShape);
        expect(shape).toEqual([2, 3]);
    });
    it('x.shape and noiseShape have same length, different value', async () => {
        const x = tf.ones([2, 3]);
        const noiseShape = [2, 1];
        const shape = getNoiseShape(x, noiseShape);
        expect(shape).toEqual([2, 1]);
    });
    it('noiseShape has null value', async () => {
        const x = tf.ones([2, 3]);
        const noiseShape = [2, null];
        const shape = getNoiseShape(x, noiseShape);
        expect(shape).toEqual([2, 3]);
    });
    it('x.shape and noiseShape has different length', async () => {
        const x = tf.ones([2, 3, 4]);
        const noiseShape = [2, 3];
        const shape = getNoiseShape(x, noiseShape);
        expect(shape).toEqual([2, 3]);
    });
    it('noiseShape is null', async () => {
        const x = tf.ones([2, 3]);
        const shape = getNoiseShape(x, null);
        expect(shape).toEqual([2, 3]);
    });
});
//# sourceMappingURL=data:application/json;base64,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