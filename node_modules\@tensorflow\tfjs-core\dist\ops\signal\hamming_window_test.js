/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../../index';
import { ALL_ENVS, describeWithFlags } from '../../jasmine_util';
import { expectArraysClose } from '../../test_util';
describeWithFlags('hammingWindow', ALL_ENVS, () => {
    it('length=3', async () => {
        const ret = tf.signal.hammingWindow(3);
        expectArraysClose(await ret.data(), [0.08, 1, 0.08]);
    });
    it('length=6', async () => {
        const ret = tf.signal.hammingWindow(6);
        expectArraysClose(await ret.data(), [0.08, 0.31, 0.77, 1., 0.77, 0.31]);
    });
    it('length=7', async () => {
        const ret = tf.signal.hammingWindow(7);
        expectArraysClose(await ret.data(), [0.08, 0.31, 0.77, 1, 0.77, 0.31, 0.08]);
    });
    it('length=20', async () => {
        const ret = tf.signal.hammingWindow(20);
        expectArraysClose(await ret.data(), [
            0.08000001, 0.10251403, 0.16785222, 0.2696188, 0.3978522,
            0.54, 0.68214786, 0.8103813, 0.9121479, 0.977486,
            1., 0.977486, 0.9121478, 0.8103812, 0.6821477,
            0.54, 0.39785212, 0.2696187, 0.16785222, 0.102514
        ]);
    });
});
//# sourceMappingURL=data:application/json;base64,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