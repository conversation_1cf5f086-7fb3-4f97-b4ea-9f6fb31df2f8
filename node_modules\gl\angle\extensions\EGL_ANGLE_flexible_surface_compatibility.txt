Name

    ANGLE_flexible_surface_compatibility

Name Strings

    EGL_ANGLE_flexible_surface_compatibility

Contributors

    <PERSON>   (jbauman 'at' google.com)
    <PERSON> (shannon<PERSON> 'at' google.com)

Contact

    <PERSON>   (jbauman 'at' google.com)

Status

    Draft

Version

    Version 3, Dec 15, 2015

Number

    EGL Extension #XXX

Extension Type

    EGL display extension

Dependencies

    This extension is written against the language of EGL 1.5.

Overview

    Some EGL implementations may allow any surface to be made current with any
    context from the same display, without restrictions due to bit depth.

IP Status

    No known claims.

New Types

    None.

New Procedures and Functions

    None.

New Tokens

    Accepted by the <attribute> parameter of eglQuerySurface and by the
    <attrib_list> parameter of eglCreateWindowSurface,
    eglCreatePlatformWindowSurface, eglCreatePbufferSurface, and
    eglCreatePbufferFromClientBuffer:

    EGL_FLEXIBLE_SURFACE_COMPATIBILITY_SUPPORTED_ANGLE  0x33A6

Changes to Chapter 2 of the EGL 1.5 Specification (EGL Operation)

    Replace the first paragraph of the first bulleted list item in section
    2.2, page 4
    (Rendering Contexts and Drawing Surfaces)

    "They support the same type of color buffer (RGB or luminance), or the
    surface has the EGL_FLEXIBLE_SURFACE_COMPATIBILITY_SUPPORTED_ANGLE
    attribute set to EGL_TRUE."

    Replace the first paragraph of the second bulleted list item in section
    2.2, page 4
    (Rendering Contexts and Drawing Surfaces)

    "They have color buffers and ancillary buffers of the same depth, or the
    surface has the EGL_FLEXIBLE_SURFACE_COMPATIBILITY_SUPPORTED_ANGLE
    attribute set to EGL_TRUE."

Changes to Chapter 3 of the EGL 1.5 Specification (EGL Functions and Errors)

    Modify the fourth paragraph of Section 3.5.1, page 32
    (Creating On-Screen rendering Surfaces)

    "<attrib_list> specifies a list of attributes for the window. The list has
    the same structure as described for eglChooseConfig. Attributes that can
    be specified in <attrib_list> include
    EGL_FLEXIBLE_SURFACE_COMPATIBILITY_SUPPORTED_ANGLE, EGL_GL_COLORSPACE,
    EGL_RENDER_BUFFER, EGL_VG_COLORSPACE, and EGL_VG_ALPHA_FORMAT."

    Add the following between paragraphs 6 and 7 of Section 3.5.1, page 32
    (Creating On-Screen Rendering Surfaces)

    "EGL_FLEXIBLE_SURFACE_COMPATIBILITY_SUPPORTED_ANGLE specifies whether the
    surface can be made current with a context with a config of different bit
    depth. Its values can be EGL_TRUE, in which case that is supported, or
    EGL_FALSE, in which case that is not supported."

    Modify the fourth paragraph of Section 3.5.2, page 35
    (Creating Off-Screen Rendering Surfaces)

    "<attrib_list> specifies a list of attributes for the pbuffer. The list
    has the same structure as described for eglChooseConfig. Attributes that
    can be specified in <attrib_list> include EGL_WIDTH, EGL_HEIGHT,
    EGL_LARGEST_PBUFFER, EGL_TEXTURE_FORMAT, EGL_TEXTURE_TARGET,
    EGL_MIPMAP_TEXTURE, EGL_GL_COLORSPACE, EGL_VG_COLORSPACE,
    EGL_FLEXIBLE_SURFACE_COMPATIBILITY_SUPPORTED_ANGLE, and
    EGL_VG_ALPHA_FORMAT."

    Modify paragraph twelve of Section 3.5.2, page 36

    "EGL_GL_COLORSPACE, EGL_VG_COLORSPACE,
    EGL_FLEXIBLE_SURFACE_COMPATIBILITY_SUPPORTED_ANGLE, and
    EGL_VG_ALPHA_FORMAT have the same meaning and default values as when used
    with eglCreatePlatformWindowSurface."

    Modify the fifth paragraph of Section 3.5.3, page 37
    (Binding Off-Screen Rendering Surfaces To Client Buffers)

    "<attrib_list> specifies a list of attributes for the pbuffer. The list
    has the same structure as described for eglChooseConfig. Attributes that
    can be specified in <attrib_list> include EGL_TEXTURE_FORMAT,
    EGL_TEXTURE_TARGET, EGL_- MIPMAP_TEXTURE, and
    EGL_FLEXIBLE_SURFACE_COMPATIBILITY_SUPPORTED_ANGLE. The meaning of these
    attributes is as described above for eglCreatePbufferSurface. The
    EGL_VG_COLORSPACE and EGL_VG_ALPHA_FORMAT attributes of the surface are
    determined by the VGImageFormat of buffer."

    Add the following entry to Table 3.5, page 44 (Queryable surface
    attributes and types)

    Attribute                                          Type    Description
    -------------------------------------------------- ------- ------------------------
    EGL_FLEXIBLE_SURFACE_COMPATIBILITY_SUPPORTED_ANGLE boolean Surface can be made
                                                               current with contexts
                                                               of a different bit depth

Issues

    None

Revision History

    Version 3, Dec 15, 2015 (John Bauman)
        - Modify EGL_FLEXIBLE_SURFACE_COMPATIBILITY_SUPPORTED_ANGLE value.

    Version 2, Dec 1, 2015 (John Bauman)
        - Add EGL_FLEXIBLE_SURFACE_COMPATIBILITY_SUPPORTED_ANGLE to specify
          new behavior.

    Version 1, Nov 24, 2015 (John Bauman)
        - Initial Draft
