/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../../index';
import { ALL_ENVS, describeWithFlags } from '../../jasmine_util';
import { expectArraysClose } from '../../test_util';
describeWithFlags('frame', ALL_ENVS, () => {
    it('3 length frames', async () => {
        const input = tf.tensor1d([1, 2, 3, 4, 5]);
        const frameLength = 3;
        const frameStep = 1;
        const output = tf.signal.frame(input, frameLength, frameStep);
        expect(output.shape).toEqual([3, 3]);
        expectArraysClose(await output.data(), [1, 2, 3, 2, 3, 4, 3, 4, 5]);
    });
    it('3 length frames with step 2', async () => {
        const input = tf.tensor1d([1, 2, 3, 4, 5]);
        const frameLength = 3;
        const frameStep = 2;
        const output = tf.signal.frame(input, frameLength, frameStep);
        expect(output.shape).toEqual([2, 3]);
        expectArraysClose(await output.data(), [1, 2, 3, 3, 4, 5]);
    });
    it('3 length frames with step 5', async () => {
        const input = tf.tensor1d([1, 2, 3, 4, 5]);
        const frameLength = 3;
        const frameStep = 5;
        const output = tf.signal.frame(input, frameLength, frameStep);
        expect(output.shape).toEqual([1, 3]);
        expectArraysClose(await output.data(), [1, 2, 3]);
    });
    it('Exceeding frame length', async () => {
        const input = tf.tensor1d([1, 2, 3, 4, 5]);
        const frameLength = 6;
        const frameStep = 1;
        const output = tf.signal.frame(input, frameLength, frameStep);
        expect(output.shape).toEqual([0, 6]);
        expectArraysClose(await output.data(), []);
    });
    it('Zero frame step', async () => {
        const input = tf.tensor1d([1, 2, 3, 4, 5]);
        const frameLength = 6;
        const frameStep = 0;
        const output = tf.signal.frame(input, frameLength, frameStep);
        expect(output.shape).toEqual([0, 6]);
        expectArraysClose(await output.data(), []);
    });
    it('Padding with default value', async () => {
        const input = tf.tensor1d([1, 2, 3, 4, 5]);
        const frameLength = 3;
        const frameStep = 3;
        const padEnd = true;
        const output = tf.signal.frame(input, frameLength, frameStep, padEnd);
        expect(output.shape).toEqual([2, 3]);
        expectArraysClose(await output.data(), [1, 2, 3, 4, 5, 0]);
    });
    it('Padding with the given value', async () => {
        const input = tf.tensor1d([1, 2, 3, 4, 5]);
        const frameLength = 3;
        const frameStep = 3;
        const padEnd = true;
        const padValue = 100;
        const output = tf.signal.frame(input, frameLength, frameStep, padEnd, padValue);
        expect(output.shape).toEqual([2, 3]);
        expectArraysClose(await output.data(), [1, 2, 3, 4, 5, 100]);
    });
    it('Padding all remaining frames with step=1', async () => {
        const input = tf.tensor1d([1, 2, 3, 4, 5]);
        const frameLength = 4;
        const frameStep = 1;
        const padEnd = true;
        const output = tf.signal.frame(input, frameLength, frameStep, padEnd);
        expect(output.shape).toEqual([5, 4]);
        expectArraysClose(await output.data(), [1, 2, 3, 4, 2, 3, 4, 5, 3, 4, 5, 0, 4, 5, 0, 0, 5, 0, 0, 0]);
    });
    it('Padding all remaining frames with step=1 and given pad-value', async () => {
        const input = tf.tensor1d([1, 2, 3, 4, 5]);
        const frameLength = 4;
        const frameStep = 1;
        const padEnd = true;
        const padValue = 42;
        const output = tf.signal.frame(input, frameLength, frameStep, padEnd, padValue);
        expect(output.shape).toEqual([5, 4]);
        expectArraysClose(await output.data(), [1, 2, 3, 4, 2, 3, 4, 5, 3, 4, 5, 42, 4, 5, 42, 42, 5, 42, 42, 42]);
    });
    it('Padding all remaining frames with step=2', async () => {
        const input = tf.tensor1d([1, 2, 3, 4, 5]);
        const output = tf.signal.frame(input, 4, 2, true);
        expect(output.shape).toEqual([3, 4]);
        expectArraysClose(await output.data(), [1, 2, 3, 4, 3, 4, 5, 0, 5, 0, 0, 0]);
    });
});
//# sourceMappingURL=data:application/json;base64,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