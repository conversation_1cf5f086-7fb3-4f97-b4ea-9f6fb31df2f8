/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../../index';
import { ALL_ENVS, describeWithFlags } from '../../jasmine_util';
import { expectArraysClose } from '../../test_util';
describeWithFlags('resizeNearestNeighbor', ALL_ENVS, () => {
    it('simple alignCorners=false', async () => {
        const input = tf.tensor3d([2, 2, 4, 4], [2, 2, 1]);
        const output = input.resizeNearestNeighbor([3, 3], false);
        expectArraysClose(await output.data(), [2, 2, 2, 2, 2, 2, 4, 4, 4]);
    });
    it('simple alignCorners=true', async () => {
        const input = tf.tensor3d([2, 2, 4, 4], [2, 2, 1]);
        const output = input.resizeNearestNeighbor([3, 3], true);
        expectArraysClose(await output.data(), [2, 2, 2, 4, 4, 4, 4, 4, 4]);
    });
    it('5x2To2x2 alignCorners=false halfPixelCenters=true', async () => {
        const input = tf.tensor4d([1, 2, 3, 4, 5, 1, 2, 3, 4, 5], [1, 2, 5, 1]);
        const output = input.resizeNearestNeighbor([2, 2], false, true);
        expectArraysClose(await output.data(), [2, 4, 2, 4]);
    });
    it('2x2To1x1 alignCorners=false halfPixelCenters=true', async () => {
        const input = tf.tensor4d([1, 2, 3, 4], [1, 2, 2, 1]);
        const output = input.resizeNearestNeighbor([1, 1], false, true);
        expectArraysClose(await output.data(), [4]);
    });
    it('2x2To3x3 alignCorners=false halfPixelCenters=true', async () => {
        const input = tf.tensor4d([1, 2, 3, 4], [1, 2, 2, 1]);
        const output = input.resizeNearestNeighbor([3, 3], false, true);
        expectArraysClose(await output.data(), [1, 2, 2, 3, 4, 4, 3, 4, 4]);
    });
    it('3x3To2x2 alignCorners=false halfPixelCenters=true', async () => {
        const input = tf.tensor4d([1, 2, 3, 4, 5, 6, 7, 8, 9], [1, 3, 3, 1]);
        const output = input.resizeNearestNeighbor([2, 2], false, true);
        expectArraysClose(await output.data(), [1, 3, 7, 9]);
    });
    it('2x2To2x5 alignCorners=false halfPixelCenters=true', async () => {
        const input = tf.tensor4d([1, 2, 3, 4], [1, 2, 2, 1]);
        const output = input.resizeNearestNeighbor([2, 5], false, true);
        expectArraysClose(await output.data(), [1, 1, 2, 2, 2, 3, 3, 4, 4, 4]);
    });
    it('4x4To3x3 alignCorners=false halfPixelCenters=true', async () => {
        const input = tf.tensor4d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16], [1, 4, 4, 1]);
        const output = input.resizeNearestNeighbor([3, 3], false, true);
        expectArraysClose(await output.data(), [1, 3, 4, 9, 11, 12, 13, 15, 16]);
    });
    it('2x2To5x2 alignCorners=false halfPixelCenters=true', async () => {
        const input = tf.tensor4d([1, 2, 3, 4], [1, 2, 2, 1]);
        const output = input.resizeNearestNeighbor([5, 2], false, true);
        expectArraysClose(await output.data(), [1, 2, 1, 2, 3, 4, 3, 4, 3, 4]);
    });
    it('2x2To4x4 alignCorners=false halfPixelCenters=true', async () => {
        const input = tf.tensor4d([1, 2, 3, 4], [1, 2, 2, 1]);
        const output = input.resizeNearestNeighbor([4, 4], false, true);
        expectArraysClose(await output.data(), [1, 1, 2, 2, 1, 1, 2, 2, 3, 3, 4, 4, 3, 3, 4, 4]);
    });
    it('matches tensorflow w/ random numbers alignCorners=false', async () => {
        const input = tf.tensor3d([
            1.19074044, 0.91373104, 2.01611669, -0.52270832, 0.38725395,
            1.30809779, 0.61835143, 3.49600659, 2.09230986, 0.56473997,
            0.03823943, 1.19864896
        ], [2, 3, 2]);
        const output = input.resizeNearestNeighbor([4, 5], false);
        expectArraysClose(await output.data(), [
            1.19074047, 0.913731039, 1.19074047, 0.913731039, 2.01611662,
            -0.522708297, 2.01611662, -0.522708297, 0.38725394, 1.30809784,
            1.19074047, 0.913731039, 1.19074047, 0.913731039, 2.01611662,
            -0.522708297, 2.01611662, -0.522708297, 0.38725394, 1.30809784,
            0.61835146, 3.49600649, 0.61835146, 3.49600649, 2.09230995,
            0.564739943, 2.09230995, 0.564739943, 0.0382394306, 1.19864893,
            0.61835146, 3.49600649, 0.61835146, 3.49600649, 2.09230995,
            0.564739943, 2.09230995, 0.564739943, 0.0382394306, 1.19864893
        ]);
    });
    it('matches tensorflow w/ random numbers alignCorners=true', async () => {
        const input = tf.tensor3d([
            1.19074044, 0.91373104, 2.01611669, -0.52270832, 0.38725395,
            1.30809779, 0.61835143, 3.49600659, 2.09230986, 0.56473997,
            0.03823943, 1.19864896
        ], [2, 3, 2]);
        const output = input.resizeNearestNeighbor([4, 5], true);
        expectArraysClose(await output.data(), [
            1.19074044, 0.91373104, 2.01611669, -0.52270832, 2.01611669, -0.52270832,
            0.38725395, 1.30809779, 0.38725395, 1.30809779, 1.19074044, 0.91373104,
            2.01611669, -0.52270832, 2.01611669, -0.52270832, 0.38725395, 1.30809779,
            0.38725395, 1.30809779, 0.61835143, 3.49600659, 2.09230986, 0.56473997,
            2.09230986, 0.56473997, 0.03823943, 1.19864896, 0.03823943, 1.19864896,
            0.61835143, 3.49600659, 2.09230986, 0.56473997, 2.09230986, 0.56473997,
            0.03823943, 1.19864896, 0.03823943, 1.19864896
        ]);
    });
    it('batch of 2, simple, alignCorners=true', async () => {
        const input = tf.tensor4d([2, 2, 4, 4, 3, 3, 5, 5], [2, 2, 2, 1]);
        const output = input.resizeNearestNeighbor([3, 3], true /* alignCorners */);
        expectArraysClose(await output.data(), [2, 2, 2, 4, 4, 4, 4, 4, 4, 3, 3, 3, 5, 5, 5, 5, 5, 5]);
    });
    it('throws when passed a non-tensor', () => {
        const e = /Argument 'images' passed to 'resizeNearestNeighbor' must be a Tensor/;
        expect(() => tf.image.resizeNearestNeighbor({}, [
            1, 1
        ])).toThrowError(e);
    });
    it('accepts a tensor-like object', async () => {
        const input = [[[2], [2]], [[4], [4]]]; // 2x2x1
        const output = tf.image.resizeNearestNeighbor(input, [3, 3], false);
        expectArraysClose(await output.data(), [2, 2, 2, 2, 2, 2, 4, 4, 4]);
    });
    it('does not throw when some output dim is 1 and alignCorners=true', () => {
        const input = tf.tensor3d([2, 2, 4, 4], [2, 2, 1]);
        expect(() => input.resizeNearestNeighbor([1, 3], true)).not.toThrow();
    });
});
describeWithFlags('resizeNearestNeighbor gradients', ALL_ENVS, () => {
    it('greyscale: upscale, same aspect ratio', async () => {
        const input = tf.tensor3d([[[100.0], [50.0]], [[60.0], [20.0]]]);
        const dy = tf.tensor3d([
            [[1.0], [2.0], [3.0], [4.0]], [[5.0], [6.0], [7.0], [8.0]],
            [[9.0], [10.0], [11.0], [12.0]], [[13.0], [14.0], [15.0], [16.0]]
        ]);
        const size = [4, 4];
        const alignCorners = false;
        const g = tf.grad((i) => tf.image.resizeNearestNeighbor(i, size, alignCorners));
        const output = g(input, dy);
        const expected = tf.tensor3d([[[14.0], [22.0]], [[46.0], [54.0]]]);
        expectArraysClose(await output.data(), await expected.data());
        expect(output.shape).toEqual(expected.shape);
        expect(output.dtype).toBe(expected.dtype);
    });
    it('with clones, greyscale: upscale, same aspect ratio', async () => {
        const input = tf.tensor3d([[[100.0], [50.0]], [[60.0], [20.0]]]);
        const dy = tf.tensor3d([
            [[1.0], [2.0], [3.0], [4.0]], [[5.0], [6.0], [7.0], [8.0]],
            [[9.0], [10.0], [11.0], [12.0]], [[13.0], [14.0], [15.0], [16.0]]
        ]);
        const size = [4, 4];
        const alignCorners = false;
        const g = tf.grad((i) => tf.image.resizeNearestNeighbor(i.clone(), size, alignCorners)
            .clone());
        const output = g(input, dy);
        const expected = tf.tensor3d([[[14.0], [22.0]], [[46.0], [54.0]]]);
        expectArraysClose(await output.data(), await expected.data());
        expect(output.shape).toEqual(expected.shape);
        expect(output.dtype).toBe(expected.dtype);
    });
    it('greyscale: upscale, same aspect ratio, align corners', async () => {
        const input = tf.tensor3d([[[100.0], [50.0]], [[60.0], [20.0]]]);
        const dy = tf.tensor3d([
            [[1.0], [2.0], [3.0], [4.0]], [[5.0], [6.0], [7.0], [8.0]],
            [[9.0], [10.0], [11.0], [12.0]], [[13.0], [14.0], [15.0], [16.0]]
        ]);
        const size = [4, 4];
        const alignCorners = true;
        const g = tf.grad((i) => tf.image.resizeNearestNeighbor(i, size, alignCorners));
        const output = g(input, dy);
        const expected = tf.tensor3d([[[14.0], [22.0]], [[46.0], [54.0]]]);
        expectArraysClose(await output.data(), await expected.data());
        expect(output.shape).toEqual(expected.shape);
        expect(output.dtype).toBe(expected.dtype);
    });
    it('greyscale: upscale, taller than wider', async () => {
        const input = tf.tensor3d([[[100.0], [50.0]], [[60.0], [20.0]]]);
        const dy = tf.tensor3d([
            [[1.0], [2.0], [3.0], [4.0]], [[5.0], [6.0], [7.0], [8.0]],
            [[9.0], [10.0], [11.0], [12.0]], [[13.0], [14.0], [15.0], [16.0]],
            [[17.0], [18.0], [19.0], [20.0]], [[21.0], [22.0], [23.0], [24.0]],
            [[25.0], [26.0], [27.0], [28.0]], [[29.0], [30.0], [31.0], [32.0]],
            [[33.0], [34.0], [35.0], [36.0]]
        ]);
        const size = [9, 4];
        const alignCorners = false;
        const g = tf.grad((i) => tf.image.resizeNearestNeighbor(i, size, alignCorners));
        const output = g(input, dy);
        const expected = tf.tensor3d([[[95.0], [115.0]], [[220.0], [236.0]]]);
        expectArraysClose(await output.data(), await expected.data());
        expect(output.shape).toEqual(expected.shape);
        expect(output.dtype).toBe(expected.dtype);
    });
    it('greyscale: upscale, taller than wider, align corners', async () => {
        const input = tf.tensor3d([[[100.0], [50.0]], [[60.0], [20.0]]]);
        const dy = tf.tensor3d([
            [[1.0], [2.0], [3.0], [4.0]], [[5.0], [6.0], [7.0], [8.0]],
            [[9.0], [10.0], [11.0], [12.0]], [[13.0], [14.0], [15.0], [16.0]],
            [[17.0], [18.0], [19.0], [20.0]], [[21.0], [22.0], [23.0], [24.0]],
            [[25.0], [26.0], [27.0], [28.0]], [[29.0], [30.0], [31.0], [32.0]],
            [[33.0], [34.0], [35.0], [36.0]]
        ]);
        const size = [9, 4];
        const alignCorners = true;
        const g = tf.grad((i) => tf.image.resizeNearestNeighbor(i, size, alignCorners));
        const output = g(input, dy);
        const expected = tf.tensor3d([[[60.0], [76.0]], [[255.0], [275.0]]]);
        expectArraysClose(await output.data(), await expected.data());
        expect(output.shape).toEqual(expected.shape);
        expect(output.dtype).toBe(expected.dtype);
    });
    it('greyscale: upscale, wider than taller', async () => {
        const input = tf.tensor3d([[[100.0], [50.0]], [[60.0], [20.0]]]);
        const dy = tf.tensor3d([
            [[1.0], [2.0], [3.0], [4.0], [5.0], [6.0], [7.0]],
            [[8.0], [9.0], [10.0], [11.0], [12.0], [13.0], [14.0]],
            [[15.0], [16.0], [17.0], [18.0], [19.0], [20.0], [21.0]],
            [[22.0], [23.0], [24.0], [25.0], [26.0], [27.0], [28.0]]
        ]);
        const size = [4, 7];
        const alignCorners = false;
        const g = tf.grad((i) => tf.image.resizeNearestNeighbor(i, size, alignCorners));
        const output = g(input, dy);
        const expected = tf.tensor3d([[[48.0], [57.0]], [[160.0], [141.0]]]);
        expectArraysClose(await output.data(), await expected.data());
        expect(output.shape).toEqual(expected.shape);
        expect(output.dtype).toBe(expected.dtype);
    });
    it('greyscale: upscale, wider than taller, align corners', async () => {
        const input = tf.tensor3d([[[100.0], [50.0]], [[60.0], [20.0]]]);
        const dy = tf.tensor3d([
            [[1.0], [2.0], [3.0], [4.0], [5.0], [6.0], [7.0]],
            [[8.0], [9.0], [10.0], [11.0], [12.0], [13.0], [14.0]],
            [[15.0], [16.0], [17.0], [18.0], [19.0], [20.0], [21.0]],
            [[22.0], [23.0], [24.0], [25.0], [26.0], [27.0], [28.0]]
        ]);
        const size = [4, 7];
        const alignCorners = true;
        const g = tf.grad((i) => tf.image.resizeNearestNeighbor(i, size, alignCorners));
        const output = g(input, dy);
        const expected = tf.tensor3d([[[33.0], [72.0]], [[117.0], [184.0]]]);
        expectArraysClose(await output.data(), await expected.data());
        expect(output.shape).toEqual(expected.shape);
        expect(output.dtype).toBe(expected.dtype);
    });
    //
    // Downscaling
    //
    it('greyscale: downscale, same aspect ratio', async () => {
        const input = tf.tensor3d([
            [[100.0], [50.0], [25.0], [10.0]], [[60.0], [20.0], [80.0], [20.0]],
            [[40.0], [15.0], [200.0], [203.0]], [[40.0], [10.0], [230.0], [200.0]]
        ]);
        const dy = tf.tensor3d([[[1.0], [2.0]], [[3.0], [4.0]]]);
        const size = [2, 2];
        const alignCorners = false;
        const g = tf.grad((i) => tf.image.resizeNearestNeighbor(i, size, alignCorners));
        const output = g(input, dy);
        const expected = tf.tensor3d([
            [[1.0], [0.0], [2.0], [0.0]], [[0.0], [0.0], [0.0], [0.0]],
            [[3.0], [0.0], [4.0], [0.0]], [[0.0], [0.0], [0.0], [0.0]]
        ]);
        expectArraysClose(await output.data(), await expected.data());
        expect(output.shape).toEqual(expected.shape);
        expect(output.dtype).toBe(expected.dtype);
    });
    it('greyscale: downscale, same aspect ratio, align corners', async () => {
        const input = tf.tensor3d([
            [[100.0], [50.0], [25.0], [10.0]], [[60.0], [20.0], [80.0], [20.0]],
            [[40.0], [15.0], [200.0], [203.0]], [[40.0], [10.0], [230.0], [200.0]]
        ]);
        const dy = tf.tensor3d([[[1.0], [2.0]], [[3.0], [4.0]]]);
        const size = [2, 2];
        const alignCorners = true;
        const g = tf.grad((i) => tf.image.resizeNearestNeighbor(i, size, alignCorners));
        const output = g(input, dy);
        const expected = tf.tensor3d([
            [[1.0], [0.0], [0.0], [2.0]], [[0.0], [0.0], [0.0], [0.0]],
            [[0.0], [0.0], [0.0], [0.0]], [[3.0], [0.0], [0.0], [4.0]]
        ]);
        expectArraysClose(await output.data(), await expected.data());
        expect(output.shape).toEqual(expected.shape);
        expect(output.dtype).toBe(expected.dtype);
    });
    it('greyscale: downscale, taller than wider', async () => {
        const input = tf.tensor3d([
            [[100.0], [50.0], [25.0], [10.0]], [[60.0], [20.0], [80.0], [20.0]],
            [[40.0], [15.0], [200.0], [203.0]], [[40.0], [10.0], [230.0], [200.0]]
        ]);
        const dy = tf.tensor3d([[[1.0], [2.0]], [[3.0], [4.0]], [[5.0], [6.0]]]);
        const size = [3, 2];
        const alignCorners = false;
        const g = tf.grad((i) => tf.image.resizeNearestNeighbor(i, size, alignCorners));
        const output = g(input, dy);
        const expected = tf.tensor3d([
            [[1.0], [0.0], [2.0], [0.0]], [[3.0], [0.0], [4.0], [0.0]],
            [[5.0], [0.0], [6.0], [0.0]], [[0.0], [0.0], [0.0], [0.0]]
        ]);
        expectArraysClose(await output.data(), await expected.data());
        expect(output.shape).toEqual(expected.shape);
        expect(output.dtype).toBe(expected.dtype);
    });
    it('greyscale: downscale, taller than wider, align corners', async () => {
        const input = tf.tensor3d([
            [[100.0], [50.0], [25.0], [10.0]], [[60.0], [20.0], [80.0], [20.0]],
            [[40.0], [15.0], [200.0], [203.0]], [[40.0], [10.0], [230.0], [200.0]]
        ]);
        const dy = tf.tensor3d([[[1.0], [2.0]], [[3.0], [4.0]], [[5.0], [6.0]]]);
        const size = [3, 2];
        const alignCorners = true;
        const g = tf.grad((i) => tf.image.resizeNearestNeighbor(i, size, alignCorners));
        const output = g(input, dy);
        const expected = tf.tensor3d([
            [[1.0], [0.0], [0.0], [2.0]], [[0.0], [0.0], [0.0], [0.0]],
            [[3.0], [0.0], [0.0], [4.0]], [[5.0], [0.0], [0.0], [6.0]]
        ]);
        expectArraysClose(await output.data(), await expected.data());
        expect(output.shape).toEqual(expected.shape);
        expect(output.dtype).toBe(expected.dtype);
    });
    it('greyscale: downscale, taller than wider', async () => {
        const input = tf.tensor3d([
            [[100.0], [50.0], [25.0], [10.0]], [[60.0], [20.0], [80.0], [20.0]],
            [[40.0], [15.0], [200.0], [203.0]], [[40.0], [10.0], [230.0], [200.0]]
        ]);
        const dy = tf.tensor3d([[[1.0], [2.0], [3.0]], [[4.0], [5.0], [6.0]]]);
        const size = [2, 3];
        const alignCorners = false;
        const g = tf.grad((i) => tf.image.resizeNearestNeighbor(i, size, alignCorners));
        const output = g(input, dy);
        const expected = tf.tensor3d([
            [[1.0], [2.0], [3.0], [0.0]], [[0.0], [0.0], [0.0], [0.0]],
            [[4.0], [5.0], [6.0], [0.0]], [[0.0], [0.0], [0.0], [0.0]]
        ]);
        expectArraysClose(await output.data(), await expected.data());
        expect(output.shape).toEqual(expected.shape);
        expect(output.dtype).toBe(expected.dtype);
    });
    it('greyscale: downscale, taller than wider, align corners', async () => {
        const input = tf.tensor3d([
            [[100.0], [50.0], [25.0], [10.0]], [[60.0], [20.0], [80.0], [20.0]],
            [[40.0], [15.0], [200.0], [203.0]], [[40.0], [10.0], [230.0], [200.0]]
        ]);
        const dy = tf.tensor3d([[[1.0], [2.0], [3.0]], [[4.0], [5.0], [6.0]]]);
        const size = [2, 3];
        const alignCorners = true;
        const g = tf.grad((i) => tf.image.resizeNearestNeighbor(i, size, alignCorners));
        const output = g(input, dy);
        const expected = tf.tensor3d([
            [[1.0], [0.0], [2.0], [3.0]], [[0.0], [0.0], [0.0], [0.0]],
            [[0.0], [0.0], [0.0], [0.0]], [[4.0], [0.0], [5.0], [6.0]]
        ]);
        expectArraysClose(await output.data(), await expected.data());
        expect(output.shape).toEqual(expected.shape);
        expect(output.dtype).toBe(expected.dtype);
    });
    it('greyscale: downscale, same size', async () => {
        const input = tf.tensor3d([[[100.0], [50.0]], [[60.0], [20.0]]]);
        const dy = tf.tensor3d([[[1.0], [2.0]], [[3.0], [4.0]]]);
        const size = [2, 2];
        const alignCorners = false;
        const g = tf.grad((i) => tf.image.resizeNearestNeighbor(i, size, alignCorners));
        const output = g(input, dy);
        const expected = tf.tensor3d([[[1.0], [2.0]], [[3.0], [4.0]]]);
        expectArraysClose(await output.data(), await expected.data());
        expect(output.shape).toEqual(expected.shape);
        expect(output.dtype).toBe(expected.dtype);
    });
    it('greyscale: downscale, same size, align corners', async () => {
        const input = tf.tensor3d([[[100.0], [50.0]], [[60.0], [20.0]]]);
        const dy = tf.tensor3d([[[1.0], [2.0]], [[3.0], [4.0]]]);
        const size = [2, 2];
        const alignCorners = true;
        const g = tf.grad((i) => tf.image.resizeNearestNeighbor(i, size, alignCorners));
        const output = g(input, dy);
        const expected = tf.tensor3d([[[1.0], [2.0]], [[3.0], [4.0]]]);
        expectArraysClose(await output.data(), await expected.data());
        expect(output.shape).toEqual(expected.shape);
        expect(output.dtype).toBe(expected.dtype);
    });
    //
    // 3 channel images
    //
    it('color: upscale, wider than taller', async () => {
        const input = tf.tensor3d([
            [
                [100.26818084716797, 74.61857604980469, 81.62117767333984],
                [127.86964416503906, 85.0583267211914, 102.95439147949219]
            ],
            [
                [104.3798828125, 96.70733642578125, 92.60601043701172],
                [77.63021850585938, 68.55794525146484, 96.17212677001953]
            ]
        ]);
        const dy = tf.tensor3d([
            [
                [1.0, 2.0, 3.0], [4.0, 5.0, 6.0], [7.0, 8.0, 9.0], [10.0, 11.0, 12.0],
                [13.0, 14.0, 15.0]
            ],
            [
                [16.0, 17.0, 18.0], [19.0, 20.0, 21.0], [22.0, 23.0, 24.0],
                [25.0, 26.0, 27.0], [28.0, 29.0, 30.0]
            ],
            [
                [31.0, 32.0, 33.0], [34.0, 35.0, 36.0], [37.0, 38.0, 39.0],
                [40.0, 41.0, 42.0], [43.0, 44.0, 45.0]
            ]
        ]);
        const size = [3, 5];
        const alignCorners = false;
        const g = tf.grad((i) => tf.image.resizeNearestNeighbor(i, size, alignCorners));
        const output = g(input, dy);
        const expected = tf.tensor3d([
            [[69.0, 75.0, 81.0], [76.0, 80.0, 84.0]],
            [[102.0, 105.0, 108.0], [83.0, 85.0, 87.0]]
        ]);
        expectArraysClose(await output.data(), await expected.data());
        expect(output.shape).toEqual(expected.shape);
        expect(output.dtype).toBe(expected.dtype);
    });
    it('color: upscale, wider than taller, align corners', async () => {
        const input = tf.tensor3d([
            [
                [100.26818084716797, 74.61857604980469, 81.62117767333984],
                [127.86964416503906, 85.0583267211914, 102.95439147949219]
            ],
            [
                [104.3798828125, 96.70733642578125, 92.60601043701172],
                [77.63021850585938, 68.55794525146484, 96.17212677001953]
            ]
        ]);
        const dy = tf.tensor3d([
            [
                [1.0, 2.0, 3.0], [4.0, 5.0, 6.0], [7.0, 8.0, 9.0], [10.0, 11.0, 12.0],
                [13.0, 14.0, 15.0]
            ],
            [
                [16.0, 17.0, 18.0], [19.0, 20.0, 21.0], [22.0, 23.0, 24.0],
                [25.0, 26.0, 27.0], [28.0, 29.0, 30.0]
            ],
            [
                [31.0, 32.0, 33.0], [34.0, 35.0, 36.0], [37.0, 38.0, 39.0],
                [40.0, 41.0, 42.0], [43.0, 44.0, 45.0]
            ]
        ]);
        const size = [3, 5];
        const alignCorners = true;
        const g = tf.grad((i) => tf.image.resizeNearestNeighbor(i, size, alignCorners));
        const output = g(input, dy);
        const expected = tf.tensor3d([
            [[5.0, 7.0, 9.0], [30.0, 33.0, 36.0]],
            [[100.0, 104.0, 108.0], [195.0, 201.0, 207.0]]
        ]);
        expectArraysClose(await output.data(), await expected.data());
        expect(output.shape).toEqual(expected.shape);
        expect(output.dtype).toBe(expected.dtype);
    });
    it('color: downscale, taller than wider', async () => {
        const input = tf.tensor3d([
            [
                [97.98934936523438, 77.24969482421875, 113.70111846923828],
                [111.34081268310547, 113.15758514404297, 157.90521240234375],
                [105.77980041503906, 85.75989532470703, 69.62374114990234],
                [125.94231414794922, 73.11385345458984, 87.03099822998047]
            ],
            [
                [62.25117111206055, 90.23927307128906, 119.1966552734375],
                [93.55166625976562, 95.9106674194336, 115.56237030029297],
                [102.98121643066406, 98.1983413696289, 97.55982971191406],
                [86.47753143310547, 97.04051208496094, 121.50492095947266]
            ],
            [
                [92.4140853881836, 118.45619201660156, 108.0341796875],
                [126.43061065673828, 123.28077697753906, 121.03379821777344],
                [128.6694793701172, 98.47042846679688, 114.47464752197266],
                [93.31566619873047, 95.2713623046875, 102.51188659667969]
            ],
            [
                [101.55884552001953, 83.31947326660156, 119.08016204833984],
                [128.28546142578125, 92.56212615966797, 74.85054779052734],
                [88.9786148071289, 119.43685913085938, 73.06110382080078],
                [98.17908477783203, 105.54570007324219, 93.45832061767578]
            ]
        ]);
        const dy = tf.tensor3d([[[1.0, 2.0, 3.0]], [[4.0, 5.0, 6.0]], [[7.0, 8.0, 9.0]]]);
        const size = [3, 1];
        const alignCorners = false;
        const g = tf.grad((i) => tf.image.resizeNearestNeighbor(i, size, alignCorners));
        const output = g(input, dy);
        const expected = tf.tensor3d([
            [[1.0, 2.0, 3.0], [0.0, 0.0, 0.0], [0.0, 0.0, 0.0], [0.0, 0.0, 0.0]],
            [[4.0, 5.0, 6.0], [0.0, 0.0, 0.0], [0.0, 0.0, 0.0], [0.0, 0.0, 0.0]],
            [[7.0, 8.0, 9.0], [0.0, 0.0, 0.0], [0.0, 0.0, 0.0], [0.0, 0.0, 0.0]],
            [[0.0, 0.0, 0.0], [0.0, 0.0, 0.0], [0.0, 0.0, 0.0], [0.0, 0.0, 0.0]]
        ]);
        expectArraysClose(await output.data(), await expected.data());
        expect(output.shape).toEqual(expected.shape);
        expect(output.dtype).toBe(expected.dtype);
    });
    it('color: downscale, taller than wider, align corners', async () => {
        const input = tf.tensor3d([
            [
                [97.98934936523438, 77.24969482421875, 113.70111846923828],
                [111.34081268310547, 113.15758514404297, 157.90521240234375],
                [105.77980041503906, 85.75989532470703, 69.62374114990234],
                [125.94231414794922, 73.11385345458984, 87.03099822998047]
            ],
            [
                [62.25117111206055, 90.23927307128906, 119.1966552734375],
                [93.55166625976562, 95.9106674194336, 115.56237030029297],
                [102.98121643066406, 98.1983413696289, 97.55982971191406],
                [86.47753143310547, 97.04051208496094, 121.50492095947266]
            ],
            [
                [92.4140853881836, 118.45619201660156, 108.0341796875],
                [126.43061065673828, 123.28077697753906, 121.03379821777344],
                [128.6694793701172, 98.47042846679688, 114.47464752197266],
                [93.31566619873047, 95.2713623046875, 102.51188659667969]
            ],
            [
                [101.55884552001953, 83.31947326660156, 119.08016204833984],
                [128.28546142578125, 92.56212615966797, 74.85054779052734],
                [88.9786148071289, 119.43685913085938, 73.06110382080078],
                [98.17908477783203, 105.54570007324219, 93.45832061767578]
            ]
        ]);
        const dy = tf.tensor3d([[[1.0, 2.0, 3.0]], [[4.0, 5.0, 6.0]], [[7.0, 8.0, 9.0]]]);
        const size = [3, 1];
        const alignCorners = true;
        const g = tf.grad((i) => tf.image.resizeNearestNeighbor(i, size, alignCorners));
        const output = g(input, dy);
        const expected = tf.tensor3d([
            [[1.0, 2.0, 3.0], [0.0, 0.0, 0.0], [0.0, 0.0, 0.0], [0.0, 0.0, 0.0]],
            [[0.0, 0.0, 0.0], [0.0, 0.0, 0.0], [0.0, 0.0, 0.0], [0.0, 0.0, 0.0]],
            [[4.0, 5.0, 6.0], [0.0, 0.0, 0.0], [0.0, 0.0, 0.0], [0.0, 0.0, 0.0]],
            [[7.0, 8.0, 9.0], [0.0, 0.0, 0.0], [0.0, 0.0, 0.0], [0.0, 0.0, 0.0]]
        ]);
        expectArraysClose(await output.data(), await expected.data());
        expect(output.shape).toEqual(expected.shape);
        expect(output.dtype).toBe(expected.dtype);
    });
    it('color: same size', async () => {
        const input = tf.tensor3d([
            [
                [100.26818084716797, 74.61857604980469, 81.62117767333984],
                [127.86964416503906, 85.0583267211914, 102.95439147949219]
            ],
            [
                [104.3798828125, 96.70733642578125, 92.60601043701172],
                [77.63021850585938, 68.55794525146484, 96.17212677001953]
            ]
        ]);
        const dy = tf.tensor3d([
            [[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]], [[7.0, 8.0, 9.0], [10.0, 11.0, 12.0]]
        ]);
        const size = [2, 2];
        const alignCorners = false;
        const g = tf.grad((i) => tf.image.resizeNearestNeighbor(i, size, alignCorners));
        const output = g(input, dy);
        const expected = tf.tensor3d([
            [[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]], [[7.0, 8.0, 9.0], [10.0, 11.0, 12.0]]
        ]);
        expectArraysClose(await output.data(), await expected.data());
        expect(output.shape).toEqual(expected.shape);
        expect(output.dtype).toBe(expected.dtype);
    });
});
//# sourceMappingURL=data:application/json;base64,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