/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../../index';
import { ALL_ENVS, describeWithFlags } from '../../jasmine_util';
import { expectArraysClose } from '../../test_util';
import { scalar, tensor1d, tensor2d, tensor3d } from '../ops';
describeWithFlags('bandPart', ALL_ENVS, () => {
    it('keeps tensor unchanged', async () => {
        const x = tensor2d([1, 1, 1, 1, 1, 1, 1, 1, 1], [3, 3]);
        expectArraysClose(await tf.linalg.bandPart(x, -1, -1).array(), [[1, 1, 1], [1, 1, 1], [1, 1, 1]]);
    });
    it('upper triangular matrix', async () => {
        const x = tensor2d([1, 1, 1, 1, 1, 1, 1, 1, 1], [3, 3]);
        expectArraysClose(await tf.linalg.bandPart(x, 0, -1).array(), [[1, 1, 1], [0, 1, 1], [0, 0, 1]]);
    });
    it('lower triangular matrix', async () => {
        const x = tensor2d([1, 1, 1, 1, 1, 1, 1, 1, 1], [3, 3]);
        expectArraysClose(await tf.linalg.bandPart(x, -1, 0).array(), [[1, 0, 0], [1, 1, 0], [1, 1, 1]]);
    });
    it('diagonal elements', async () => {
        const x = tensor2d([1, 1, 1, 1, 1, 1, 1, 1, 1], [3, 3]);
        expectArraysClose(await tf.linalg.bandPart(x, 0, 0).array(), [[1, 0, 0], [0, 1, 0], [0, 0, 1]]);
    });
    it('lower triangular elements', async () => {
        const x = tensor2d([1, 1, 1, 1, 1, 1, 1, 1, 1], [3, 3]);
        expectArraysClose(await tf.linalg.bandPart(x, 1, 0).array(), [[1, 0, 0], [1, 1, 0], [0, 1, 1]]);
    });
    it('upper triangular elements', async () => {
        const x = tensor2d([1, 1, 1, 1, 1, 1, 1, 1, 1], [3, 3]);
        expectArraysClose(await tf.linalg.bandPart(x, 0, 1).array(), [[1, 1, 0], [0, 1, 1], [0, 0, 1]]);
    });
    it('4X4 matrix - tensorflow python examples', async () => {
        const x = tensor2d([[0, 1, 2, 3], [-1, 0, 1, 2], [-2, -1, 0, 1], [-3, -2, -1, 0]]);
        expectArraysClose(await tf.linalg.bandPart(x, 1, -1).array(), [[0, 1, 2, 3], [-1, 0, 1, 2], [0, -1, 0, 1], [0, 0, -1, 0]]);
        expectArraysClose(await tf.linalg.bandPart(x, 2, 1).array(), [[0, 1, 0, 0], [-1, 0, 1, 0], [-2, -1, 0, 1], [0, -2, -1, 0]]);
    });
    it('3 dimensional matrix', async () => {
        const x = tensor3d([[[1, 1], [1, 1]], [[1, 1], [1, 1]]]);
        expectArraysClose(await tf.linalg.bandPart(x, 0, 0).array(), [[[1, 0], [0, 1]], [[1, 0], [0, 1]]]);
    });
    it('2X3X3 tensor', async () => {
        const x = tensor3d([[[1, 1, 1], [1, 1, 1], [1, 1, 1]], [[1, 1, 1], [1, 1, 1], [1, 1, 1]]]);
        expectArraysClose(await tf.linalg.bandPart(x, 1, 2).array(), [[[1, 1, 1], [1, 1, 1], [0, 1, 1]], [[1, 1, 1], [1, 1, 1], [0, 1, 1]]]);
    });
    const la = tf.linalg;
    it('fails for scalar', async () => {
        const x = scalar(1);
        expect(() => la.bandPart(x, 1, 2)).toThrowError(/bandPart.*rank/i);
    });
    it('fails for 1D tensor', async () => {
        const x = tensor1d([1, 2, 3, 4, 5]);
        expect(() => la.bandPart(x, 1, 2)).toThrowError(/bandPart.*rank/i);
    });
    it('fails if numLower or numUpper too large', async () => {
        const a = tf.tensor2d([[1, 2, 3], [4, 5, 6]]);
        for (const numLower of [3, 5, 8, 13]) {
            for (const numUpper of [-1, 0, 1, 2]) {
                expect(() => tf.linalg.bandPart(a, numLower, numUpper))
                    .toThrowError(/bandPart.*numLower/i);
            }
        }
        for (const numLower of [-1, 0, 1]) {
            for (const numUpper of [4, 5, 9]) {
                expect(() => tf.linalg.bandPart(a, numLower, numUpper))
                    .toThrowError(/bandPart.*numUpper/i);
            }
        }
        for (const numLower of [3, 5, 8, 13]) {
            for (const numUpper of [4, 5, 9]) {
                expect(() => tf.linalg.bandPart(a, numLower, numUpper))
                    .toThrowError(/bandPart.*(numLower|numUpper)/i);
            }
        }
    });
    it('works for 3x4 example', async () => {
        const a = tf.tensor2d([[1, 2, 3, 4], [5, 6, 7, 8], [9, 10, 11, 12]]);
        expectArraysClose(await la.bandPart(a, 0, 0).array(), [[1, 0, 0, 0], [0, 6, 0, 0], [0, 0, 11, 0]]);
        expectArraysClose(await la.bandPart(a, 0, 1).array(), [[1, 2, 0, 0], [0, 6, 7, 0], [0, 0, 11, 12]]);
        expectArraysClose(await la.bandPart(a, 0, 2).array(), [[1, 2, 3, 0], [0, 6, 7, 8], [0, 0, 11, 12]]);
        for (const numUpper of [3, 4, -1, -2]) {
            expectArraysClose(await la.bandPart(a, 0, numUpper).array(), [[1, 2, 3, 4], [0, 6, 7, 8], [0, 0, 11, 12]]);
        }
        expectArraysClose(await la.bandPart(a, 1, 0).array(), [[1, 0, 0, 0], [5, 6, 0, 0], [0, 10, 11, 0]]);
        expectArraysClose(await la.bandPart(a, 1, 1).array(), [[1, 2, 0, 0], [5, 6, 7, 0], [0, 10, 11, 12]]);
        expectArraysClose(await la.bandPart(a, 1, 2).array(), [[1, 2, 3, 0], [5, 6, 7, 8], [0, 10, 11, 12]]);
        for (const numUpper of [3, 4, -1, -2]) {
            expectArraysClose(await la.bandPart(a, 1, numUpper).array(), [[1, 2, 3, 4], [5, 6, 7, 8], [0, 10, 11, 12]]);
        }
        for (const numLower of [2, 3, -1, -2]) {
            expectArraysClose(await la.bandPart(a, numLower, 0).array(), [[1, 0, 0, 0], [5, 6, 0, 0], [9, 10, 11, 0]]);
            expectArraysClose(await la.bandPart(a, numLower, 1).array(), [[1, 2, 0, 0], [5, 6, 7, 0], [9, 10, 11, 12]]);
            expectArraysClose(await la.bandPart(a, numLower, 2).array(), [[1, 2, 3, 0], [5, 6, 7, 8], [9, 10, 11, 12]]);
            for (const numUpper of [3, 4, -1, -2]) {
                expectArraysClose(await la.bandPart(a, numLower, numUpper).array(), [[1, 2, 3, 4], [5, 6, 7, 8], [9, 10, 11, 12]]);
            }
        }
    });
    it('works for tensor numLower and tensor numUpper', async () => {
        const x = tensor2d([1, 1, 1, 1, 1, 1, 1, 1, 1], [3, 3]);
        expectArraysClose(await tf.linalg
            .bandPart(x, tf.scalar(-1, 'int32'), tf.scalar(0, 'int32'))
            .array(), [[1, 0, 0], [1, 1, 0], [1, 1, 1]]);
    });
});
//# sourceMappingURL=data:application/json;base64,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