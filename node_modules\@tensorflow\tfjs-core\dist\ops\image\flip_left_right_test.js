/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { getTestImageAsTensor4d } from '../../image_test_util';
import * as tf from '../../index';
import { ALL_ENVS, describeWithFlags } from '../../jasmine_util';
import { expectArraysClose } from '../../test_util';
describeWithFlags('flipLeftRight', ALL_ENVS, () => {
    it('should flip (1x2x2x4)', async () => {
        const image = tf.tensor([
            // batch 1
            [
                // row 1
                [
                    [1, 2, 3, 255],
                    [4, 5, 6, 255],
                ],
                // row 2
                [
                    [7, 8, 9, 255],
                    [10, 11, 12, 255],
                ],
            ],
        ]);
        const flipped = tf.image.flipLeftRight(image);
        expectArraysClose([4, 5, 6, 255, 1, 2, 3, 255, 10, 11, 12, 255, 7, 8, 9, 255], await flipped.data());
    });
    it('should flip (2x2x2x4)', async () => {
        const image = tf.tensor([
            // batch 1
            [
                // row 1
                [
                    [1, 2, 3, 255],
                    [4, 5, 6, 255],
                ],
                // row 2
                [
                    [7, 8, 9, 255],
                    [10, 11, 12, 255],
                ],
            ],
            // batch 2
            [
                // row 1
                [
                    [101, 102, 103, 255],
                    [104, 105, 106, 255],
                ],
                // row 2
                [
                    [107, 108, 109, 255],
                    [110, 111, 112, 255],
                ],
            ],
        ]);
        const flipped = tf.image.flipLeftRight(image);
        expectArraysClose([
            // batch 1
            ...[4, 5, 6, 255, 1, 2, 3, 255, 10, 11, 12, 255, 7, 8, 9, 255],
            // batch 2
            ...[104, 105, 106, 255, 101, 102, 103, 255, 110, 111, 112, 255, 107,
                108, 109, 255],
        ], await flipped.data());
    });
    it('should flip (from image)', async () => {
        const flippedPixels = tf.image.flipLeftRight(getTestImageAsTensor4d()).toInt();
        const flippedPixelsData = await flippedPixels.data();
        const expected = [
            230, 133, 18, 255, 241, 153, 43, 255, 224, 156, 55, 255, 212, 157, 75,
            255, 200, 155, 98, 255, 183, 138, 109, 255, 171, 120, 117, 255, 156, 100,
            111, 255, 233, 148, 31, 255, 250, 177, 64, 255, 241, 188, 82, 255, 230,
            193, 104, 255, 220, 190, 128, 255, 202, 174, 137, 255, 186, 152, 140, 255,
            168, 129, 130, 255, 222, 164, 41, 255, 247, 201, 81, 255, 243, 220, 106,
            255, 235, 227, 128, 255, 225, 228, 151, 255, 211, 216, 162, 255, 199, 198,
            168, 255, 179, 176, 159, 255, 191, 170, 61, 255, 218, 210, 103, 255, 213,
            230, 126, 255, 201, 236, 142, 255, 191, 239, 165, 255, 184, 234, 181, 255,
            179, 226, 194, 255, 163, 208, 187, 255, 135, 166, 86, 255, 162, 206, 127,
            255, 155, 226, 146, 255, 141, 232, 162, 255, 130, 235, 179, 255, 121, 231,
            192, 255, 119, 226, 206, 255, 108, 214, 202, 255, 71, 143, 97, 255, 98,
            181, 135, 255, 94, 206, 156, 255, 87, 220, 175, 255, 76, 225, 193, 255,
            64, 219, 201, 255, 62, 217, 213, 255, 55, 207, 212, 255, 15, 115, 105,
            255, 39, 150, 141, 255, 37, 177, 164, 255, 35, 200, 186, 255, 30, 209,
            205, 255, 19, 203, 211, 255, 19, 204, 222, 255, 18, 200, 224, 255, 0,
            102, 113, 255, 6, 133, 140, 255, 3, 158, 162, 255, 4, 182, 186, 255,
            0, 194, 204, 255, 0, 189, 209, 255, 0, 192, 221, 255, 0, 193, 228,
            255
        ];
        expectArraysClose(expected, flippedPixelsData);
    });
    it('throws when input is int32', async () => {
        const image = tf.tensor([[[[1, 2, 3, 255]], [[7, 8, 9, 255]]]], [1, 2, 1, 4], 'int32');
        expect(() => tf.image.flipLeftRight(image))
            .toThrowError(/Argument 'image' passed to 'flipLeftRight' must be float32/);
    });
});
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZmxpcF9sZWZ0X3JpZ2h0X3Rlc3QuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi8uLi90ZmpzLWNvcmUvc3JjL29wcy9pbWFnZS9mbGlwX2xlZnRfcmlnaHRfdGVzdC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7Ozs7Ozs7Ozs7O0dBZUc7QUFDSCxPQUFPLEVBQUMsc0JBQXNCLEVBQUMsTUFBTSx1QkFBdUIsQ0FBQztBQUM3RCxPQUFPLEtBQUssRUFBRSxNQUFNLGFBQWEsQ0FBQztBQUNsQyxPQUFPLEVBQUMsUUFBUSxFQUFFLGlCQUFpQixFQUFDLE1BQU0sb0JBQW9CLENBQUM7QUFDL0QsT0FBTyxFQUFDLGlCQUFpQixFQUFDLE1BQU0saUJBQWlCLENBQUM7QUFFbEQsaUJBQWlCLENBQUMsZUFBZSxFQUFFLFFBQVEsRUFBRSxHQUFHLEVBQUU7SUFDaEQsRUFBRSxDQUFDLHVCQUF1QixFQUFFLEtBQUssSUFBSSxFQUFFO1FBQ3JDLE1BQU0sS0FBSyxHQUFnQixFQUFFLENBQUMsTUFBTSxDQUFDO1lBQ25DLFVBQVU7WUFDVjtnQkFDRSxRQUFRO2dCQUNSO29CQUNFLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsR0FBRyxDQUFDO29CQUNkLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsR0FBRyxDQUFDO2lCQUNmO2dCQUNELFFBQVE7Z0JBQ1I7b0JBQ0UsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxHQUFHLENBQUM7b0JBQ2QsQ0FBQyxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxHQUFHLENBQUM7aUJBQ2xCO2FBQ0Y7U0FDRixDQUFDLENBQUM7UUFDSCxNQUFNLE9BQU8sR0FBRyxFQUFFLENBQUMsS0FBSyxDQUFDLGFBQWEsQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUM5QyxpQkFBaUIsQ0FDYixDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLEdBQUcsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxHQUFHLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsR0FBRyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxFQUMzRCxNQUFNLE9BQU8sQ0FBQyxJQUFJLEVBQUUsQ0FBQyxDQUFDO0lBQzVCLENBQUMsQ0FBQyxDQUFDO0lBRUgsRUFBRSxDQUFDLHVCQUF1QixFQUFFLEtBQUssSUFBSSxFQUFFO1FBQ3JDLE1BQU0sS0FBSyxHQUFnQixFQUFFLENBQUMsTUFBTSxDQUFDO1lBQ25DLFVBQVU7WUFDVjtnQkFDRSxRQUFRO2dCQUNSO29CQUNFLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsR0FBRyxDQUFDO29CQUNkLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsR0FBRyxDQUFDO2lCQUNmO2dCQUNELFFBQVE7Z0JBQ1I7b0JBQ0UsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxHQUFHLENBQUM7b0JBQ2QsQ0FBQyxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxHQUFHLENBQUM7aUJBQ2xCO2FBQ0Y7WUFDRCxVQUFVO1lBQ1Y7Z0JBQ0UsUUFBUTtnQkFDUjtvQkFDRSxDQUFDLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsQ0FBQztvQkFDcEIsQ0FBQyxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLENBQUM7aUJBQ3JCO2dCQUNELFFBQVE7Z0JBQ1I7b0JBQ0UsQ0FBQyxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLENBQUM7b0JBQ3BCLENBQUMsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxDQUFDO2lCQUNyQjthQUNGO1NBQ0YsQ0FBQyxDQUFDO1FBQ0gsTUFBTSxPQUFPLEdBQUcsRUFBRSxDQUFDLEtBQUssQ0FBQyxhQUFhLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDOUMsaUJBQWlCLENBQ2I7WUFDRSxVQUFVO1lBQ1YsR0FBRyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLEdBQUcsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxHQUFHLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsR0FBRyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLEdBQUcsQ0FBQztZQUM5RCxVQUFVO1lBQ1YsR0FBRyxDQUFDLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUc7Z0JBQy9ELEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxDQUFDO1NBQ25CLEVBQ0QsTUFBTSxPQUFPLENBQUMsSUFBSSxFQUFFLENBQUMsQ0FBQztJQUM1QixDQUFDLENBQUMsQ0FBQztJQUVILEVBQUUsQ0FBQywwQkFBMEIsRUFBRSxLQUFLLElBQUksRUFBRTtRQUN4QyxNQUFNLGFBQWEsR0FDZixFQUFFLENBQUMsS0FBSyxDQUFDLGFBQWEsQ0FBQyxzQkFBc0IsRUFBRSxDQUFDLENBQUMsS0FBSyxFQUFFLENBQUM7UUFDN0QsTUFBTSxpQkFBaUIsR0FBRyxNQUFNLGFBQWEsQ0FBQyxJQUFJLEVBQUUsQ0FBQztRQUVyRCxNQUFNLFFBQVEsR0FBRztZQUNmLEdBQUcsRUFBRSxHQUFHLEVBQUUsRUFBRSxFQUFHLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEVBQUUsRUFBRyxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxFQUFFLEVBQUcsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsRUFBRTtZQUN4RSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxFQUFFLEVBQUcsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUc7WUFDekUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEVBQUUsRUFBRyxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxFQUFFLEVBQUcsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsRUFBRSxFQUFHLEdBQUcsRUFBRSxHQUFHO1lBQ3pFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRztZQUN6RSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxFQUFFLEVBQUcsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsRUFBRSxFQUFHLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUc7WUFDekUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHO1lBQ3pFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsRUFBRSxFQUFHLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRztZQUN6RSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUc7WUFDekUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEVBQUUsRUFBRyxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHO1lBQ3pFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRztZQUN6RSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsRUFBRSxFQUFHLEdBQUcsRUFBRSxFQUFFLEVBQUcsR0FBRyxFQUFFLEVBQUU7WUFDeEUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsRUFBRSxFQUFHLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEVBQUUsRUFBRyxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxFQUFFLEVBQUcsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHO1lBQ3pFLEVBQUUsRUFBRyxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxFQUFFLEVBQUcsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsRUFBRSxFQUFHLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEVBQUUsRUFBRyxHQUFHLEVBQUUsR0FBRztZQUN6RSxHQUFHLEVBQUUsRUFBRSxFQUFHLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEVBQUUsRUFBRyxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxFQUFFLEVBQUcsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsRUFBRSxFQUFHLEdBQUc7WUFDekUsR0FBRyxFQUFFLEdBQUcsRUFBRSxFQUFFLEVBQUcsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsRUFBRSxFQUFHLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEVBQUUsRUFBRyxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxDQUFDO1lBQ3ZFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLENBQUMsRUFBSSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxDQUFDLEVBQUksR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsQ0FBQyxFQUFJLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRztZQUN6RSxDQUFDLEVBQUksR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsQ0FBQyxFQUFJLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLENBQUMsRUFBSSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxDQUFDLEVBQUksR0FBRyxFQUFFLEdBQUc7WUFDekUsR0FBRztTQUNKLENBQUM7UUFFRixpQkFBaUIsQ0FBQyxRQUFRLEVBQUUsaUJBQWlCLENBQUMsQ0FBQztJQUNqRCxDQUFDLENBQUMsQ0FBQztJQUVILEVBQUUsQ0FBQyw0QkFBNEIsRUFBRSxLQUFLLElBQUksRUFBRTtRQUMxQyxNQUFNLEtBQUssR0FBZ0IsRUFBRSxDQUFDLE1BQU0sQ0FDaEMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxHQUFHLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLE9BQU8sQ0FBQyxDQUFDO1FBQ25FLE1BQU0sQ0FBQyxHQUFHLEVBQUUsQ0FBQyxFQUFFLENBQUMsS0FBSyxDQUFDLGFBQWEsQ0FBQyxLQUFLLENBQUMsQ0FBQzthQUN0QyxZQUFZLENBQ1QsNERBQTRELENBQUMsQ0FBQztJQUN4RSxDQUFDLENBQUMsQ0FBQztBQUNMLENBQUMsQ0FBQyxDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IDIwMjAgR29vZ2xlIExMQy4gQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiBodHRwOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICogPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAqL1xuaW1wb3J0IHtnZXRUZXN0SW1hZ2VBc1RlbnNvcjRkfSBmcm9tICcuLi8uLi9pbWFnZV90ZXN0X3V0aWwnO1xuaW1wb3J0ICogYXMgdGYgZnJvbSAnLi4vLi4vaW5kZXgnO1xuaW1wb3J0IHtBTExfRU5WUywgZGVzY3JpYmVXaXRoRmxhZ3N9IGZyb20gJy4uLy4uL2phc21pbmVfdXRpbCc7XG5pbXBvcnQge2V4cGVjdEFycmF5c0Nsb3NlfSBmcm9tICcuLi8uLi90ZXN0X3V0aWwnO1xuXG5kZXNjcmliZVdpdGhGbGFncygnZmxpcExlZnRSaWdodCcsIEFMTF9FTlZTLCAoKSA9PiB7XG4gIGl0KCdzaG91bGQgZmxpcCAoMXgyeDJ4NCknLCBhc3luYyAoKSA9PiB7XG4gICAgY29uc3QgaW1hZ2U6IHRmLlRlbnNvcjREID0gdGYudGVuc29yKFtcbiAgICAgIC8vIGJhdGNoIDFcbiAgICAgIFtcbiAgICAgICAgLy8gcm93IDFcbiAgICAgICAgW1xuICAgICAgICAgIFsxLCAyLCAzLCAyNTVdLFxuICAgICAgICAgIFs0LCA1LCA2LCAyNTVdLFxuICAgICAgICBdLFxuICAgICAgICAvLyByb3cgMlxuICAgICAgICBbXG4gICAgICAgICAgWzcsIDgsIDksIDI1NV0sXG4gICAgICAgICAgWzEwLCAxMSwgMTIsIDI1NV0sXG4gICAgICAgIF0sXG4gICAgICBdLFxuICAgIF0pO1xuICAgIGNvbnN0IGZsaXBwZWQgPSB0Zi5pbWFnZS5mbGlwTGVmdFJpZ2h0KGltYWdlKTtcbiAgICBleHBlY3RBcnJheXNDbG9zZShcbiAgICAgICAgWzQsIDUsIDYsIDI1NSwgMSwgMiwgMywgMjU1LCAxMCwgMTEsIDEyLCAyNTUsIDcsIDgsIDksIDI1NV0sXG4gICAgICAgIGF3YWl0IGZsaXBwZWQuZGF0YSgpKTtcbiAgfSk7XG5cbiAgaXQoJ3Nob3VsZCBmbGlwICgyeDJ4Mng0KScsIGFzeW5jICgpID0+IHtcbiAgICBjb25zdCBpbWFnZTogdGYuVGVuc29yNEQgPSB0Zi50ZW5zb3IoW1xuICAgICAgLy8gYmF0Y2ggMVxuICAgICAgW1xuICAgICAgICAvLyByb3cgMVxuICAgICAgICBbXG4gICAgICAgICAgWzEsIDIsIDMsIDI1NV0sXG4gICAgICAgICAgWzQsIDUsIDYsIDI1NV0sXG4gICAgICAgIF0sXG4gICAgICAgIC8vIHJvdyAyXG4gICAgICAgIFtcbiAgICAgICAgICBbNywgOCwgOSwgMjU1XSxcbiAgICAgICAgICBbMTAsIDExLCAxMiwgMjU1XSxcbiAgICAgICAgXSxcbiAgICAgIF0sXG4gICAgICAvLyBiYXRjaCAyXG4gICAgICBbXG4gICAgICAgIC8vIHJvdyAxXG4gICAgICAgIFtcbiAgICAgICAgICBbMTAxLCAxMDIsIDEwMywgMjU1XSxcbiAgICAgICAgICBbMTA0LCAxMDUsIDEwNiwgMjU1XSxcbiAgICAgICAgXSxcbiAgICAgICAgLy8gcm93IDJcbiAgICAgICAgW1xuICAgICAgICAgIFsxMDcsIDEwOCwgMTA5LCAyNTVdLFxuICAgICAgICAgIFsxMTAsIDExMSwgMTEyLCAyNTVdLFxuICAgICAgICBdLFxuICAgICAgXSxcbiAgICBdKTtcbiAgICBjb25zdCBmbGlwcGVkID0gdGYuaW1hZ2UuZmxpcExlZnRSaWdodChpbWFnZSk7XG4gICAgZXhwZWN0QXJyYXlzQ2xvc2UoXG4gICAgICAgIFtcbiAgICAgICAgICAvLyBiYXRjaCAxXG4gICAgICAgICAgLi4uWzQsIDUsIDYsIDI1NSwgMSwgMiwgMywgMjU1LCAxMCwgMTEsIDEyLCAyNTUsIDcsIDgsIDksIDI1NV0sXG4gICAgICAgICAgLy8gYmF0Y2ggMlxuICAgICAgICAgIC4uLlsxMDQsIDEwNSwgMTA2LCAyNTUsIDEwMSwgMTAyLCAxMDMsIDI1NSwgMTEwLCAxMTEsIDExMiwgMjU1LCAxMDcsXG4gICAgICAgICAgICAgIDEwOCwgMTA5LCAyNTVdLFxuICAgICAgICBdLFxuICAgICAgICBhd2FpdCBmbGlwcGVkLmRhdGEoKSk7XG4gIH0pO1xuXG4gIGl0KCdzaG91bGQgZmxpcCAoZnJvbSBpbWFnZSknLCBhc3luYyAoKSA9PiB7XG4gICAgY29uc3QgZmxpcHBlZFBpeGVscyA9XG4gICAgICAgIHRmLmltYWdlLmZsaXBMZWZ0UmlnaHQoZ2V0VGVzdEltYWdlQXNUZW5zb3I0ZCgpKS50b0ludCgpO1xuICAgIGNvbnN0IGZsaXBwZWRQaXhlbHNEYXRhID0gYXdhaXQgZmxpcHBlZFBpeGVscy5kYXRhKCk7XG5cbiAgICBjb25zdCBleHBlY3RlZCA9IFtcbiAgICAgIDIzMCwgMTMzLCAxOCwgIDI1NSwgMjQxLCAxNTMsIDQzLCAgMjU1LCAyMjQsIDE1NiwgNTUsICAyNTUsIDIxMiwgMTU3LCA3NSxcbiAgICAgIDI1NSwgMjAwLCAxNTUsIDk4LCAgMjU1LCAxODMsIDEzOCwgMTA5LCAyNTUsIDE3MSwgMTIwLCAxMTcsIDI1NSwgMTU2LCAxMDAsXG4gICAgICAxMTEsIDI1NSwgMjMzLCAxNDgsIDMxLCAgMjU1LCAyNTAsIDE3NywgNjQsICAyNTUsIDI0MSwgMTg4LCA4MiwgIDI1NSwgMjMwLFxuICAgICAgMTkzLCAxMDQsIDI1NSwgMjIwLCAxOTAsIDEyOCwgMjU1LCAyMDIsIDE3NCwgMTM3LCAyNTUsIDE4NiwgMTUyLCAxNDAsIDI1NSxcbiAgICAgIDE2OCwgMTI5LCAxMzAsIDI1NSwgMjIyLCAxNjQsIDQxLCAgMjU1LCAyNDcsIDIwMSwgODEsICAyNTUsIDI0MywgMjIwLCAxMDYsXG4gICAgICAyNTUsIDIzNSwgMjI3LCAxMjgsIDI1NSwgMjI1LCAyMjgsIDE1MSwgMjU1LCAyMTEsIDIxNiwgMTYyLCAyNTUsIDE5OSwgMTk4LFxuICAgICAgMTY4LCAyNTUsIDE3OSwgMTc2LCAxNTksIDI1NSwgMTkxLCAxNzAsIDYxLCAgMjU1LCAyMTgsIDIxMCwgMTAzLCAyNTUsIDIxMyxcbiAgICAgIDIzMCwgMTI2LCAyNTUsIDIwMSwgMjM2LCAxNDIsIDI1NSwgMTkxLCAyMzksIDE2NSwgMjU1LCAxODQsIDIzNCwgMTgxLCAyNTUsXG4gICAgICAxNzksIDIyNiwgMTk0LCAyNTUsIDE2MywgMjA4LCAxODcsIDI1NSwgMTM1LCAxNjYsIDg2LCAgMjU1LCAxNjIsIDIwNiwgMTI3LFxuICAgICAgMjU1LCAxNTUsIDIyNiwgMTQ2LCAyNTUsIDE0MSwgMjMyLCAxNjIsIDI1NSwgMTMwLCAyMzUsIDE3OSwgMjU1LCAxMjEsIDIzMSxcbiAgICAgIDE5MiwgMjU1LCAxMTksIDIyNiwgMjA2LCAyNTUsIDEwOCwgMjE0LCAyMDIsIDI1NSwgNzEsICAxNDMsIDk3LCAgMjU1LCA5OCxcbiAgICAgIDE4MSwgMTM1LCAyNTUsIDk0LCAgMjA2LCAxNTYsIDI1NSwgODcsICAyMjAsIDE3NSwgMjU1LCA3NiwgIDIyNSwgMTkzLCAyNTUsXG4gICAgICA2NCwgIDIxOSwgMjAxLCAyNTUsIDYyLCAgMjE3LCAyMTMsIDI1NSwgNTUsICAyMDcsIDIxMiwgMjU1LCAxNSwgIDExNSwgMTA1LFxuICAgICAgMjU1LCAzOSwgIDE1MCwgMTQxLCAyNTUsIDM3LCAgMTc3LCAxNjQsIDI1NSwgMzUsICAyMDAsIDE4NiwgMjU1LCAzMCwgIDIwOSxcbiAgICAgIDIwNSwgMjU1LCAxOSwgIDIwMywgMjExLCAyNTUsIDE5LCAgMjA0LCAyMjIsIDI1NSwgMTgsICAyMDAsIDIyNCwgMjU1LCAwLFxuICAgICAgMTAyLCAxMTMsIDI1NSwgNiwgICAxMzMsIDE0MCwgMjU1LCAzLCAgIDE1OCwgMTYyLCAyNTUsIDQsICAgMTgyLCAxODYsIDI1NSxcbiAgICAgIDAsICAgMTk0LCAyMDQsIDI1NSwgMCwgICAxODksIDIwOSwgMjU1LCAwLCAgIDE5MiwgMjIxLCAyNTUsIDAsICAgMTkzLCAyMjgsXG4gICAgICAyNTVcbiAgICBdO1xuXG4gICAgZXhwZWN0QXJyYXlzQ2xvc2UoZXhwZWN0ZWQsIGZsaXBwZWRQaXhlbHNEYXRhKTtcbiAgfSk7XG5cbiAgaXQoJ3Rocm93cyB3aGVuIGlucHV0IGlzIGludDMyJywgYXN5bmMgKCkgPT4ge1xuICAgIGNvbnN0IGltYWdlOiB0Zi5UZW5zb3I0RCA9IHRmLnRlbnNvcihcbiAgICAgICAgW1tbWzEsIDIsIDMsIDI1NV1dLCBbWzcsIDgsIDksIDI1NV1dXV0sIFsxLCAyLCAxLCA0XSwgJ2ludDMyJyk7XG4gICAgZXhwZWN0KCgpID0+IHRmLmltYWdlLmZsaXBMZWZ0UmlnaHQoaW1hZ2UpKVxuICAgICAgICAudG9UaHJvd0Vycm9yKFxuICAgICAgICAgICAgL0FyZ3VtZW50ICdpbWFnZScgcGFzc2VkIHRvICdmbGlwTGVmdFJpZ2h0JyBtdXN0IGJlIGZsb2F0MzIvKTtcbiAgfSk7XG59KTtcbiJdfQ==