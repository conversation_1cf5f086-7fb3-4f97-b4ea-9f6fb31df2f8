/**
 * @license
 * Copyright 2017 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../index';
import { ALL_ENVS, describeWithFlags } from '../jasmine_util';
import { expectArraysClose, expectArraysEqual } from '../test_util';
const sqArr = (arr) => arr.map(d => d * d);
const sumArr = (arr) => arr.reduce((prev, curr) => prev + curr, 0);
// tslint:disable-next-line:no-any
const flatten = (arr) => {
    // tslint:disable-next-line:no-any
    return arr.reduce((prev, curr) => {
        return prev.concat(Array.isArray(curr) ? flatten(curr) : curr);
    }, []);
};
describeWithFlags('localResponseNormalization with Tensor3D', ALL_ENVS, () => {
    it('throws error with invalid input', () => {
        // tslint:disable-next-line:no-any
        const x = tf.tensor2d([1, 20, 300, 4], [1, 4]);
        const radius = 3;
        expect(() => x.localResponseNormalization(radius)).toThrowError();
    });
    it('throws error with invalid radius', () => {
        const x = tf.tensor3d([1, 20, 300, 4], [1, 1, 4]);
        const radius = 0.5;
        expect(() => x.localResponseNormalization(radius)).toThrowError();
    });
    it('computes simple normalization across channels', async () => {
        const xT = tf.tensor3d([1, 20, 300, 4], [1, 1, 4]);
        const radius = 1;
        const bias = 1;
        const alpha = 1;
        const beta = 0.5;
        const result = xT.localResponseNormalization(radius, bias, alpha, beta);
        const f = (...vals) => Math.pow(bias + alpha * sumArr(sqArr(vals)), -beta);
        const x = await xT.array();
        expectArraysClose(await result.data(), [
            x[0][0][0] * f(x[0][0][0], x[0][0][1]),
            x[0][0][1] * f(x[0][0][0], x[0][0][1], x[0][0][2]),
            x[0][0][2] * f(x[0][0][1], x[0][0][2], x[0][0][3]),
            x[0][0][3] * f(x[0][0][2], x[0][0][3]),
        ]);
    });
    it('uses beta = 1.0 to test GPU optimization', async () => {
        const xT = tf.tensor3d([1, 20, 300, 4], [1, 1, 4]);
        const radius = 1;
        const bias = 1;
        const alpha = 1;
        const beta = 1.0;
        const result = xT.localResponseNormalization(radius, bias, alpha, beta);
        const f = (...vals) => Math.pow(bias + alpha * sumArr(sqArr(vals)), -beta);
        const x = await xT.array();
        expectArraysClose(await result.data(), [
            x[0][0][0] * f(x[0][0][0], x[0][0][1]),
            x[0][0][1] * f(x[0][0][0], x[0][0][1], x[0][0][2]),
            x[0][0][2] * f(x[0][0][1], x[0][0][2], x[0][0][3]),
            x[0][0][3] * f(x[0][0][2], x[0][0][3]),
        ]);
    });
    it('uses beta = 0.75 to test GPU optimization', async () => {
        const xT = tf.tensor3d([1, 20, 300, 4], [1, 1, 4]);
        const radius = 1;
        const bias = 1;
        const alpha = 1;
        const beta = 0.75;
        const result = xT.localResponseNormalization(radius, bias, alpha, beta);
        const f = (...vals) => Math.pow(bias + alpha * sumArr(sqArr(vals)), -beta);
        const x = await xT.array();
        expectArraysClose(await result.data(), [
            x[0][0][0] * f(x[0][0][0], x[0][0][1]),
            x[0][0][1] * f(x[0][0][0], x[0][0][1], x[0][0][2]),
            x[0][0][2] * f(x[0][0][1], x[0][0][2], x[0][0][3]),
            x[0][0][3] * f(x[0][0][2], x[0][0][3]),
        ]);
    });
    it('computes complex normalization across channels', async () => {
        const xT = tf.tensor3d([1, 20, 300, 4, 5, 15, 24, 200, 1, 20, 300, 4, 5, 15, 24, 200], [2, 2, 4]);
        const radius = 1;
        const bias = 1;
        const alpha = 1;
        const beta = 0.5;
        const result = xT.localResponseNormalization(radius, bias, alpha, beta);
        const f = (...vals) => Math.pow(bias + alpha * sumArr(sqArr(vals)), -beta);
        // 1       | 2       | 3       | 4
        // ------- | ------- | ------- | -------
        // o x . . | x o x . | . x o x | . . x o
        const x = await xT.array();
        expectArraysClose(await result.data(), [
            // 1 - 4
            x[0][0][0] * f(x[0][0][0], x[0][0][1]),
            x[0][0][1] * f(x[0][0][0], x[0][0][1], x[0][0][2]),
            x[0][0][2] * f(x[0][0][1], x[0][0][2], x[0][0][3]),
            x[0][0][3] * f(x[0][0][2], x[0][0][3]),
            // 1 - 4
            x[0][1][0] * f(x[0][1][0], x[0][1][1]),
            x[0][1][1] * f(x[0][1][0], x[0][1][1], x[0][1][2]),
            x[0][1][2] * f(x[0][1][1], x[0][1][2], x[0][1][3]),
            x[0][1][3] * f(x[0][1][2], x[0][1][3]),
            // 1 - 4
            x[1][0][0] * f(x[1][0][0], x[1][0][1]),
            x[1][0][1] * f(x[1][0][0], x[1][0][1], x[1][0][2]),
            x[1][0][2] * f(x[1][0][1], x[1][0][2], x[1][0][3]),
            x[1][0][3] * f(x[1][0][2], x[1][0][3]),
            // 1 - 4
            x[1][1][0] * f(x[1][1][0], x[1][1][1]),
            x[1][1][1] * f(x[1][1][0], x[1][1][1], x[1][1][2]),
            x[1][1][2] * f(x[1][1][1], x[1][1][2], x[1][1][3]),
            x[1][1][3] * f(x[1][1][2], x[1][1][3]),
        ]);
    });
    it('yields same result as tensorflow', async () => {
        // t = tf.random_uniform([1, 3, 3, 8])
        // l = tf.nn.lrn(t, depth_radius=2)
        // print(tf.Session().run([t, l]))
        const input = [
            [
                [
                    0.95782757, 0.12892687, 0.63624668, 0.70160735, 0.77376258,
                    0.54166114, 0.71172535, 0.65087497
                ],
                [
                    0.91872108, 0.38846886, 0.37847793, 0.50477624, 0.42154622,
                    0.43310916, 0.36253822, 0.07576156
                ],
                [
                    0.48662257, 0.4154036, 0.81704032, 0.91660416, 0.87671542, 0.64215934,
                    0.29933751, 0.90671134
                ]
            ],
            [
                [
                    0.6208992, 0.60847163, 0.41475761, 0.2127713, 0.65306914, 0.13923979,
                    0.32003641, 0.28183973
                ],
                [
                    0.04751575, 0.26870155, 0.45150304, 0.58678186, 0.99118924,
                    0.58878231, 0.30913198, 0.18836617
                ],
                [
                    0.16166461, 0.56322742, 0.67908955, 0.2269547, 0.38491273, 0.97113752,
                    0.51210916, 0.69430435
                ]
            ],
            [
                [
                    0.06625497, 0.13011181, 0.59202921, 0.88871598, 0.6366322, 0.47911358,
                    0.96530843, 0.74259472
                ],
                [
                    0.62660718, 0.0445286, 0.18430257, 0.76863647, 0.87511849, 0.53588808,
                    0.27980685, 0.30281997
                ],
                [
                    0.73987067, 0.91034842, 0.26241004, 0.72832751, 0.78974342,
                    0.50751543, 0.05434644, 0.8231523
                ]
            ]
        ];
        const expected = [
            [
                [
                    0.62630326, 0.07662392, 0.34354961, 0.41885775, 0.42621866,
                    0.29751951, 0.42365381, 0.4364861
                ],
                [
                    0.62828875, 0.251122, 0.23605582, 0.36483878, 0.30624411, 0.32672295,
                    0.29576892, 0.06582346
                ],
                [
                    0.3376624, 0.24321821, 0.42558169, 0.46646208, 0.45103404, 0.32380751,
                    0.17021206, 0.59476018
                ]
            ],
            [
                [
                    0.44719055, 0.43318295, 0.26775005, 0.14921051, 0.49148726,
                    0.10764983, 0.25084552, 0.25714993
                ],
                [
                    0.04202608, 0.21094096, 0.27973703, 0.34166718, 0.57487047,
                    0.35158369, 0.19708875, 0.15495601
                ],
                [
                    0.12034657, 0.41341963, 0.47968671, 0.13278878, 0.22735766,
                    0.57154536, 0.30411762, 0.42352781
                ]
            ],
            [
                [
                    0.05656794, 0.08849642, 0.36951816, 0.53186077, 0.33065733,
                    0.24236222, 0.54666328, 0.45085984
                ],
                [
                    0.52425432, 0.03133496, 0.11043368, 0.46954039, 0.5271349, 0.31946796,
                    0.1876673, 0.25085902
                ],
                [
                    0.47316891, 0.5277527, 0.13831842, 0.40036613, 0.50113004, 0.28860986,
                    0.03395459, 0.59127772
                ]
            ]
        ];
        const x = tf.tensor3d(flatten(input), [3, 3, 8]);
        const radius = 2;
        const bias = 1;
        const alpha = 1;
        const beta = 0.5;
        const result = x.localResponseNormalization(radius, bias, alpha, beta);
        expectArraysClose(await result.data(), flatten(expected));
    });
    it('accepts a tensor-like object', async () => {
        const x = [[[1, 20, 300, 4]]]; // 1x1x4
        const radius = 1;
        const bias = 1;
        const alpha = 1;
        const beta = 0.5;
        const result = tf.localResponseNormalization(x, radius, bias, alpha, beta);
        const f = (...vals) => Math.pow(bias + alpha * sumArr(sqArr(vals)), -beta);
        expectArraysClose(await result.data(), [
            x[0][0][0] * f(x[0][0][0], x[0][0][1]),
            x[0][0][1] * f(x[0][0][0], x[0][0][1], x[0][0][2]),
            x[0][0][2] * f(x[0][0][1], x[0][0][2], x[0][0][3]),
            x[0][0][3] * f(x[0][0][2], x[0][0][3]),
        ]);
    });
});
describeWithFlags('localResponseNormalization with Tensor4D', ALL_ENVS, () => {
    it('throws error with invalid input', () => {
        // tslint:disable-next-line:no-any
        const x = tf.tensor2d([1, 20, 300, 4], [1, 4]);
        const radius = 3;
        expect(() => x.localResponseNormalization(radius)).toThrowError();
    });
    it('throws error with invalid radius', () => {
        const x = tf.tensor4d([1, 20, 300, 4], [1, 1, 1, 4]);
        const radius = 0.5;
        expect(() => x.localResponseNormalization(radius)).toThrowError();
    });
    it('computes simple normalization across channels', async () => {
        const xT = tf.tensor4d([1, 20, 300, 4, 1, 20, 300, 4], [2, 1, 1, 4]);
        const radius = 1;
        const bias = 1;
        const alpha = 1;
        const beta = 0.5;
        const result = xT.localResponseNormalization(radius, bias, alpha, beta);
        const f = (...vals) => Math.pow(bias + alpha * sumArr(sqArr(vals)), -beta);
        // Easier to read using these vars
        const b0 = 0;
        const b1 = 1;
        const x = await xT.array();
        expectArraysClose(await result.data(), [
            x[b0][0][0][0] * f(x[b0][0][0][0], x[b0][0][0][1]),
            x[b0][0][0][1] * f(x[b0][0][0][0], x[b0][0][0][1], x[b0][0][0][2]),
            x[b0][0][0][2] * f(x[b0][0][0][1], x[b0][0][0][2], x[b0][0][0][3]),
            x[b0][0][0][3] * f(x[b0][0][0][2], x[b0][0][0][3]),
            x[b1][0][0][0] * f(x[b1][0][0][0], x[b1][0][0][1]),
            x[b1][0][0][1] * f(x[b1][0][0][0], x[b1][0][0][1], x[b1][0][0][2]),
            x[b1][0][0][2] * f(x[b1][0][0][1], x[b1][0][0][2], x[b1][0][0][3]),
            x[b1][0][0][3] * f(x[b1][0][0][2], x[b1][0][0][3]),
        ]);
    });
    it('yields same result as tensorflow', async () => {
        // t = tf.random_uniform([2, 3, 3, 8])
        // l = tf.nn.lrn(t, depth_radius=2)
        // print(tf.Session().run([t, l]))
        const input = [
            [
                [
                    [
                        0.5659827, 0.57000327, 0.75555623, 0.89843333, 0.55120194,
                        0.53531718, 0.56402838, 0.95481384
                    ],
                    [
                        0.57334661, 0.65172958, 0.75794137, 0.80764937, 0.376616,
                        0.92726362, 0.36422753, 0.60535395
                    ],
                    [
                        0.82404268, 0.01054764, 0.4649173, 0.91637003, 0.82287347, 0.043468,
                        0.44953859, 0.92056584
                    ]
                ],
                [
                    [
                        0.68583369, 0.52534163, 0.53325927, 0.39608097, 0.9337523,
                        0.37397444, 0.81212556, 0.5697
                    ],
                    [
                        0.34278774, 0.57656682, 0.2356832, 0.02636456, 0.49111438,
                        0.17981696, 0.65398049, 0.70132935
                    ],
                    [
                        0.14241767, 0.68376505, 0.65419888, 0.69369483, 0.21489143,
                        0.46235347, 0.0559243, 0.60612857
                    ]
                ],
                [
                    [
                        0.59678483, 0.09368539, 0.3017447, 0.36870825, 0.68145788,
                        0.52048779, 0.46136606, 0.94114387
                    ],
                    [
                        0.3156569, 0.75275254, 0.31970251, 0.3154043, 0.61088014,
                        0.13359487, 0.99048364, 0.33625424
                    ],
                    [
                        0.82103574, 0.52066624, 0.63629258, 0.42294252, 0.93214262,
                        0.57041013, 0.66087878, 0.7019999
                    ]
                ]
            ],
            [
                [
                    [
                        0.21894431, 0.43085241, 0.79883206, 0.19462204, 0.68623316,
                        0.08703053, 0.82380795, 0.85634673
                    ],
                    [
                        0.45011401, 0.70312083, 0.86319792, 0.83205295, 0.67109787,
                        0.82081223, 0.46556532, 0.46408331
                    ],
                    [
                        0.07028461, 0.0038743, 0.44619524, 0.0611403, 0.96373355,
                        0.80561554, 0.42428243, 0.46897113
                    ]
                ],
                [
                    [
                        0.21006894, 0.48764861, 0.36842632, 0.23030031, 0.69685507,
                        0.31707478, 0.68662715, 0.0639503
                    ],
                    [
                        0.53940296, 0.50777435, 0.12625301, 0.12324154, 0.89205229,
                        0.69380629, 0.33191144, 0.81000078
                    ],
                    [
                        0.52650976, 0.71220326, 0.07246161, 0.08874547, 0.42528927,
                        0.36320579, 0.54055619, 0.79342318
                    ]
                ],
                [
                    [
                        0.75916636, 0.74499428, 0.76877356, 0.87210917, 0.93040991,
                        0.49491942, 0.70801985, 0.14901721
                    ],
                    [
                        0.27037835, 0.89302075, 0.69147241, 0.23044991, 0.98916364,
                        0.60161841, 0.63691151, 0.56759977
                    ],
                    [
                        0.56307781, 0.92782414, 0.25880754, 0.98518133, 0.04097319,
                        0.24640906, 0.54566145, 0.99261606
                    ]
                ]
            ]
        ];
        const expected = [
            [
                [
                    [
                        0.38019636, 0.32782161, 0.414222, 0.49507114, 0.3040463, 0.28107059,
                        0.33586296, 0.60191077
                    ],
                    [
                        0.37577698, 0.37752095, 0.42895618, 0.4225589, 0.2054275,
                        0.52219951, 0.23032214, 0.39414096
                    ],
                    [
                        0.59856331, 0.00637784, 0.25168711, 0.5541048, 0.48015645,
                        0.02301128, 0.27214608, 0.6427291
                    ]
                ],
                [
                    [
                        0.48127589, 0.35518789, 0.30486941, 0.23976389, 0.52926594,
                        0.21061926, 0.46920502, 0.39090639
                    ],
                    [
                        0.27937523, 0.46979892, 0.17829391, 0.02044933, 0.37045884,
                        0.12140442, 0.44160855, 0.50198948
                    ],
                    [
                        0.10289387, 0.44164398, 0.41853485, 0.42720893, 0.14580171,
                        0.31817055, 0.043797, 0.48155668
                    ]
                ],
                [
                    [
                        0.49458414, 0.07425242, 0.21042404, 0.26262277, 0.46205613,
                        0.30202535, 0.27406475, 0.61140078
                    ],
                    [
                        0.23736385, 0.55076694, 0.2135559, 0.21463785, 0.38077739,
                        0.08309806, 0.62830603, 0.23137885
                    ],
                    [
                        0.5355776, 0.32740855, 0.3451882, 0.24221195, 0.51988536,
                        0.31387195, 0.37391993, 0.46748781
                    ]
                ]
            ],
            [
                [
                    [
                        0.16003507, 0.31178808, 0.51775187, 0.12722474, 0.40769571,
                        0.05085804, 0.48455271, 0.5505302
                    ],
                    [
                        0.2880325, 0.39714804, 0.45591024, 0.4131493, 0.34525412, 0.4554069,
                        0.29119283, 0.31980222
                    ],
                    [
                        0.0640529, 0.00352532, 0.3052578, 0.03666528, 0.56009793,
                        0.46656418, 0.24587312, 0.32762629
                    ]
                ],
                [
                    [
                        0.17643087, 0.40210918, 0.2634095, 0.16233148, 0.4649446,
                        0.21803913, 0.47819966, 0.05093931
                    ],
                    [
                        0.43121469, 0.403974, 0.08191212, 0.07693455, 0.57362044,
                        0.39671475, 0.19025819, 0.54028469
                    ],
                    [
                        0.39356521, 0.53120333, 0.05151648, 0.06554616, 0.33433318,
                        0.2425479, 0.36161765, 0.5536595
                    ]
                ],
                [
                    [
                        0.46011236, 0.39919043, 0.36865807, 0.43511948, 0.46734285,
                        0.26861796, 0.43624333, 0.11205748
                    ],
                    [
                        0.17642327, 0.57622254, 0.37609601, 0.12030836, 0.54640025,
                        0.34052721, 0.36361033, 0.3926385
                    ],
                    [
                        0.37581176, 0.51741964, 0.14429154, 0.57254595, 0.02646073,
                        0.13531584, 0.35629693, 0.64837402
                    ]
                ]
            ]
        ];
        const x = tf.tensor4d(flatten(input), [2, 3, 3, 8]);
        const radius = 2;
        const result = x.localResponseNormalization(radius);
        expectArraysClose(await result.data(), flatten(expected));
    });
    it('yields same result as tensorflow with inner most dims of odd shape', async () => {
        // t = tf.random_uniform([1, 5, 5, 3])
        // l = tf.nn.lrn(t, depth_radius=2)
        // print(tf.Session().run([t, l]))
        const input = [[
                [
                    [0.08576167, 0.5713569, 0.10008252],
                    [0.9822943, 0.11068773, 0.5733849],
                    [0.52175903, 0.7347398, 0.760726], [0.7118578, 0.3927865, 0.7521831],
                    [0.849753, 0.43948555, 0.42316127]
                ],
                [
                    [0.5843748, 0.27483034, 0.45537806],
                    [0.91386235, 0.56130767, 0.2968701],
                    [0.37907827, 0.11928034, 0.32693362],
                    [0.8294349, 0.9177762, 0.01197743],
                    [0.44460166, 0.22238493, 0.93720853]
                ],
                [
                    [0.12325168, 0.62378526, 0.6220398],
                    [0.9955342, 0.8281578, 0.9977399],
                    [0.78915524, 0.48492992, 0.70430815],
                    [0.856709, 0.91682327, 0.53920233],
                    [0.9217057, 0.32411182, 0.16391528]
                ],
                [
                    [0.3235209, 0.43057775, 0.5644517],
                    [0.93911314, 0.09265935, 0.05458856],
                    [0.06284857, 0.6895604, 0.88354754],
                    [0.32220483, 0.72595966, 0.3620714],
                    [0.15844965, 0.931878, 0.8501971]
                ],
                [
                    [0.07301581, 0.7518866, 0.40925968],
                    [0.82419384, 0.40474093, 0.53465044],
                    [0.34532738, 0.21671772, 0.50855494],
                    [0.04778886, 0.7952956, 0.64908195],
                    [0.8807392, 0.09571135, 0.7910882]
                ]
            ]];
        const expected = [[
                [
                    [0.07398141, 0.4928751, 0.08633514],
                    [0.6468731, 0.07289152, 0.37759283],
                    [0.33744285, 0.4751862, 0.49199253],
                    [0.4770374, 0.2632181, 0.50406057],
                    [0.58718365, 0.30368677, 0.2924066]
                ],
                [
                    [0.45850667, 0.21563481, 0.35729447],
                    [0.6108259, 0.37517825, 0.19842808],
                    [0.3370665, 0.10606097, 0.29070085],
                    [0.52141804, 0.5769532, 0.00752952],
                    [0.30495936, 0.15253738, 0.6428463]
                ],
                [
                    [0.09209093, 0.46607855, 0.4647744],
                    [0.51949346, 0.43215245, 0.5206444],
                    [0.5143535, 0.31606635, 0.45905212],
                    [0.50611794, 0.54163164, 0.31854454],
                    [0.65478665, 0.23025146, 0.11644664]
                ],
                [
                    [0.25507566, 0.3394832, 0.4450343],
                    [0.68247277, 0.06733745, 0.03967063],
                    [0.04180532, 0.45867857, 0.587714],
                    [0.24273802, 0.546913, 0.27277213], [0.097959, 0.5761189, 0.525621]
                ],
                [
                    [0.05538246, 0.57030565, 0.31042328],
                    [0.56486595, 0.27739152, 0.36642575],
                    [0.2892991, 0.18155594, 0.42604348],
                    [0.03332775, 0.55463576, 0.452667],
                    [0.56725365, 0.06164437, 0.50951254]
                ]
            ]];
        const x = tf.tensor4d(input, [1, 5, 5, 3]);
        const radius = 2;
        const result = x.localResponseNormalization(radius);
        expectArraysClose(await result.data(), flatten(expected));
    });
    it('throws when passed a non-tensor', () => {
        const e = /Argument 'x' passed to 'localResponseNormalization' must be a Tensor/;
        expect(() => tf.localResponseNormalization({}))
            .toThrowError(e);
    });
    it('gradient with 3D input', async () => {
        const input = [
            [
                [
                    0.95782757, 0.12892687, 0.63624668, 0.70160735, 0.77376258,
                    0.54166114, 0.71172535, 0.65087497
                ],
                [
                    0.91872108, 0.38846886, 0.37847793, 0.50477624, 0.42154622,
                    0.43310916, 0.36253822, 0.07576156
                ],
                [
                    0.48662257, 0.4154036, 0.81704032, 0.91660416, 0.87671542, 0.64215934,
                    0.29933751, 0.90671134
                ]
            ],
            [
                [
                    0.6208992, 0.60847163, 0.41475761, 0.2127713, 0.65306914, 0.13923979,
                    0.32003641, 0.28183973
                ],
                [
                    0.04751575, 0.26870155, 0.45150304, 0.58678186, 0.99118924,
                    0.58878231, 0.30913198, 0.18836617
                ],
                [
                    0.16166461, 0.56322742, 0.67908955, 0.2269547, 0.38491273, 0.97113752,
                    0.51210916, 0.69430435
                ]
            ],
            [
                [
                    0.06625497, 0.13011181, 0.59202921, 0.88871598, 0.6366322, 0.47911358,
                    0.96530843, 0.74259472
                ],
                [
                    0.62660718, 0.0445286, 0.18430257, 0.76863647, 0.87511849, 0.53588808,
                    0.27980685, 0.30281997
                ],
                [
                    0.73987067, 0.91034842, 0.26241004, 0.72832751, 0.78974342,
                    0.50751543, 0.05434644, 0.8231523
                ]
            ]
        ];
        const expected = [[
                [
                    [
                        0.27552658, 0.52414668, 0.11137494, 0.24928074, 0.07215497,
                        0.16210511, 0.19277242, 0.38672262
                    ],
                    [
                        0.23314378, 0.38181645, 0.30470729, 0.35180706, 0.37793165,
                        0.41450983, 0.60044503, 0.83605933
                    ],
                    [
                        0.51801264, 0.38517883, 0.02934788, 0.03102355, 0.08222333,
                        0.09746625, 0.4151727, 0.29936206
                    ]
                ],
                [
                    [
                        0.37059873, 0.32463685, 0.26611608, 0.54228389, 0.30733055,
                        0.66392428, 0.55629295, 0.79049641
                    ],
                    [
                        0.87162501, 0.68129337, 0.35793597, 0.18797961, -0.03660985,
                        0.23235559, 0.48184156, 0.76417446
                    ],
                    [
                        0.65893668, 0.41059417, 0.26254228, 0.40696776, 0.3330358, 0.01789692,
                        0.3162199, 0.28867012
                    ]
                ],
                [
                    [
                        0.83880937, 0.62594998, 0.324698, 0.13046435, 0.09858654, 0.17851587,
                        0.09067203, 0.30748016
                    ],
                    [
                        0.57213897, 0.67710453, 0.45385274, 0.19951296, 0.07371041,
                        0.20141563, 0.51362634, 0.7163325
                    ],
                    [
                        0.33668244, 0.09696329, 0.33500126, 0.08948036, 0.26512182,
                        0.19593786, 0.59144169, 0.379444
                    ]
                ]
            ]];
        const radius = 2.0;
        const bias = 1.0;
        const alpha = 1.0;
        const beta = 0.5;
        const t = tf.tensor3d(input);
        const dy = tf.onesLike(t);
        const gradients = tf.grad((t) => tf.localResponseNormalization(t, radius, bias, alpha, beta))(t, dy);
        expectArraysEqual(gradients.shape, t.shape);
        expectArraysClose(await gradients.data(), flatten(expected));
    });
    it('gradient with clones', () => {
        const t = tf.zeros([3, 3, 8]);
        const radius = 2.0;
        const bias = 1.0;
        const alpha = 1.0;
        const beta = 0.5;
        const dt = tf.grad((t) => tf.localResponseNormalization(t.clone(), radius, bias, alpha, beta)
            .clone())(t);
        expectArraysEqual(dt.shape, t.shape);
    });
    it('gradient with 4D input', async () => {
        const input = [
            [
                [
                    [
                        0.5659827, 0.57000327, 0.75555623, 0.89843333, 0.55120194,
                        0.53531718, 0.56402838, 0.95481384
                    ],
                    [
                        0.57334661, 0.65172958, 0.75794137, 0.80764937, 0.376616,
                        0.92726362, 0.36422753, 0.60535395
                    ],
                    [
                        0.82404268, 0.01054764, 0.4649173, 0.91637003, 0.82287347, 0.043468,
                        0.44953859, 0.92056584
                    ]
                ],
                [
                    [
                        0.68583369, 0.52534163, 0.53325927, 0.39608097, 0.9337523,
                        0.37397444, 0.81212556, 0.5697
                    ],
                    [
                        0.34278774, 0.57656682, 0.2356832, 0.02636456, 0.49111438,
                        0.17981696, 0.65398049, 0.70132935
                    ],
                    [
                        0.14241767, 0.68376505, 0.65419888, 0.69369483, 0.21489143,
                        0.46235347, 0.0559243, 0.60612857
                    ]
                ],
                [
                    [
                        0.59678483, 0.09368539, 0.3017447, 0.36870825, 0.68145788,
                        0.52048779, 0.46136606, 0.94114387
                    ],
                    [
                        0.3156569, 0.75275254, 0.31970251, 0.3154043, 0.61088014,
                        0.13359487, 0.99048364, 0.33625424
                    ],
                    [
                        0.82103574, 0.52066624, 0.63629258, 0.42294252, 0.93214262,
                        0.57041013, 0.66087878, 0.7019999
                    ]
                ]
            ],
            [
                [
                    [
                        0.21894431, 0.43085241, 0.79883206, 0.19462204, 0.68623316,
                        0.08703053, 0.82380795, 0.85634673
                    ],
                    [
                        0.45011401, 0.70312083, 0.86319792, 0.83205295, 0.67109787,
                        0.82081223, 0.46556532, 0.46408331
                    ],
                    [
                        0.07028461, 0.0038743, 0.44619524, 0.0611403, 0.96373355,
                        0.80561554, 0.42428243, 0.46897113
                    ]
                ],
                [
                    [
                        0.21006894, 0.48764861, 0.36842632, 0.23030031, 0.69685507,
                        0.31707478, 0.68662715, 0.0639503
                    ],
                    [
                        0.53940296, 0.50777435, 0.12625301, 0.12324154, 0.89205229,
                        0.69380629, 0.33191144, 0.81000078
                    ],
                    [
                        0.52650976, 0.71220326, 0.07246161, 0.08874547, 0.42528927,
                        0.36320579, 0.54055619, 0.79342318
                    ]
                ],
                [
                    [
                        0.75916636, 0.74499428, 0.76877356, 0.87210917, 0.93040991,
                        0.49491942, 0.70801985, 0.14901721
                    ],
                    [
                        0.27037835, 0.89302075, 0.69147241, 0.23044991, 0.98916364,
                        0.60161841, 0.63691151, 0.56759977
                    ],
                    [
                        0.56307781, 0.92782414, 0.25880754, 0.98518133, 0.04097319,
                        0.24640906, 0.54566145, 0.99261606
                    ]
                ]
            ]
        ];
        const dyVals = [
            [
                [
                    [
                        1.40394282, -1.68962789, -0.21134049, 1.15015793, 1.51244378,
                        0.42844626, -2.70123291, 0.06449971
                    ],
                    [
                        -0.29038581, 0.67567694, 0.95617437, -1.07383668, 0.20920482,
                        0.39050213, -0.81124371, 2.42158198
                    ],
                    [
                        -1.01235235, -0.63514435, -1.49017262, -0.01205151, 0.78492945,
                        -0.20330679, -2.31419802, -0.31220308
                    ]
                ],
                [
                    [
                        0.07061944, -0.46716127, 0.91232526, -1.30444264, -0.07080109,
                        0.13207501, 0.26701283, -0.48946589
                    ],
                    [
                        -0.74995744, -0.79466617, -1.03790498, -0.32234526, 1.33345711,
                        0.11863081, 1.93010819, 0.47857195
                    ],
                    [
                        0.37702683, -0.7804451, 0.45868117, 1.06967258, -0.65336537,
                        0.3594887, 0.62512684, 0.77009726
                    ]
                ],
                [
                    [
                        0.76865023, 1.00893021, -0.24408816, -0.3943336, 0.47094285,
                        -2.61926222, 1.52929449, 0.7862013
                    ],
                    [
                        -1.20878386, -0.26222935, -0.9076528, 0.03079577, -0.01467486,
                        -0.06949636, 0.05466342, 1.44880533
                    ],
                    [
                        0.05611863, 0.15142779, 0.7802065, -1.2623471, 0.09119794,
                        -0.20110528, 0.17715968, -0.48476508
                    ]
                ]
            ],
            [
                [
                    [
                        0.1549256, 0.94472402, -0.70033115, -1.05752802, -0.63035947,
                        -1.35643113, -0.27211693, 2.33576941
                    ],
                    [
                        0.81070906, -0.58353454, -0.3253817, 2.53953528, -1.40062141,
                        1.7728076, -0.59849483, 1.49650824
                    ],
                    [
                        -0.00610052, -2.29434419, -1.77995121, -0.66354084, -0.70676774,
                        -0.81570011, -1.30821037, 0.40997007
                    ]
                ],
                [
                    [
                        -1.02013469, -0.74198806, -0.82677251, -0.00890179, -1.62196338,
                        -0.5095427, 1.26501179, 0.12931485
                    ],
                    [
                        -1.14763546, 0.11011696, -0.23312508, 0.29730096, -0.49138394,
                        -0.27012363, -0.15987533, -1.84277928
                    ],
                    [
                        -0.03816459, -0.73517877, -2.00476885, 0.47192496, -0.27395752,
                        0.99806124, 1.54439747, -1.02016675
                    ]
                ],
                [
                    [
                        -1.27831209, -0.6961385, -0.73713994, -1.97954738, 0.39108652,
                        -0.46152538, 1.8255372, 2.18119025
                    ],
                    [
                        0.56322283, -1.59858179, 1.54127491, -0.57665956, -1.0098567,
                        0.93239671, 0.25231698, -0.7346009
                    ],
                    [
                        0.41614994, -1.20103085, 0.4330301, -1.23348403, -0.46117213,
                        -0.3780126, 0.35449561, -0.60129249
                    ]
                ]
            ]
        ];
        const depthRadius = 1;
        const bias = 1;
        const alpha = 1;
        const beta = 0.75;
        const expected = [
            [
                [
                    [
                        0.88732064, -0.98597342, -0.00569269, 0.09561057, 0.42255375,
                        0.30286378, -1.17104781, 0.44769961
                    ],
                    [
                        -0.22329885, 0.19271846, 0.41454071, -0.50674957, 0.14660946,
                        0.1591837, -0.83707076, 1.19177234
                    ],
                    [
                        -0.26728818, -0.3847312, -0.72818488, 0.09040837, 0.24023688,
                        -0.11545581, -1.09341288, 0.33930668
                    ]
                ],
                [
                    [
                        0.10079086, -0.38184536, 0.60918945, -0.7267822, 0.13867335,
                        0.03526202, 0.17270499, -0.2705338
                    ],
                    [
                        -0.38344458, -0.15589149, -0.68160093, -0.27644777, 0.79392856,
                        -0.14384332, 0.67121017, -0.23130262
                    ],
                    [
                        0.31069142, -0.39895257, 0.11755499, 0.39481708, -0.5234766,
                        0.2511853, 0.40955079, 0.3492966
                    ]
                ],
                [
                    [
                        0.32660595, 0.7240563, -0.18117335, -0.2649861, 0.67781603,
                        -1.46250272, 0.8465963, -0.05466701
                    ],
                    [
                        -0.71582067, 0.20831716, -0.50778204, 0.07256755, -0.00893679,
                        -0.03798783, -0.18604305, 0.75747406
                    ],
                    [
                        -0.00540833, -0.07677216, 0.41930205, -0.69235319, 0.20631291,
                        -0.11946303, 0.19601521, -0.21237698
                    ]
                ]
            ],
            [
                [
                    [
                        0.0800111, 0.60922205, -0.31155977, -0.46448132, -0.15912701,
                        -0.72455585, -0.5727275, 0.71780092
                    ],
                    [
                        0.50568235, -0.31544152, -0.40618286, 0.97909468, -1.15286613,
                        0.8145386, -0.77758539, 0.93794745
                    ],
                    [
                        -0.00535599, -1.99269259, -1.15343952, -0.31053686, 0.01680636,
                        0.10109296, -0.66026396, 0.35474917
                    ]
                ],
                [
                    [
                        -0.74113333, -0.20625943, -0.4339568, 0.21517368, -0.5734458,
                        -0.23481363, 0.53855389, 0.05860626
                    ],
                    [
                        -0.61435795, 0.29290834, -0.19639145, 0.20930134, -0.08880179,
                        0.02209887, 0.21427482, -0.51696646
                    ],
                    [
                        0.13036536, -0.19079237, -1.43941545, 0.42789665, -0.29732707,
                        0.52354813, 0.78893, -0.59992862
                    ]
                ],
                [
                    [
                        -0.328383, 0.15830949, 0.13110149, -0.492423, 0.46827313,
                        -0.58950633, 0.56422544, 1.44929576
                    ],
                    [
                        0.46141064, -0.80682266, 0.92562175, -0.28897452, -0.30567497,
                        0.50646484, 0.16439518, -0.38878182
                    ],
                    [
                        0.41004074, -0.38593128, 0.42881966, -0.22443436, -0.24573228,
                        -0.2941249, 0.31119603, -0.17903978
                    ]
                ]
            ]
        ];
        const t = tf.tensor(input);
        const dy = tf.tensor(dyVals);
        const gradients = tf.grad(t => tf.localResponseNormalization(t, depthRadius, bias, alpha, beta))(t, dy);
        expectArraysClose(await gradients.data(), flatten(expected));
    });
});
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibG9jYWxfcmVzcG9uc2Vfbm9ybWFsaXphdGlvbl90ZXN0LmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vLi4vdGZqcy1jb3JlL3NyYy9vcHMvbG9jYWxfcmVzcG9uc2Vfbm9ybWFsaXphdGlvbl90ZXN0LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7Ozs7Ozs7Ozs7R0FlRztBQUVILE9BQU8sS0FBSyxFQUFFLE1BQU0sVUFBVSxDQUFDO0FBQy9CLE9BQU8sRUFBQyxRQUFRLEVBQUUsaUJBQWlCLEVBQUMsTUFBTSxpQkFBaUIsQ0FBQztBQUU1RCxPQUFPLEVBQUMsaUJBQWlCLEVBQUUsaUJBQWlCLEVBQUMsTUFBTSxjQUFjLENBQUM7QUFFbEUsTUFBTSxLQUFLLEdBQUcsQ0FBQyxHQUFhLEVBQUUsRUFBRSxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUM7QUFDckQsTUFBTSxNQUFNLEdBQUcsQ0FBQyxHQUFhLEVBQUUsRUFBRSxDQUFDLEdBQUcsQ0FBQyxNQUFNLENBQUMsQ0FBQyxJQUFJLEVBQUUsSUFBSSxFQUFFLEVBQUUsQ0FBQyxJQUFJLEdBQUcsSUFBSSxFQUFFLENBQUMsQ0FBQyxDQUFDO0FBRTdFLGtDQUFrQztBQUNsQyxNQUFNLE9BQU8sR0FBRyxDQUFDLEdBQVEsRUFBWSxFQUFFO0lBQ3JDLGtDQUFrQztJQUNsQyxPQUFPLEdBQUcsQ0FBQyxNQUFNLENBQUMsQ0FBQyxJQUFTLEVBQUUsSUFBUyxFQUFFLEVBQUU7UUFDekMsT0FBTyxJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUM7SUFDakUsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDO0FBQ1QsQ0FBQyxDQUFDO0FBRUYsaUJBQWlCLENBQUMsMENBQTBDLEVBQUUsUUFBUSxFQUFFLEdBQUcsRUFBRTtJQUMzRSxFQUFFLENBQUMsaUNBQWlDLEVBQUUsR0FBRyxFQUFFO1FBQ3pDLGtDQUFrQztRQUNsQyxNQUFNLENBQUMsR0FBUSxFQUFFLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxFQUFFLEVBQUUsRUFBRSxHQUFHLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUNwRCxNQUFNLE1BQU0sR0FBRyxDQUFDLENBQUM7UUFFakIsTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLENBQUMsQ0FBQywwQkFBMEIsQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLFlBQVksRUFBRSxDQUFDO0lBQ3BFLENBQUMsQ0FBQyxDQUFDO0lBRUgsRUFBRSxDQUFDLGtDQUFrQyxFQUFFLEdBQUcsRUFBRTtRQUMxQyxNQUFNLENBQUMsR0FBRyxFQUFFLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxFQUFFLEVBQUUsRUFBRSxHQUFHLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDbEQsTUFBTSxNQUFNLEdBQUcsR0FBRyxDQUFDO1FBRW5CLE1BQU0sQ0FBQyxHQUFHLEVBQUUsQ0FBQyxDQUFDLENBQUMsMEJBQTBCLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxZQUFZLEVBQUUsQ0FBQztJQUNwRSxDQUFDLENBQUMsQ0FBQztJQUVILEVBQUUsQ0FBQywrQ0FBK0MsRUFBRSxLQUFLLElBQUksRUFBRTtRQUM3RCxNQUFNLEVBQUUsR0FBRyxFQUFFLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxFQUFFLEVBQUUsRUFBRSxHQUFHLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDbkQsTUFBTSxNQUFNLEdBQUcsQ0FBQyxDQUFDO1FBQ2pCLE1BQU0sSUFBSSxHQUFHLENBQUMsQ0FBQztRQUNmLE1BQU0sS0FBSyxHQUFHLENBQUMsQ0FBQztRQUNoQixNQUFNLElBQUksR0FBRyxHQUFHLENBQUM7UUFFakIsTUFBTSxNQUFNLEdBQUcsRUFBRSxDQUFDLDBCQUEwQixDQUFDLE1BQU0sRUFBRSxJQUFJLEVBQUUsS0FBSyxFQUFFLElBQUksQ0FBQyxDQUFDO1FBRXhFLE1BQU0sQ0FBQyxHQUFHLENBQUMsR0FBRyxJQUFjLEVBQUUsRUFBRSxDQUM1QixJQUFJLENBQUMsR0FBRyxDQUFDLElBQUksR0FBRyxLQUFLLEdBQUcsTUFBTSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDLENBQUM7UUFFeEQsTUFBTSxDQUFDLEdBQUcsTUFBTSxFQUFFLENBQUMsS0FBSyxFQUFFLENBQUM7UUFDM0IsaUJBQWlCLENBQUMsTUFBTSxNQUFNLENBQUMsSUFBSSxFQUFFLEVBQUU7WUFDckMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ3RDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDbEQsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUNsRCxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7U0FDdkMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxFQUFFLENBQUMsMENBQTBDLEVBQUUsS0FBSyxJQUFJLEVBQUU7UUFDeEQsTUFBTSxFQUFFLEdBQUcsRUFBRSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsRUFBRSxFQUFFLEVBQUUsR0FBRyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ25ELE1BQU0sTUFBTSxHQUFHLENBQUMsQ0FBQztRQUNqQixNQUFNLElBQUksR0FBRyxDQUFDLENBQUM7UUFDZixNQUFNLEtBQUssR0FBRyxDQUFDLENBQUM7UUFDaEIsTUFBTSxJQUFJLEdBQUcsR0FBRyxDQUFDO1FBRWpCLE1BQU0sTUFBTSxHQUFHLEVBQUUsQ0FBQywwQkFBMEIsQ0FBQyxNQUFNLEVBQUUsSUFBSSxFQUFFLEtBQUssRUFBRSxJQUFJLENBQUMsQ0FBQztRQUV4RSxNQUFNLENBQUMsR0FBRyxDQUFDLEdBQUcsSUFBYyxFQUFFLEVBQUUsQ0FDNUIsSUFBSSxDQUFDLEdBQUcsQ0FBQyxJQUFJLEdBQUcsS0FBSyxHQUFHLE1BQU0sQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxDQUFDO1FBRXhELE1BQU0sQ0FBQyxHQUFHLE1BQU0sRUFBRSxDQUFDLEtBQUssRUFBRSxDQUFDO1FBQzNCLGlCQUFpQixDQUFDLE1BQU0sTUFBTSxDQUFDLElBQUksRUFBRSxFQUFFO1lBQ3JDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUN0QyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ2xELENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDbEQsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1NBQ3ZDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsRUFBRSxDQUFDLDJDQUEyQyxFQUFFLEtBQUssSUFBSSxFQUFFO1FBQ3pELE1BQU0sRUFBRSxHQUFHLEVBQUUsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLEVBQUUsRUFBRSxFQUFFLEdBQUcsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUNuRCxNQUFNLE1BQU0sR0FBRyxDQUFDLENBQUM7UUFDakIsTUFBTSxJQUFJLEdBQUcsQ0FBQyxDQUFDO1FBQ2YsTUFBTSxLQUFLLEdBQUcsQ0FBQyxDQUFDO1FBQ2hCLE1BQU0sSUFBSSxHQUFHLElBQUksQ0FBQztRQUVsQixNQUFNLE1BQU0sR0FBRyxFQUFFLENBQUMsMEJBQTBCLENBQUMsTUFBTSxFQUFFLElBQUksRUFBRSxLQUFLLEVBQUUsSUFBSSxDQUFDLENBQUM7UUFFeEUsTUFBTSxDQUFDLEdBQUcsQ0FBQyxHQUFHLElBQWMsRUFBRSxFQUFFLENBQzVCLElBQUksQ0FBQyxHQUFHLENBQUMsSUFBSSxHQUFHLEtBQUssR0FBRyxNQUFNLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUV4RCxNQUFNLENBQUMsR0FBRyxNQUFNLEVBQUUsQ0FBQyxLQUFLLEVBQUUsQ0FBQztRQUMzQixpQkFBaUIsQ0FBQyxNQUFNLE1BQU0sQ0FBQyxJQUFJLEVBQUUsRUFBRTtZQUNyQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDdEMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUNsRCxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ2xELENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztTQUN2QyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILEVBQUUsQ0FBQyxnREFBZ0QsRUFBRSxLQUFLLElBQUksRUFBRTtRQUM5RCxNQUFNLEVBQUUsR0FBRyxFQUFFLENBQUMsUUFBUSxDQUNsQixDQUFDLENBQUMsRUFBRSxFQUFFLEVBQUUsR0FBRyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxHQUFHLEVBQUUsQ0FBQyxFQUFFLEVBQUUsRUFBRSxHQUFHLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEdBQUcsQ0FBQyxFQUM5RCxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUNmLE1BQU0sTUFBTSxHQUFHLENBQUMsQ0FBQztRQUNqQixNQUFNLElBQUksR0FBRyxDQUFDLENBQUM7UUFDZixNQUFNLEtBQUssR0FBRyxDQUFDLENBQUM7UUFDaEIsTUFBTSxJQUFJLEdBQUcsR0FBRyxDQUFDO1FBQ2pCLE1BQU0sTUFBTSxHQUFHLEVBQUUsQ0FBQywwQkFBMEIsQ0FBQyxNQUFNLEVBQUUsSUFBSSxFQUFFLEtBQUssRUFBRSxJQUFJLENBQUMsQ0FBQztRQUV4RSxNQUFNLENBQUMsR0FBRyxDQUFDLEdBQUcsSUFBYyxFQUFFLEVBQUUsQ0FDNUIsSUFBSSxDQUFDLEdBQUcsQ0FBQyxJQUFJLEdBQUcsS0FBSyxHQUFHLE1BQU0sQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxDQUFDO1FBRXhELGtDQUFrQztRQUNsQyx3Q0FBd0M7UUFDeEMsd0NBQXdDO1FBRXhDLE1BQU0sQ0FBQyxHQUFHLE1BQU0sRUFBRSxDQUFDLEtBQUssRUFBRSxDQUFDO1FBQzNCLGlCQUFpQixDQUFDLE1BQU0sTUFBTSxDQUFDLElBQUksRUFBRSxFQUFFO1lBQ3JDLFFBQVE7WUFDUixDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDdEMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUNsRCxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ2xELENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUV0QyxRQUFRO1lBQ1IsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ3RDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDbEQsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUNsRCxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFFdEMsUUFBUTtZQUNSLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUN0QyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ2xELENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDbEQsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBRXRDLFFBQVE7WUFDUixDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDdEMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUNsRCxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ2xELENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztTQUN2QyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILEVBQUUsQ0FBQyxrQ0FBa0MsRUFBRSxLQUFLLElBQUksRUFBRTtRQUNoRCxzQ0FBc0M7UUFDdEMsbUNBQW1DO1FBQ25DLGtDQUFrQztRQUVsQyxNQUFNLEtBQUssR0FBRztZQUNaO2dCQUNFO29CQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO29CQUMxRCxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7aUJBQ25DO2dCQUNEO29CQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO29CQUMxRCxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7aUJBQ25DO2dCQUNEO29CQUNFLFVBQVUsRUFBRSxTQUFTLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTtvQkFDckUsVUFBVSxFQUFFLFVBQVU7aUJBQ3ZCO2FBQ0Y7WUFFRDtnQkFDRTtvQkFDRSxTQUFTLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxTQUFTLEVBQUUsVUFBVSxFQUFFLFVBQVU7b0JBQ3BFLFVBQVUsRUFBRSxVQUFVO2lCQUN2QjtnQkFDRDtvQkFDRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTtvQkFDMUQsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO2lCQUNuQztnQkFDRDtvQkFDRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxTQUFTLEVBQUUsVUFBVSxFQUFFLFVBQVU7b0JBQ3JFLFVBQVUsRUFBRSxVQUFVO2lCQUN2QjthQUNGO1lBRUQ7Z0JBQ0U7b0JBQ0UsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFNBQVMsRUFBRSxVQUFVO29CQUNyRSxVQUFVLEVBQUUsVUFBVTtpQkFDdkI7Z0JBQ0Q7b0JBQ0UsVUFBVSxFQUFFLFNBQVMsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO29CQUNyRSxVQUFVLEVBQUUsVUFBVTtpQkFDdkI7Z0JBQ0Q7b0JBQ0UsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7b0JBQzFELFVBQVUsRUFBRSxVQUFVLEVBQUUsU0FBUztpQkFDbEM7YUFDRjtTQUNGLENBQUM7UUFFRixNQUFNLFFBQVEsR0FBRztZQUNmO2dCQUNFO29CQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO29CQUMxRCxVQUFVLEVBQUUsVUFBVSxFQUFFLFNBQVM7aUJBQ2xDO2dCQUNEO29CQUNFLFVBQVUsRUFBRSxRQUFRLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTtvQkFDcEUsVUFBVSxFQUFFLFVBQVU7aUJBQ3ZCO2dCQUNEO29CQUNFLFNBQVMsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTtvQkFDckUsVUFBVSxFQUFFLFVBQVU7aUJBQ3ZCO2FBQ0Y7WUFFRDtnQkFDRTtvQkFDRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTtvQkFDMUQsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO2lCQUNuQztnQkFDRDtvQkFDRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTtvQkFDMUQsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO2lCQUNuQztnQkFDRDtvQkFDRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTtvQkFDMUQsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO2lCQUNuQzthQUNGO1lBRUQ7Z0JBQ0U7b0JBQ0UsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7b0JBQzFELFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTtpQkFDbkM7Z0JBQ0Q7b0JBQ0UsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFNBQVMsRUFBRSxVQUFVO29CQUNyRSxTQUFTLEVBQUUsVUFBVTtpQkFDdEI7Z0JBQ0Q7b0JBQ0UsVUFBVSxFQUFFLFNBQVMsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO29CQUNyRSxVQUFVLEVBQUUsVUFBVTtpQkFDdkI7YUFDRjtTQUNGLENBQUM7UUFFRixNQUFNLENBQUMsR0FBRyxFQUFFLENBQUMsUUFBUSxDQUFDLE9BQU8sQ0FBQyxLQUFLLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUNqRCxNQUFNLE1BQU0sR0FBRyxDQUFDLENBQUM7UUFDakIsTUFBTSxJQUFJLEdBQUcsQ0FBQyxDQUFDO1FBQ2YsTUFBTSxLQUFLLEdBQUcsQ0FBQyxDQUFDO1FBQ2hCLE1BQU0sSUFBSSxHQUFHLEdBQUcsQ0FBQztRQUVqQixNQUFNLE1BQU0sR0FBRyxDQUFDLENBQUMsMEJBQTBCLENBQUMsTUFBTSxFQUFFLElBQUksRUFBRSxLQUFLLEVBQUUsSUFBSSxDQUFDLENBQUM7UUFFdkUsaUJBQWlCLENBQUMsTUFBTSxNQUFNLENBQUMsSUFBSSxFQUFFLEVBQUUsT0FBTyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUM7SUFDNUQsQ0FBQyxDQUFDLENBQUM7SUFFSCxFQUFFLENBQUMsOEJBQThCLEVBQUUsS0FBSyxJQUFJLEVBQUU7UUFDNUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLEVBQUUsRUFBRSxHQUFHLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUUsUUFBUTtRQUN4QyxNQUFNLE1BQU0sR0FBRyxDQUFDLENBQUM7UUFDakIsTUFBTSxJQUFJLEdBQUcsQ0FBQyxDQUFDO1FBQ2YsTUFBTSxLQUFLLEdBQUcsQ0FBQyxDQUFDO1FBQ2hCLE1BQU0sSUFBSSxHQUFHLEdBQUcsQ0FBQztRQUVqQixNQUFNLE1BQU0sR0FBRyxFQUFFLENBQUMsMEJBQTBCLENBQUMsQ0FBQyxFQUFFLE1BQU0sRUFBRSxJQUFJLEVBQUUsS0FBSyxFQUFFLElBQUksQ0FBQyxDQUFDO1FBRTNFLE1BQU0sQ0FBQyxHQUFHLENBQUMsR0FBRyxJQUFjLEVBQUUsRUFBRSxDQUM1QixJQUFJLENBQUMsR0FBRyxDQUFDLElBQUksR0FBRyxLQUFLLEdBQUcsTUFBTSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDLENBQUM7UUFFeEQsaUJBQWlCLENBQUMsTUFBTSxNQUFNLENBQUMsSUFBSSxFQUFFLEVBQUU7WUFDckMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ3RDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDbEQsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUNsRCxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7U0FDdkMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7QUFDTCxDQUFDLENBQUMsQ0FBQztBQUVILGlCQUFpQixDQUFDLDBDQUEwQyxFQUFFLFFBQVEsRUFBRSxHQUFHLEVBQUU7SUFDM0UsRUFBRSxDQUFDLGlDQUFpQyxFQUFFLEdBQUcsRUFBRTtRQUN6QyxrQ0FBa0M7UUFDbEMsTUFBTSxDQUFDLEdBQVEsRUFBRSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsRUFBRSxFQUFFLEVBQUUsR0FBRyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDcEQsTUFBTSxNQUFNLEdBQUcsQ0FBQyxDQUFDO1FBRWpCLE1BQU0sQ0FBQyxHQUFHLEVBQUUsQ0FBQyxDQUFDLENBQUMsMEJBQTBCLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxZQUFZLEVBQUUsQ0FBQztJQUNwRSxDQUFDLENBQUMsQ0FBQztJQUVILEVBQUUsQ0FBQyxrQ0FBa0MsRUFBRSxHQUFHLEVBQUU7UUFDMUMsTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsRUFBRSxFQUFFLEVBQUUsR0FBRyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUNyRCxNQUFNLE1BQU0sR0FBRyxHQUFHLENBQUM7UUFFbkIsTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLENBQUMsQ0FBQywwQkFBMEIsQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLFlBQVksRUFBRSxDQUFDO0lBQ3BFLENBQUMsQ0FBQyxDQUFDO0lBRUgsRUFBRSxDQUFDLCtDQUErQyxFQUFFLEtBQUssSUFBSSxFQUFFO1FBQzdELE1BQU0sRUFBRSxHQUFHLEVBQUUsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLEVBQUUsRUFBRSxFQUFFLEdBQUcsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUUsRUFBRSxHQUFHLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3JFLE1BQU0sTUFBTSxHQUFHLENBQUMsQ0FBQztRQUNqQixNQUFNLElBQUksR0FBRyxDQUFDLENBQUM7UUFDZixNQUFNLEtBQUssR0FBRyxDQUFDLENBQUM7UUFDaEIsTUFBTSxJQUFJLEdBQUcsR0FBRyxDQUFDO1FBRWpCLE1BQU0sTUFBTSxHQUFHLEVBQUUsQ0FBQywwQkFBMEIsQ0FBQyxNQUFNLEVBQUUsSUFBSSxFQUFFLEtBQUssRUFBRSxJQUFJLENBQUMsQ0FBQztRQUV4RSxNQUFNLENBQUMsR0FBRyxDQUFDLEdBQUcsSUFBYyxFQUFFLEVBQUUsQ0FDNUIsSUFBSSxDQUFDLEdBQUcsQ0FBQyxJQUFJLEdBQUcsS0FBSyxHQUFHLE1BQU0sQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxDQUFDO1FBRXhELGtDQUFrQztRQUNsQyxNQUFNLEVBQUUsR0FBRyxDQUFDLENBQUM7UUFDYixNQUFNLEVBQUUsR0FBRyxDQUFDLENBQUM7UUFDYixNQUFNLENBQUMsR0FBRyxNQUFNLEVBQUUsQ0FBQyxLQUFLLEVBQUUsQ0FBQztRQUMzQixpQkFBaUIsQ0FBQyxNQUFNLE1BQU0sQ0FBQyxJQUFJLEVBQUUsRUFBRTtZQUNyQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDbEQsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUNsRSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ2xFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUVsRCxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDbEQsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUNsRSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ2xFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztTQUNuRCxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILEVBQUUsQ0FBQyxrQ0FBa0MsRUFBRSxLQUFLLElBQUksRUFBRTtRQUNoRCxzQ0FBc0M7UUFDdEMsbUNBQW1DO1FBQ25DLGtDQUFrQztRQUVsQyxNQUFNLEtBQUssR0FBRztZQUNaO2dCQUNFO29CQUNFO3dCQUNFLFNBQVMsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3dCQUN6RCxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7cUJBQ25DO29CQUNEO3dCQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxRQUFRO3dCQUN4RCxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7cUJBQ25DO29CQUNEO3dCQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsU0FBUyxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsUUFBUTt3QkFDbkUsVUFBVSxFQUFFLFVBQVU7cUJBQ3ZCO2lCQUNGO2dCQUVEO29CQUNFO3dCQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxTQUFTO3dCQUN6RCxVQUFVLEVBQUUsVUFBVSxFQUFFLE1BQU07cUJBQy9CO29CQUNEO3dCQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsU0FBUyxFQUFFLFVBQVUsRUFBRSxVQUFVO3dCQUN6RCxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7cUJBQ25DO29CQUNEO3dCQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3dCQUMxRCxVQUFVLEVBQUUsU0FBUyxFQUFFLFVBQVU7cUJBQ2xDO2lCQUNGO2dCQUVEO29CQUNFO3dCQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsU0FBUyxFQUFFLFVBQVUsRUFBRSxVQUFVO3dCQUN6RCxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7cUJBQ25DO29CQUNEO3dCQUNFLFNBQVMsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFNBQVMsRUFBRSxVQUFVO3dCQUN4RCxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7cUJBQ25DO29CQUNEO3dCQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3dCQUMxRCxVQUFVLEVBQUUsVUFBVSxFQUFFLFNBQVM7cUJBQ2xDO2lCQUNGO2FBQ0Y7WUFFRDtnQkFDRTtvQkFDRTt3QkFDRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTt3QkFDMUQsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3FCQUNuQztvQkFDRDt3QkFDRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTt3QkFDMUQsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3FCQUNuQztvQkFDRDt3QkFDRSxVQUFVLEVBQUUsU0FBUyxFQUFFLFVBQVUsRUFBRSxTQUFTLEVBQUUsVUFBVTt3QkFDeEQsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3FCQUNuQztpQkFDRjtnQkFFRDtvQkFDRTt3QkFDRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTt3QkFDMUQsVUFBVSxFQUFFLFVBQVUsRUFBRSxTQUFTO3FCQUNsQztvQkFDRDt3QkFDRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTt3QkFDMUQsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3FCQUNuQztvQkFDRDt3QkFDRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTt3QkFDMUQsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3FCQUNuQztpQkFDRjtnQkFFRDtvQkFDRTt3QkFDRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTt3QkFDMUQsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3FCQUNuQztvQkFDRDt3QkFDRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTt3QkFDMUQsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3FCQUNuQztvQkFDRDt3QkFDRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTt3QkFDMUQsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3FCQUNuQztpQkFDRjthQUNGO1NBQ0YsQ0FBQztRQUVGLE1BQU0sUUFBUSxHQUFHO1lBQ2Y7Z0JBQ0U7b0JBQ0U7d0JBQ0UsVUFBVSxFQUFFLFVBQVUsRUFBRSxRQUFRLEVBQUUsVUFBVSxFQUFFLFNBQVMsRUFBRSxVQUFVO3dCQUNuRSxVQUFVLEVBQUUsVUFBVTtxQkFDdkI7b0JBQ0Q7d0JBQ0UsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsU0FBUyxFQUFFLFNBQVM7d0JBQ3hELFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTtxQkFDbkM7b0JBQ0Q7d0JBQ0UsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsU0FBUyxFQUFFLFVBQVU7d0JBQ3pELFVBQVUsRUFBRSxVQUFVLEVBQUUsU0FBUztxQkFDbEM7aUJBQ0Y7Z0JBRUQ7b0JBQ0U7d0JBQ0UsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7d0JBQzFELFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTtxQkFDbkM7b0JBQ0Q7d0JBQ0UsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7d0JBQzFELFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTtxQkFDbkM7b0JBQ0Q7d0JBQ0UsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7d0JBQzFELFVBQVUsRUFBRSxRQUFRLEVBQUUsVUFBVTtxQkFDakM7aUJBQ0Y7Z0JBRUQ7b0JBQ0U7d0JBQ0UsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7d0JBQzFELFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTtxQkFDbkM7b0JBQ0Q7d0JBQ0UsVUFBVSxFQUFFLFVBQVUsRUFBRSxTQUFTLEVBQUUsVUFBVSxFQUFFLFVBQVU7d0JBQ3pELFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTtxQkFDbkM7b0JBQ0Q7d0JBQ0UsU0FBUyxFQUFFLFVBQVUsRUFBRSxTQUFTLEVBQUUsVUFBVSxFQUFFLFVBQVU7d0JBQ3hELFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTtxQkFDbkM7aUJBQ0Y7YUFDRjtZQUVEO2dCQUNFO29CQUNFO3dCQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3dCQUMxRCxVQUFVLEVBQUUsVUFBVSxFQUFFLFNBQVM7cUJBQ2xDO29CQUNEO3dCQUNFLFNBQVMsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFNBQVMsRUFBRSxVQUFVLEVBQUUsU0FBUzt3QkFDbkUsVUFBVSxFQUFFLFVBQVU7cUJBQ3ZCO29CQUNEO3dCQUNFLFNBQVMsRUFBRSxVQUFVLEVBQUUsU0FBUyxFQUFFLFVBQVUsRUFBRSxVQUFVO3dCQUN4RCxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7cUJBQ25DO2lCQUNGO2dCQUVEO29CQUNFO3dCQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsU0FBUyxFQUFFLFVBQVUsRUFBRSxTQUFTO3dCQUN4RCxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7cUJBQ25DO29CQUNEO3dCQUNFLFVBQVUsRUFBRSxRQUFRLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3dCQUN4RCxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7cUJBQ25DO29CQUNEO3dCQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3dCQUMxRCxTQUFTLEVBQUUsVUFBVSxFQUFFLFNBQVM7cUJBQ2pDO2lCQUNGO2dCQUVEO29CQUNFO3dCQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3dCQUMxRCxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7cUJBQ25DO29CQUNEO3dCQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3dCQUMxRCxVQUFVLEVBQUUsVUFBVSxFQUFFLFNBQVM7cUJBQ2xDO29CQUNEO3dCQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3dCQUMxRCxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7cUJBQ25DO2lCQUNGO2FBQ0Y7U0FDRixDQUFDO1FBRUYsTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLFFBQVEsQ0FBQyxPQUFPLENBQUMsS0FBSyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3BELE1BQU0sTUFBTSxHQUFHLENBQUMsQ0FBQztRQUVqQixNQUFNLE1BQU0sR0FBRyxDQUFDLENBQUMsMEJBQTBCLENBQUMsTUFBTSxDQUFDLENBQUM7UUFFcEQsaUJBQWlCLENBQUMsTUFBTSxNQUFNLENBQUMsSUFBSSxFQUFFLEVBQUUsT0FBTyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUM7SUFDNUQsQ0FBQyxDQUFDLENBQUM7SUFFSCxFQUFFLENBQUMsb0VBQW9FLEVBQ3BFLEtBQUssSUFBSSxFQUFFO1FBQ1Qsc0NBQXNDO1FBQ3RDLG1DQUFtQztRQUNuQyxrQ0FBa0M7UUFFbEMsTUFBTSxLQUFLLEdBQUcsQ0FBQztnQkFDYjtvQkFDRSxDQUFDLFVBQVUsRUFBRSxTQUFTLEVBQUUsVUFBVSxDQUFDO29CQUNuQyxDQUFDLFNBQVMsRUFBRSxVQUFVLEVBQUUsU0FBUyxDQUFDO29CQUNsQyxDQUFDLFVBQVUsRUFBRSxTQUFTLEVBQUUsUUFBUSxDQUFDLEVBQUUsQ0FBQyxTQUFTLEVBQUUsU0FBUyxFQUFFLFNBQVMsQ0FBQztvQkFDcEUsQ0FBQyxRQUFRLEVBQUUsVUFBVSxFQUFFLFVBQVUsQ0FBQztpQkFDbkM7Z0JBRUQ7b0JBQ0UsQ0FBQyxTQUFTLEVBQUUsVUFBVSxFQUFFLFVBQVUsQ0FBQztvQkFDbkMsQ0FBQyxVQUFVLEVBQUUsVUFBVSxFQUFFLFNBQVMsQ0FBQztvQkFDbkMsQ0FBQyxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsQ0FBQztvQkFDcEMsQ0FBQyxTQUFTLEVBQUUsU0FBUyxFQUFFLFVBQVUsQ0FBQztvQkFDbEMsQ0FBQyxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsQ0FBQztpQkFDckM7Z0JBRUQ7b0JBQ0UsQ0FBQyxVQUFVLEVBQUUsVUFBVSxFQUFFLFNBQVMsQ0FBQztvQkFDbkMsQ0FBQyxTQUFTLEVBQUUsU0FBUyxFQUFFLFNBQVMsQ0FBQztvQkFDakMsQ0FBQyxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsQ0FBQztvQkFDcEMsQ0FBQyxRQUFRLEVBQUUsVUFBVSxFQUFFLFVBQVUsQ0FBQztvQkFDbEMsQ0FBQyxTQUFTLEVBQUUsVUFBVSxFQUFFLFVBQVUsQ0FBQztpQkFDcEM7Z0JBRUQ7b0JBQ0UsQ0FBQyxTQUFTLEVBQUUsVUFBVSxFQUFFLFNBQVMsQ0FBQztvQkFDbEMsQ0FBQyxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsQ0FBQztvQkFDcEMsQ0FBQyxVQUFVLEVBQUUsU0FBUyxFQUFFLFVBQVUsQ0FBQztvQkFDbkMsQ0FBQyxVQUFVLEVBQUUsVUFBVSxFQUFFLFNBQVMsQ0FBQztvQkFDbkMsQ0FBQyxVQUFVLEVBQUUsUUFBUSxFQUFFLFNBQVMsQ0FBQztpQkFDbEM7Z0JBRUQ7b0JBQ0UsQ0FBQyxVQUFVLEVBQUUsU0FBUyxFQUFFLFVBQVUsQ0FBQztvQkFDbkMsQ0FBQyxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsQ0FBQztvQkFDcEMsQ0FBQyxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsQ0FBQztvQkFDcEMsQ0FBQyxVQUFVLEVBQUUsU0FBUyxFQUFFLFVBQVUsQ0FBQztvQkFDbkMsQ0FBQyxTQUFTLEVBQUUsVUFBVSxFQUFFLFNBQVMsQ0FBQztpQkFDbkM7YUFDRixDQUFDLENBQUM7UUFFSCxNQUFNLFFBQVEsR0FBRyxDQUFDO2dCQUNoQjtvQkFDRSxDQUFDLFVBQVUsRUFBRSxTQUFTLEVBQUUsVUFBVSxDQUFDO29CQUNuQyxDQUFDLFNBQVMsRUFBRSxVQUFVLEVBQUUsVUFBVSxDQUFDO29CQUNuQyxDQUFDLFVBQVUsRUFBRSxTQUFTLEVBQUUsVUFBVSxDQUFDO29CQUNuQyxDQUFDLFNBQVMsRUFBRSxTQUFTLEVBQUUsVUFBVSxDQUFDO29CQUNsQyxDQUFDLFVBQVUsRUFBRSxVQUFVLEVBQUUsU0FBUyxDQUFDO2lCQUNwQztnQkFFRDtvQkFDRSxDQUFDLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxDQUFDO29CQUNwQyxDQUFDLFNBQVMsRUFBRSxVQUFVLEVBQUUsVUFBVSxDQUFDO29CQUNuQyxDQUFDLFNBQVMsRUFBRSxVQUFVLEVBQUUsVUFBVSxDQUFDO29CQUNuQyxDQUFDLFVBQVUsRUFBRSxTQUFTLEVBQUUsVUFBVSxDQUFDO29CQUNuQyxDQUFDLFVBQVUsRUFBRSxVQUFVLEVBQUUsU0FBUyxDQUFDO2lCQUNwQztnQkFFRDtvQkFDRSxDQUFDLFVBQVUsRUFBRSxVQUFVLEVBQUUsU0FBUyxDQUFDO29CQUNuQyxDQUFDLFVBQVUsRUFBRSxVQUFVLEVBQUUsU0FBUyxDQUFDO29CQUNuQyxDQUFDLFNBQVMsRUFBRSxVQUFVLEVBQUUsVUFBVSxDQUFDO29CQUNuQyxDQUFDLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxDQUFDO29CQUNwQyxDQUFDLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxDQUFDO2lCQUNyQztnQkFFRDtvQkFDRSxDQUFDLFVBQVUsRUFBRSxTQUFTLEVBQUUsU0FBUyxDQUFDO29CQUNsQyxDQUFDLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxDQUFDO29CQUNwQyxDQUFDLFVBQVUsRUFBRSxVQUFVLEVBQUUsUUFBUSxDQUFDO29CQUNsQyxDQUFDLFVBQVUsRUFBRSxRQUFRLEVBQUUsVUFBVSxDQUFDLEVBQUUsQ0FBQyxRQUFRLEVBQUUsU0FBUyxFQUFFLFFBQVEsQ0FBQztpQkFDcEU7Z0JBRUQ7b0JBQ0UsQ0FBQyxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsQ0FBQztvQkFDcEMsQ0FBQyxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsQ0FBQztvQkFDcEMsQ0FBQyxTQUFTLEVBQUUsVUFBVSxFQUFFLFVBQVUsQ0FBQztvQkFDbkMsQ0FBQyxVQUFVLEVBQUUsVUFBVSxFQUFFLFFBQVEsQ0FBQztvQkFDbEMsQ0FBQyxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsQ0FBQztpQkFDckM7YUFDRixDQUFDLENBQUM7UUFFSCxNQUFNLENBQUMsR0FBRyxFQUFFLENBQUMsUUFBUSxDQUFDLEtBQUssRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDM0MsTUFBTSxNQUFNLEdBQUcsQ0FBQyxDQUFDO1FBRWpCLE1BQU0sTUFBTSxHQUFHLENBQUMsQ0FBQywwQkFBMEIsQ0FBQyxNQUFNLENBQUMsQ0FBQztRQUVwRCxpQkFBaUIsQ0FBQyxNQUFNLE1BQU0sQ0FBQyxJQUFJLEVBQUUsRUFBRSxPQUFPLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQztJQUM1RCxDQUFDLENBQUMsQ0FBQztJQUVOLEVBQUUsQ0FBQyxpQ0FBaUMsRUFBRSxHQUFHLEVBQUU7UUFDekMsTUFBTSxDQUFDLEdBQ0gsc0VBQXNFLENBQUM7UUFDM0UsTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLEVBQUUsQ0FBQywwQkFBMEIsQ0FBQyxFQUFpQixDQUFDLENBQUM7YUFDekQsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDO0lBQ3ZCLENBQUMsQ0FBQyxDQUFDO0lBRUgsRUFBRSxDQUFDLHdCQUF3QixFQUFFLEtBQUssSUFBSSxFQUFFO1FBQ3RDLE1BQU0sS0FBSyxHQUFHO1lBQ1o7Z0JBQ0U7b0JBQ0UsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7b0JBQzFELFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTtpQkFDbkM7Z0JBQ0Q7b0JBQ0UsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7b0JBQzFELFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTtpQkFDbkM7Z0JBQ0Q7b0JBQ0UsVUFBVSxFQUFFLFNBQVMsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO29CQUNyRSxVQUFVLEVBQUUsVUFBVTtpQkFDdkI7YUFDRjtZQUVEO2dCQUNFO29CQUNFLFNBQVMsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFNBQVMsRUFBRSxVQUFVLEVBQUUsVUFBVTtvQkFDcEUsVUFBVSxFQUFFLFVBQVU7aUJBQ3ZCO2dCQUNEO29CQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO29CQUMxRCxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7aUJBQ25DO2dCQUNEO29CQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFNBQVMsRUFBRSxVQUFVLEVBQUUsVUFBVTtvQkFDckUsVUFBVSxFQUFFLFVBQVU7aUJBQ3ZCO2FBQ0Y7WUFFRDtnQkFDRTtvQkFDRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsU0FBUyxFQUFFLFVBQVU7b0JBQ3JFLFVBQVUsRUFBRSxVQUFVO2lCQUN2QjtnQkFDRDtvQkFDRSxVQUFVLEVBQUUsU0FBUyxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7b0JBQ3JFLFVBQVUsRUFBRSxVQUFVO2lCQUN2QjtnQkFDRDtvQkFDRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTtvQkFDMUQsVUFBVSxFQUFFLFVBQVUsRUFBRSxTQUFTO2lCQUNsQzthQUNGO1NBQ0YsQ0FBQztRQUVGLE1BQU0sUUFBUSxHQUFHLENBQUM7Z0JBQ2hCO29CQUNFO3dCQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3dCQUMxRCxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7cUJBQ25DO29CQUNEO3dCQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3dCQUMxRCxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7cUJBQ25DO29CQUNEO3dCQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3dCQUMxRCxVQUFVLEVBQUUsU0FBUyxFQUFFLFVBQVU7cUJBQ2xDO2lCQUNGO2dCQUVEO29CQUNFO3dCQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3dCQUMxRCxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7cUJBQ25DO29CQUNEO3dCQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxDQUFDLFVBQVU7d0JBQzNELFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTtxQkFDbkM7b0JBQ0Q7d0JBQ0UsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFNBQVMsRUFBRSxVQUFVO3dCQUNyRSxTQUFTLEVBQUUsVUFBVTtxQkFDdEI7aUJBQ0Y7Z0JBRUQ7b0JBQ0U7d0JBQ0UsVUFBVSxFQUFFLFVBQVUsRUFBRSxRQUFRLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3dCQUNwRSxVQUFVLEVBQUUsVUFBVTtxQkFDdkI7b0JBQ0Q7d0JBQ0UsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7d0JBQzFELFVBQVUsRUFBRSxVQUFVLEVBQUUsU0FBUztxQkFDbEM7b0JBQ0Q7d0JBQ0UsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7d0JBQzFELFVBQVUsRUFBRSxVQUFVLEVBQUUsUUFBUTtxQkFDakM7aUJBQ0Y7YUFDRixDQUFDLENBQUM7UUFDSCxNQUFNLE1BQU0sR0FBRyxHQUFHLENBQUM7UUFDbkIsTUFBTSxJQUFJLEdBQUcsR0FBRyxDQUFDO1FBQ2pCLE1BQU0sS0FBSyxHQUFHLEdBQUcsQ0FBQztRQUNsQixNQUFNLElBQUksR0FBRyxHQUFHLENBQUM7UUFFakIsTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLFFBQVEsQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUM3QixNQUFNLEVBQUUsR0FBRyxFQUFFLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBRTFCLE1BQU0sU0FBUyxHQUFHLEVBQUUsQ0FBQyxJQUFJLENBQ3JCLENBQUMsQ0FBYyxFQUFFLEVBQUUsQ0FDZixFQUFFLENBQUMsMEJBQTBCLENBQUMsQ0FBQyxFQUFFLE1BQU0sRUFBRSxJQUFJLEVBQUUsS0FBSyxFQUFFLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDO1FBRTVFLGlCQUFpQixDQUFDLFNBQVMsQ0FBQyxLQUFLLEVBQUUsQ0FBQyxDQUFDLEtBQUssQ0FBQyxDQUFDO1FBQzVDLGlCQUFpQixDQUFDLE1BQU0sU0FBUyxDQUFDLElBQUksRUFBRSxFQUFFLE9BQU8sQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDO0lBQy9ELENBQUMsQ0FBQyxDQUFDO0lBRUgsRUFBRSxDQUFDLHNCQUFzQixFQUFFLEdBQUcsRUFBRTtRQUM5QixNQUFNLENBQUMsR0FBRyxFQUFFLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQzlCLE1BQU0sTUFBTSxHQUFHLEdBQUcsQ0FBQztRQUNuQixNQUFNLElBQUksR0FBRyxHQUFHLENBQUM7UUFDakIsTUFBTSxLQUFLLEdBQUcsR0FBRyxDQUFDO1FBQ2xCLE1BQU0sSUFBSSxHQUFHLEdBQUcsQ0FBQztRQUNqQixNQUFNLEVBQUUsR0FBRyxFQUFFLENBQUMsSUFBSSxDQUNkLENBQUMsQ0FBYyxFQUFFLEVBQUUsQ0FDZixFQUFFLENBQUMsMEJBQTBCLENBQUMsQ0FBQyxDQUFDLEtBQUssRUFBRSxFQUFFLE1BQU0sRUFBRSxJQUFJLEVBQUUsS0FBSyxFQUFFLElBQUksQ0FBQzthQUM5RCxLQUFLLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBRXpCLGlCQUFpQixDQUFDLEVBQUUsQ0FBQyxLQUFLLEVBQUUsQ0FBQyxDQUFDLEtBQUssQ0FBQyxDQUFDO0lBQ3ZDLENBQUMsQ0FBQyxDQUFDO0lBRUgsRUFBRSxDQUFDLHdCQUF3QixFQUFFLEtBQUssSUFBSSxFQUFFO1FBQ3RDLE1BQU0sS0FBSyxHQUFHO1lBQ1o7Z0JBQ0U7b0JBQ0U7d0JBQ0UsU0FBUyxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7d0JBQ3pELFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTtxQkFDbkM7b0JBQ0Q7d0JBQ0UsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFFBQVE7d0JBQ3hELFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTtxQkFDbkM7b0JBQ0Q7d0JBQ0UsVUFBVSxFQUFFLFVBQVUsRUFBRSxTQUFTLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxRQUFRO3dCQUNuRSxVQUFVLEVBQUUsVUFBVTtxQkFDdkI7aUJBQ0Y7Z0JBRUQ7b0JBQ0U7d0JBQ0UsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFNBQVM7d0JBQ3pELFVBQVUsRUFBRSxVQUFVLEVBQUUsTUFBTTtxQkFDL0I7b0JBQ0Q7d0JBQ0UsVUFBVSxFQUFFLFVBQVUsRUFBRSxTQUFTLEVBQUUsVUFBVSxFQUFFLFVBQVU7d0JBQ3pELFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTtxQkFDbkM7b0JBQ0Q7d0JBQ0UsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7d0JBQzFELFVBQVUsRUFBRSxTQUFTLEVBQUUsVUFBVTtxQkFDbEM7aUJBQ0Y7Z0JBRUQ7b0JBQ0U7d0JBQ0UsVUFBVSxFQUFFLFVBQVUsRUFBRSxTQUFTLEVBQUUsVUFBVSxFQUFFLFVBQVU7d0JBQ3pELFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTtxQkFDbkM7b0JBQ0Q7d0JBQ0UsU0FBUyxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsU0FBUyxFQUFFLFVBQVU7d0JBQ3hELFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTtxQkFDbkM7b0JBQ0Q7d0JBQ0UsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7d0JBQzFELFVBQVUsRUFBRSxVQUFVLEVBQUUsU0FBUztxQkFDbEM7aUJBQ0Y7YUFDRjtZQUVEO2dCQUNFO29CQUNFO3dCQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3dCQUMxRCxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7cUJBQ25DO29CQUNEO3dCQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3dCQUMxRCxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7cUJBQ25DO29CQUNEO3dCQUNFLFVBQVUsRUFBRSxTQUFTLEVBQUUsVUFBVSxFQUFFLFNBQVMsRUFBRSxVQUFVO3dCQUN4RCxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7cUJBQ25DO2lCQUNGO2dCQUVEO29CQUNFO3dCQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3dCQUMxRCxVQUFVLEVBQUUsVUFBVSxFQUFFLFNBQVM7cUJBQ2xDO29CQUNEO3dCQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3dCQUMxRCxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7cUJBQ25DO29CQUNEO3dCQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3dCQUMxRCxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7cUJBQ25DO2lCQUNGO2dCQUVEO29CQUNFO3dCQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3dCQUMxRCxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7cUJBQ25DO29CQUNEO3dCQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3dCQUMxRCxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7cUJBQ25DO29CQUNEO3dCQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3dCQUMxRCxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7cUJBQ25DO2lCQUNGO2FBQ0Y7U0FDRixDQUFDO1FBRUYsTUFBTSxNQUFNLEdBQUc7WUFDYjtnQkFDRTtvQkFDRTt3QkFDRSxVQUFVLEVBQUUsQ0FBQyxVQUFVLEVBQUUsQ0FBQyxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7d0JBQzVELFVBQVUsRUFBRSxDQUFDLFVBQVUsRUFBRSxVQUFVO3FCQUNwQztvQkFDRDt3QkFDRSxDQUFDLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLENBQUMsVUFBVSxFQUFFLFVBQVU7d0JBQzVELFVBQVUsRUFBRSxDQUFDLFVBQVUsRUFBRSxVQUFVO3FCQUNwQztvQkFDRDt3QkFDRSxDQUFDLFVBQVUsRUFBRSxDQUFDLFVBQVUsRUFBRSxDQUFDLFVBQVUsRUFBRSxDQUFDLFVBQVUsRUFBRSxVQUFVO3dCQUM5RCxDQUFDLFVBQVUsRUFBRSxDQUFDLFVBQVUsRUFBRSxDQUFDLFVBQVU7cUJBQ3RDO2lCQUNGO2dCQUVEO29CQUNFO3dCQUNFLFVBQVUsRUFBRSxDQUFDLFVBQVUsRUFBRSxVQUFVLEVBQUUsQ0FBQyxVQUFVLEVBQUUsQ0FBQyxVQUFVO3dCQUM3RCxVQUFVLEVBQUUsVUFBVSxFQUFFLENBQUMsVUFBVTtxQkFDcEM7b0JBQ0Q7d0JBQ0UsQ0FBQyxVQUFVLEVBQUUsQ0FBQyxVQUFVLEVBQUUsQ0FBQyxVQUFVLEVBQUUsQ0FBQyxVQUFVLEVBQUUsVUFBVTt3QkFDOUQsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3FCQUNuQztvQkFDRDt3QkFDRSxVQUFVLEVBQUUsQ0FBQyxTQUFTLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxDQUFDLFVBQVU7d0JBQzNELFNBQVMsRUFBRSxVQUFVLEVBQUUsVUFBVTtxQkFDbEM7aUJBQ0Y7Z0JBRUQ7b0JBQ0U7d0JBQ0UsVUFBVSxFQUFFLFVBQVUsRUFBRSxDQUFDLFVBQVUsRUFBRSxDQUFDLFNBQVMsRUFBRSxVQUFVO3dCQUMzRCxDQUFDLFVBQVUsRUFBRSxVQUFVLEVBQUUsU0FBUztxQkFDbkM7b0JBQ0Q7d0JBQ0UsQ0FBQyxVQUFVLEVBQUUsQ0FBQyxVQUFVLEVBQUUsQ0FBQyxTQUFTLEVBQUUsVUFBVSxFQUFFLENBQUMsVUFBVTt3QkFDN0QsQ0FBQyxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7cUJBQ3BDO29CQUNEO3dCQUNFLFVBQVUsRUFBRSxVQUFVLEVBQUUsU0FBUyxFQUFFLENBQUMsU0FBUyxFQUFFLFVBQVU7d0JBQ3pELENBQUMsVUFBVSxFQUFFLFVBQVUsRUFBRSxDQUFDLFVBQVU7cUJBQ3JDO2lCQUNGO2FBQ0Y7WUFFRDtnQkFDRTtvQkFDRTt3QkFDRSxTQUFTLEVBQUUsVUFBVSxFQUFFLENBQUMsVUFBVSxFQUFFLENBQUMsVUFBVSxFQUFFLENBQUMsVUFBVTt3QkFDNUQsQ0FBQyxVQUFVLEVBQUUsQ0FBQyxVQUFVLEVBQUUsVUFBVTtxQkFDckM7b0JBQ0Q7d0JBQ0UsVUFBVSxFQUFFLENBQUMsVUFBVSxFQUFFLENBQUMsU0FBUyxFQUFFLFVBQVUsRUFBRSxDQUFDLFVBQVU7d0JBQzVELFNBQVMsRUFBRSxDQUFDLFVBQVUsRUFBRSxVQUFVO3FCQUNuQztvQkFDRDt3QkFDRSxDQUFDLFVBQVUsRUFBRSxDQUFDLFVBQVUsRUFBRSxDQUFDLFVBQVUsRUFBRSxDQUFDLFVBQVUsRUFBRSxDQUFDLFVBQVU7d0JBQy9ELENBQUMsVUFBVSxFQUFFLENBQUMsVUFBVSxFQUFFLFVBQVU7cUJBQ3JDO2lCQUNGO2dCQUVEO29CQUNFO3dCQUNFLENBQUMsVUFBVSxFQUFFLENBQUMsVUFBVSxFQUFFLENBQUMsVUFBVSxFQUFFLENBQUMsVUFBVSxFQUFFLENBQUMsVUFBVTt3QkFDL0QsQ0FBQyxTQUFTLEVBQUUsVUFBVSxFQUFFLFVBQVU7cUJBQ25DO29CQUNEO3dCQUNFLENBQUMsVUFBVSxFQUFFLFVBQVUsRUFBRSxDQUFDLFVBQVUsRUFBRSxVQUFVLEVBQUUsQ0FBQyxVQUFVO3dCQUM3RCxDQUFDLFVBQVUsRUFBRSxDQUFDLFVBQVUsRUFBRSxDQUFDLFVBQVU7cUJBQ3RDO29CQUNEO3dCQUNFLENBQUMsVUFBVSxFQUFFLENBQUMsVUFBVSxFQUFFLENBQUMsVUFBVSxFQUFFLFVBQVUsRUFBRSxDQUFDLFVBQVU7d0JBQzlELFVBQVUsRUFBRSxVQUFVLEVBQUUsQ0FBQyxVQUFVO3FCQUNwQztpQkFDRjtnQkFFRDtvQkFDRTt3QkFDRSxDQUFDLFVBQVUsRUFBRSxDQUFDLFNBQVMsRUFBRSxDQUFDLFVBQVUsRUFBRSxDQUFDLFVBQVUsRUFBRSxVQUFVO3dCQUM3RCxDQUFDLFVBQVUsRUFBRSxTQUFTLEVBQUUsVUFBVTtxQkFDbkM7b0JBQ0Q7d0JBQ0UsVUFBVSxFQUFFLENBQUMsVUFBVSxFQUFFLFVBQVUsRUFBRSxDQUFDLFVBQVUsRUFBRSxDQUFDLFNBQVM7d0JBQzVELFVBQVUsRUFBRSxVQUFVLEVBQUUsQ0FBQyxTQUFTO3FCQUNuQztvQkFDRDt3QkFDRSxVQUFVLEVBQUUsQ0FBQyxVQUFVLEVBQUUsU0FBUyxFQUFFLENBQUMsVUFBVSxFQUFFLENBQUMsVUFBVTt3QkFDNUQsQ0FBQyxTQUFTLEVBQUUsVUFBVSxFQUFFLENBQUMsVUFBVTtxQkFDcEM7aUJBQ0Y7YUFDRjtTQUNGLENBQUM7UUFFRixNQUFNLFdBQVcsR0FBRyxDQUFDLENBQUM7UUFDdEIsTUFBTSxJQUFJLEdBQUcsQ0FBQyxDQUFDO1FBQ2YsTUFBTSxLQUFLLEdBQUcsQ0FBQyxDQUFDO1FBQ2hCLE1BQU0sSUFBSSxHQUFHLElBQUksQ0FBQztRQUVsQixNQUFNLFFBQVEsR0FBRztZQUNmO2dCQUNFO29CQUNFO3dCQUNFLFVBQVUsRUFBRSxDQUFDLFVBQVUsRUFBRSxDQUFDLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVTt3QkFDNUQsVUFBVSxFQUFFLENBQUMsVUFBVSxFQUFFLFVBQVU7cUJBQ3BDO29CQUNEO3dCQUNFLENBQUMsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsQ0FBQyxVQUFVLEVBQUUsVUFBVTt3QkFDNUQsU0FBUyxFQUFFLENBQUMsVUFBVSxFQUFFLFVBQVU7cUJBQ25DO29CQUNEO3dCQUNFLENBQUMsVUFBVSxFQUFFLENBQUMsU0FBUyxFQUFFLENBQUMsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3dCQUM1RCxDQUFDLFVBQVUsRUFBRSxDQUFDLFVBQVUsRUFBRSxVQUFVO3FCQUNyQztpQkFDRjtnQkFFRDtvQkFDRTt3QkFDRSxVQUFVLEVBQUUsQ0FBQyxVQUFVLEVBQUUsVUFBVSxFQUFFLENBQUMsU0FBUyxFQUFFLFVBQVU7d0JBQzNELFVBQVUsRUFBRSxVQUFVLEVBQUUsQ0FBQyxTQUFTO3FCQUNuQztvQkFDRDt3QkFDRSxDQUFDLFVBQVUsRUFBRSxDQUFDLFVBQVUsRUFBRSxDQUFDLFVBQVUsRUFBRSxDQUFDLFVBQVUsRUFBRSxVQUFVO3dCQUM5RCxDQUFDLFVBQVUsRUFBRSxVQUFVLEVBQUUsQ0FBQyxVQUFVO3FCQUNyQztvQkFDRDt3QkFDRSxVQUFVLEVBQUUsQ0FBQyxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxDQUFDLFNBQVM7d0JBQzNELFNBQVMsRUFBRSxVQUFVLEVBQUUsU0FBUztxQkFDakM7aUJBQ0Y7Z0JBRUQ7b0JBQ0U7d0JBQ0UsVUFBVSxFQUFFLFNBQVMsRUFBRSxDQUFDLFVBQVUsRUFBRSxDQUFDLFNBQVMsRUFBRSxVQUFVO3dCQUMxRCxDQUFDLFVBQVUsRUFBRSxTQUFTLEVBQUUsQ0FBQyxVQUFVO3FCQUNwQztvQkFDRDt3QkFDRSxDQUFDLFVBQVUsRUFBRSxVQUFVLEVBQUUsQ0FBQyxVQUFVLEVBQUUsVUFBVSxFQUFFLENBQUMsVUFBVTt3QkFDN0QsQ0FBQyxVQUFVLEVBQUUsQ0FBQyxVQUFVLEVBQUUsVUFBVTtxQkFDckM7b0JBQ0Q7d0JBQ0UsQ0FBQyxVQUFVLEVBQUUsQ0FBQyxVQUFVLEVBQUUsVUFBVSxFQUFFLENBQUMsVUFBVSxFQUFFLFVBQVU7d0JBQzdELENBQUMsVUFBVSxFQUFFLFVBQVUsRUFBRSxDQUFDLFVBQVU7cUJBQ3JDO2lCQUNGO2FBQ0Y7WUFFRDtnQkFDRTtvQkFDRTt3QkFDRSxTQUFTLEVBQUUsVUFBVSxFQUFFLENBQUMsVUFBVSxFQUFFLENBQUMsVUFBVSxFQUFFLENBQUMsVUFBVTt3QkFDNUQsQ0FBQyxVQUFVLEVBQUUsQ0FBQyxTQUFTLEVBQUUsVUFBVTtxQkFDcEM7b0JBQ0Q7d0JBQ0UsVUFBVSxFQUFFLENBQUMsVUFBVSxFQUFFLENBQUMsVUFBVSxFQUFFLFVBQVUsRUFBRSxDQUFDLFVBQVU7d0JBQzdELFNBQVMsRUFBRSxDQUFDLFVBQVUsRUFBRSxVQUFVO3FCQUNuQztvQkFDRDt3QkFDRSxDQUFDLFVBQVUsRUFBRSxDQUFDLFVBQVUsRUFBRSxDQUFDLFVBQVUsRUFBRSxDQUFDLFVBQVUsRUFBRSxVQUFVO3dCQUM5RCxVQUFVLEVBQUUsQ0FBQyxVQUFVLEVBQUUsVUFBVTtxQkFDcEM7aUJBQ0Y7Z0JBRUQ7b0JBQ0U7d0JBQ0UsQ0FBQyxVQUFVLEVBQUUsQ0FBQyxVQUFVLEVBQUUsQ0FBQyxTQUFTLEVBQUUsVUFBVSxFQUFFLENBQUMsU0FBUzt3QkFDNUQsQ0FBQyxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVU7cUJBQ3BDO29CQUNEO3dCQUNFLENBQUMsVUFBVSxFQUFFLFVBQVUsRUFBRSxDQUFDLFVBQVUsRUFBRSxVQUFVLEVBQUUsQ0FBQyxVQUFVO3dCQUM3RCxVQUFVLEVBQUUsVUFBVSxFQUFFLENBQUMsVUFBVTtxQkFDcEM7b0JBQ0Q7d0JBQ0UsVUFBVSxFQUFFLENBQUMsVUFBVSxFQUFFLENBQUMsVUFBVSxFQUFFLFVBQVUsRUFBRSxDQUFDLFVBQVU7d0JBQzdELFVBQVUsRUFBRSxPQUFPLEVBQUUsQ0FBQyxVQUFVO3FCQUNqQztpQkFDRjtnQkFFRDtvQkFDRTt3QkFDRSxDQUFDLFFBQVEsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLENBQUMsUUFBUSxFQUFFLFVBQVU7d0JBQ3hELENBQUMsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVO3FCQUNwQztvQkFDRDt3QkFDRSxVQUFVLEVBQUUsQ0FBQyxVQUFVLEVBQUUsVUFBVSxFQUFFLENBQUMsVUFBVSxFQUFFLENBQUMsVUFBVTt3QkFDN0QsVUFBVSxFQUFFLFVBQVUsRUFBRSxDQUFDLFVBQVU7cUJBQ3BDO29CQUNEO3dCQUNFLFVBQVUsRUFBRSxDQUFDLFVBQVUsRUFBRSxVQUFVLEVBQUUsQ0FBQyxVQUFVLEVBQUUsQ0FBQyxVQUFVO3dCQUM3RCxDQUFDLFNBQVMsRUFBRSxVQUFVLEVBQUUsQ0FBQyxVQUFVO3FCQUNwQztpQkFDRjthQUNGO1NBQ0YsQ0FBQztRQUVGLE1BQU0sQ0FBQyxHQUFHLEVBQUUsQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDM0IsTUFBTSxFQUFFLEdBQUcsRUFBRSxDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQztRQUU3QixNQUFNLFNBQVMsR0FBRyxFQUFFLENBQUMsSUFBSSxDQUNyQixDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQywwQkFBMEIsQ0FDOUIsQ0FBYSxFQUFFLFdBQVcsRUFBRSxJQUFJLEVBQUUsS0FBSyxFQUFFLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLEVBQWMsQ0FBQyxDQUFDO1FBRTNFLGlCQUFpQixDQUFDLE1BQU0sU0FBUyxDQUFDLElBQUksRUFBRSxFQUFFLE9BQU8sQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDO0lBQy9ELENBQUMsQ0FBQyxDQUFDO0FBQ0wsQ0FBQyxDQUFDLENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgMjAxNyBHb29nbGUgTExDLiBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqIGh0dHA6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKiA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICovXG5cbmltcG9ydCAqIGFzIHRmIGZyb20gJy4uL2luZGV4JztcbmltcG9ydCB7QUxMX0VOVlMsIGRlc2NyaWJlV2l0aEZsYWdzfSBmcm9tICcuLi9qYXNtaW5lX3V0aWwnO1xuaW1wb3J0IHtUZW5zb3I0RH0gZnJvbSAnLi4vdGVuc29yJztcbmltcG9ydCB7ZXhwZWN0QXJyYXlzQ2xvc2UsIGV4cGVjdEFycmF5c0VxdWFsfSBmcm9tICcuLi90ZXN0X3V0aWwnO1xuXG5jb25zdCBzcUFyciA9IChhcnI6IG51bWJlcltdKSA9PiBhcnIubWFwKGQgPT4gZCAqIGQpO1xuY29uc3Qgc3VtQXJyID0gKGFycjogbnVtYmVyW10pID0+IGFyci5yZWR1Y2UoKHByZXYsIGN1cnIpID0+IHByZXYgKyBjdXJyLCAwKTtcblxuLy8gdHNsaW50OmRpc2FibGUtbmV4dC1saW5lOm5vLWFueVxuY29uc3QgZmxhdHRlbiA9IChhcnI6IGFueSk6IG51bWJlcltdID0+IHtcbiAgLy8gdHNsaW50OmRpc2FibGUtbmV4dC1saW5lOm5vLWFueVxuICByZXR1cm4gYXJyLnJlZHVjZSgocHJldjogYW55LCBjdXJyOiBhbnkpID0+IHtcbiAgICByZXR1cm4gcHJldi5jb25jYXQoQXJyYXkuaXNBcnJheShjdXJyKSA/IGZsYXR0ZW4oY3VycikgOiBjdXJyKTtcbiAgfSwgW10pO1xufTtcblxuZGVzY3JpYmVXaXRoRmxhZ3MoJ2xvY2FsUmVzcG9uc2VOb3JtYWxpemF0aW9uIHdpdGggVGVuc29yM0QnLCBBTExfRU5WUywgKCkgPT4ge1xuICBpdCgndGhyb3dzIGVycm9yIHdpdGggaW52YWxpZCBpbnB1dCcsICgpID0+IHtcbiAgICAvLyB0c2xpbnQ6ZGlzYWJsZS1uZXh0LWxpbmU6bm8tYW55XG4gICAgY29uc3QgeDogYW55ID0gdGYudGVuc29yMmQoWzEsIDIwLCAzMDAsIDRdLCBbMSwgNF0pO1xuICAgIGNvbnN0IHJhZGl1cyA9IDM7XG5cbiAgICBleHBlY3QoKCkgPT4geC5sb2NhbFJlc3BvbnNlTm9ybWFsaXphdGlvbihyYWRpdXMpKS50b1Rocm93RXJyb3IoKTtcbiAgfSk7XG5cbiAgaXQoJ3Rocm93cyBlcnJvciB3aXRoIGludmFsaWQgcmFkaXVzJywgKCkgPT4ge1xuICAgIGNvbnN0IHggPSB0Zi50ZW5zb3IzZChbMSwgMjAsIDMwMCwgNF0sIFsxLCAxLCA0XSk7XG4gICAgY29uc3QgcmFkaXVzID0gMC41O1xuXG4gICAgZXhwZWN0KCgpID0+IHgubG9jYWxSZXNwb25zZU5vcm1hbGl6YXRpb24ocmFkaXVzKSkudG9UaHJvd0Vycm9yKCk7XG4gIH0pO1xuXG4gIGl0KCdjb21wdXRlcyBzaW1wbGUgbm9ybWFsaXphdGlvbiBhY3Jvc3MgY2hhbm5lbHMnLCBhc3luYyAoKSA9PiB7XG4gICAgY29uc3QgeFQgPSB0Zi50ZW5zb3IzZChbMSwgMjAsIDMwMCwgNF0sIFsxLCAxLCA0XSk7XG4gICAgY29uc3QgcmFkaXVzID0gMTtcbiAgICBjb25zdCBiaWFzID0gMTtcbiAgICBjb25zdCBhbHBoYSA9IDE7XG4gICAgY29uc3QgYmV0YSA9IDAuNTtcblxuICAgIGNvbnN0IHJlc3VsdCA9IHhULmxvY2FsUmVzcG9uc2VOb3JtYWxpemF0aW9uKHJhZGl1cywgYmlhcywgYWxwaGEsIGJldGEpO1xuXG4gICAgY29uc3QgZiA9ICguLi52YWxzOiBudW1iZXJbXSkgPT5cbiAgICAgICAgTWF0aC5wb3coYmlhcyArIGFscGhhICogc3VtQXJyKHNxQXJyKHZhbHMpKSwgLWJldGEpO1xuXG4gICAgY29uc3QgeCA9IGF3YWl0IHhULmFycmF5KCk7XG4gICAgZXhwZWN0QXJyYXlzQ2xvc2UoYXdhaXQgcmVzdWx0LmRhdGEoKSwgW1xuICAgICAgeFswXVswXVswXSAqIGYoeFswXVswXVswXSwgeFswXVswXVsxXSksXG4gICAgICB4WzBdWzBdWzFdICogZih4WzBdWzBdWzBdLCB4WzBdWzBdWzFdLCB4WzBdWzBdWzJdKSxcbiAgICAgIHhbMF1bMF1bMl0gKiBmKHhbMF1bMF1bMV0sIHhbMF1bMF1bMl0sIHhbMF1bMF1bM10pLFxuICAgICAgeFswXVswXVszXSAqIGYoeFswXVswXVsyXSwgeFswXVswXVszXSksXG4gICAgXSk7XG4gIH0pO1xuXG4gIGl0KCd1c2VzIGJldGEgPSAxLjAgdG8gdGVzdCBHUFUgb3B0aW1pemF0aW9uJywgYXN5bmMgKCkgPT4ge1xuICAgIGNvbnN0IHhUID0gdGYudGVuc29yM2QoWzEsIDIwLCAzMDAsIDRdLCBbMSwgMSwgNF0pO1xuICAgIGNvbnN0IHJhZGl1cyA9IDE7XG4gICAgY29uc3QgYmlhcyA9IDE7XG4gICAgY29uc3QgYWxwaGEgPSAxO1xuICAgIGNvbnN0IGJldGEgPSAxLjA7XG5cbiAgICBjb25zdCByZXN1bHQgPSB4VC5sb2NhbFJlc3BvbnNlTm9ybWFsaXphdGlvbihyYWRpdXMsIGJpYXMsIGFscGhhLCBiZXRhKTtcblxuICAgIGNvbnN0IGYgPSAoLi4udmFsczogbnVtYmVyW10pID0+XG4gICAgICAgIE1hdGgucG93KGJpYXMgKyBhbHBoYSAqIHN1bUFycihzcUFycih2YWxzKSksIC1iZXRhKTtcblxuICAgIGNvbnN0IHggPSBhd2FpdCB4VC5hcnJheSgpO1xuICAgIGV4cGVjdEFycmF5c0Nsb3NlKGF3YWl0IHJlc3VsdC5kYXRhKCksIFtcbiAgICAgIHhbMF1bMF1bMF0gKiBmKHhbMF1bMF1bMF0sIHhbMF1bMF1bMV0pLFxuICAgICAgeFswXVswXVsxXSAqIGYoeFswXVswXVswXSwgeFswXVswXVsxXSwgeFswXVswXVsyXSksXG4gICAgICB4WzBdWzBdWzJdICogZih4WzBdWzBdWzFdLCB4WzBdWzBdWzJdLCB4WzBdWzBdWzNdKSxcbiAgICAgIHhbMF1bMF1bM10gKiBmKHhbMF1bMF1bMl0sIHhbMF1bMF1bM10pLFxuICAgIF0pO1xuICB9KTtcblxuICBpdCgndXNlcyBiZXRhID0gMC43NSB0byB0ZXN0IEdQVSBvcHRpbWl6YXRpb24nLCBhc3luYyAoKSA9PiB7XG4gICAgY29uc3QgeFQgPSB0Zi50ZW5zb3IzZChbMSwgMjAsIDMwMCwgNF0sIFsxLCAxLCA0XSk7XG4gICAgY29uc3QgcmFkaXVzID0gMTtcbiAgICBjb25zdCBiaWFzID0gMTtcbiAgICBjb25zdCBhbHBoYSA9IDE7XG4gICAgY29uc3QgYmV0YSA9IDAuNzU7XG5cbiAgICBjb25zdCByZXN1bHQgPSB4VC5sb2NhbFJlc3BvbnNlTm9ybWFsaXphdGlvbihyYWRpdXMsIGJpYXMsIGFscGhhLCBiZXRhKTtcblxuICAgIGNvbnN0IGYgPSAoLi4udmFsczogbnVtYmVyW10pID0+XG4gICAgICAgIE1hdGgucG93KGJpYXMgKyBhbHBoYSAqIHN1bUFycihzcUFycih2YWxzKSksIC1iZXRhKTtcblxuICAgIGNvbnN0IHggPSBhd2FpdCB4VC5hcnJheSgpO1xuICAgIGV4cGVjdEFycmF5c0Nsb3NlKGF3YWl0IHJlc3VsdC5kYXRhKCksIFtcbiAgICAgIHhbMF1bMF1bMF0gKiBmKHhbMF1bMF1bMF0sIHhbMF1bMF1bMV0pLFxuICAgICAgeFswXVswXVsxXSAqIGYoeFswXVswXVswXSwgeFswXVswXVsxXSwgeFswXVswXVsyXSksXG4gICAgICB4WzBdWzBdWzJdICogZih4WzBdWzBdWzFdLCB4WzBdWzBdWzJdLCB4WzBdWzBdWzNdKSxcbiAgICAgIHhbMF1bMF1bM10gKiBmKHhbMF1bMF1bMl0sIHhbMF1bMF1bM10pLFxuICAgIF0pO1xuICB9KTtcblxuICBpdCgnY29tcHV0ZXMgY29tcGxleCBub3JtYWxpemF0aW9uIGFjcm9zcyBjaGFubmVscycsIGFzeW5jICgpID0+IHtcbiAgICBjb25zdCB4VCA9IHRmLnRlbnNvcjNkKFxuICAgICAgICBbMSwgMjAsIDMwMCwgNCwgNSwgMTUsIDI0LCAyMDAsIDEsIDIwLCAzMDAsIDQsIDUsIDE1LCAyNCwgMjAwXSxcbiAgICAgICAgWzIsIDIsIDRdKTtcbiAgICBjb25zdCByYWRpdXMgPSAxO1xuICAgIGNvbnN0IGJpYXMgPSAxO1xuICAgIGNvbnN0IGFscGhhID0gMTtcbiAgICBjb25zdCBiZXRhID0gMC41O1xuICAgIGNvbnN0IHJlc3VsdCA9IHhULmxvY2FsUmVzcG9uc2VOb3JtYWxpemF0aW9uKHJhZGl1cywgYmlhcywgYWxwaGEsIGJldGEpO1xuXG4gICAgY29uc3QgZiA9ICguLi52YWxzOiBudW1iZXJbXSkgPT5cbiAgICAgICAgTWF0aC5wb3coYmlhcyArIGFscGhhICogc3VtQXJyKHNxQXJyKHZhbHMpKSwgLWJldGEpO1xuXG4gICAgLy8gMSAgICAgICB8IDIgICAgICAgfCAzICAgICAgIHwgNFxuICAgIC8vIC0tLS0tLS0gfCAtLS0tLS0tIHwgLS0tLS0tLSB8IC0tLS0tLS1cbiAgICAvLyBvIHggLiAuIHwgeCBvIHggLiB8IC4geCBvIHggfCAuIC4geCBvXG5cbiAgICBjb25zdCB4ID0gYXdhaXQgeFQuYXJyYXkoKTtcbiAgICBleHBlY3RBcnJheXNDbG9zZShhd2FpdCByZXN1bHQuZGF0YSgpLCBbXG4gICAgICAvLyAxIC0gNFxuICAgICAgeFswXVswXVswXSAqIGYoeFswXVswXVswXSwgeFswXVswXVsxXSksXG4gICAgICB4WzBdWzBdWzFdICogZih4WzBdWzBdWzBdLCB4WzBdWzBdWzFdLCB4WzBdWzBdWzJdKSxcbiAgICAgIHhbMF1bMF1bMl0gKiBmKHhbMF1bMF1bMV0sIHhbMF1bMF1bMl0sIHhbMF1bMF1bM10pLFxuICAgICAgeFswXVswXVszXSAqIGYoeFswXVswXVsyXSwgeFswXVswXVszXSksXG5cbiAgICAgIC8vIDEgLSA0XG4gICAgICB4WzBdWzFdWzBdICogZih4WzBdWzFdWzBdLCB4WzBdWzFdWzFdKSxcbiAgICAgIHhbMF1bMV1bMV0gKiBmKHhbMF1bMV1bMF0sIHhbMF1bMV1bMV0sIHhbMF1bMV1bMl0pLFxuICAgICAgeFswXVsxXVsyXSAqIGYoeFswXVsxXVsxXSwgeFswXVsxXVsyXSwgeFswXVsxXVszXSksXG4gICAgICB4WzBdWzFdWzNdICogZih4WzBdWzFdWzJdLCB4WzBdWzFdWzNdKSxcblxuICAgICAgLy8gMSAtIDRcbiAgICAgIHhbMV1bMF1bMF0gKiBmKHhbMV1bMF1bMF0sIHhbMV1bMF1bMV0pLFxuICAgICAgeFsxXVswXVsxXSAqIGYoeFsxXVswXVswXSwgeFsxXVswXVsxXSwgeFsxXVswXVsyXSksXG4gICAgICB4WzFdWzBdWzJdICogZih4WzFdWzBdWzFdLCB4WzFdWzBdWzJdLCB4WzFdWzBdWzNdKSxcbiAgICAgIHhbMV1bMF1bM10gKiBmKHhbMV1bMF1bMl0sIHhbMV1bMF1bM10pLFxuXG4gICAgICAvLyAxIC0gNFxuICAgICAgeFsxXVsxXVswXSAqIGYoeFsxXVsxXVswXSwgeFsxXVsxXVsxXSksXG4gICAgICB4WzFdWzFdWzFdICogZih4WzFdWzFdWzBdLCB4WzFdWzFdWzFdLCB4WzFdWzFdWzJdKSxcbiAgICAgIHhbMV1bMV1bMl0gKiBmKHhbMV1bMV1bMV0sIHhbMV1bMV1bMl0sIHhbMV1bMV1bM10pLFxuICAgICAgeFsxXVsxXVszXSAqIGYoeFsxXVsxXVsyXSwgeFsxXVsxXVszXSksXG4gICAgXSk7XG4gIH0pO1xuXG4gIGl0KCd5aWVsZHMgc2FtZSByZXN1bHQgYXMgdGVuc29yZmxvdycsIGFzeW5jICgpID0+IHtcbiAgICAvLyB0ID0gdGYucmFuZG9tX3VuaWZvcm0oWzEsIDMsIDMsIDhdKVxuICAgIC8vIGwgPSB0Zi5ubi5scm4odCwgZGVwdGhfcmFkaXVzPTIpXG4gICAgLy8gcHJpbnQodGYuU2Vzc2lvbigpLnJ1bihbdCwgbF0pKVxuXG4gICAgY29uc3QgaW5wdXQgPSBbXG4gICAgICBbXG4gICAgICAgIFtcbiAgICAgICAgICAwLjk1NzgyNzU3LCAwLjEyODkyNjg3LCAwLjYzNjI0NjY4LCAwLjcwMTYwNzM1LCAwLjc3Mzc2MjU4LFxuICAgICAgICAgIDAuNTQxNjYxMTQsIDAuNzExNzI1MzUsIDAuNjUwODc0OTdcbiAgICAgICAgXSxcbiAgICAgICAgW1xuICAgICAgICAgIDAuOTE4NzIxMDgsIDAuMzg4NDY4ODYsIDAuMzc4NDc3OTMsIDAuNTA0Nzc2MjQsIDAuNDIxNTQ2MjIsXG4gICAgICAgICAgMC40MzMxMDkxNiwgMC4zNjI1MzgyMiwgMC4wNzU3NjE1NlxuICAgICAgICBdLFxuICAgICAgICBbXG4gICAgICAgICAgMC40ODY2MjI1NywgMC40MTU0MDM2LCAwLjgxNzA0MDMyLCAwLjkxNjYwNDE2LCAwLjg3NjcxNTQyLCAwLjY0MjE1OTM0LFxuICAgICAgICAgIDAuMjk5MzM3NTEsIDAuOTA2NzExMzRcbiAgICAgICAgXVxuICAgICAgXSxcblxuICAgICAgW1xuICAgICAgICBbXG4gICAgICAgICAgMC42MjA4OTkyLCAwLjYwODQ3MTYzLCAwLjQxNDc1NzYxLCAwLjIxMjc3MTMsIDAuNjUzMDY5MTQsIDAuMTM5MjM5NzksXG4gICAgICAgICAgMC4zMjAwMzY0MSwgMC4yODE4Mzk3M1xuICAgICAgICBdLFxuICAgICAgICBbXG4gICAgICAgICAgMC4wNDc1MTU3NSwgMC4yNjg3MDE1NSwgMC40NTE1MDMwNCwgMC41ODY3ODE4NiwgMC45OTExODkyNCxcbiAgICAgICAgICAwLjU4ODc4MjMxLCAwLjMwOTEzMTk4LCAwLjE4ODM2NjE3XG4gICAgICAgIF0sXG4gICAgICAgIFtcbiAgICAgICAgICAwLjE2MTY2NDYxLCAwLjU2MzIyNzQyLCAwLjY3OTA4OTU1LCAwLjIyNjk1NDcsIDAuMzg0OTEyNzMsIDAuOTcxMTM3NTIsXG4gICAgICAgICAgMC41MTIxMDkxNiwgMC42OTQzMDQzNVxuICAgICAgICBdXG4gICAgICBdLFxuXG4gICAgICBbXG4gICAgICAgIFtcbiAgICAgICAgICAwLjA2NjI1NDk3LCAwLjEzMDExMTgxLCAwLjU5MjAyOTIxLCAwLjg4ODcxNTk4LCAwLjYzNjYzMjIsIDAuNDc5MTEzNTgsXG4gICAgICAgICAgMC45NjUzMDg0MywgMC43NDI1OTQ3MlxuICAgICAgICBdLFxuICAgICAgICBbXG4gICAgICAgICAgMC42MjY2MDcxOCwgMC4wNDQ1Mjg2LCAwLjE4NDMwMjU3LCAwLjc2ODYzNjQ3LCAwLjg3NTExODQ5LCAwLjUzNTg4ODA4LFxuICAgICAgICAgIDAuMjc5ODA2ODUsIDAuMzAyODE5OTdcbiAgICAgICAgXSxcbiAgICAgICAgW1xuICAgICAgICAgIDAuNzM5ODcwNjcsIDAuOTEwMzQ4NDIsIDAuMjYyNDEwMDQsIDAuNzI4MzI3NTEsIDAuNzg5NzQzNDIsXG4gICAgICAgICAgMC41MDc1MTU0MywgMC4wNTQzNDY0NCwgMC44MjMxNTIzXG4gICAgICAgIF1cbiAgICAgIF1cbiAgICBdO1xuXG4gICAgY29uc3QgZXhwZWN0ZWQgPSBbXG4gICAgICBbXG4gICAgICAgIFtcbiAgICAgICAgICAwLjYyNjMwMzI2LCAwLjA3NjYyMzkyLCAwLjM0MzU0OTYxLCAwLjQxODg1Nzc1LCAwLjQyNjIxODY2LFxuICAgICAgICAgIDAuMjk3NTE5NTEsIDAuNDIzNjUzODEsIDAuNDM2NDg2MVxuICAgICAgICBdLFxuICAgICAgICBbXG4gICAgICAgICAgMC42MjgyODg3NSwgMC4yNTExMjIsIDAuMjM2MDU1ODIsIDAuMzY0ODM4NzgsIDAuMzA2MjQ0MTEsIDAuMzI2NzIyOTUsXG4gICAgICAgICAgMC4yOTU3Njg5MiwgMC4wNjU4MjM0NlxuICAgICAgICBdLFxuICAgICAgICBbXG4gICAgICAgICAgMC4zMzc2NjI0LCAwLjI0MzIxODIxLCAwLjQyNTU4MTY5LCAwLjQ2NjQ2MjA4LCAwLjQ1MTAzNDA0LCAwLjMyMzgwNzUxLFxuICAgICAgICAgIDAuMTcwMjEyMDYsIDAuNTk0NzYwMThcbiAgICAgICAgXVxuICAgICAgXSxcblxuICAgICAgW1xuICAgICAgICBbXG4gICAgICAgICAgMC40NDcxOTA1NSwgMC40MzMxODI5NSwgMC4yNjc3NTAwNSwgMC4xNDkyMTA1MSwgMC40OTE0ODcyNixcbiAgICAgICAgICAwLjEwNzY0OTgzLCAwLjI1MDg0NTUyLCAwLjI1NzE0OTkzXG4gICAgICAgIF0sXG4gICAgICAgIFtcbiAgICAgICAgICAwLjA0MjAyNjA4LCAwLjIxMDk0MDk2LCAwLjI3OTczNzAzLCAwLjM0MTY2NzE4LCAwLjU3NDg3MDQ3LFxuICAgICAgICAgIDAuMzUxNTgzNjksIDAuMTk3MDg4NzUsIDAuMTU0OTU2MDFcbiAgICAgICAgXSxcbiAgICAgICAgW1xuICAgICAgICAgIDAuMTIwMzQ2NTcsIDAuNDEzNDE5NjMsIDAuNDc5Njg2NzEsIDAuMTMyNzg4NzgsIDAuMjI3MzU3NjYsXG4gICAgICAgICAgMC41NzE1NDUzNiwgMC4zMDQxMTc2MiwgMC40MjM1Mjc4MVxuICAgICAgICBdXG4gICAgICBdLFxuXG4gICAgICBbXG4gICAgICAgIFtcbiAgICAgICAgICAwLjA1NjU2Nzk0LCAwLjA4ODQ5NjQyLCAwLjM2OTUxODE2LCAwLjUzMTg2MDc3LCAwLjMzMDY1NzMzLFxuICAgICAgICAgIDAuMjQyMzYyMjIsIDAuNTQ2NjYzMjgsIDAuNDUwODU5ODRcbiAgICAgICAgXSxcbiAgICAgICAgW1xuICAgICAgICAgIDAuNTI0MjU0MzIsIDAuMDMxMzM0OTYsIDAuMTEwNDMzNjgsIDAuNDY5NTQwMzksIDAuNTI3MTM0OSwgMC4zMTk0Njc5NixcbiAgICAgICAgICAwLjE4NzY2NzMsIDAuMjUwODU5MDJcbiAgICAgICAgXSxcbiAgICAgICAgW1xuICAgICAgICAgIDAuNDczMTY4OTEsIDAuNTI3NzUyNywgMC4xMzgzMTg0MiwgMC40MDAzNjYxMywgMC41MDExMzAwNCwgMC4yODg2MDk4NixcbiAgICAgICAgICAwLjAzMzk1NDU5LCAwLjU5MTI3NzcyXG4gICAgICAgIF1cbiAgICAgIF1cbiAgICBdO1xuXG4gICAgY29uc3QgeCA9IHRmLnRlbnNvcjNkKGZsYXR0ZW4oaW5wdXQpLCBbMywgMywgOF0pO1xuICAgIGNvbnN0IHJhZGl1cyA9IDI7XG4gICAgY29uc3QgYmlhcyA9IDE7XG4gICAgY29uc3QgYWxwaGEgPSAxO1xuICAgIGNvbnN0IGJldGEgPSAwLjU7XG5cbiAgICBjb25zdCByZXN1bHQgPSB4LmxvY2FsUmVzcG9uc2VOb3JtYWxpemF0aW9uKHJhZGl1cywgYmlhcywgYWxwaGEsIGJldGEpO1xuXG4gICAgZXhwZWN0QXJyYXlzQ2xvc2UoYXdhaXQgcmVzdWx0LmRhdGEoKSwgZmxhdHRlbihleHBlY3RlZCkpO1xuICB9KTtcblxuICBpdCgnYWNjZXB0cyBhIHRlbnNvci1saWtlIG9iamVjdCcsIGFzeW5jICgpID0+IHtcbiAgICBjb25zdCB4ID0gW1tbMSwgMjAsIDMwMCwgNF1dXTsgIC8vIDF4MXg0XG4gICAgY29uc3QgcmFkaXVzID0gMTtcbiAgICBjb25zdCBiaWFzID0gMTtcbiAgICBjb25zdCBhbHBoYSA9IDE7XG4gICAgY29uc3QgYmV0YSA9IDAuNTtcblxuICAgIGNvbnN0IHJlc3VsdCA9IHRmLmxvY2FsUmVzcG9uc2VOb3JtYWxpemF0aW9uKHgsIHJhZGl1cywgYmlhcywgYWxwaGEsIGJldGEpO1xuXG4gICAgY29uc3QgZiA9ICguLi52YWxzOiBudW1iZXJbXSkgPT5cbiAgICAgICAgTWF0aC5wb3coYmlhcyArIGFscGhhICogc3VtQXJyKHNxQXJyKHZhbHMpKSwgLWJldGEpO1xuXG4gICAgZXhwZWN0QXJyYXlzQ2xvc2UoYXdhaXQgcmVzdWx0LmRhdGEoKSwgW1xuICAgICAgeFswXVswXVswXSAqIGYoeFswXVswXVswXSwgeFswXVswXVsxXSksXG4gICAgICB4WzBdWzBdWzFdICogZih4WzBdWzBdWzBdLCB4WzBdWzBdWzFdLCB4WzBdWzBdWzJdKSxcbiAgICAgIHhbMF1bMF1bMl0gKiBmKHhbMF1bMF1bMV0sIHhbMF1bMF1bMl0sIHhbMF1bMF1bM10pLFxuICAgICAgeFswXVswXVszXSAqIGYoeFswXVswXVsyXSwgeFswXVswXVszXSksXG4gICAgXSk7XG4gIH0pO1xufSk7XG5cbmRlc2NyaWJlV2l0aEZsYWdzKCdsb2NhbFJlc3BvbnNlTm9ybWFsaXphdGlvbiB3aXRoIFRlbnNvcjREJywgQUxMX0VOVlMsICgpID0+IHtcbiAgaXQoJ3Rocm93cyBlcnJvciB3aXRoIGludmFsaWQgaW5wdXQnLCAoKSA9PiB7XG4gICAgLy8gdHNsaW50OmRpc2FibGUtbmV4dC1saW5lOm5vLWFueVxuICAgIGNvbnN0IHg6IGFueSA9IHRmLnRlbnNvcjJkKFsxLCAyMCwgMzAwLCA0XSwgWzEsIDRdKTtcbiAgICBjb25zdCByYWRpdXMgPSAzO1xuXG4gICAgZXhwZWN0KCgpID0+IHgubG9jYWxSZXNwb25zZU5vcm1hbGl6YXRpb24ocmFkaXVzKSkudG9UaHJvd0Vycm9yKCk7XG4gIH0pO1xuXG4gIGl0KCd0aHJvd3MgZXJyb3Igd2l0aCBpbnZhbGlkIHJhZGl1cycsICgpID0+IHtcbiAgICBjb25zdCB4ID0gdGYudGVuc29yNGQoWzEsIDIwLCAzMDAsIDRdLCBbMSwgMSwgMSwgNF0pO1xuICAgIGNvbnN0IHJhZGl1cyA9IDAuNTtcblxuICAgIGV4cGVjdCgoKSA9PiB4LmxvY2FsUmVzcG9uc2VOb3JtYWxpemF0aW9uKHJhZGl1cykpLnRvVGhyb3dFcnJvcigpO1xuICB9KTtcblxuICBpdCgnY29tcHV0ZXMgc2ltcGxlIG5vcm1hbGl6YXRpb24gYWNyb3NzIGNoYW5uZWxzJywgYXN5bmMgKCkgPT4ge1xuICAgIGNvbnN0IHhUID0gdGYudGVuc29yNGQoWzEsIDIwLCAzMDAsIDQsIDEsIDIwLCAzMDAsIDRdLCBbMiwgMSwgMSwgNF0pO1xuICAgIGNvbnN0IHJhZGl1cyA9IDE7XG4gICAgY29uc3QgYmlhcyA9IDE7XG4gICAgY29uc3QgYWxwaGEgPSAxO1xuICAgIGNvbnN0IGJldGEgPSAwLjU7XG5cbiAgICBjb25zdCByZXN1bHQgPSB4VC5sb2NhbFJlc3BvbnNlTm9ybWFsaXphdGlvbihyYWRpdXMsIGJpYXMsIGFscGhhLCBiZXRhKTtcblxuICAgIGNvbnN0IGYgPSAoLi4udmFsczogbnVtYmVyW10pID0+XG4gICAgICAgIE1hdGgucG93KGJpYXMgKyBhbHBoYSAqIHN1bUFycihzcUFycih2YWxzKSksIC1iZXRhKTtcblxuICAgIC8vIEVhc2llciB0byByZWFkIHVzaW5nIHRoZXNlIHZhcnNcbiAgICBjb25zdCBiMCA9IDA7XG4gICAgY29uc3QgYjEgPSAxO1xuICAgIGNvbnN0IHggPSBhd2FpdCB4VC5hcnJheSgpO1xuICAgIGV4cGVjdEFycmF5c0Nsb3NlKGF3YWl0IHJlc3VsdC5kYXRhKCksIFtcbiAgICAgIHhbYjBdWzBdWzBdWzBdICogZih4W2IwXVswXVswXVswXSwgeFtiMF1bMF1bMF1bMV0pLFxuICAgICAgeFtiMF1bMF1bMF1bMV0gKiBmKHhbYjBdWzBdWzBdWzBdLCB4W2IwXVswXVswXVsxXSwgeFtiMF1bMF1bMF1bMl0pLFxuICAgICAgeFtiMF1bMF1bMF1bMl0gKiBmKHhbYjBdWzBdWzBdWzFdLCB4W2IwXVswXVswXVsyXSwgeFtiMF1bMF1bMF1bM10pLFxuICAgICAgeFtiMF1bMF1bMF1bM10gKiBmKHhbYjBdWzBdWzBdWzJdLCB4W2IwXVswXVswXVszXSksXG5cbiAgICAgIHhbYjFdWzBdWzBdWzBdICogZih4W2IxXVswXVswXVswXSwgeFtiMV1bMF1bMF1bMV0pLFxuICAgICAgeFtiMV1bMF1bMF1bMV0gKiBmKHhbYjFdWzBdWzBdWzBdLCB4W2IxXVswXVswXVsxXSwgeFtiMV1bMF1bMF1bMl0pLFxuICAgICAgeFtiMV1bMF1bMF1bMl0gKiBmKHhbYjFdWzBdWzBdWzFdLCB4W2IxXVswXVswXVsyXSwgeFtiMV1bMF1bMF1bM10pLFxuICAgICAgeFtiMV1bMF1bMF1bM10gKiBmKHhbYjFdWzBdWzBdWzJdLCB4W2IxXVswXVswXVszXSksXG4gICAgXSk7XG4gIH0pO1xuXG4gIGl0KCd5aWVsZHMgc2FtZSByZXN1bHQgYXMgdGVuc29yZmxvdycsIGFzeW5jICgpID0+IHtcbiAgICAvLyB0ID0gdGYucmFuZG9tX3VuaWZvcm0oWzIsIDMsIDMsIDhdKVxuICAgIC8vIGwgPSB0Zi5ubi5scm4odCwgZGVwdGhfcmFkaXVzPTIpXG4gICAgLy8gcHJpbnQodGYuU2Vzc2lvbigpLnJ1bihbdCwgbF0pKVxuXG4gICAgY29uc3QgaW5wdXQgPSBbXG4gICAgICBbXG4gICAgICAgIFtcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjU2NTk4MjcsIDAuNTcwMDAzMjcsIDAuNzU1NTU2MjMsIDAuODk4NDMzMzMsIDAuNTUxMjAxOTQsXG4gICAgICAgICAgICAwLjUzNTMxNzE4LCAwLjU2NDAyODM4LCAwLjk1NDgxMzg0XG4gICAgICAgICAgXSxcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjU3MzM0NjYxLCAwLjY1MTcyOTU4LCAwLjc1Nzk0MTM3LCAwLjgwNzY0OTM3LCAwLjM3NjYxNixcbiAgICAgICAgICAgIDAuOTI3MjYzNjIsIDAuMzY0MjI3NTMsIDAuNjA1MzUzOTVcbiAgICAgICAgICBdLFxuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuODI0MDQyNjgsIDAuMDEwNTQ3NjQsIDAuNDY0OTE3MywgMC45MTYzNzAwMywgMC44MjI4NzM0NywgMC4wNDM0NjgsXG4gICAgICAgICAgICAwLjQ0OTUzODU5LCAwLjkyMDU2NTg0XG4gICAgICAgICAgXVxuICAgICAgICBdLFxuXG4gICAgICAgIFtcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjY4NTgzMzY5LCAwLjUyNTM0MTYzLCAwLjUzMzI1OTI3LCAwLjM5NjA4MDk3LCAwLjkzMzc1MjMsXG4gICAgICAgICAgICAwLjM3Mzk3NDQ0LCAwLjgxMjEyNTU2LCAwLjU2OTdcbiAgICAgICAgICBdLFxuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuMzQyNzg3NzQsIDAuNTc2NTY2ODIsIDAuMjM1NjgzMiwgMC4wMjYzNjQ1NiwgMC40OTExMTQzOCxcbiAgICAgICAgICAgIDAuMTc5ODE2OTYsIDAuNjUzOTgwNDksIDAuNzAxMzI5MzVcbiAgICAgICAgICBdLFxuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuMTQyNDE3NjcsIDAuNjgzNzY1MDUsIDAuNjU0MTk4ODgsIDAuNjkzNjk0ODMsIDAuMjE0ODkxNDMsXG4gICAgICAgICAgICAwLjQ2MjM1MzQ3LCAwLjA1NTkyNDMsIDAuNjA2MTI4NTdcbiAgICAgICAgICBdXG4gICAgICAgIF0sXG5cbiAgICAgICAgW1xuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuNTk2Nzg0ODMsIDAuMDkzNjg1MzksIDAuMzAxNzQ0NywgMC4zNjg3MDgyNSwgMC42ODE0NTc4OCxcbiAgICAgICAgICAgIDAuNTIwNDg3NzksIDAuNDYxMzY2MDYsIDAuOTQxMTQzODdcbiAgICAgICAgICBdLFxuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuMzE1NjU2OSwgMC43NTI3NTI1NCwgMC4zMTk3MDI1MSwgMC4zMTU0MDQzLCAwLjYxMDg4MDE0LFxuICAgICAgICAgICAgMC4xMzM1OTQ4NywgMC45OTA0ODM2NCwgMC4zMzYyNTQyNFxuICAgICAgICAgIF0sXG4gICAgICAgICAgW1xuICAgICAgICAgICAgMC44MjEwMzU3NCwgMC41MjA2NjYyNCwgMC42MzYyOTI1OCwgMC40MjI5NDI1MiwgMC45MzIxNDI2MixcbiAgICAgICAgICAgIDAuNTcwNDEwMTMsIDAuNjYwODc4NzgsIDAuNzAxOTk5OVxuICAgICAgICAgIF1cbiAgICAgICAgXVxuICAgICAgXSxcblxuICAgICAgW1xuICAgICAgICBbXG4gICAgICAgICAgW1xuICAgICAgICAgICAgMC4yMTg5NDQzMSwgMC40MzA4NTI0MSwgMC43OTg4MzIwNiwgMC4xOTQ2MjIwNCwgMC42ODYyMzMxNixcbiAgICAgICAgICAgIDAuMDg3MDMwNTMsIDAuODIzODA3OTUsIDAuODU2MzQ2NzNcbiAgICAgICAgICBdLFxuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuNDUwMTE0MDEsIDAuNzAzMTIwODMsIDAuODYzMTk3OTIsIDAuODMyMDUyOTUsIDAuNjcxMDk3ODcsXG4gICAgICAgICAgICAwLjgyMDgxMjIzLCAwLjQ2NTU2NTMyLCAwLjQ2NDA4MzMxXG4gICAgICAgICAgXSxcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjA3MDI4NDYxLCAwLjAwMzg3NDMsIDAuNDQ2MTk1MjQsIDAuMDYxMTQwMywgMC45NjM3MzM1NSxcbiAgICAgICAgICAgIDAuODA1NjE1NTQsIDAuNDI0MjgyNDMsIDAuNDY4OTcxMTNcbiAgICAgICAgICBdXG4gICAgICAgIF0sXG5cbiAgICAgICAgW1xuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuMjEwMDY4OTQsIDAuNDg3NjQ4NjEsIDAuMzY4NDI2MzIsIDAuMjMwMzAwMzEsIDAuNjk2ODU1MDcsXG4gICAgICAgICAgICAwLjMxNzA3NDc4LCAwLjY4NjYyNzE1LCAwLjA2Mzk1MDNcbiAgICAgICAgICBdLFxuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuNTM5NDAyOTYsIDAuNTA3Nzc0MzUsIDAuMTI2MjUzMDEsIDAuMTIzMjQxNTQsIDAuODkyMDUyMjksXG4gICAgICAgICAgICAwLjY5MzgwNjI5LCAwLjMzMTkxMTQ0LCAwLjgxMDAwMDc4XG4gICAgICAgICAgXSxcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjUyNjUwOTc2LCAwLjcxMjIwMzI2LCAwLjA3MjQ2MTYxLCAwLjA4ODc0NTQ3LCAwLjQyNTI4OTI3LFxuICAgICAgICAgICAgMC4zNjMyMDU3OSwgMC41NDA1NTYxOSwgMC43OTM0MjMxOFxuICAgICAgICAgIF1cbiAgICAgICAgXSxcblxuICAgICAgICBbXG4gICAgICAgICAgW1xuICAgICAgICAgICAgMC43NTkxNjYzNiwgMC43NDQ5OTQyOCwgMC43Njg3NzM1NiwgMC44NzIxMDkxNywgMC45MzA0MDk5MSxcbiAgICAgICAgICAgIDAuNDk0OTE5NDIsIDAuNzA4MDE5ODUsIDAuMTQ5MDE3MjFcbiAgICAgICAgICBdLFxuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuMjcwMzc4MzUsIDAuODkzMDIwNzUsIDAuNjkxNDcyNDEsIDAuMjMwNDQ5OTEsIDAuOTg5MTYzNjQsXG4gICAgICAgICAgICAwLjYwMTYxODQxLCAwLjYzNjkxMTUxLCAwLjU2NzU5OTc3XG4gICAgICAgICAgXSxcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjU2MzA3NzgxLCAwLjkyNzgyNDE0LCAwLjI1ODgwNzU0LCAwLjk4NTE4MTMzLCAwLjA0MDk3MzE5LFxuICAgICAgICAgICAgMC4yNDY0MDkwNiwgMC41NDU2NjE0NSwgMC45OTI2MTYwNlxuICAgICAgICAgIF1cbiAgICAgICAgXVxuICAgICAgXVxuICAgIF07XG5cbiAgICBjb25zdCBleHBlY3RlZCA9IFtcbiAgICAgIFtcbiAgICAgICAgW1xuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuMzgwMTk2MzYsIDAuMzI3ODIxNjEsIDAuNDE0MjIyLCAwLjQ5NTA3MTE0LCAwLjMwNDA0NjMsIDAuMjgxMDcwNTksXG4gICAgICAgICAgICAwLjMzNTg2Mjk2LCAwLjYwMTkxMDc3XG4gICAgICAgICAgXSxcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjM3NTc3Njk4LCAwLjM3NzUyMDk1LCAwLjQyODk1NjE4LCAwLjQyMjU1ODksIDAuMjA1NDI3NSxcbiAgICAgICAgICAgIDAuNTIyMTk5NTEsIDAuMjMwMzIyMTQsIDAuMzk0MTQwOTZcbiAgICAgICAgICBdLFxuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuNTk4NTYzMzEsIDAuMDA2Mzc3ODQsIDAuMjUxNjg3MTEsIDAuNTU0MTA0OCwgMC40ODAxNTY0NSxcbiAgICAgICAgICAgIDAuMDIzMDExMjgsIDAuMjcyMTQ2MDgsIDAuNjQyNzI5MVxuICAgICAgICAgIF1cbiAgICAgICAgXSxcblxuICAgICAgICBbXG4gICAgICAgICAgW1xuICAgICAgICAgICAgMC40ODEyNzU4OSwgMC4zNTUxODc4OSwgMC4zMDQ4Njk0MSwgMC4yMzk3NjM4OSwgMC41MjkyNjU5NCxcbiAgICAgICAgICAgIDAuMjEwNjE5MjYsIDAuNDY5MjA1MDIsIDAuMzkwOTA2MzlcbiAgICAgICAgICBdLFxuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuMjc5Mzc1MjMsIDAuNDY5Nzk4OTIsIDAuMTc4MjkzOTEsIDAuMDIwNDQ5MzMsIDAuMzcwNDU4ODQsXG4gICAgICAgICAgICAwLjEyMTQwNDQyLCAwLjQ0MTYwODU1LCAwLjUwMTk4OTQ4XG4gICAgICAgICAgXSxcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjEwMjg5Mzg3LCAwLjQ0MTY0Mzk4LCAwLjQxODUzNDg1LCAwLjQyNzIwODkzLCAwLjE0NTgwMTcxLFxuICAgICAgICAgICAgMC4zMTgxNzA1NSwgMC4wNDM3OTcsIDAuNDgxNTU2NjhcbiAgICAgICAgICBdXG4gICAgICAgIF0sXG5cbiAgICAgICAgW1xuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuNDk0NTg0MTQsIDAuMDc0MjUyNDIsIDAuMjEwNDI0MDQsIDAuMjYyNjIyNzcsIDAuNDYyMDU2MTMsXG4gICAgICAgICAgICAwLjMwMjAyNTM1LCAwLjI3NDA2NDc1LCAwLjYxMTQwMDc4XG4gICAgICAgICAgXSxcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjIzNzM2Mzg1LCAwLjU1MDc2Njk0LCAwLjIxMzU1NTksIDAuMjE0NjM3ODUsIDAuMzgwNzc3MzksXG4gICAgICAgICAgICAwLjA4MzA5ODA2LCAwLjYyODMwNjAzLCAwLjIzMTM3ODg1XG4gICAgICAgICAgXSxcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjUzNTU3NzYsIDAuMzI3NDA4NTUsIDAuMzQ1MTg4MiwgMC4yNDIyMTE5NSwgMC41MTk4ODUzNixcbiAgICAgICAgICAgIDAuMzEzODcxOTUsIDAuMzczOTE5OTMsIDAuNDY3NDg3ODFcbiAgICAgICAgICBdXG4gICAgICAgIF1cbiAgICAgIF0sXG5cbiAgICAgIFtcbiAgICAgICAgW1xuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuMTYwMDM1MDcsIDAuMzExNzg4MDgsIDAuNTE3NzUxODcsIDAuMTI3MjI0NzQsIDAuNDA3Njk1NzEsXG4gICAgICAgICAgICAwLjA1MDg1ODA0LCAwLjQ4NDU1MjcxLCAwLjU1MDUzMDJcbiAgICAgICAgICBdLFxuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuMjg4MDMyNSwgMC4zOTcxNDgwNCwgMC40NTU5MTAyNCwgMC40MTMxNDkzLCAwLjM0NTI1NDEyLCAwLjQ1NTQwNjksXG4gICAgICAgICAgICAwLjI5MTE5MjgzLCAwLjMxOTgwMjIyXG4gICAgICAgICAgXSxcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjA2NDA1MjksIDAuMDAzNTI1MzIsIDAuMzA1MjU3OCwgMC4wMzY2NjUyOCwgMC41NjAwOTc5MyxcbiAgICAgICAgICAgIDAuNDY2NTY0MTgsIDAuMjQ1ODczMTIsIDAuMzI3NjI2MjlcbiAgICAgICAgICBdXG4gICAgICAgIF0sXG5cbiAgICAgICAgW1xuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuMTc2NDMwODcsIDAuNDAyMTA5MTgsIDAuMjYzNDA5NSwgMC4xNjIzMzE0OCwgMC40NjQ5NDQ2LFxuICAgICAgICAgICAgMC4yMTgwMzkxMywgMC40NzgxOTk2NiwgMC4wNTA5MzkzMVxuICAgICAgICAgIF0sXG4gICAgICAgICAgW1xuICAgICAgICAgICAgMC40MzEyMTQ2OSwgMC40MDM5NzQsIDAuMDgxOTEyMTIsIDAuMDc2OTM0NTUsIDAuNTczNjIwNDQsXG4gICAgICAgICAgICAwLjM5NjcxNDc1LCAwLjE5MDI1ODE5LCAwLjU0MDI4NDY5XG4gICAgICAgICAgXSxcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjM5MzU2NTIxLCAwLjUzMTIwMzMzLCAwLjA1MTUxNjQ4LCAwLjA2NTU0NjE2LCAwLjMzNDMzMzE4LFxuICAgICAgICAgICAgMC4yNDI1NDc5LCAwLjM2MTYxNzY1LCAwLjU1MzY1OTVcbiAgICAgICAgICBdXG4gICAgICAgIF0sXG5cbiAgICAgICAgW1xuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuNDYwMTEyMzYsIDAuMzk5MTkwNDMsIDAuMzY4NjU4MDcsIDAuNDM1MTE5NDgsIDAuNDY3MzQyODUsXG4gICAgICAgICAgICAwLjI2ODYxNzk2LCAwLjQzNjI0MzMzLCAwLjExMjA1NzQ4XG4gICAgICAgICAgXSxcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjE3NjQyMzI3LCAwLjU3NjIyMjU0LCAwLjM3NjA5NjAxLCAwLjEyMDMwODM2LCAwLjU0NjQwMDI1LFxuICAgICAgICAgICAgMC4zNDA1MjcyMSwgMC4zNjM2MTAzMywgMC4zOTI2Mzg1XG4gICAgICAgICAgXSxcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjM3NTgxMTc2LCAwLjUxNzQxOTY0LCAwLjE0NDI5MTU0LCAwLjU3MjU0NTk1LCAwLjAyNjQ2MDczLFxuICAgICAgICAgICAgMC4xMzUzMTU4NCwgMC4zNTYyOTY5MywgMC42NDgzNzQwMlxuICAgICAgICAgIF1cbiAgICAgICAgXVxuICAgICAgXVxuICAgIF07XG5cbiAgICBjb25zdCB4ID0gdGYudGVuc29yNGQoZmxhdHRlbihpbnB1dCksIFsyLCAzLCAzLCA4XSk7XG4gICAgY29uc3QgcmFkaXVzID0gMjtcblxuICAgIGNvbnN0IHJlc3VsdCA9IHgubG9jYWxSZXNwb25zZU5vcm1hbGl6YXRpb24ocmFkaXVzKTtcblxuICAgIGV4cGVjdEFycmF5c0Nsb3NlKGF3YWl0IHJlc3VsdC5kYXRhKCksIGZsYXR0ZW4oZXhwZWN0ZWQpKTtcbiAgfSk7XG5cbiAgaXQoJ3lpZWxkcyBzYW1lIHJlc3VsdCBhcyB0ZW5zb3JmbG93IHdpdGggaW5uZXIgbW9zdCBkaW1zIG9mIG9kZCBzaGFwZScsXG4gICAgIGFzeW5jICgpID0+IHtcbiAgICAgICAvLyB0ID0gdGYucmFuZG9tX3VuaWZvcm0oWzEsIDUsIDUsIDNdKVxuICAgICAgIC8vIGwgPSB0Zi5ubi5scm4odCwgZGVwdGhfcmFkaXVzPTIpXG4gICAgICAgLy8gcHJpbnQodGYuU2Vzc2lvbigpLnJ1bihbdCwgbF0pKVxuXG4gICAgICAgY29uc3QgaW5wdXQgPSBbW1xuICAgICAgICAgW1xuICAgICAgICAgICBbMC4wODU3NjE2NywgMC41NzEzNTY5LCAwLjEwMDA4MjUyXSxcbiAgICAgICAgICAgWzAuOTgyMjk0MywgMC4xMTA2ODc3MywgMC41NzMzODQ5XSxcbiAgICAgICAgICAgWzAuNTIxNzU5MDMsIDAuNzM0NzM5OCwgMC43NjA3MjZdLCBbMC43MTE4NTc4LCAwLjM5Mjc4NjUsIDAuNzUyMTgzMV0sXG4gICAgICAgICAgIFswLjg0OTc1MywgMC40Mzk0ODU1NSwgMC40MjMxNjEyN11cbiAgICAgICAgIF0sXG5cbiAgICAgICAgIFtcbiAgICAgICAgICAgWzAuNTg0Mzc0OCwgMC4yNzQ4MzAzNCwgMC40NTUzNzgwNl0sXG4gICAgICAgICAgIFswLjkxMzg2MjM1LCAwLjU2MTMwNzY3LCAwLjI5Njg3MDFdLFxuICAgICAgICAgICBbMC4zNzkwNzgyNywgMC4xMTkyODAzNCwgMC4zMjY5MzM2Ml0sXG4gICAgICAgICAgIFswLjgyOTQzNDksIDAuOTE3Nzc2MiwgMC4wMTE5Nzc0M10sXG4gICAgICAgICAgIFswLjQ0NDYwMTY2LCAwLjIyMjM4NDkzLCAwLjkzNzIwODUzXVxuICAgICAgICAgXSxcblxuICAgICAgICAgW1xuICAgICAgICAgICBbMC4xMjMyNTE2OCwgMC42MjM3ODUyNiwgMC42MjIwMzk4XSxcbiAgICAgICAgICAgWzAuOTk1NTM0MiwgMC44MjgxNTc4LCAwLjk5NzczOTldLFxuICAgICAgICAgICBbMC43ODkxNTUyNCwgMC40ODQ5Mjk5MiwgMC43MDQzMDgxNV0sXG4gICAgICAgICAgIFswLjg1NjcwOSwgMC45MTY4MjMyNywgMC41MzkyMDIzM10sXG4gICAgICAgICAgIFswLjkyMTcwNTcsIDAuMzI0MTExODIsIDAuMTYzOTE1MjhdXG4gICAgICAgICBdLFxuXG4gICAgICAgICBbXG4gICAgICAgICAgIFswLjMyMzUyMDksIDAuNDMwNTc3NzUsIDAuNTY0NDUxN10sXG4gICAgICAgICAgIFswLjkzOTExMzE0LCAwLjA5MjY1OTM1LCAwLjA1NDU4ODU2XSxcbiAgICAgICAgICAgWzAuMDYyODQ4NTcsIDAuNjg5NTYwNCwgMC44ODM1NDc1NF0sXG4gICAgICAgICAgIFswLjMyMjIwNDgzLCAwLjcyNTk1OTY2LCAwLjM2MjA3MTRdLFxuICAgICAgICAgICBbMC4xNTg0NDk2NSwgMC45MzE4NzgsIDAuODUwMTk3MV1cbiAgICAgICAgIF0sXG5cbiAgICAgICAgIFtcbiAgICAgICAgICAgWzAuMDczMDE1ODEsIDAuNzUxODg2NiwgMC40MDkyNTk2OF0sXG4gICAgICAgICAgIFswLjgyNDE5Mzg0LCAwLjQwNDc0MDkzLCAwLjUzNDY1MDQ0XSxcbiAgICAgICAgICAgWzAuMzQ1MzI3MzgsIDAuMjE2NzE3NzIsIDAuNTA4NTU0OTRdLFxuICAgICAgICAgICBbMC4wNDc3ODg4NiwgMC43OTUyOTU2LCAwLjY0OTA4MTk1XSxcbiAgICAgICAgICAgWzAuODgwNzM5MiwgMC4wOTU3MTEzNSwgMC43OTEwODgyXVxuICAgICAgICAgXVxuICAgICAgIF1dO1xuXG4gICAgICAgY29uc3QgZXhwZWN0ZWQgPSBbW1xuICAgICAgICAgW1xuICAgICAgICAgICBbMC4wNzM5ODE0MSwgMC40OTI4NzUxLCAwLjA4NjMzNTE0XSxcbiAgICAgICAgICAgWzAuNjQ2ODczMSwgMC4wNzI4OTE1MiwgMC4zNzc1OTI4M10sXG4gICAgICAgICAgIFswLjMzNzQ0Mjg1LCAwLjQ3NTE4NjIsIDAuNDkxOTkyNTNdLFxuICAgICAgICAgICBbMC40NzcwMzc0LCAwLjI2MzIxODEsIDAuNTA0MDYwNTddLFxuICAgICAgICAgICBbMC41ODcxODM2NSwgMC4zMDM2ODY3NywgMC4yOTI0MDY2XVxuICAgICAgICAgXSxcblxuICAgICAgICAgW1xuICAgICAgICAgICBbMC40NTg1MDY2NywgMC4yMTU2MzQ4MSwgMC4zNTcyOTQ0N10sXG4gICAgICAgICAgIFswLjYxMDgyNTksIDAuMzc1MTc4MjUsIDAuMTk4NDI4MDhdLFxuICAgICAgICAgICBbMC4zMzcwNjY1LCAwLjEwNjA2MDk3LCAwLjI5MDcwMDg1XSxcbiAgICAgICAgICAgWzAuNTIxNDE4MDQsIDAuNTc2OTUzMiwgMC4wMDc1Mjk1Ml0sXG4gICAgICAgICAgIFswLjMwNDk1OTM2LCAwLjE1MjUzNzM4LCAwLjY0Mjg0NjNdXG4gICAgICAgICBdLFxuXG4gICAgICAgICBbXG4gICAgICAgICAgIFswLjA5MjA5MDkzLCAwLjQ2NjA3ODU1LCAwLjQ2NDc3NDRdLFxuICAgICAgICAgICBbMC41MTk0OTM0NiwgMC40MzIxNTI0NSwgMC41MjA2NDQ0XSxcbiAgICAgICAgICAgWzAuNTE0MzUzNSwgMC4zMTYwNjYzNSwgMC40NTkwNTIxMl0sXG4gICAgICAgICAgIFswLjUwNjExNzk0LCAwLjU0MTYzMTY0LCAwLjMxODU0NDU0XSxcbiAgICAgICAgICAgWzAuNjU0Nzg2NjUsIDAuMjMwMjUxNDYsIDAuMTE2NDQ2NjRdXG4gICAgICAgICBdLFxuXG4gICAgICAgICBbXG4gICAgICAgICAgIFswLjI1NTA3NTY2LCAwLjMzOTQ4MzIsIDAuNDQ1MDM0M10sXG4gICAgICAgICAgIFswLjY4MjQ3Mjc3LCAwLjA2NzMzNzQ1LCAwLjAzOTY3MDYzXSxcbiAgICAgICAgICAgWzAuMDQxODA1MzIsIDAuNDU4Njc4NTcsIDAuNTg3NzE0XSxcbiAgICAgICAgICAgWzAuMjQyNzM4MDIsIDAuNTQ2OTEzLCAwLjI3Mjc3MjEzXSwgWzAuMDk3OTU5LCAwLjU3NjExODksIDAuNTI1NjIxXVxuICAgICAgICAgXSxcblxuICAgICAgICAgW1xuICAgICAgICAgICBbMC4wNTUzODI0NiwgMC41NzAzMDU2NSwgMC4zMTA0MjMyOF0sXG4gICAgICAgICAgIFswLjU2NDg2NTk1LCAwLjI3NzM5MTUyLCAwLjM2NjQyNTc1XSxcbiAgICAgICAgICAgWzAuMjg5Mjk5MSwgMC4xODE1NTU5NCwgMC40MjYwNDM0OF0sXG4gICAgICAgICAgIFswLjAzMzMyNzc1LCAwLjU1NDYzNTc2LCAwLjQ1MjY2N10sXG4gICAgICAgICAgIFswLjU2NzI1MzY1LCAwLjA2MTY0NDM3LCAwLjUwOTUxMjU0XVxuICAgICAgICAgXVxuICAgICAgIF1dO1xuXG4gICAgICAgY29uc3QgeCA9IHRmLnRlbnNvcjRkKGlucHV0LCBbMSwgNSwgNSwgM10pO1xuICAgICAgIGNvbnN0IHJhZGl1cyA9IDI7XG5cbiAgICAgICBjb25zdCByZXN1bHQgPSB4LmxvY2FsUmVzcG9uc2VOb3JtYWxpemF0aW9uKHJhZGl1cyk7XG5cbiAgICAgICBleHBlY3RBcnJheXNDbG9zZShhd2FpdCByZXN1bHQuZGF0YSgpLCBmbGF0dGVuKGV4cGVjdGVkKSk7XG4gICAgIH0pO1xuXG4gIGl0KCd0aHJvd3Mgd2hlbiBwYXNzZWQgYSBub24tdGVuc29yJywgKCkgPT4ge1xuICAgIGNvbnN0IGUgPVxuICAgICAgICAvQXJndW1lbnQgJ3gnIHBhc3NlZCB0byAnbG9jYWxSZXNwb25zZU5vcm1hbGl6YXRpb24nIG11c3QgYmUgYSBUZW5zb3IvO1xuICAgIGV4cGVjdCgoKSA9PiB0Zi5sb2NhbFJlc3BvbnNlTm9ybWFsaXphdGlvbih7fSBhcyB0Zi5UZW5zb3IzRCkpXG4gICAgICAgIC50b1Rocm93RXJyb3IoZSk7XG4gIH0pO1xuXG4gIGl0KCdncmFkaWVudCB3aXRoIDNEIGlucHV0JywgYXN5bmMgKCkgPT4ge1xuICAgIGNvbnN0IGlucHV0ID0gW1xuICAgICAgW1xuICAgICAgICBbXG4gICAgICAgICAgMC45NTc4Mjc1NywgMC4xMjg5MjY4NywgMC42MzYyNDY2OCwgMC43MDE2MDczNSwgMC43NzM3NjI1OCxcbiAgICAgICAgICAwLjU0MTY2MTE0LCAwLjcxMTcyNTM1LCAwLjY1MDg3NDk3XG4gICAgICAgIF0sXG4gICAgICAgIFtcbiAgICAgICAgICAwLjkxODcyMTA4LCAwLjM4ODQ2ODg2LCAwLjM3ODQ3NzkzLCAwLjUwNDc3NjI0LCAwLjQyMTU0NjIyLFxuICAgICAgICAgIDAuNDMzMTA5MTYsIDAuMzYyNTM4MjIsIDAuMDc1NzYxNTZcbiAgICAgICAgXSxcbiAgICAgICAgW1xuICAgICAgICAgIDAuNDg2NjIyNTcsIDAuNDE1NDAzNiwgMC44MTcwNDAzMiwgMC45MTY2MDQxNiwgMC44NzY3MTU0MiwgMC42NDIxNTkzNCxcbiAgICAgICAgICAwLjI5OTMzNzUxLCAwLjkwNjcxMTM0XG4gICAgICAgIF1cbiAgICAgIF0sXG5cbiAgICAgIFtcbiAgICAgICAgW1xuICAgICAgICAgIDAuNjIwODk5MiwgMC42MDg0NzE2MywgMC40MTQ3NTc2MSwgMC4yMTI3NzEzLCAwLjY1MzA2OTE0LCAwLjEzOTIzOTc5LFxuICAgICAgICAgIDAuMzIwMDM2NDEsIDAuMjgxODM5NzNcbiAgICAgICAgXSxcbiAgICAgICAgW1xuICAgICAgICAgIDAuMDQ3NTE1NzUsIDAuMjY4NzAxNTUsIDAuNDUxNTAzMDQsIDAuNTg2NzgxODYsIDAuOTkxMTg5MjQsXG4gICAgICAgICAgMC41ODg3ODIzMSwgMC4zMDkxMzE5OCwgMC4xODgzNjYxN1xuICAgICAgICBdLFxuICAgICAgICBbXG4gICAgICAgICAgMC4xNjE2NjQ2MSwgMC41NjMyMjc0MiwgMC42NzkwODk1NSwgMC4yMjY5NTQ3LCAwLjM4NDkxMjczLCAwLjk3MTEzNzUyLFxuICAgICAgICAgIDAuNTEyMTA5MTYsIDAuNjk0MzA0MzVcbiAgICAgICAgXVxuICAgICAgXSxcblxuICAgICAgW1xuICAgICAgICBbXG4gICAgICAgICAgMC4wNjYyNTQ5NywgMC4xMzAxMTE4MSwgMC41OTIwMjkyMSwgMC44ODg3MTU5OCwgMC42MzY2MzIyLCAwLjQ3OTExMzU4LFxuICAgICAgICAgIDAuOTY1MzA4NDMsIDAuNzQyNTk0NzJcbiAgICAgICAgXSxcbiAgICAgICAgW1xuICAgICAgICAgIDAuNjI2NjA3MTgsIDAuMDQ0NTI4NiwgMC4xODQzMDI1NywgMC43Njg2MzY0NywgMC44NzUxMTg0OSwgMC41MzU4ODgwOCxcbiAgICAgICAgICAwLjI3OTgwNjg1LCAwLjMwMjgxOTk3XG4gICAgICAgIF0sXG4gICAgICAgIFtcbiAgICAgICAgICAwLjczOTg3MDY3LCAwLjkxMDM0ODQyLCAwLjI2MjQxMDA0LCAwLjcyODMyNzUxLCAwLjc4OTc0MzQyLFxuICAgICAgICAgIDAuNTA3NTE1NDMsIDAuMDU0MzQ2NDQsIDAuODIzMTUyM1xuICAgICAgICBdXG4gICAgICBdXG4gICAgXTtcblxuICAgIGNvbnN0IGV4cGVjdGVkID0gW1tcbiAgICAgIFtcbiAgICAgICAgW1xuICAgICAgICAgIDAuMjc1NTI2NTgsIDAuNTI0MTQ2NjgsIDAuMTExMzc0OTQsIDAuMjQ5MjgwNzQsIDAuMDcyMTU0OTcsXG4gICAgICAgICAgMC4xNjIxMDUxMSwgMC4xOTI3NzI0MiwgMC4zODY3MjI2MlxuICAgICAgICBdLFxuICAgICAgICBbXG4gICAgICAgICAgMC4yMzMxNDM3OCwgMC4zODE4MTY0NSwgMC4zMDQ3MDcyOSwgMC4zNTE4MDcwNiwgMC4zNzc5MzE2NSxcbiAgICAgICAgICAwLjQxNDUwOTgzLCAwLjYwMDQ0NTAzLCAwLjgzNjA1OTMzXG4gICAgICAgIF0sXG4gICAgICAgIFtcbiAgICAgICAgICAwLjUxODAxMjY0LCAwLjM4NTE3ODgzLCAwLjAyOTM0Nzg4LCAwLjAzMTAyMzU1LCAwLjA4MjIyMzMzLFxuICAgICAgICAgIDAuMDk3NDY2MjUsIDAuNDE1MTcyNywgMC4yOTkzNjIwNlxuICAgICAgICBdXG4gICAgICBdLFxuXG4gICAgICBbXG4gICAgICAgIFtcbiAgICAgICAgICAwLjM3MDU5ODczLCAwLjMyNDYzNjg1LCAwLjI2NjExNjA4LCAwLjU0MjI4Mzg5LCAwLjMwNzMzMDU1LFxuICAgICAgICAgIDAuNjYzOTI0MjgsIDAuNTU2MjkyOTUsIDAuNzkwNDk2NDFcbiAgICAgICAgXSxcbiAgICAgICAgW1xuICAgICAgICAgIDAuODcxNjI1MDEsIDAuNjgxMjkzMzcsIDAuMzU3OTM1OTcsIDAuMTg3OTc5NjEsIC0wLjAzNjYwOTg1LFxuICAgICAgICAgIDAuMjMyMzU1NTksIDAuNDgxODQxNTYsIDAuNzY0MTc0NDZcbiAgICAgICAgXSxcbiAgICAgICAgW1xuICAgICAgICAgIDAuNjU4OTM2NjgsIDAuNDEwNTk0MTcsIDAuMjYyNTQyMjgsIDAuNDA2OTY3NzYsIDAuMzMzMDM1OCwgMC4wMTc4OTY5MixcbiAgICAgICAgICAwLjMxNjIxOTksIDAuMjg4NjcwMTJcbiAgICAgICAgXVxuICAgICAgXSxcblxuICAgICAgW1xuICAgICAgICBbXG4gICAgICAgICAgMC44Mzg4MDkzNywgMC42MjU5NDk5OCwgMC4zMjQ2OTgsIDAuMTMwNDY0MzUsIDAuMDk4NTg2NTQsIDAuMTc4NTE1ODcsXG4gICAgICAgICAgMC4wOTA2NzIwMywgMC4zMDc0ODAxNlxuICAgICAgICBdLFxuICAgICAgICBbXG4gICAgICAgICAgMC41NzIxMzg5NywgMC42NzcxMDQ1MywgMC40NTM4NTI3NCwgMC4xOTk1MTI5NiwgMC4wNzM3MTA0MSxcbiAgICAgICAgICAwLjIwMTQxNTYzLCAwLjUxMzYyNjM0LCAwLjcxNjMzMjVcbiAgICAgICAgXSxcbiAgICAgICAgW1xuICAgICAgICAgIDAuMzM2NjgyNDQsIDAuMDk2OTYzMjksIDAuMzM1MDAxMjYsIDAuMDg5NDgwMzYsIDAuMjY1MTIxODIsXG4gICAgICAgICAgMC4xOTU5Mzc4NiwgMC41OTE0NDE2OSwgMC4zNzk0NDRcbiAgICAgICAgXVxuICAgICAgXVxuICAgIF1dO1xuICAgIGNvbnN0IHJhZGl1cyA9IDIuMDtcbiAgICBjb25zdCBiaWFzID0gMS4wO1xuICAgIGNvbnN0IGFscGhhID0gMS4wO1xuICAgIGNvbnN0IGJldGEgPSAwLjU7XG5cbiAgICBjb25zdCB0ID0gdGYudGVuc29yM2QoaW5wdXQpO1xuICAgIGNvbnN0IGR5ID0gdGYub25lc0xpa2UodCk7XG5cbiAgICBjb25zdCBncmFkaWVudHMgPSB0Zi5ncmFkKFxuICAgICAgICAodDogdGYuVGVuc29yM0QpID0+XG4gICAgICAgICAgICB0Zi5sb2NhbFJlc3BvbnNlTm9ybWFsaXphdGlvbih0LCByYWRpdXMsIGJpYXMsIGFscGhhLCBiZXRhKSkodCwgZHkpO1xuXG4gICAgZXhwZWN0QXJyYXlzRXF1YWwoZ3JhZGllbnRzLnNoYXBlLCB0LnNoYXBlKTtcbiAgICBleHBlY3RBcnJheXNDbG9zZShhd2FpdCBncmFkaWVudHMuZGF0YSgpLCBmbGF0dGVuKGV4cGVjdGVkKSk7XG4gIH0pO1xuXG4gIGl0KCdncmFkaWVudCB3aXRoIGNsb25lcycsICgpID0+IHtcbiAgICBjb25zdCB0ID0gdGYuemVyb3MoWzMsIDMsIDhdKTtcbiAgICBjb25zdCByYWRpdXMgPSAyLjA7XG4gICAgY29uc3QgYmlhcyA9IDEuMDtcbiAgICBjb25zdCBhbHBoYSA9IDEuMDtcbiAgICBjb25zdCBiZXRhID0gMC41O1xuICAgIGNvbnN0IGR0ID0gdGYuZ3JhZChcbiAgICAgICAgKHQ6IHRmLlRlbnNvcjNEKSA9PlxuICAgICAgICAgICAgdGYubG9jYWxSZXNwb25zZU5vcm1hbGl6YXRpb24odC5jbG9uZSgpLCByYWRpdXMsIGJpYXMsIGFscGhhLCBiZXRhKVxuICAgICAgICAgICAgICAgIC5jbG9uZSgpKSh0KTtcblxuICAgIGV4cGVjdEFycmF5c0VxdWFsKGR0LnNoYXBlLCB0LnNoYXBlKTtcbiAgfSk7XG5cbiAgaXQoJ2dyYWRpZW50IHdpdGggNEQgaW5wdXQnLCBhc3luYyAoKSA9PiB7XG4gICAgY29uc3QgaW5wdXQgPSBbXG4gICAgICBbXG4gICAgICAgIFtcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjU2NTk4MjcsIDAuNTcwMDAzMjcsIDAuNzU1NTU2MjMsIDAuODk4NDMzMzMsIDAuNTUxMjAxOTQsXG4gICAgICAgICAgICAwLjUzNTMxNzE4LCAwLjU2NDAyODM4LCAwLjk1NDgxMzg0XG4gICAgICAgICAgXSxcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjU3MzM0NjYxLCAwLjY1MTcyOTU4LCAwLjc1Nzk0MTM3LCAwLjgwNzY0OTM3LCAwLjM3NjYxNixcbiAgICAgICAgICAgIDAuOTI3MjYzNjIsIDAuMzY0MjI3NTMsIDAuNjA1MzUzOTVcbiAgICAgICAgICBdLFxuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuODI0MDQyNjgsIDAuMDEwNTQ3NjQsIDAuNDY0OTE3MywgMC45MTYzNzAwMywgMC44MjI4NzM0NywgMC4wNDM0NjgsXG4gICAgICAgICAgICAwLjQ0OTUzODU5LCAwLjkyMDU2NTg0XG4gICAgICAgICAgXVxuICAgICAgICBdLFxuXG4gICAgICAgIFtcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjY4NTgzMzY5LCAwLjUyNTM0MTYzLCAwLjUzMzI1OTI3LCAwLjM5NjA4MDk3LCAwLjkzMzc1MjMsXG4gICAgICAgICAgICAwLjM3Mzk3NDQ0LCAwLjgxMjEyNTU2LCAwLjU2OTdcbiAgICAgICAgICBdLFxuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuMzQyNzg3NzQsIDAuNTc2NTY2ODIsIDAuMjM1NjgzMiwgMC4wMjYzNjQ1NiwgMC40OTExMTQzOCxcbiAgICAgICAgICAgIDAuMTc5ODE2OTYsIDAuNjUzOTgwNDksIDAuNzAxMzI5MzVcbiAgICAgICAgICBdLFxuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuMTQyNDE3NjcsIDAuNjgzNzY1MDUsIDAuNjU0MTk4ODgsIDAuNjkzNjk0ODMsIDAuMjE0ODkxNDMsXG4gICAgICAgICAgICAwLjQ2MjM1MzQ3LCAwLjA1NTkyNDMsIDAuNjA2MTI4NTdcbiAgICAgICAgICBdXG4gICAgICAgIF0sXG5cbiAgICAgICAgW1xuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuNTk2Nzg0ODMsIDAuMDkzNjg1MzksIDAuMzAxNzQ0NywgMC4zNjg3MDgyNSwgMC42ODE0NTc4OCxcbiAgICAgICAgICAgIDAuNTIwNDg3NzksIDAuNDYxMzY2MDYsIDAuOTQxMTQzODdcbiAgICAgICAgICBdLFxuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuMzE1NjU2OSwgMC43NTI3NTI1NCwgMC4zMTk3MDI1MSwgMC4zMTU0MDQzLCAwLjYxMDg4MDE0LFxuICAgICAgICAgICAgMC4xMzM1OTQ4NywgMC45OTA0ODM2NCwgMC4zMzYyNTQyNFxuICAgICAgICAgIF0sXG4gICAgICAgICAgW1xuICAgICAgICAgICAgMC44MjEwMzU3NCwgMC41MjA2NjYyNCwgMC42MzYyOTI1OCwgMC40MjI5NDI1MiwgMC45MzIxNDI2MixcbiAgICAgICAgICAgIDAuNTcwNDEwMTMsIDAuNjYwODc4NzgsIDAuNzAxOTk5OVxuICAgICAgICAgIF1cbiAgICAgICAgXVxuICAgICAgXSxcblxuICAgICAgW1xuICAgICAgICBbXG4gICAgICAgICAgW1xuICAgICAgICAgICAgMC4yMTg5NDQzMSwgMC40MzA4NTI0MSwgMC43OTg4MzIwNiwgMC4xOTQ2MjIwNCwgMC42ODYyMzMxNixcbiAgICAgICAgICAgIDAuMDg3MDMwNTMsIDAuODIzODA3OTUsIDAuODU2MzQ2NzNcbiAgICAgICAgICBdLFxuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuNDUwMTE0MDEsIDAuNzAzMTIwODMsIDAuODYzMTk3OTIsIDAuODMyMDUyOTUsIDAuNjcxMDk3ODcsXG4gICAgICAgICAgICAwLjgyMDgxMjIzLCAwLjQ2NTU2NTMyLCAwLjQ2NDA4MzMxXG4gICAgICAgICAgXSxcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjA3MDI4NDYxLCAwLjAwMzg3NDMsIDAuNDQ2MTk1MjQsIDAuMDYxMTQwMywgMC45NjM3MzM1NSxcbiAgICAgICAgICAgIDAuODA1NjE1NTQsIDAuNDI0MjgyNDMsIDAuNDY4OTcxMTNcbiAgICAgICAgICBdXG4gICAgICAgIF0sXG5cbiAgICAgICAgW1xuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuMjEwMDY4OTQsIDAuNDg3NjQ4NjEsIDAuMzY4NDI2MzIsIDAuMjMwMzAwMzEsIDAuNjk2ODU1MDcsXG4gICAgICAgICAgICAwLjMxNzA3NDc4LCAwLjY4NjYyNzE1LCAwLjA2Mzk1MDNcbiAgICAgICAgICBdLFxuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuNTM5NDAyOTYsIDAuNTA3Nzc0MzUsIDAuMTI2MjUzMDEsIDAuMTIzMjQxNTQsIDAuODkyMDUyMjksXG4gICAgICAgICAgICAwLjY5MzgwNjI5LCAwLjMzMTkxMTQ0LCAwLjgxMDAwMDc4XG4gICAgICAgICAgXSxcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjUyNjUwOTc2LCAwLjcxMjIwMzI2LCAwLjA3MjQ2MTYxLCAwLjA4ODc0NTQ3LCAwLjQyNTI4OTI3LFxuICAgICAgICAgICAgMC4zNjMyMDU3OSwgMC41NDA1NTYxOSwgMC43OTM0MjMxOFxuICAgICAgICAgIF1cbiAgICAgICAgXSxcblxuICAgICAgICBbXG4gICAgICAgICAgW1xuICAgICAgICAgICAgMC43NTkxNjYzNiwgMC43NDQ5OTQyOCwgMC43Njg3NzM1NiwgMC44NzIxMDkxNywgMC45MzA0MDk5MSxcbiAgICAgICAgICAgIDAuNDk0OTE5NDIsIDAuNzA4MDE5ODUsIDAuMTQ5MDE3MjFcbiAgICAgICAgICBdLFxuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuMjcwMzc4MzUsIDAuODkzMDIwNzUsIDAuNjkxNDcyNDEsIDAuMjMwNDQ5OTEsIDAuOTg5MTYzNjQsXG4gICAgICAgICAgICAwLjYwMTYxODQxLCAwLjYzNjkxMTUxLCAwLjU2NzU5OTc3XG4gICAgICAgICAgXSxcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjU2MzA3NzgxLCAwLjkyNzgyNDE0LCAwLjI1ODgwNzU0LCAwLjk4NTE4MTMzLCAwLjA0MDk3MzE5LFxuICAgICAgICAgICAgMC4yNDY0MDkwNiwgMC41NDU2NjE0NSwgMC45OTI2MTYwNlxuICAgICAgICAgIF1cbiAgICAgICAgXVxuICAgICAgXVxuICAgIF07XG5cbiAgICBjb25zdCBkeVZhbHMgPSBbXG4gICAgICBbXG4gICAgICAgIFtcbiAgICAgICAgICBbXG4gICAgICAgICAgICAxLjQwMzk0MjgyLCAtMS42ODk2Mjc4OSwgLTAuMjExMzQwNDksIDEuMTUwMTU3OTMsIDEuNTEyNDQzNzgsXG4gICAgICAgICAgICAwLjQyODQ0NjI2LCAtMi43MDEyMzI5MSwgMC4wNjQ0OTk3MVxuICAgICAgICAgIF0sXG4gICAgICAgICAgW1xuICAgICAgICAgICAgLTAuMjkwMzg1ODEsIDAuNjc1Njc2OTQsIDAuOTU2MTc0MzcsIC0xLjA3MzgzNjY4LCAwLjIwOTIwNDgyLFxuICAgICAgICAgICAgMC4zOTA1MDIxMywgLTAuODExMjQzNzEsIDIuNDIxNTgxOThcbiAgICAgICAgICBdLFxuICAgICAgICAgIFtcbiAgICAgICAgICAgIC0xLjAxMjM1MjM1LCAtMC42MzUxNDQzNSwgLTEuNDkwMTcyNjIsIC0wLjAxMjA1MTUxLCAwLjc4NDkyOTQ1LFxuICAgICAgICAgICAgLTAuMjAzMzA2NzksIC0yLjMxNDE5ODAyLCAtMC4zMTIyMDMwOFxuICAgICAgICAgIF1cbiAgICAgICAgXSxcblxuICAgICAgICBbXG4gICAgICAgICAgW1xuICAgICAgICAgICAgMC4wNzA2MTk0NCwgLTAuNDY3MTYxMjcsIDAuOTEyMzI1MjYsIC0xLjMwNDQ0MjY0LCAtMC4wNzA4MDEwOSxcbiAgICAgICAgICAgIDAuMTMyMDc1MDEsIDAuMjY3MDEyODMsIC0wLjQ4OTQ2NTg5XG4gICAgICAgICAgXSxcbiAgICAgICAgICBbXG4gICAgICAgICAgICAtMC43NDk5NTc0NCwgLTAuNzk0NjY2MTcsIC0xLjAzNzkwNDk4LCAtMC4zMjIzNDUyNiwgMS4zMzM0NTcxMSxcbiAgICAgICAgICAgIDAuMTE4NjMwODEsIDEuOTMwMTA4MTksIDAuNDc4NTcxOTVcbiAgICAgICAgICBdLFxuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuMzc3MDI2ODMsIC0wLjc4MDQ0NTEsIDAuNDU4NjgxMTcsIDEuMDY5NjcyNTgsIC0wLjY1MzM2NTM3LFxuICAgICAgICAgICAgMC4zNTk0ODg3LCAwLjYyNTEyNjg0LCAwLjc3MDA5NzI2XG4gICAgICAgICAgXVxuICAgICAgICBdLFxuXG4gICAgICAgIFtcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjc2ODY1MDIzLCAxLjAwODkzMDIxLCAtMC4yNDQwODgxNiwgLTAuMzk0MzMzNiwgMC40NzA5NDI4NSxcbiAgICAgICAgICAgIC0yLjYxOTI2MjIyLCAxLjUyOTI5NDQ5LCAwLjc4NjIwMTNcbiAgICAgICAgICBdLFxuICAgICAgICAgIFtcbiAgICAgICAgICAgIC0xLjIwODc4Mzg2LCAtMC4yNjIyMjkzNSwgLTAuOTA3NjUyOCwgMC4wMzA3OTU3NywgLTAuMDE0Njc0ODYsXG4gICAgICAgICAgICAtMC4wNjk0OTYzNiwgMC4wNTQ2NjM0MiwgMS40NDg4MDUzM1xuICAgICAgICAgIF0sXG4gICAgICAgICAgW1xuICAgICAgICAgICAgMC4wNTYxMTg2MywgMC4xNTE0Mjc3OSwgMC43ODAyMDY1LCAtMS4yNjIzNDcxLCAwLjA5MTE5Nzk0LFxuICAgICAgICAgICAgLTAuMjAxMTA1MjgsIDAuMTc3MTU5NjgsIC0wLjQ4NDc2NTA4XG4gICAgICAgICAgXVxuICAgICAgICBdXG4gICAgICBdLFxuXG4gICAgICBbXG4gICAgICAgIFtcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjE1NDkyNTYsIDAuOTQ0NzI0MDIsIC0wLjcwMDMzMTE1LCAtMS4wNTc1MjgwMiwgLTAuNjMwMzU5NDcsXG4gICAgICAgICAgICAtMS4zNTY0MzExMywgLTAuMjcyMTE2OTMsIDIuMzM1NzY5NDFcbiAgICAgICAgICBdLFxuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuODEwNzA5MDYsIC0wLjU4MzUzNDU0LCAtMC4zMjUzODE3LCAyLjUzOTUzNTI4LCAtMS40MDA2MjE0MSxcbiAgICAgICAgICAgIDEuNzcyODA3NiwgLTAuNTk4NDk0ODMsIDEuNDk2NTA4MjRcbiAgICAgICAgICBdLFxuICAgICAgICAgIFtcbiAgICAgICAgICAgIC0wLjAwNjEwMDUyLCAtMi4yOTQzNDQxOSwgLTEuNzc5OTUxMjEsIC0wLjY2MzU0MDg0LCAtMC43MDY3Njc3NCxcbiAgICAgICAgICAgIC0wLjgxNTcwMDExLCAtMS4zMDgyMTAzNywgMC40MDk5NzAwN1xuICAgICAgICAgIF1cbiAgICAgICAgXSxcblxuICAgICAgICBbXG4gICAgICAgICAgW1xuICAgICAgICAgICAgLTEuMDIwMTM0NjksIC0wLjc0MTk4ODA2LCAtMC44MjY3NzI1MSwgLTAuMDA4OTAxNzksIC0xLjYyMTk2MzM4LFxuICAgICAgICAgICAgLTAuNTA5NTQyNywgMS4yNjUwMTE3OSwgMC4xMjkzMTQ4NVxuICAgICAgICAgIF0sXG4gICAgICAgICAgW1xuICAgICAgICAgICAgLTEuMTQ3NjM1NDYsIDAuMTEwMTE2OTYsIC0wLjIzMzEyNTA4LCAwLjI5NzMwMDk2LCAtMC40OTEzODM5NCxcbiAgICAgICAgICAgIC0wLjI3MDEyMzYzLCAtMC4xNTk4NzUzMywgLTEuODQyNzc5MjhcbiAgICAgICAgICBdLFxuICAgICAgICAgIFtcbiAgICAgICAgICAgIC0wLjAzODE2NDU5LCAtMC43MzUxNzg3NywgLTIuMDA0NzY4ODUsIDAuNDcxOTI0OTYsIC0wLjI3Mzk1NzUyLFxuICAgICAgICAgICAgMC45OTgwNjEyNCwgMS41NDQzOTc0NywgLTEuMDIwMTY2NzVcbiAgICAgICAgICBdXG4gICAgICAgIF0sXG5cbiAgICAgICAgW1xuICAgICAgICAgIFtcbiAgICAgICAgICAgIC0xLjI3ODMxMjA5LCAtMC42OTYxMzg1LCAtMC43MzcxMzk5NCwgLTEuOTc5NTQ3MzgsIDAuMzkxMDg2NTIsXG4gICAgICAgICAgICAtMC40NjE1MjUzOCwgMS44MjU1MzcyLCAyLjE4MTE5MDI1XG4gICAgICAgICAgXSxcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjU2MzIyMjgzLCAtMS41OTg1ODE3OSwgMS41NDEyNzQ5MSwgLTAuNTc2NjU5NTYsIC0xLjAwOTg1NjcsXG4gICAgICAgICAgICAwLjkzMjM5NjcxLCAwLjI1MjMxNjk4LCAtMC43MzQ2MDA5XG4gICAgICAgICAgXSxcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjQxNjE0OTk0LCAtMS4yMDEwMzA4NSwgMC40MzMwMzAxLCAtMS4yMzM0ODQwMywgLTAuNDYxMTcyMTMsXG4gICAgICAgICAgICAtMC4zNzgwMTI2LCAwLjM1NDQ5NTYxLCAtMC42MDEyOTI0OVxuICAgICAgICAgIF1cbiAgICAgICAgXVxuICAgICAgXVxuICAgIF07XG5cbiAgICBjb25zdCBkZXB0aFJhZGl1cyA9IDE7XG4gICAgY29uc3QgYmlhcyA9IDE7XG4gICAgY29uc3QgYWxwaGEgPSAxO1xuICAgIGNvbnN0IGJldGEgPSAwLjc1O1xuXG4gICAgY29uc3QgZXhwZWN0ZWQgPSBbXG4gICAgICBbXG4gICAgICAgIFtcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjg4NzMyMDY0LCAtMC45ODU5NzM0MiwgLTAuMDA1NjkyNjksIDAuMDk1NjEwNTcsIDAuNDIyNTUzNzUsXG4gICAgICAgICAgICAwLjMwMjg2Mzc4LCAtMS4xNzEwNDc4MSwgMC40NDc2OTk2MVxuICAgICAgICAgIF0sXG4gICAgICAgICAgW1xuICAgICAgICAgICAgLTAuMjIzMjk4ODUsIDAuMTkyNzE4NDYsIDAuNDE0NTQwNzEsIC0wLjUwNjc0OTU3LCAwLjE0NjYwOTQ2LFxuICAgICAgICAgICAgMC4xNTkxODM3LCAtMC44MzcwNzA3NiwgMS4xOTE3NzIzNFxuICAgICAgICAgIF0sXG4gICAgICAgICAgW1xuICAgICAgICAgICAgLTAuMjY3Mjg4MTgsIC0wLjM4NDczMTIsIC0wLjcyODE4NDg4LCAwLjA5MDQwODM3LCAwLjI0MDIzNjg4LFxuICAgICAgICAgICAgLTAuMTE1NDU1ODEsIC0xLjA5MzQxMjg4LCAwLjMzOTMwNjY4XG4gICAgICAgICAgXVxuICAgICAgICBdLFxuXG4gICAgICAgIFtcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjEwMDc5MDg2LCAtMC4zODE4NDUzNiwgMC42MDkxODk0NSwgLTAuNzI2NzgyMiwgMC4xMzg2NzMzNSxcbiAgICAgICAgICAgIDAuMDM1MjYyMDIsIDAuMTcyNzA0OTksIC0wLjI3MDUzMzhcbiAgICAgICAgICBdLFxuICAgICAgICAgIFtcbiAgICAgICAgICAgIC0wLjM4MzQ0NDU4LCAtMC4xNTU4OTE0OSwgLTAuNjgxNjAwOTMsIC0wLjI3NjQ0Nzc3LCAwLjc5MzkyODU2LFxuICAgICAgICAgICAgLTAuMTQzODQzMzIsIDAuNjcxMjEwMTcsIC0wLjIzMTMwMjYyXG4gICAgICAgICAgXSxcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjMxMDY5MTQyLCAtMC4zOTg5NTI1NywgMC4xMTc1NTQ5OSwgMC4zOTQ4MTcwOCwgLTAuNTIzNDc2NixcbiAgICAgICAgICAgIDAuMjUxMTg1MywgMC40MDk1NTA3OSwgMC4zNDkyOTY2XG4gICAgICAgICAgXVxuICAgICAgICBdLFxuXG4gICAgICAgIFtcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjMyNjYwNTk1LCAwLjcyNDA1NjMsIC0wLjE4MTE3MzM1LCAtMC4yNjQ5ODYxLCAwLjY3NzgxNjAzLFxuICAgICAgICAgICAgLTEuNDYyNTAyNzIsIDAuODQ2NTk2MywgLTAuMDU0NjY3MDFcbiAgICAgICAgICBdLFxuICAgICAgICAgIFtcbiAgICAgICAgICAgIC0wLjcxNTgyMDY3LCAwLjIwODMxNzE2LCAtMC41MDc3ODIwNCwgMC4wNzI1Njc1NSwgLTAuMDA4OTM2NzksXG4gICAgICAgICAgICAtMC4wMzc5ODc4MywgLTAuMTg2MDQzMDUsIDAuNzU3NDc0MDZcbiAgICAgICAgICBdLFxuICAgICAgICAgIFtcbiAgICAgICAgICAgIC0wLjAwNTQwODMzLCAtMC4wNzY3NzIxNiwgMC40MTkzMDIwNSwgLTAuNjkyMzUzMTksIDAuMjA2MzEyOTEsXG4gICAgICAgICAgICAtMC4xMTk0NjMwMywgMC4xOTYwMTUyMSwgLTAuMjEyMzc2OThcbiAgICAgICAgICBdXG4gICAgICAgIF1cbiAgICAgIF0sXG5cbiAgICAgIFtcbiAgICAgICAgW1xuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuMDgwMDExMSwgMC42MDkyMjIwNSwgLTAuMzExNTU5NzcsIC0wLjQ2NDQ4MTMyLCAtMC4xNTkxMjcwMSxcbiAgICAgICAgICAgIC0wLjcyNDU1NTg1LCAtMC41NzI3Mjc1LCAwLjcxNzgwMDkyXG4gICAgICAgICAgXSxcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjUwNTY4MjM1LCAtMC4zMTU0NDE1MiwgLTAuNDA2MTgyODYsIDAuOTc5MDk0NjgsIC0xLjE1Mjg2NjEzLFxuICAgICAgICAgICAgMC44MTQ1Mzg2LCAtMC43Nzc1ODUzOSwgMC45Mzc5NDc0NVxuICAgICAgICAgIF0sXG4gICAgICAgICAgW1xuICAgICAgICAgICAgLTAuMDA1MzU1OTksIC0xLjk5MjY5MjU5LCAtMS4xNTM0Mzk1MiwgLTAuMzEwNTM2ODYsIDAuMDE2ODA2MzYsXG4gICAgICAgICAgICAwLjEwMTA5Mjk2LCAtMC42NjAyNjM5NiwgMC4zNTQ3NDkxN1xuICAgICAgICAgIF1cbiAgICAgICAgXSxcblxuICAgICAgICBbXG4gICAgICAgICAgW1xuICAgICAgICAgICAgLTAuNzQxMTMzMzMsIC0wLjIwNjI1OTQzLCAtMC40MzM5NTY4LCAwLjIxNTE3MzY4LCAtMC41NzM0NDU4LFxuICAgICAgICAgICAgLTAuMjM0ODEzNjMsIDAuNTM4NTUzODksIDAuMDU4NjA2MjZcbiAgICAgICAgICBdLFxuICAgICAgICAgIFtcbiAgICAgICAgICAgIC0wLjYxNDM1Nzk1LCAwLjI5MjkwODM0LCAtMC4xOTYzOTE0NSwgMC4yMDkzMDEzNCwgLTAuMDg4ODAxNzksXG4gICAgICAgICAgICAwLjAyMjA5ODg3LCAwLjIxNDI3NDgyLCAtMC41MTY5NjY0NlxuICAgICAgICAgIF0sXG4gICAgICAgICAgW1xuICAgICAgICAgICAgMC4xMzAzNjUzNiwgLTAuMTkwNzkyMzcsIC0xLjQzOTQxNTQ1LCAwLjQyNzg5NjY1LCAtMC4yOTczMjcwNyxcbiAgICAgICAgICAgIDAuNTIzNTQ4MTMsIDAuNzg4OTMsIC0wLjU5OTkyODYyXG4gICAgICAgICAgXVxuICAgICAgICBdLFxuXG4gICAgICAgIFtcbiAgICAgICAgICBbXG4gICAgICAgICAgICAtMC4zMjgzODMsIDAuMTU4MzA5NDksIDAuMTMxMTAxNDksIC0wLjQ5MjQyMywgMC40NjgyNzMxMyxcbiAgICAgICAgICAgIC0wLjU4OTUwNjMzLCAwLjU2NDIyNTQ0LCAxLjQ0OTI5NTc2XG4gICAgICAgICAgXSxcbiAgICAgICAgICBbXG4gICAgICAgICAgICAwLjQ2MTQxMDY0LCAtMC44MDY4MjI2NiwgMC45MjU2MjE3NSwgLTAuMjg4OTc0NTIsIC0wLjMwNTY3NDk3LFxuICAgICAgICAgICAgMC41MDY0NjQ4NCwgMC4xNjQzOTUxOCwgLTAuMzg4NzgxODJcbiAgICAgICAgICBdLFxuICAgICAgICAgIFtcbiAgICAgICAgICAgIDAuNDEwMDQwNzQsIC0wLjM4NTkzMTI4LCAwLjQyODgxOTY2LCAtMC4yMjQ0MzQzNiwgLTAuMjQ1NzMyMjgsXG4gICAgICAgICAgICAtMC4yOTQxMjQ5LCAwLjMxMTE5NjAzLCAtMC4xNzkwMzk3OFxuICAgICAgICAgIF1cbiAgICAgICAgXVxuICAgICAgXVxuICAgIF07XG5cbiAgICBjb25zdCB0ID0gdGYudGVuc29yKGlucHV0KTtcbiAgICBjb25zdCBkeSA9IHRmLnRlbnNvcihkeVZhbHMpO1xuXG4gICAgY29uc3QgZ3JhZGllbnRzID0gdGYuZ3JhZChcbiAgICAgICAgdCA9PiB0Zi5sb2NhbFJlc3BvbnNlTm9ybWFsaXphdGlvbihcbiAgICAgICAgICAgIHQgYXMgVGVuc29yNEQsIGRlcHRoUmFkaXVzLCBiaWFzLCBhbHBoYSwgYmV0YSkpKHQsIGR5IGFzIFRlbnNvcjREKTtcblxuICAgIGV4cGVjdEFycmF5c0Nsb3NlKGF3YWl0IGdyYWRpZW50cy5kYXRhKCksIGZsYXR0ZW4oZXhwZWN0ZWQpKTtcbiAgfSk7XG59KTtcbiJdfQ==