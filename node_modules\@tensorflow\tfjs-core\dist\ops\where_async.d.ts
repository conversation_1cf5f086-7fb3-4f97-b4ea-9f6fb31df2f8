/// <amd-module name="@tensorflow/tfjs-core/dist/ops/where_async" />
import { Tensor, Tensor2D } from '../tensor';
import { TensorLike } from '../types';
/**
 * Returns the coordinates of true elements of condition.
 *
 * The coordinates are returned in a 2-D tensor where the first dimension (rows)
 * represents the number of true elements, and the second dimension (columns)
 * represents the coordinates of the true elements. Keep in mind, the shape of
 * the output tensor can vary depending on how many true values there are in
 * input. Indices are output in row-major order. The resulting tensor has the
 * shape `[numTrueElems, condition.rank]`.
 *
 * This is analogous to calling the python `tf.where(cond)` without an x or y.
 *
 * ```js
 * const cond = tf.tensor1d([false, false, true], 'bool');
 * const result = await tf.whereAsync(cond);
 * result.print();
 * ```
 *
 * @doc {heading: 'Operations', subheading: 'Logical'}
 */
declare function whereAsync_(condition: Tensor | TensorLike): Promise<Tensor2D>;
export declare const whereAsync: typeof whereAsync_;
export {};
