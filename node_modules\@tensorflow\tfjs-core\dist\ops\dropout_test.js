/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../index';
import { ALL_ENVS, describeWithFlags } from '../jasmine_util';
import { expectArraysClose } from '../test_util';
import { tensor1d, tensor2d } from './ops';
describeWithFlags('dropout', ALL_ENVS, () => {
    it('x 1d array, rate 0', async () => {
        const x = tensor1d([1, 2, 2, 1]);
        const rate = 0;
        const output = tf.dropout(x, rate);
        expect(output.dtype).toEqual(x.dtype);
        expect(output.shape).toEqual(x.shape);
        expectArraysClose(await x.data(), await output.data());
    });
    it('x 1d array, rate 0.75', async () => {
        const x = tensor1d([1, 2, 2, 1]);
        const rate = 0.75;
        const output = tf.dropout(x, rate);
        expect(output.dtype).toEqual(x.dtype);
        expect(output.shape).toEqual(x.shape);
        const xValues = await x.data();
        const outputValues = await output.data();
        for (let i = 0; i < xValues.length; i++) {
            if (outputValues[i] !== 0) {
                expect(outputValues[i]).toBeCloseTo(1 / (1 - rate) * xValues[i]);
            }
        }
    });
    it('x 2d array, rate 0', async () => {
        const x = tensor2d([1, 5, 2, 4, 3, 6], [2, 3]);
        const rate = 0;
        const output = tf.dropout(x, rate);
        expect(output.dtype).toEqual(x.dtype);
        expect(output.shape).toEqual(x.shape);
        expectArraysClose(await x.data(), await output.data());
    });
    it('x 2d array, rate 0.75', async () => {
        const x = tensor2d([1, 5, 2, 4, 3, 6], [2, 3]);
        const rate = 0.75;
        const output = tf.dropout(x, rate);
        expect(output.dtype).toEqual(x.dtype);
        expect(output.shape).toEqual(x.shape);
        const xValues = await x.data();
        const outputValues = await output.data();
        for (let i = 0; i < xValues.length; i++) {
            if (outputValues[i] !== 0) {
                expect(outputValues[i]).toBeCloseTo(1 / (1 - rate) * xValues[i]);
            }
        }
    });
    it('x 1d array, rate 0.75, with noise shape length = 1', async () => {
        const x = tensor1d([1, 2, 2, 1]);
        const rate = 0.75;
        const noiseShape = [1];
        const output = tf.dropout(x, rate, noiseShape);
        expect(output.dtype).toEqual(x.dtype);
        expect(output.shape).toEqual(x.shape);
        const xValues = await x.data();
        const outputValues = await output.data();
        const maskedOutput = outputValues[0];
        for (let i = 0; i < xValues.length; i++) {
            if (maskedOutput === 0) {
                expect(outputValues[i]).toBe(maskedOutput);
            }
            if (outputValues[i] !== 0) {
                expect(outputValues[i]).toBeCloseTo(1 / (1 - rate) * xValues[i]);
            }
        }
    });
    it('x 2d array, rate 0.75, with noise shape length = 2', async () => {
        const x = tensor2d([1, 5, 2, 4, 3, 6], [2, 3]);
        const rate = 0.75;
        const noiseShape = [2, 1];
        const output = tf.dropout(x, rate, noiseShape);
        expect(output.dtype).toEqual(x.dtype);
        expect(output.shape).toEqual(x.shape);
        const xValues = await x.data();
        const outputValues = await output.data();
        for (let i = 0; i < x.shape[0]; i++) {
            const maskedOutput = outputValues[i * x.shape[1]];
            if (maskedOutput !== 0) {
                expect(maskedOutput)
                    .toBeCloseTo(1 / (1 - rate) * xValues[i * x.shape[1]]);
            }
            else {
                for (let j = 0; j < x.shape[1]; j++) {
                    expect(outputValues[i * x.shape[1] + j]).toBe(maskedOutput);
                }
            }
        }
    });
    it('broadcast noise shape', async () => {
        const x = tensor2d([1, 5, 2, 4, 3, 6], [2, 3]);
        const rate = 0.75;
        // broadcast noise shape, same output as using noiseShape [2, 1]
        const noiseShape = [1];
        const output = tf.dropout(x, rate, noiseShape);
        expect(output.dtype).toEqual(x.dtype);
        expect(output.shape).toEqual(x.shape);
        const xValues = await x.data();
        const outputValues = await output.data();
        for (let i = 0; i < x.shape[0]; i++) {
            const maskedOutput = outputValues[i * x.shape[1]];
            if (maskedOutput !== 0) {
                expect(maskedOutput)
                    .toBeCloseTo(1 / (1 - rate) * xValues[i * x.shape[1]]);
            }
            else {
                for (let j = 0; j < x.shape[1]; j++) {
                    expect(outputValues[i * x.shape[1] + j]).toBe(maskedOutput);
                }
            }
        }
    });
    it('x 1d array, rate 0.75, with seed', async () => {
        const x = tensor1d([1, 2, 2, 1]);
        const rate = 0.75;
        const seed = 23;
        const output = tf.dropout(x, rate, null, seed);
        expect(output.dtype).toEqual(x.dtype);
        expect(output.shape).toEqual(x.shape);
        const xValues = await x.data();
        const outputValues = await output.data();
        for (let i = 0; i < xValues.length; i++) {
            if (outputValues[i] !== 0) {
                expect(outputValues[i]).toBeCloseTo(1 / (1 - rate) * xValues[i]);
            }
        }
    });
    it('x TensorLike object', async () => {
        const x = [1.0, 2.0, 2.0, 1.0];
        const rate = 0;
        const output = tf.dropout(x, rate);
        expect(output.dtype).toEqual('float32');
        expect(output.shape).toEqual([4]);
        expectArraysClose(await output.data(), x);
    });
    it('throws when x.dtype != float32', async () => {
        const x = tensor1d([1, 2, 2, 1], 'int32');
        const rate = 0.75;
        expect(() => tf.dropout(x, rate)).toThrowError();
    });
    it('throws when rate is not in the range [0, 1)', async () => {
        const x = tensor1d([1, 2, 2, 1]);
        const rate = 1.5;
        expect(() => tf.dropout(x, rate)).toThrowError();
    });
});
//# sourceMappingURL=data:application/json;base64,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