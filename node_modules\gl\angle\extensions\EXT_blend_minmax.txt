Name

    EXT_blend_minmax

Name Strings

    GL_EXT_blend_minmax

Version

    Last Modified Date: September 17, 2009
    Version:            1.5

Number

    OpenGL Extension #37
    OpenGL ES Extension #65

Dependencies

    There is an interaction with OpenGL ES.

Overview

    Blending capability is extended by respecifying the entire blend
    equation.  While this document defines only two new equations, the
    BlendEquationEXT procedure that it defines will be used by subsequent
    extensions to define additional blending equations.

    The two new equations defined by this extension produce the minimum
    (or maximum) color components of the source and destination colors.
    Taking the maximum is useful for applications such as maximum projection
    in medical imaging.

Issues

    *   I've prefixed the ADD token with FUNC, to indicate that the blend
        equation includes the parameters specified by BlendFunc.  (The min
        and max equations don't.)  Is this necessary?  Is it too ugly?
        Is there a better way to accomplish the same thing?
    
New Procedures and Functions

    void BlendEquationEXT(enum mode);

New Tokens

    Accepted by the <mode> parameter of BlendEquationEXT:

        FUNC_ADD_EXT                     0x8006
        MIN_EXT                          0x8007
        MAX_EXT                          0x8008

    Accepted by the <pname> parameter of GetBooleanv, GetIntegerv,
    GetFloatv, and GetDoublev:

        BLEND_EQUATION_EXT               0x8009

Additions to Chapter 2 of the GL Specification (OpenGL Operation)

    None

Additions to Chapter 3 of the GL Specification (Rasterization)

    None

Additions to Chapter 4 of the GL Specification (Per-Fragment Operations
and the Framebuffer)

    The GL Specification defines a single blending equation.  This
    extension introduces a blend equation mode that is specified by calling
    BlendEquationEXT with one of three enumerated values.  The default
    value FUNC_ADD_EXT specifies that the blending equation defined in
    the GL Specification be used.  This equation is

        C' = (Cs * S) + (Cd * D)

             /  1.0     C' > 1.0
        C = (
             \   C'     C' <= 1.0

    where Cs and Cd are the source and destination colors, and S and D are
    as specified by BlendFunc.

    If BlendEquationEXT is called with <mode> set to MIN_EXT, the
    blending equation becomes

        C = min (Cs, Cd)

    Finally, if BlendEquationEXT is called with <mode> set to MAX_EXT, the
    blending equation becomes

        C = max (Cs, Cd)

    In all cases the blending equation is evaluated separately for each
    color component.

Additions to Chapter 5 of the GL Specification (Special Functions)

    None

Additions to Chapter 6 of the GL Specification (State and State Requests)

    None

Additions to the GLX Specification

    None

GLX Protocol

    A new GL rendering command is added. The following command is sent to the 
    server as part of a glXRender request:

        BlendEquationEXT
            2           8               rendering command length
            2           4097            rendering command opcode
            4           ENUM            mode

Dependencies on OpenGL ES

    If the GL is OpenGL ES, only the new MIN_EXT and MAX_EXT blend equations
    are introduced by this extension.  BlendEquationOES, FUNC_ADD_OES, and
    BLEND_EQUATION_OES are introduced by the OES_blend_subtract extension,
    which is required for this extension to operate.  Alternatively,
    OpenGL ES 2.0 is required, which introduces BlendEquation, FUNC_ADD, and
    BLEND_EQUATION without the suffixes.

    MIN_EXT and MAX_EXT should be added to Table 4.blendeq described in the
    OES_blend_subtract extension specification, and Table 4.1 of the OpenGL
    ES 2.0 specification.

    Mentions of GetDoublev, Begin/End, and GLX in this extension specification
    can be ignored for OpenGL ES.  Also, BlendEquationEXT and FUNC_ADD_EXT
    instead have the OES suffix courtesy of OES_blend_subtract, or no suffix
    courtesy of core OpenGL ES 2.0.

Errors

    INVALID_ENUM is generated by BlendEquationEXT if its single parameter
    is not FUNC_ADD_EXT, MIN_EXT, or MAX_EXT.

    INVALID_OPERATION is generated if BlendEquationEXT is executed between
    the execution of Begin and the corresponding execution to End.

New State

    Get Value           Get Command     Type    Initial Value   Attribute
    ---------           -----------     ----    -------------   ---------
    BLEND_EQUATION_EXT  GetIntegerv     Z3      FUNC_ADD_EXT    color-buffer

New Implementation Dependent State

    None

Revision History

    Version 1.5, September 17, 2009 (Jon Leech) -
        Merge into OpenGL Registry version of the extension and assign
        OpenGL ES extension number.
    Version 1.4, May 19, 2009 (Benj Lipchak) -
        Adapted for OpenGL ES.
    Version 1.3, May 31, 1995 -
        Last SGI revision.
