/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../index';
import { ALL_ENVS, describeWithFlags } from '../jasmine_util';
import { expectArraysClose } from '../test_util';
import { backend } from '../index';
describeWithFlags('where', ALL_ENVS, () => {
    it('Scalars.', async () => {
        const a = tf.scalar(10);
        const b = tf.scalar(20);
        const c = tf.scalar(1, 'bool');
        expectArraysClose(await tf.where(c, a, b).data(), [10]);
    });
    it('Invalid condition type', () => {
        const c = tf.tensor1d([1, 0, 1, 0], 'int32');
        const a = tf.tensor1d([10, 10, 10, 10], 'bool');
        const b = tf.tensor1d([20, 20, 20, 20], 'bool');
        const f = () => {
            tf.where(c, a, b);
        };
        expect(f).toThrowError();
    });
    it('Tensor1D', async () => {
        const c = tf.tensor1d([1, 0, 1, 0], 'bool');
        const a = tf.tensor1d([10, 10, 10, 10]);
        const b = tf.tensor1d([20, 20, 20, 20]);
        expectArraysClose(await tf.where(c, a, b).data(), [10, 20, 10, 20]);
    });
    it('Tensor1D different a/b shapes', () => {
        let c = tf.tensor1d([1, 0, 1, 0], 'bool');
        let a = tf.tensor1d([10, 10, 10]);
        let b = tf.tensor1d([20, 20, 20, 20]);
        let f = () => {
            tf.where(c, a, b);
        };
        expect(f).toThrowError();
        c = tf.tensor1d([1, 0, 1, 0], 'bool');
        a = tf.tensor1d([10, 10, 10, 10]);
        b = tf.tensor1d([20, 20, 20]);
        f = () => {
            tf.where(c, a, b);
        };
        expect(f).toThrowError();
    });
    it('Tensor1D different condition/a shapes', () => {
        const c = tf.tensor1d([1, 0, 1, 0], 'bool');
        const a = tf.tensor1d([10, 10, 10]);
        const b = tf.tensor1d([20, 20, 20]);
        const f = () => {
            tf.where(c, a, b);
        };
        expect(f).toThrowError();
    });
    it('Tensor2D', async () => {
        const c = tf.tensor2d([[1, 0], [0, 1]], [2, 2], 'bool');
        const a = tf.tensor2d([[10, 10], [10, 10]], [2, 2]);
        const b = tf.tensor2d([[5, 5], [5, 5]], [2, 2]);
        expectArraysClose(await tf.where(c, a, b).data(), [10, 5, 5, 10]);
    });
    it('Tensor2D different a/b shapes', () => {
        let c = tf.tensor2d([[1, 1], [0, 0]], [2, 2], 'bool');
        let a = tf.tensor2d([[5, 5, 5], [5, 5, 5]], [2, 3]);
        let b = tf.tensor2d([[4, 4], [4, 4]], [2, 2]);
        let f = () => {
            tf.where(c, a, b);
        };
        expect(f).toThrowError();
        c = tf.tensor2d([[1, 1], [0, 0]], [2, 2], 'bool');
        a = tf.tensor2d([[5, 5], [5, 5]], [2, 2]);
        b = tf.tensor2d([[4, 4, 4], [4, 4, 4]], [2, 3]);
        f = () => {
            tf.where(c, a, b);
        };
        expect(f).toThrowError();
    });
    it('Tensor2D different condition/a shapes', () => {
        const c = tf.tensor2d([[1, 0], [0, 1]], [2, 2], 'bool');
        const a = tf.tensor2d([[10, 10, 10], [10, 10, 10]], [2, 3]);
        const b = tf.tensor2d([[5, 5, 5], [5, 5, 5]], [2, 3]);
        const f = () => {
            tf.where(c, a, b);
        };
        expect(f).toThrowError();
    });
    it('broadcasting Tensor2D shapes', async () => {
        const c = tf.tensor1d([1, 0], 'bool');
        let a = tf.tensor2d([[10], [10]], [2, 1]);
        let b = tf.tensor2d([[5], [5]], [2, 1]);
        let res = tf.where(c, a, b);
        expect(res.shape).toEqual([2, 2]);
        expectArraysClose(await res.data(), [10, 5, 10, 5]);
        a = tf.tensor2d([[10, 10], [10, 10], [10, 10]], [3, 2]);
        b = tf.tensor2d([[5], [5], [5]], [3, 1]);
        res = tf.where(c, a, b);
        expect(res.shape).toEqual([3, 2]);
        expectArraysClose(await res.data(), [10, 5, 10, 5, 10, 5]);
    });
    it('Tensor3D', async () => {
        const c = tf.tensor3d([[[1], [0], [1]], [[0], [0], [0]]], [2, 3, 1], 'bool');
        const a = tf.tensor3d([[[5], [5], [5]], [[5], [5], [5]]], [2, 3, 1]);
        const b = tf.tensor3d([[[3], [3], [3]], [[3], [3], [3]]], [2, 3, 1]);
        expectArraysClose(await tf.where(c, a, b).data(), [5, 3, 5, 3, 3, 3]);
    });
    it('Tensor3D with scalar condition', async () => {
        const a = tf.ones([1, 3, 3]);
        const b = tf.zeros([1, 3, 3]);
        expectArraysClose(await tf.where(tf.ones([1], 'bool'), a, b).data(), [1, 1, 1, 1, 1, 1, 1, 1, 1]);
        expectArraysClose(await tf.where(tf.zeros([1], 'bool'), a, b).data(), [0, 0, 0, 0, 0, 0, 0, 0, 0]);
    });
    it('Tensor3D different a/b shapes', () => {
        const c = tf.tensor3d([[[1], [0], [1]], [[0], [0], [0]]], [2, 3, 1], 'bool');
        let a = tf.tensor3d([[[5], [5]], [[5], [5]]], [2, 2, 1]);
        let b = tf.tensor3d([[[3], [3], [3]], [[3], [3], [3]]], [2, 3, 1]);
        let f = () => {
            tf.where(c, a, b);
        };
        expect(f).toThrowError();
        a = tf.tensor3d([[[5], [5], [5]], [[5], [5], [5]]], [2, 3, 1]);
        b = tf.tensor3d([[[3], [3]], [[3], [3]]], [2, 2, 1]);
        f = () => {
            tf.where(c, a, b);
        };
        expect(f).toThrowError();
    });
    it('Tensor3D different condition/a shapes', () => {
        const c = tf.tensor3d([[[1], [0]], [[0], [0]]], [2, 2, 1], 'bool');
        const a = tf.tensor3d([[[5], [5], [5]], [[5], [5], [5]]], [2, 3, 1]);
        const b = tf.tensor3d([[[3], [3], [3]], [[3], [3], [3]]], [2, 3, 1]);
        const f = () => {
            tf.where(c, a, b);
        };
        expect(f).toThrowError();
    });
    it('broadcasting Tensor3D shapes', async () => {
        const c = tf.tensor1d([1, 0], 'bool');
        let a = tf.tensor3d([[[9]], [[9]], [[9]], [[9]]], [4, 1, 1]);
        let b = tf.tensor3d([[[8]], [[8]], [[8]], [[8]]], [4, 1, 1]);
        let res = tf.where(c, a, b);
        expect(res.shape).toEqual([4, 1, 2]);
        expectArraysClose(await res.data(), [9, 8, 9, 8, 9, 8, 9, 8]);
        a = tf.tensor2d([[9], [9]], [2, 1]);
        b = tf.tensor3d([[[8]], [[8]], [[8]], [[8]]], [4, 1, 1]);
        res = tf.where(c, a, b);
        expect(res.shape).toEqual([4, 2, 2]);
        expectArraysClose(await res.data(), [9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8]);
    });
    it('Tensor4D', async () => {
        const c = tf.tensor4d([1, 0, 1, 1], [2, 2, 1, 1], 'bool');
        const a = tf.tensor4d([7, 7, 7, 7], [2, 2, 1, 1]);
        const b = tf.tensor4d([3, 3, 3, 3], [2, 2, 1, 1]);
        expectArraysClose(await tf.where(c, a, b).data(), [7, 3, 7, 7]);
    });
    it('Tensor4D different a/b shapes', () => {
        const c = tf.tensor4d([1, 0, 1, 1], [2, 2, 1, 1], 'bool');
        const a = tf.tensor4d([7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [2, 3, 2, 1]);
        const b = tf.tensor4d([3, 3, 3, 3], [2, 2, 1, 1]);
        const f = () => {
            tf.where(c, a, b);
        };
        expect(f).toThrowError();
    });
    it('Tensor4D different condition/a shapes', () => {
        const c = tf.tensor4d([0, 0, 1, 0, 1, 1, 0, 0, 1, 0, 1, 1], [2, 3, 2, 1], 'bool');
        const a = tf.tensor4d([7, 7, 7, 7], [2, 2, 1, 1]);
        const b = tf.tensor4d([3, 3, 3, 3], [2, 2, 1, 1]);
        const f = () => {
            tf.where(c, a, b);
        };
        expect(f).toThrowError();
    });
    it('TensorLike', async () => {
        expectArraysClose(await tf.where(true, 10, 20).data(), [10]);
    });
    it('TensorLike Chained', async () => {
        const a = tf.scalar(10);
        expectArraysClose(await a.where(true, 20).data(), [10]);
    });
    it('int32', async () => {
        if (backend() && backend().floatPrecision() === 32) {
            // TODO: Use skip() instead when it is implemented
            const c = tf.tensor1d([1, 0, 0], 'bool');
            const a = tf.tensor1d([12345678, 10, 10], 'int32');
            const b = tf.tensor1d([20, 20, -12345678], 'int32');
            const res = tf.where(c, a, b);
            expect(res.dtype).toEqual('int32');
            expectArraysClose(await res.data(), [12345678, 20, -12345678]);
        }
    });
    it('throws when passed condition as a non-tensor', () => {
        expect(() => tf.where({}, tf.scalar(1, 'bool'), tf.scalar(1, 'bool')))
            .toThrowError(/Argument 'condition' passed to 'where' must be a Tensor/);
    });
    it('throws when passed a as a non-tensor', () => {
        expect(() => tf.where(tf.scalar(1, 'bool'), {}, tf.scalar(1, 'bool')))
            .toThrowError(/Argument 'a' passed to 'where' must be a Tensor/);
    });
    it('throws when passed b as a non-tensor', () => {
        expect(() => tf.where(tf.scalar(1, 'bool'), tf.scalar(1, 'bool'), {}))
            .toThrowError(/Argument 'b' passed to 'where' must be a Tensor/);
    });
    it('accepts a tensor-like object', async () => {
        const a = 10;
        const b = 20;
        const c = 1;
        expectArraysClose(await tf.where(c, a, b).data(), [10]);
    });
    it('1D gradient', async () => {
        const c = tf.tensor1d([1, 0, 1], 'bool');
        const a = tf.tensor1d([1, 2, 3]);
        const b = tf.tensor1d([4, 5, 6]);
        const dy = tf.tensor1d([1, 2, 3]);
        const grads = tf.grads((c, a, b) => tf.where(c, a, b));
        const [dc, da, db] = grads([c, a, b], dy);
        expectArraysClose(await dc.data(), [0, 0, 0]);
        expectArraysClose(await da.data(), [1, 0, 3]);
        expectArraysClose(await db.data(), [0, 2, 0]);
        expect(dc.shape).toEqual(c.shape);
        expect(da.shape).toEqual(a.shape);
        expect(db.shape).toEqual(b.shape);
    });
    it('gradient with clones', async () => {
        const c = tf.tensor1d([1, 0, 1], 'bool');
        const a = tf.tensor1d([1, 2, 3]);
        const b = tf.tensor1d([4, 5, 6]);
        const dy = tf.tensor1d([1, 2, 3]);
        const grads = tf.grads((c, a, b) => tf.where(c.clone(), a.clone(), b.clone()).clone());
        const [dc, da, db] = grads([c, a, b], dy);
        expectArraysClose(await dc.data(), [0, 0, 0]);
        expectArraysClose(await da.data(), [1, 0, 3]);
        expectArraysClose(await db.data(), [0, 2, 0]);
        expect(dc.shape).toEqual(c.shape);
        expect(da.shape).toEqual(a.shape);
        expect(db.shape).toEqual(b.shape);
    });
    it('2D gradient', async () => {
        const c = tf.tensor2d([1, 0, 1, 1, 1, 0], [2, 3], 'bool');
        const a = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const b = tf.tensor2d([7, 8, 9, 10, 11, 12], [2, 3]);
        const dy = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const grads = tf.grads((c, a, b) => tf.where(c, a, b));
        const [dc, da, db] = grads([c, a, b], dy);
        expectArraysClose(await dc.data(), [0, 0, 0, 0, 0, 0]);
        expectArraysClose(await da.data(), [1, 0, 3, 4, 5, 0]);
        expectArraysClose(await db.data(), [0, 2, 0, 0, 0, 6]);
        expect(dc.shape).toEqual(c.shape);
        expect(da.shape).toEqual(a.shape);
        expect(db.shape).toEqual(b.shape);
    });
    it('3D gradient', async () => {
        const c = tf.tensor3d([1, 1, 0, 1, 1, 0], [2, 3, 1], 'bool');
        const a = tf.tensor3d([1, 2, 3, 4, 5, 6], [2, 3, 1]);
        const b = tf.tensor3d([7, 8, 9, 10, 11, 12], [2, 3, 1]);
        const dy = tf.tensor3d([1, 2, 3, 4, 5, 6], [2, 3, 1]);
        const grads = tf.grads((c, a, b) => tf.where(c, a, b));
        const [dc, da, db] = grads([c, a, b], dy);
        expectArraysClose(await dc.data(), [0, 0, 0, 0, 0, 0]);
        expectArraysClose(await da.data(), [1, 2, 0, 4, 5, 0]);
        expectArraysClose(await db.data(), [0, 0, 3, 0, 0, 6]);
        expect(dc.shape).toEqual(c.shape);
        expect(da.shape).toEqual(a.shape);
        expect(db.shape).toEqual(b.shape);
    });
    it('4D gradient', async () => {
        const c = tf.tensor4d([1, 1, 0, 1], [2, 2, 1, 1], 'bool');
        const a = tf.tensor4d([1, 2, 3, 4], [2, 2, 1, 1]);
        const b = tf.tensor4d([5, 6, 7, 8], [2, 2, 1, 1]);
        const dy = tf.tensor4d([1, 2, 3, 4], [2, 2, 1, 1]);
        const grads = tf.grads((c, a, b) => tf.where(c, a, b));
        const [dc, da, db] = grads([c, a, b], dy);
        expectArraysClose(await dc.data(), [0, 0, 0, 0]);
        expectArraysClose(await da.data(), [1, 2, 0, 4]);
        expectArraysClose(await db.data(), [0, 0, 3, 0]);
        expect(dc.shape).toEqual(c.shape);
        expect(da.shape).toEqual(a.shape);
        expect(db.shape).toEqual(b.shape);
    });
});
//# sourceMappingURL=data:application/json;base64,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