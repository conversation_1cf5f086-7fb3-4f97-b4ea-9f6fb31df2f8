/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../../index';
import { ALL_ENVS, describeWithFlags } from '../../jasmine_util';
import { expectArraysClose } from '../../test_util';
describeWithFlags('fused matmul', ALL_ENVS, () => {
    it('fused A x B', async () => {
        const a = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const b = tf.tensor2d([0, 1, -3, 2, 2, 1], [3, 2]);
        const c = tf.fused.matMul({ a, b });
        expect(c.shape).toEqual([2, 2]);
        expectArraysClose(await c.data(), [0, 8, -3, 20]);
    });
    it('fused A x B with relu', async () => {
        const a = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const b = tf.tensor2d([0, 1, -3, 2, 2, 1], [3, 2]);
        const transposeA = false;
        const transposeB = false;
        const c = tf.fused.matMul({ a, b, transposeA, transposeB, bias: null, activation: 'relu' });
        expect(c.shape).toEqual([2, 2]);
        expectArraysClose(await c.data(), [0, 8, 0, 20]);
    });
    it('fused A x B with elu', async () => {
        const a = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const b = tf.tensor2d([0, 1, -3, 2, 2, 1], [3, 2]);
        const transposeA = false;
        const transposeB = false;
        const c = tf.fused.matMul({ a, b, transposeA, transposeB, bias: null, activation: 'elu' });
        expect(c.shape).toEqual([2, 2]);
        expectArraysClose(await c.data(), [0, 8, -0.9502, 20]);
    });
    it('fused A x B with relu6', async () => {
        const a = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const b = tf.tensor2d([0, 1, -3, 2, 2, 1], [3, 2]);
        const transposeA = false;
        const transposeB = false;
        const c = tf.fused.matMul({ a, b, transposeA, transposeB, bias: null, activation: 'relu6' });
        expect(c.shape).toEqual([2, 2]);
        expectArraysClose(await c.data(), [0, 6, 0, 6]);
    });
    it('fused A x B with prelu', async () => {
        const a = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const b = tf.tensor2d([0, 1, -3, 2, 2, 1], [3, 2]);
        const alpha = tf.tensor2d([0.5, 0.5], [1, 2]);
        const transposeA = false;
        const transposeB = false;
        const c = tf.fused.matMul({
            a,
            b,
            transposeA,
            transposeB,
            bias: null,
            activation: 'prelu',
            preluActivationWeights: alpha
        });
        expect(c.shape).toEqual([2, 2]);
        expectArraysClose(await c.data(), [0, 8, -1.5, 20]);
    });
    it('fused A x B with leakyrelu', async () => {
        const a = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const b = tf.tensor2d([0, 1, -3, 2, 2, 1], [3, 2]);
        const alpha = 0.3;
        const transposeA = false;
        const transposeB = false;
        const c = tf.fused.matMul({
            a,
            b,
            transposeA,
            transposeB,
            bias: null,
            activation: 'leakyrelu',
            leakyreluAlpha: alpha
        });
        expect(c.shape).toEqual([2, 2]);
        expectArraysClose(await c.data(), [0, 8, -0.9000000357627869, 20]);
    });
    it('fused A x B with leakyrelu not provided.', async () => {
        const a = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const b = tf.tensor2d([0, 1, -3, 2, 2, 1], [3, 2]);
        const transposeA = false;
        const transposeB = false;
        const c = tf.fused.matMul({
            a,
            b,
            transposeA,
            transposeB,
            bias: null,
            activation: 'leakyrelu'
        });
        expect(c.shape).toEqual([2, 2]);
        // leakyRelu should use default alpha=0.2.
        expectArraysClose(await c.data(), [0, 8, -0.6000000238418579, 20]);
    });
    it('fused A x B with sigmoid', async () => {
        const a = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const b = tf.tensor2d([0, 1, -3, 2, 2, 1], [3, 2]);
        const transposeA = false;
        const transposeB = false;
        const c = tf.fused.matMul({ a, b, transposeA, transposeB, bias: null, activation: 'sigmoid' });
        expect(c.shape).toEqual([2, 2]);
        expectArraysClose(await c.data(), [0.5, 0.99966455, 0.04742587, 1]);
    });
    it('fused A x B with relu transpose', async () => {
        const a = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const b = tf.tensor2d([0, 1, -3, 2, 2, 1], [2, 3]);
        const transposeA = false;
        const transposeB = true;
        const c = tf.fused.matMul({ a, b, transposeA, transposeB, bias: null, activation: 'relu' });
        expect(c.shape).toEqual([2, 2]);
        expectArraysClose(await c.data(), [0, 9, 0, 24]);
    });
    it('fused A x B with 2d bias and relu', async () => {
        const a = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const b = tf.tensor2d([0, 1, -3, 2, 2, 1], [3, 2]);
        const c = tf.tensor2d([1, 1, 1, 1], [2, 2]);
        const transposeA = false;
        const transposeB = false;
        const d = tf.fused.matMul({ a, b, transposeA, transposeB, bias: c, activation: 'relu' });
        expect(d.shape).toEqual([2, 2]);
        expectArraysClose(await d.data(), [1, 9, 0, 21]);
    });
    it('fused A x B with relu and broadcasted bias', async () => {
        const a = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const b = tf.tensor2d([0, 1, -3, 2, 2, 1], [3, 2]);
        const c = tf.tensor1d([1, 1]);
        const act = 'relu';
        const transposeA = false;
        const transposeB = false;
        const d = tf.fused.matMul({ a, b, transposeA, transposeB, bias: c, activation: act });
        expect(d.shape).toEqual([2, 2]);
        expectArraysClose(await d.data(), [1, 9, 0, 21]);
    });
    it('fused A x B with elu and broadcasted bias', async () => {
        const a = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const b = tf.tensor2d([0, 1, -3, 2, 2, 1], [3, 2]);
        const c = tf.tensor1d([1, 1]);
        const act = 'elu';
        const transposeA = false;
        const transposeB = false;
        const d = tf.fused.matMul({ a, b, transposeA, transposeB, bias: c, activation: act });
        expect(d.shape).toEqual([2, 2]);
        expectArraysClose(await d.data(), [1, 9, -0.8647, 21]);
    });
    it('fused A x B with elu and broadcasted shape', async () => {
        const a = tf.tensor3d([1, 2, 3, 4, 5, 6], [1, 2, 3]);
        const b = tf.tensor2d([0, 1, -3, 2, 2, 1], [3, 2]);
        const c = tf.tensor1d([1, 1]);
        const act = 'elu';
        const transposeA = false;
        const transposeB = false;
        const d = tf.fused.matMul({ a, b, transposeA, transposeB, bias: c, activation: act });
        expect(d.shape).toEqual([1, 2, 2]);
        expectArraysClose(await d.data(), [1, 9, -0.8647, 21]);
    });
    it('fused A x B with relu and broadcasted bias different rank', async () => {
        const a = tf.tensor3d([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], [2, 2, 3]);
        const b = tf.tensor3d([0, 1, -3, 2, 2, 1, 0, 1, -3, 2, 2, 1], [2, 3, 2]);
        const c = tf.tensor2d([1, 2], [1, 2]);
        const act = 'relu';
        const transposeA = false;
        const transposeB = false;
        const d = tf.fused.matMul({ a, b, transposeA, transposeB, bias: c, activation: act });
        expect(d.shape).toEqual([2, 2, 2]);
        expectArraysClose(await d.data(), [2, 6, 0, 18, 0, 30, 0, 42]);
    });
    it('fused A x B with 2d bias only', async () => {
        const a = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const b = tf.tensor2d([0, 1, -3, 2, 2, 1], [3, 2]);
        const c = tf.tensor2d([1, 1, 1, 1], [2, 2]);
        const transposeA = false;
        const transposeB = false;
        const d = tf.fused.matMul({ a, b, transposeA, transposeB, bias: c, activation: 'linear' });
        expect(d.shape).toEqual([2, 2]);
        expectArraysClose(await d.data(), [1, 9, -2, 21]);
    });
    it('fused A x B with relu gradient', async () => {
        const a = tf.tensor2d([1, 2, 3, 10, 20, -30], [2, 3]);
        const b = tf.tensor2d([2, 3, 4, -1, 2, 3], [3, 2]);
        const dy = tf.tensor2d([1, 10, 20, 30], [2, 2]);
        const transposeA = false;
        const transposeB = false;
        const grads = tf.grads((a, b) => {
            const prod = tf.matMul(a, b, transposeA, transposeB);
            return tf.relu(prod);
        });
        const fusedGrads = tf.grads((a, b) => {
            return tf.fused.matMul({ a, b, transposeA, transposeB, bias: null, activation: 'relu' });
        });
        const [da, db] = grads([a, b], dy);
        const [fusedDa, fusedDb] = fusedGrads([a, b], dy);
        expectArraysClose(await da.array(), await fusedDa.array());
        expectArraysClose(await db.data(), await fusedDb.array());
    });
    it('gradient with clones A x B with relu', () => {
        const a = tf.tensor2d([1, 2, 3, 10, 20, -30], [2, 3]);
        const b = tf.tensor2d([2, 3, 4, -1, 2, 3], [3, 2]);
        const dy = tf.tensor2d([1, 10, 20, 30], [2, 2]);
        const transposeA = false;
        const transposeB = false;
        const fusedGrads = tf.grads((a, b) => {
            return tf.fused
                .matMul({
                a: a.clone(),
                b: b.clone(),
                transposeA,
                transposeB,
                bias: null,
                activation: 'relu'
            })
                .clone();
        });
        const [fusedDa, fusedDb] = fusedGrads([a, b], dy);
        expect(fusedDa.shape).toEqual(a.shape);
        expect(fusedDb.shape).toEqual(b.shape);
    });
    it('fused A x B with relu bias gradient', async () => {
        const a = tf.tensor2d([1, 2, 3, 10, 20, -30], [2, 3]);
        const b = tf.tensor2d([2, 3, 4, -1, 2, 3], [3, 2]);
        const c = tf.tensor2d([1, 1, 1, 1], [2, 2]);
        const transposeA = false;
        const transposeB = false;
        const dy = tf.tensor2d([1, 10, 20, 30], [2, 2]);
        const grads = tf.grads((a, b, c) => {
            const prod = tf.matMul(a, b, transposeA, transposeB);
            const sum = tf.add(prod, c);
            return tf.relu(sum);
        });
        const fusedGrads = tf.grads((a, b, c) => {
            return tf.fused.matMul({ a, b, transposeA, transposeB, bias: c, activation: 'relu' });
        });
        const [da, db, dc] = grads([a, b, c], dy);
        const [fusedDa, fusedDb, fusedDc] = fusedGrads([a, b, c], dy);
        expectArraysClose(await da.array(), await fusedDa.array());
        expectArraysClose(await db.array(), await fusedDb.array());
        expectArraysClose(await dc.array(), await fusedDc.array());
    });
    it('fused A x B with relu bias gradient transpose', async () => {
        const a = tf.tensor2d([1, 2, 3, 10, 20, -30], [3, 2]);
        const b = tf.tensor2d([2, 3, 4, -1, 2, 3], [3, 2]);
        const c = tf.tensor2d([1, 1, 1, 1], [2, 2]);
        const transposeA = true;
        const transposeB = false;
        const dy = tf.tensor2d([1, 10, 20, 30], [2, 2]);
        const grads = tf.grads((a, b, c) => {
            const prod = tf.matMul(a, b, transposeA, transposeB);
            const sum = tf.add(prod, c);
            return tf.relu(sum);
        });
        const fusedGrads = tf.grads((a, b, c) => {
            return tf.fused.matMul({ a, b, transposeA, transposeB, bias: c, activation: 'relu' });
        });
        const [da, db, dc] = grads([a, b, c], dy);
        const [fusedDa, fusedDb, fusedDc] = fusedGrads([a, b, c], dy);
        expectArraysClose(await da.array(), await fusedDa.array());
        expectArraysClose(await db.array(), await fusedDb.array());
        expectArraysClose(await dc.array(), await fusedDc.array());
    });
    it('fused A x B with relu and broadcasted bias gradient', async () => {
        const a = tf.tensor2d([1, 2, 3, 10, 20, -30], [2, 3]);
        const b = tf.tensor2d([2, 3, 4, -1, 2, 3], [3, 2]);
        const c = tf.tensor2d([[1]]);
        const transposeA = false;
        const transposeB = false;
        const dy = tf.tensor2d([1, 10, 20, 30], [2, 2]);
        const grads = tf.grads((a, b, c) => {
            const prod = tf.matMul(a, b, transposeA, transposeB);
            const sum = tf.add(prod, c);
            return tf.relu(sum);
        });
        const fusedGrads = tf.grads((a, b, c) => {
            return tf.fused.matMul({ a, b, transposeA, transposeB, bias: c, activation: 'relu' });
        });
        const [da, db, dc] = grads([a, b, c], dy);
        const [fusedDa, fusedDb, fusedDc] = fusedGrads([a, b, c], dy);
        expectArraysClose(await da.array(), await fusedDa.array());
        expectArraysClose(await db.array(), await fusedDb.array());
        expectArraysClose(await dc.array(), await fusedDc.array());
    });
    it('fused matmul with relu6 and gradients', async () => {
        const a = tf.tensor2d([1, 2, 3, 10, 20, -30], [2, 3]);
        const b = tf.tensor2d([2, 3, 4, -1, 2, 3], [3, 2]);
        const dy = tf.tensor2d([1, 10, 20, 30], [2, 2]);
        const transposeA = false;
        const transposeB = false;
        const fusedGrads = tf.grads((a, b) => {
            return tf.fused.matMul({ a, b, transposeA, transposeB, bias: null, activation: 'relu6' });
        });
        const [fusedDa, fusedDb] = fusedGrads([a, b], dy);
        const grads = tf.grads((a, b) => {
            const prod = tf.matMul(a, b, transposeA, transposeB);
            return tf.relu6(prod);
        });
        const [da, db] = grads([a, b], dy);
        expectArraysClose(await da.array(), await fusedDa.array());
        expectArraysClose(await db.data(), await fusedDb.array());
    });
});
//# sourceMappingURL=data:application/json;base64,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