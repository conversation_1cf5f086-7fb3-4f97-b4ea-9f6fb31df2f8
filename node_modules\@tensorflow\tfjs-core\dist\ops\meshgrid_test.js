/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF {} KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../index';
import { ALL_ENVS, describeWithFlags } from '../jasmine_util';
import { expectArraysEqual, expectArraysClose } from '../test_util';
describeWithFlags('no input', ALL_ENVS, () => {
    it('Should return an empty tensor ', async () => {
        expect(tf.meshgrid()).toEqual([]);
    });
});
describeWithFlags('single input', ALL_ENVS, () => {
    it('Should return a tensor with the same data', async () => {
        const x = [1, 2, 3, 4];
        const [got] = tf.meshgrid(x);
        expectArraysEqual(await got.data(), x);
    });
});
describeWithFlags('simple inputs', ALL_ENVS, () => {
    it('Should handle the simple 2D case', async () => {
        const x = [1, 2, 3];
        const y = [4, 5, 6, 7];
        const [X, Y] = tf.meshgrid(x, y);
        // 'close' instead of 'equal' because of matmul precision
        // in certain backends (WebGL).
        expectArraysClose(await X.data(), [[1, 2, 3], [1, 2, 3], [1, 2, 3], [1, 2, 3]]);
        expectArraysClose(await Y.data(), [[4, 4, 4], [5, 5, 5], [6, 6, 6], [7, 7, 7]]);
    });
    it('Should support \'ij\' indexing', async () => {
        const x = [1, 2, 3];
        const y = [4, 5, 6, 7];
        const [X, Y] = tf.meshgrid(x, y, { indexing: 'ij' });
        // 'close' instead of 'equal' because of matmul precision
        // in certain backends (WebGL).
        expectArraysClose(await X.data(), [[1, 1, 1, 1], [2, 2, 2, 2], [3, 3, 3, 3]]);
        expectArraysClose(await Y.data(), [[4, 5, 6, 7], [4, 5, 6, 7], [4, 5, 6, 7]]);
    });
});
describeWithFlags('higher dimensional input', ALL_ENVS, () => {
    it('Should flatten higher dimensional', async () => {
        const x = [1, 2, 3];
        const a = [[1, 1], [1, 1]];
        const [X, A] = tf.meshgrid(x, a);
        // 'close' instead of 'equal' because of matmul precision
        // in certain backends (WebGL).
        expectArraysClose(await X.data(), [[1, 2, 3], [1, 2, 3], [1, 2, 3], [1, 2, 3]]);
        expectArraysClose(await A.data(), [[1, 1, 1], [1, 1, 1], [1, 1, 1], [1, 1, 1]]);
    });
});
describeWithFlags('dtypes', ALL_ENVS, () => {
    it('Should use float32 for arrays of numbers', async () => {
        const x = [1, 2];
        const y = [3, 4];
        const [X, Y] = tf.meshgrid(x, y);
        expect(X.dtype).toBe('float32');
        expect(Y.dtype).toBe('float32');
    });
    it('Should use the input tensor dtype', async () => {
        const x = tf.tensor1d([1, 2], 'int32');
        const y = tf.tensor1d([3, 4], 'float32');
        const [X, Y] = tf.meshgrid(x, y);
        expect(X.dtype).toBe('int32');
        expect(Y.dtype).toBe('float32');
    });
});
describeWithFlags('scalars', ALL_ENVS, () => {
    it('Should treat them as 1D tensors', async () => {
        const [X] = tf.meshgrid(0);
        // 'close' instead of 'equal' because of matmul precision
        // in certain backends (WebGL).
        expectArraysClose(await X.data(), [0]);
        const [Y, Z] = tf.meshgrid([0], 1);
        expectArraysClose(await Y.data(), [[0]]);
        expectArraysClose(await Z.data(), [[1]]);
    });
});
describeWithFlags('invalid arguments', ALL_ENVS, () => {
    it('Should throw an Error', () => {
        expect(() => tf.meshgrid((() => { }))).toThrow();
        expect(() => tf.meshgrid([1], (() => { }))).toThrow();
        expect(() => tf.meshgrid([1], [2], { indexing: 'foobar' })).toThrow();
    });
});
//# sourceMappingURL=data:application/json;base64,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