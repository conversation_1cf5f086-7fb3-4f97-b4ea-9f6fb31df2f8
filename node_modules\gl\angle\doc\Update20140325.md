# ES 3.0 Development Branch merging

ANGLE will soon be merging its ES 3.0 development branch to master, to make
available (and more visible) the changes we've been making over the past several
months in support of ES 3.0, and to remove divergence between the master and
development branches.

The previous master branch will still be available as the es2only-legacy branch,
and SHAs will not change, so dependencies on individual commits of ANGLE will
continue to work as expected. However, new contributions against es2only-legacy
will generally not be considered, and future work should be done on master.

This merge doesn't signify completion of ES 3.0, as we have some features still
left to implement there, but interested developers can explore the work in
progress. A significant portion of 3.0 features have been implemented,
including:

* 2D array textures, 3D textures
* Expanded texture format support
* Uniform Buffer Objects
* Vertex Array Objects
* Sampler objects, expanded sampler types
* Transform Feedback
* Texture Swizzle
* GLSL integer support

ES 3.0 features should not yet be considered stable, even where implemented, and
some features are present only via naive implementation so far. There is still
quite a bit of work ahead of us before ES 3.0 support is complete, but this
merge should provide insight to those interested in what we've been working on!
