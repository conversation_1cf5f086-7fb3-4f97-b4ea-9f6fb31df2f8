/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
/// <amd-module name="@tensorflow/tfjs-core/dist/ops/sparse/sparse_fill_empty_rows" />
import { Scalar, Tensor1D, Tensor2D } from '../../tensor';
import { NamedTensorMap } from '../../tensor_types';
import { ScalarLike, TensorLike } from '../../types';
/**
 * The input SparseTensor is represented via the map of inputs {`indices`,
 * `values`, `denseShape`}. The output SparseTensor has the same `denseShape`
 * but with indices `outputIndices` and values `outputValues`. This op inserts a
 * single entry for every row that doesn't have any values. The index is created
 * as `[row, 0, ..., 0]` and the inserted value is `defaultValue`.
 *
 * For example, suppose `spInput` has shape [5, 6] and non-empty values:
 * [0, 1]: a
 * [0, 3]: b
 * [2, 0]: c
 * [3, 1]: d
 *
 * Rows 1 and 4 are empty, so the output will be of shape [5, 6] with values:
 * [0, 1]: a
 * [0, 3]: b
 * [1, 0]: `defaultValue`
 * [2, 0]: c
 * [3, 1]: d
 * [4, 0]: `defaultValue`
 *
 * The output SparseTensor will be in row-major order and will have the same
 * shape as the input.
 *
 * This op also returns an indicator vector shaped [dense_shape[0]] such that
 * emptyRowIndicator[i] = True iff row i was an empty row.
 *
 * And a reverse index map vector shaped [indices.shape[0]] that is used during
 * backpropagation, reverseIndexMap[i] = outi s.t. indices[i, j] ==
 * outputIndices[outi, j] for all j
 *
 * ```js
 * const result = tf.sparse.sparseFillEmptyRows(
 *   [[0, 0], [1, 0], [1, 3], [1, 4], [3, 2], [3, 3]],
 *   [0, 10, 13, 14, 32, 33], [5, 6], -1);
 * console.log(result);
 * result['outputIndices'].print(); // [[0, 0], [1, 0], [1, 3], [1, 4],
 *                                  //  [2, 0], [3, 2], [3, 3], [4, 0]]
 * result['outputValues'].print(); // [0, 10, 13, 14,-1, 32, 33, -1]
 * result['emptyRowIndicator'].print(); // [false, false, true, false, true]
 * result['reverseIndexMap'].print(); // [0, 1, 2, 3, 5, 6]
 * ```
 * @param indices: 2-D. The indices of the sparse tensor.
 * @param values: 1-D. The values of the sparse tensor.
 * @param denseShape: 1-D. The shape of the sparse tensor.
 * @param defaultValue: 0-D. Default value to insert into location [row, 0, ...,
 *     0] for rows missing from the input sparse tensor.
 * @return A map with the following properties:
 *     - outputIndices
 *     - outputValues: 1-D. The values of the filled sparse tensor.
 *     - emptyRowIndicator: 1-D. Whether the dense row was missing in the input
 * sparse tensor.
 *     - reverseIndexMap: 1-D. A map from the input indices to the output
 * indices.
 * @doc {heading: 'Operations', subheading: 'Sparse'}
 */
declare function sparseFillEmptyRows_(indices: Tensor2D | TensorLike, values: Tensor1D | TensorLike, denseShape: Tensor1D | TensorLike, defaultValue: Scalar | ScalarLike): NamedTensorMap;
export declare const sparseFillEmptyRows: typeof sparseFillEmptyRows_;
export {};
