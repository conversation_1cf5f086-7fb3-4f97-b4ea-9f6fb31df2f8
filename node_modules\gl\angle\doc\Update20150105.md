# Multiplatform ANGLE Refactor Complete

ANGLE's multiplatform refactoring effort ([described here]
(https://code.google.com/p/angleproject/wiki/MANGLE)) is now complete, paving
the way for additional rendering backends, allowing AN<PERSON>LE to enable OpenGL ES
not just over Direct3D 9 and 11, but also desktop OpenGL.

The refactoring we've done encapsulates D3D-related assumptions and API calls at
the renderer level, so that no D3D calls are made in the renderer-agnostic
portions of the codebase, and D3D-specific feature implementations won't be
included on non-D3D platforms. For example, the creation and maintenance of
CPU-side copies of texture data, which are required to enable GL-style
per-mip-level texture creation over D3D's entire-mipchain texture creation API,
is contained entirely within the D3D renderers, allowing a GL implementation to
avoid this complication.

Work will now begin within ANGLE to add a desktop OpenGL renderer, and EGL
implementations compatible with OS X and Linux.

# ES 3.0 Development Status

Our ES 3.0 development branch was merged into mainline ANGLE in April 2014, but
ES 3.0 support is not yet complete. The majority of API functionality has been
implemented; features still pending include:

* ETC2/EAC support
* primitive restart index
* drawRangeElements
* full GetProgramBinary support in core

Additional work remains in the compiler, including:

* Array .length()
* inf/nan detection
* math utility functions, rounding
* VertexID/InstanceID support
* floating point packing functions
* operators new in ES 3.0
* name redeclaration
* relaxed array indexing
* switch statement support
* loop & iteration improvements

ES 3.0 features should not be considered stable, even where implemented, and
some features are present only via naive implementation so far, but we welcome
bugs filed against this functionality, and thank all our contributors!
