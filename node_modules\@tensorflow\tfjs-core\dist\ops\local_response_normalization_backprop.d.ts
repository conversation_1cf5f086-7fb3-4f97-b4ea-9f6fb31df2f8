/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
/// <amd-module name="@tensorflow/tfjs-core/dist/ops/local_response_normalization_backprop" />
import { Tensor4D } from '../tensor';
declare function localResponseNormalizationBackprop_<T extends Tensor4D>(x: T, y: T, dy: T, depthRadius?: number, bias?: number, alpha?: number, beta?: number): T;
export declare const localResponseNormalizationBackprop: typeof localResponseNormalizationBackprop_;
export {};
