/**
 * @license
 * Copyright 2020 Google Inc. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../index';
import { BROWSER_ENVS, describeWithFlags, NODE_ENVS } from '../jasmine_util';
import { createVideoElement, expectArraysClose, expectArraysEqual, play } from '../test_util';
class MockContext {
    getImageData(x, y, width, height) {
        const data = new Uint8ClampedArray(width * height * 4);
        for (let i = 0; i < data.length; ++i) {
            data[i] = i + 1;
        }
        return { data };
    }
}
class MockCanvas {
    constructor(width, height) {
        this.width = width;
        this.height = height;
    }
    getContext(type) {
        return new MockContext();
    }
}
describeWithFlags('fromPixels, mock canvas', NODE_ENVS, () => {
    it('accepts a canvas-like element', async () => {
        const c = new MockCanvas(2, 2);
        // tslint:disable-next-line:no-any
        const t = tf.browser.fromPixels(c);
        expect(t.dtype).toBe('int32');
        expect(t.shape).toEqual([2, 2, 3]);
        expectArraysEqual(await t.data(), [1, 2, 3, 5, 6, 7, 9, 10, 11, 13, 14, 15]);
    });
    it('accepts a canvas-like element, numChannels=4', async () => {
        const c = new MockCanvas(2, 2);
        // tslint:disable-next-line:no-any
        const t = tf.browser.fromPixels(c, 4);
        expect(t.dtype).toBe('int32');
        expect(t.shape).toEqual([2, 2, 4]);
        expectArraysEqual(await t.data(), [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]);
    });
    it('errors when passed a non-canvas object', () => {
        // tslint:disable-next-line:no-any
        expect(() => tf.browser.fromPixels(5)).toThrowError();
    });
});
describeWithFlags('fromPixels', BROWSER_ENVS, () => {
    it('ImageData 1x1x3', async () => {
        const pixels = new ImageData(1, 1);
        pixels.data[0] = 0;
        pixels.data[1] = 80;
        pixels.data[2] = 160;
        pixels.data[3] = 240;
        const array = tf.browser.fromPixels(pixels, 3);
        expectArraysEqual(await array.data(), [0, 80, 160]);
    });
    it('ImageData 1x1x4', async () => {
        const pixels = new ImageData(1, 1);
        pixels.data[0] = 0;
        pixels.data[1] = 80;
        pixels.data[2] = 160;
        pixels.data[3] = 240;
        const array = tf.browser.fromPixels(pixels, 4);
        expectArraysEqual(await array.data(), [0, 80, 160, 240]);
    });
    it('ImageData 2x2x3', async () => {
        const pixels = new ImageData(2, 2);
        for (let i = 0; i < 8; i++) {
            pixels.data[i] = i * 2;
        }
        for (let i = 8; i < 16; i++) {
            pixels.data[i] = i * 2;
        }
        const array = tf.browser.fromPixels(pixels, 3);
        expectArraysEqual(await array.data(), [0, 2, 4, 8, 10, 12, 16, 18, 20, 24, 26, 28]);
    });
    it('ImageData 2x2x4', async () => {
        const pixels = new ImageData(2, 2);
        for (let i = 0; i < 8; i++) {
            pixels.data[i] = i * 2;
        }
        for (let i = 8; i < 16; i++) {
            pixels.data[i] = i * 2;
        }
        const array = tf.browser.fromPixels(pixels, 4);
        expectArraysClose(await array.data(), new Int32Array([0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30]));
    });
    it('fromPixels, 3 channels', async () => {
        const pixels = new ImageData(1, 2);
        pixels.data[0] = 2;
        pixels.data[1] = 3;
        pixels.data[2] = 4;
        pixels.data[3] = 255; // Not used.
        pixels.data[4] = 5;
        pixels.data[5] = 6;
        pixels.data[6] = 7;
        pixels.data[7] = 255; // Not used.
        const res = tf.browser.fromPixels(pixels, 3);
        expect(res.shape).toEqual([2, 1, 3]);
        expect(res.dtype).toBe('int32');
        expectArraysClose(await res.data(), [2, 3, 4, 5, 6, 7]);
    });
    it('fromPixels, reshape, then do tf.add()', async () => {
        const pixels = new ImageData(1, 1);
        pixels.data[0] = 2;
        pixels.data[1] = 3;
        pixels.data[2] = 4;
        pixels.data[3] = 255; // Not used.
        const a = tf.browser.fromPixels(pixels, 3).reshape([1, 1, 1, 3]);
        const res = a.add(tf.scalar(2, 'int32'));
        expect(res.shape).toEqual([1, 1, 1, 3]);
        expect(res.dtype).toBe('int32');
        expectArraysClose(await res.data(), [4, 5, 6]);
    });
    it('fromPixels + fromPixels', async () => {
        const pixelsA = new ImageData(1, 1);
        pixelsA.data[0] = 255;
        pixelsA.data[1] = 3;
        pixelsA.data[2] = 4;
        pixelsA.data[3] = 255; // Not used.
        const pixelsB = new ImageData(1, 1);
        pixelsB.data[0] = 5;
        pixelsB.data[1] = 6;
        pixelsB.data[2] = 7;
        pixelsB.data[3] = 255; // Not used.
        const a = tf.browser.fromPixels(pixelsA, 3).toFloat();
        const b = tf.browser.fromPixels(pixelsB, 3).toFloat();
        const res = a.add(b);
        expect(res.shape).toEqual([1, 1, 3]);
        expect(res.dtype).toBe('float32');
        expectArraysClose(await res.data(), [260, 9, 11]);
    });
    it('fromPixels for PixelData type', async () => {
        const dataA = new Uint8Array([255, 3, 4, 255]);
        const pixelsA = { width: 1, height: 1, data: dataA };
        const dataB = new Uint8Array([5, 6, 7, 255]);
        const pixelsB = { width: 1, height: 1, data: dataB };
        const a = tf.browser.fromPixels(pixelsA, 3).toFloat();
        const b = tf.browser.fromPixels(pixelsB, 3).toFloat();
        const res = a.add(b);
        expect(res.shape).toEqual([1, 1, 3]);
        expect(res.dtype).toBe('float32');
        expectArraysClose(await res.data(), [260, 9, 11]);
    });
    it('fromPixels for HTMLCanvasElement', async () => {
        const canvas = document.createElement('canvas');
        canvas.width = 1;
        canvas.height = 1;
        const ctx = canvas.getContext('2d');
        const pixels = new ImageData(1, 1);
        pixels.data[0] = 0;
        pixels.data[1] = 80;
        pixels.data[2] = 160;
        pixels.data[3] = 240;
        ctx.putImageData(pixels, 1, 1);
        const res = tf.browser.fromPixels(canvas);
        expect(res.shape).toEqual([1, 1, 3]);
        const data = await res.data();
        expect(data.length).toEqual(1 * 1 * 3);
    });
    it('fromPixels for HTMLImageElement', async () => {
        const img = new Image(10, 10);
        img.src = 'data:image/gif;base64' +
            ',R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==';
        const res = tf.browser.fromPixels(img);
        expect(res.shape).toEqual([10, 10, 3]);
        const data = await res.data();
        expect(data.length).toEqual(10 * 10 * 3);
    });
    it('fromPixels for HTMLVideoElement', async () => {
        const source = document.createElement('source');
        source.src =
            // tslint:disable-next-line:max-line-length
            'data:video/mp4;base64,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';
        source.type = 'video/mp4';
        const video = await createVideoElement(source);
        document.body.appendChild(video);
        await play(video);
        const res = tf.browser.fromPixels(video);
        expect(res.shape).toEqual([90, 160, 3]);
        const data = await res.data();
        expect(data.length).toEqual(90 * 160 * 3);
        document.body.removeChild(video);
    }, 30000 /* 30 seconds */);
    it('throws when passed a primitive number', () => {
        const msg = /pixels passed to tf.browser.fromPixels\(\) must be either/;
        // tslint:disable-next-line:no-any
        expect(() => tf.browser.fromPixels(3)).toThrowError(msg);
    });
    it('throws when passed a string', () => {
        const msg = /pixels passed to tf.browser.fromPixels\(\) must be either/;
        // tslint:disable-next-line:no-any
        expect(() => tf.browser.fromPixels('test')).toThrowError(msg);
    });
    it('canvas and image match', async () => {
        const img = new Image();
        const size = 80;
        img.src =
            // tslint:disable-next-line:max-line-length
            'data:image/jpeg;base64,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';
        await new Promise(resolve => {
            img.onload = () => resolve(img);
        });
        img.width = size;
        img.height = size;
        const pixels = await tf.browser.fromPixels(img, 4);
        const canvas = document.createElement('canvas');
        canvas.width = size;
        canvas.height = size;
        const ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0, size, size);
        const actual = ctx.getImageData(0, 0, size, size).data;
        const actualInt32 = Int32Array.from(actual);
        const pixelsData = await pixels.data();
        expectArraysClose(pixelsData, actualInt32, 10);
    });
    if (tf.env().getBool('IS_CHROME')) {
        it('fromPixels for ImageBitmap', async () => {
            const imageDataWidth = 1;
            const imageDataHeight = 2;
            const numChannel = 3;
            const pixels = new ImageData(imageDataWidth, imageDataHeight);
            for (let i = 0; i < imageDataWidth * imageDataHeight * 4; ++i) {
                if (i % 4 === 3) {
                    pixels.data[i] = 255;
                }
                else {
                    pixels.data[i] = i;
                }
            }
            const imageBitmap = await createImageBitmap(pixels);
            const res = tf.browser.fromPixels(imageBitmap, numChannel);
            imageBitmap.close();
            expect(res.shape).toEqual([imageDataHeight, imageDataWidth, numChannel]);
            const data = await res.data();
            expect(data.length)
                .toEqual(imageDataHeight * imageDataWidth * numChannel);
            expectArraysEqual(await res.data(), [0, 1, 2, 4, 5, 6]);
        });
        it('fromPixels for ImageBitmap outShape changes', async () => {
            const imageDataWidth = 2;
            const imageDataHeight = 2;
            let numChannel = 3;
            const pixels = new ImageData(imageDataWidth, imageDataHeight);
            for (let i = 0; i < imageDataWidth * imageDataHeight * 4; ++i) {
                if (i % 4 === 3) {
                    pixels.data[i] = 255;
                }
                else {
                    pixels.data[i] = i;
                }
            }
            const imageBitmap = await createImageBitmap(pixels);
            const res = tf.browser.fromPixels(imageBitmap, numChannel);
            expect(res.shape).toEqual([imageDataHeight, imageDataWidth, numChannel]);
            const data = await res.data();
            expect(data.length)
                .toEqual(imageDataHeight * imageDataWidth * numChannel);
            expectArraysEqual(await res.data(), [0, 1, 2, 4, 5, 6, 8, 9, 10, 12, 13, 14]);
            // Change output shapes
            numChannel = 4;
            const resShapeChange = tf.browser.fromPixels(imageBitmap, numChannel);
            expect(resShapeChange.shape).toEqual([
                imageDataHeight, imageDataWidth, numChannel
            ]);
            const data2 = await resShapeChange.data();
            expect(data2.length)
                .toEqual(imageDataHeight * imageDataWidth * numChannel);
            expectArraysEqual(await resShapeChange.data(), [0, 1, 2, 255, 4, 5, 6, 255, 8, 9, 10, 255, 12, 13, 14, 255]);
        });
    }
});
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZnJvbV9waXhlbHNfdGVzdC5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uL3RmanMtY29yZS9zcmMvb3BzL2Zyb21fcGl4ZWxzX3Rlc3QudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7Ozs7Ozs7Ozs7OztHQWVHO0FBRUgsT0FBTyxLQUFLLEVBQUUsTUFBTSxVQUFVLENBQUM7QUFDL0IsT0FBTyxFQUFDLFlBQVksRUFBRSxpQkFBaUIsRUFBRSxTQUFTLEVBQUMsTUFBTSxpQkFBaUIsQ0FBQztBQUMzRSxPQUFPLEVBQUMsa0JBQWtCLEVBQUUsaUJBQWlCLEVBQUUsaUJBQWlCLEVBQUUsSUFBSSxFQUFDLE1BQU0sY0FBYyxDQUFDO0FBRTVGLE1BQU0sV0FBVztJQUNmLFlBQVksQ0FBQyxDQUFTLEVBQUUsQ0FBUyxFQUFFLEtBQWEsRUFBRSxNQUFjO1FBQzlELE1BQU0sSUFBSSxHQUFHLElBQUksaUJBQWlCLENBQUMsS0FBSyxHQUFHLE1BQU0sR0FBRyxDQUFDLENBQUMsQ0FBQztRQUN2RCxLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsSUFBSSxDQUFDLE1BQU0sRUFBRSxFQUFFLENBQUMsRUFBRTtZQUNwQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FBQztTQUNqQjtRQUNELE9BQU8sRUFBQyxJQUFJLEVBQUMsQ0FBQztJQUNoQixDQUFDO0NBQ0Y7QUFFRCxNQUFNLFVBQVU7SUFDZCxZQUFtQixLQUFhLEVBQVMsTUFBYztRQUFwQyxVQUFLLEdBQUwsS0FBSyxDQUFRO1FBQVMsV0FBTSxHQUFOLE1BQU0sQ0FBUTtJQUFHLENBQUM7SUFDM0QsVUFBVSxDQUFDLElBQVU7UUFDbkIsT0FBTyxJQUFJLFdBQVcsRUFBRSxDQUFDO0lBQzNCLENBQUM7Q0FDRjtBQUVELGlCQUFpQixDQUFDLHlCQUF5QixFQUFFLFNBQVMsRUFBRSxHQUFHLEVBQUU7SUFDM0QsRUFBRSxDQUFDLCtCQUErQixFQUFFLEtBQUssSUFBSSxFQUFFO1FBQzdDLE1BQU0sQ0FBQyxHQUFHLElBQUksVUFBVSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztRQUMvQixrQ0FBa0M7UUFDbEMsTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLE9BQU8sQ0FBQyxVQUFVLENBQUMsQ0FBUSxDQUFDLENBQUM7UUFDMUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUM7UUFDOUIsTUFBTSxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDbkMsaUJBQWlCLENBQ2IsTUFBTSxDQUFDLENBQUMsSUFBSSxFQUFFLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsQ0FBQyxDQUFDLENBQUM7SUFDakUsQ0FBQyxDQUFDLENBQUM7SUFFSCxFQUFFLENBQUMsOENBQThDLEVBQUUsS0FBSyxJQUFJLEVBQUU7UUFDNUQsTUFBTSxDQUFDLEdBQUcsSUFBSSxVQUFVLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO1FBQy9CLGtDQUFrQztRQUNsQyxNQUFNLENBQUMsR0FBRyxFQUFFLENBQUMsT0FBTyxDQUFDLFVBQVUsQ0FBQyxDQUFRLEVBQUUsQ0FBQyxDQUFDLENBQUM7UUFDN0MsTUFBTSxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUM7UUFDOUIsTUFBTSxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDbkMsaUJBQWlCLENBQ2IsTUFBTSxDQUFDLENBQUMsSUFBSSxFQUFFLEVBQ2QsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLENBQUMsQ0FBQyxDQUFDO0lBQy9ELENBQUMsQ0FBQyxDQUFDO0lBRUgsRUFBRSxDQUFDLHdDQUF3QyxFQUFFLEdBQUcsRUFBRTtRQUNoRCxrQ0FBa0M7UUFDbEMsTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLEVBQUUsQ0FBQyxPQUFPLENBQUMsVUFBVSxDQUFDLENBQVEsQ0FBQyxDQUFDLENBQUMsWUFBWSxFQUFFLENBQUM7SUFDL0QsQ0FBQyxDQUFDLENBQUM7QUFDTCxDQUFDLENBQUMsQ0FBQztBQUVILGlCQUFpQixDQUFDLFlBQVksRUFBRSxZQUFZLEVBQUUsR0FBRyxFQUFFO0lBQ2pELEVBQUUsQ0FBQyxpQkFBaUIsRUFBRSxLQUFLLElBQUksRUFBRTtRQUMvQixNQUFNLE1BQU0sR0FBRyxJQUFJLFNBQVMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7UUFDbkMsTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUM7UUFDbkIsTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsR0FBRyxFQUFFLENBQUM7UUFDcEIsTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsR0FBRyxHQUFHLENBQUM7UUFDckIsTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsR0FBRyxHQUFHLENBQUM7UUFFckIsTUFBTSxLQUFLLEdBQUcsRUFBRSxDQUFDLE9BQU8sQ0FBQyxVQUFVLENBQUMsTUFBTSxFQUFFLENBQUMsQ0FBQyxDQUFDO1FBRS9DLGlCQUFpQixDQUFDLE1BQU0sS0FBSyxDQUFDLElBQUksRUFBRSxFQUFFLENBQUMsQ0FBQyxFQUFFLEVBQUUsRUFBRSxHQUFHLENBQUMsQ0FBQyxDQUFDO0lBQ3RELENBQUMsQ0FBQyxDQUFDO0lBRUgsRUFBRSxDQUFDLGlCQUFpQixFQUFFLEtBQUssSUFBSSxFQUFFO1FBQy9CLE1BQU0sTUFBTSxHQUFHLElBQUksU0FBUyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztRQUNuQyxNQUFNLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQztRQUNuQixNQUFNLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxHQUFHLEVBQUUsQ0FBQztRQUNwQixNQUFNLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxHQUFHLEdBQUcsQ0FBQztRQUNyQixNQUFNLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxHQUFHLEdBQUcsQ0FBQztRQUVyQixNQUFNLEtBQUssR0FBRyxFQUFFLENBQUMsT0FBTyxDQUFDLFVBQVUsQ0FBQyxNQUFNLEVBQUUsQ0FBQyxDQUFDLENBQUM7UUFFL0MsaUJBQWlCLENBQUMsTUFBTSxLQUFLLENBQUMsSUFBSSxFQUFFLEVBQUUsQ0FBQyxDQUFDLEVBQUUsRUFBRSxFQUFFLEdBQUcsRUFBRSxHQUFHLENBQUMsQ0FBQyxDQUFDO0lBQzNELENBQUMsQ0FBQyxDQUFDO0lBRUgsRUFBRSxDQUFDLGlCQUFpQixFQUFFLEtBQUssSUFBSSxFQUFFO1FBQy9CLE1BQU0sTUFBTSxHQUFHLElBQUksU0FBUyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztRQUVuQyxLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsRUFBRSxFQUFFO1lBQzFCLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FBQztTQUN4QjtRQUNELEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxFQUFFLEVBQUUsQ0FBQyxFQUFFLEVBQUU7WUFDM0IsTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFDO1NBQ3hCO1FBRUQsTUFBTSxLQUFLLEdBQUcsRUFBRSxDQUFDLE9BQU8sQ0FBQyxVQUFVLENBQUMsTUFBTSxFQUFFLENBQUMsQ0FBQyxDQUFDO1FBRS9DLGlCQUFpQixDQUNiLE1BQU0sS0FBSyxDQUFDLElBQUksRUFBRSxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLENBQUMsQ0FBQyxDQUFDO0lBQ3hFLENBQUMsQ0FBQyxDQUFDO0lBRUgsRUFBRSxDQUFDLGlCQUFpQixFQUFFLEtBQUssSUFBSSxFQUFFO1FBQy9CLE1BQU0sTUFBTSxHQUFHLElBQUksU0FBUyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztRQUNuQyxLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsRUFBRSxFQUFFO1lBQzFCLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FBQztTQUN4QjtRQUNELEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxFQUFFLEVBQUUsQ0FBQyxFQUFFLEVBQUU7WUFDM0IsTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFDO1NBQ3hCO1FBRUQsTUFBTSxLQUFLLEdBQUcsRUFBRSxDQUFDLE9BQU8sQ0FBQyxVQUFVLENBQUMsTUFBTSxFQUFFLENBQUMsQ0FBQyxDQUFDO1FBRS9DLGlCQUFpQixDQUNiLE1BQU0sS0FBSyxDQUFDLElBQUksRUFBRSxFQUNsQixJQUFJLFVBQVUsQ0FDVixDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQztJQUN4RSxDQUFDLENBQUMsQ0FBQztJQUVILEVBQUUsQ0FBQyx3QkFBd0IsRUFBRSxLQUFLLElBQUksRUFBRTtRQUN0QyxNQUFNLE1BQU0sR0FBRyxJQUFJLFNBQVMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7UUFDbkMsTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUM7UUFDbkIsTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUM7UUFDbkIsTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUM7UUFDbkIsTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsR0FBRyxHQUFHLENBQUMsQ0FBRSxZQUFZO1FBQ25DLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDO1FBQ25CLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDO1FBQ25CLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDO1FBQ25CLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEdBQUcsR0FBRyxDQUFDLENBQUUsWUFBWTtRQUNuQyxNQUFNLEdBQUcsR0FBRyxFQUFFLENBQUMsT0FBTyxDQUFDLFVBQVUsQ0FBQyxNQUFNLEVBQUUsQ0FBQyxDQUFDLENBQUM7UUFDN0MsTUFBTSxDQUFDLEdBQUcsQ0FBQyxLQUFLLENBQUMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDckMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxLQUFLLENBQUMsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUM7UUFDaEMsaUJBQWlCLENBQUMsTUFBTSxHQUFHLENBQUMsSUFBSSxFQUFFLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUM7SUFDMUQsQ0FBQyxDQUFDLENBQUM7SUFFSCxFQUFFLENBQUMsdUNBQXVDLEVBQUUsS0FBSyxJQUFJLEVBQUU7UUFDckQsTUFBTSxNQUFNLEdBQUcsSUFBSSxTQUFTLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO1FBQ25DLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDO1FBQ25CLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDO1FBQ25CLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDO1FBQ25CLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEdBQUcsR0FBRyxDQUFDLENBQUUsWUFBWTtRQUNuQyxNQUFNLENBQUMsR0FBRyxFQUFFLENBQUMsT0FBTyxDQUFDLFVBQVUsQ0FBQyxNQUFNLEVBQUUsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUNqRSxNQUFNLEdBQUcsR0FBRyxDQUFDLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUMsQ0FBQyxFQUFFLE9BQU8sQ0FBQyxDQUFDLENBQUM7UUFDekMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxLQUFLLENBQUMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3hDLE1BQU0sQ0FBQyxHQUFHLENBQUMsS0FBSyxDQUFDLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxDQUFDO1FBQ2hDLGlCQUFpQixDQUFDLE1BQU0sR0FBRyxDQUFDLElBQUksRUFBRSxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDO0lBQ2pELENBQUMsQ0FBQyxDQUFDO0lBRUgsRUFBRSxDQUFDLHlCQUF5QixFQUFFLEtBQUssSUFBSSxFQUFFO1FBQ3ZDLE1BQU0sT0FBTyxHQUFHLElBQUksU0FBUyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztRQUNwQyxPQUFPLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxHQUFHLEdBQUcsQ0FBQztRQUN0QixPQUFPLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQztRQUNwQixPQUFPLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQztRQUNwQixPQUFPLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxHQUFHLEdBQUcsQ0FBQyxDQUFFLFlBQVk7UUFDcEMsTUFBTSxPQUFPLEdBQUcsSUFBSSxTQUFTLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO1FBQ3BDLE9BQU8sQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDO1FBQ3BCLE9BQU8sQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDO1FBQ3BCLE9BQU8sQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDO1FBQ3BCLE9BQU8sQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEdBQUcsR0FBRyxDQUFDLENBQUUsWUFBWTtRQUNwQyxNQUFNLENBQUMsR0FBRyxFQUFFLENBQUMsT0FBTyxDQUFDLFVBQVUsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxDQUFDLENBQUMsT0FBTyxFQUFFLENBQUM7UUFDdEQsTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLE9BQU8sQ0FBQyxVQUFVLENBQUMsT0FBTyxFQUFFLENBQUMsQ0FBQyxDQUFDLE9BQU8sRUFBRSxDQUFDO1FBQ3RELE1BQU0sR0FBRyxHQUFHLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDckIsTUFBTSxDQUFDLEdBQUcsQ0FBQyxLQUFLLENBQUMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDckMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxLQUFLLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUM7UUFDbEMsaUJBQWlCLENBQUMsTUFBTSxHQUFHLENBQUMsSUFBSSxFQUFFLEVBQUUsQ0FBQyxHQUFHLEVBQUUsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDLENBQUM7SUFDcEQsQ0FBQyxDQUFDLENBQUM7SUFDSCxFQUFFLENBQUMsK0JBQStCLEVBQUUsS0FBSyxJQUFJLEVBQUU7UUFDN0MsTUFBTSxLQUFLLEdBQUcsSUFBSSxVQUFVLENBQUMsQ0FBQyxHQUFHLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxHQUFHLENBQUMsQ0FBQyxDQUFDO1FBQy9DLE1BQU0sT0FBTyxHQUFHLEVBQUMsS0FBSyxFQUFFLENBQUMsRUFBRSxNQUFNLEVBQUUsQ0FBQyxFQUFFLElBQUksRUFBRSxLQUFLLEVBQUMsQ0FBQztRQUVuRCxNQUFNLEtBQUssR0FBRyxJQUFJLFVBQVUsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxDQUFDLENBQUM7UUFDN0MsTUFBTSxPQUFPLEdBQUcsRUFBQyxLQUFLLEVBQUUsQ0FBQyxFQUFFLE1BQU0sRUFBRSxDQUFDLEVBQUUsSUFBSSxFQUFFLEtBQUssRUFBQyxDQUFDO1FBQ25ELE1BQU0sQ0FBQyxHQUFHLEVBQUUsQ0FBQyxPQUFPLENBQUMsVUFBVSxDQUFDLE9BQU8sRUFBRSxDQUFDLENBQUMsQ0FBQyxPQUFPLEVBQUUsQ0FBQztRQUN0RCxNQUFNLENBQUMsR0FBRyxFQUFFLENBQUMsT0FBTyxDQUFDLFVBQVUsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxDQUFDLENBQUMsT0FBTyxFQUFFLENBQUM7UUFDdEQsTUFBTSxHQUFHLEdBQUcsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUNyQixNQUFNLENBQUMsR0FBRyxDQUFDLEtBQUssQ0FBQyxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUNyQyxNQUFNLENBQUMsR0FBRyxDQUFDLEtBQUssQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQztRQUNsQyxpQkFBaUIsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxJQUFJLEVBQUUsRUFBRSxDQUFDLEdBQUcsRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFDLENBQUMsQ0FBQztJQUNwRCxDQUFDLENBQUMsQ0FBQztJQUVILEVBQUUsQ0FBQyxrQ0FBa0MsRUFBRSxLQUFLLElBQUksRUFBRTtRQUNoRCxNQUFNLE1BQU0sR0FBRyxRQUFRLENBQUMsYUFBYSxDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBQ2hELE1BQU0sQ0FBQyxLQUFLLEdBQUcsQ0FBQyxDQUFDO1FBQ2pCLE1BQU0sQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFDO1FBQ2xCLE1BQU0sR0FBRyxHQUFHLE1BQU0sQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDcEMsTUFBTSxNQUFNLEdBQUcsSUFBSSxTQUFTLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO1FBQ25DLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDO1FBQ25CLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEdBQUcsRUFBRSxDQUFDO1FBQ3BCLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEdBQUcsR0FBRyxDQUFDO1FBQ3JCLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEdBQUcsR0FBRyxDQUFDO1FBQ3JCLEdBQUcsQ0FBQyxZQUFZLENBQUMsTUFBTSxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztRQUMvQixNQUFNLEdBQUcsR0FBRyxFQUFFLENBQUMsT0FBTyxDQUFDLFVBQVUsQ0FBQyxNQUFNLENBQUMsQ0FBQztRQUMxQyxNQUFNLENBQUMsR0FBRyxDQUFDLEtBQUssQ0FBQyxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUNyQyxNQUFNLElBQUksR0FBRyxNQUFNLEdBQUcsQ0FBQyxJQUFJLEVBQUUsQ0FBQztRQUM5QixNQUFNLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDLE9BQU8sQ0FBQyxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDO0lBQ3pDLENBQUMsQ0FBQyxDQUFDO0lBQ0gsRUFBRSxDQUFDLGlDQUFpQyxFQUFFLEtBQUssSUFBSSxFQUFFO1FBQy9DLE1BQU0sR0FBRyxHQUFHLElBQUksS0FBSyxDQUFDLEVBQUUsRUFBRSxFQUFFLENBQUMsQ0FBQztRQUM5QixHQUFHLENBQUMsR0FBRyxHQUFHLHVCQUF1QjtZQUM3QiwrREFBK0QsQ0FBQztRQUNwRSxNQUFNLEdBQUcsR0FBRyxFQUFFLENBQUMsT0FBTyxDQUFDLFVBQVUsQ0FBQyxHQUFHLENBQUMsQ0FBQztRQUN2QyxNQUFNLENBQUMsR0FBRyxDQUFDLEtBQUssQ0FBQyxDQUFDLE9BQU8sQ0FBQyxDQUFDLEVBQUUsRUFBRSxFQUFFLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUN2QyxNQUFNLElBQUksR0FBRyxNQUFNLEdBQUcsQ0FBQyxJQUFJLEVBQUUsQ0FBQztRQUM5QixNQUFNLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDLE9BQU8sQ0FBQyxFQUFFLEdBQUcsRUFBRSxHQUFHLENBQUMsQ0FBQyxDQUFDO0lBQzNDLENBQUMsQ0FBQyxDQUFDO0lBQ0gsRUFBRSxDQUFDLGlDQUFpQyxFQUFFLEtBQUssSUFBSSxFQUFFO1FBQy9DLE1BQU0sTUFBTSxHQUFHLFFBQVEsQ0FBQyxhQUFhLENBQUMsUUFBUSxDQUFDLENBQUM7UUFDaEQsTUFBTSxDQUFDLEdBQUc7WUFDTiwyQ0FBMkM7WUFDM0MsNGhFQUE0aEUsQ0FBQztRQUNqaUUsTUFBTSxDQUFDLElBQUksR0FBRyxXQUFXLENBQUM7UUFFMUIsTUFBTSxLQUFLLEdBQUcsTUFBTSxrQkFBa0IsQ0FBQyxNQUFNLENBQUMsQ0FBQztRQUMvQyxRQUFRLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUNqQyxNQUFNLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUVsQixNQUFNLEdBQUcsR0FBRyxFQUFFLENBQUMsT0FBTyxDQUFDLFVBQVUsQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUN6QyxNQUFNLENBQUMsR0FBRyxDQUFDLEtBQUssQ0FBQyxDQUFDLE9BQU8sQ0FBQyxDQUFDLEVBQUUsRUFBRSxHQUFHLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUN4QyxNQUFNLElBQUksR0FBRyxNQUFNLEdBQUcsQ0FBQyxJQUFJLEVBQUUsQ0FBQztRQUM5QixNQUFNLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDLE9BQU8sQ0FBQyxFQUFFLEdBQUcsR0FBRyxHQUFHLENBQUMsQ0FBQyxDQUFDO1FBQzFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLEtBQUssQ0FBQyxDQUFDO0lBQ25DLENBQUMsRUFBRSxLQUFNLENBQUMsZ0JBQWdCLENBQUMsQ0FBQztJQUU1QixFQUFFLENBQUMsdUNBQXVDLEVBQUUsR0FBRyxFQUFFO1FBQy9DLE1BQU0sR0FBRyxHQUFHLDJEQUEyRCxDQUFDO1FBQ3hFLGtDQUFrQztRQUNsQyxNQUFNLENBQUMsR0FBRyxFQUFFLENBQUMsRUFBRSxDQUFDLE9BQU8sQ0FBQyxVQUFVLENBQUMsQ0FBUSxDQUFDLENBQUMsQ0FBQyxZQUFZLENBQUMsR0FBRyxDQUFDLENBQUM7SUFDbEUsQ0FBQyxDQUFDLENBQUM7SUFFSCxFQUFFLENBQUMsNkJBQTZCLEVBQUUsR0FBRyxFQUFFO1FBQ3JDLE1BQU0sR0FBRyxHQUFHLDJEQUEyRCxDQUFDO1FBQ3hFLGtDQUFrQztRQUNsQyxNQUFNLENBQUMsR0FBRyxFQUFFLENBQUMsRUFBRSxDQUFDLE9BQU8sQ0FBQyxVQUFVLENBQUMsTUFBYSxDQUFDLENBQUMsQ0FBQyxZQUFZLENBQUMsR0FBRyxDQUFDLENBQUM7SUFDdkUsQ0FBQyxDQUFDLENBQUM7SUFFSCxFQUFFLENBQUMsd0JBQXdCLEVBQUUsS0FBSyxJQUFJLEVBQUU7UUFDdEMsTUFBTSxHQUFHLEdBQUcsSUFBSSxLQUFLLEVBQUUsQ0FBQztRQUN4QixNQUFNLElBQUksR0FBRyxFQUFFLENBQUM7UUFDaEIsR0FBRyxDQUFDLEdBQUc7WUFDSCwyQ0FBMkM7WUFDM0MseXhGQUF5eEYsQ0FBQztRQUU5eEYsTUFBTSxJQUFJLE9BQU8sQ0FBQyxPQUFPLENBQUMsRUFBRTtZQUMxQixHQUFHLENBQUMsTUFBTSxHQUFHLEdBQUcsRUFBRSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsQ0FBQztRQUNsQyxDQUFDLENBQUMsQ0FBQztRQUVILEdBQUcsQ0FBQyxLQUFLLEdBQUcsSUFBSSxDQUFDO1FBQ2pCLEdBQUcsQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFDO1FBRWxCLE1BQU0sTUFBTSxHQUFHLE1BQU0sRUFBRSxDQUFDLE9BQU8sQ0FBQyxVQUFVLENBQUMsR0FBRyxFQUFFLENBQUMsQ0FBQyxDQUFDO1FBRW5ELE1BQU0sTUFBTSxHQUFHLFFBQVEsQ0FBQyxhQUFhLENBQUMsUUFBUSxDQUFDLENBQUM7UUFDaEQsTUFBTSxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUM7UUFDcEIsTUFBTSxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUM7UUFDckIsTUFBTSxHQUFHLEdBQUcsTUFBTSxDQUFDLFVBQVUsQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUNwQyxHQUFHLENBQUMsU0FBUyxDQUFDLEdBQUcsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLElBQUksRUFBRSxJQUFJLENBQUMsQ0FBQztRQUNyQyxNQUFNLE1BQU0sR0FBRyxHQUFHLENBQUMsWUFBWSxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsSUFBSSxFQUFFLElBQUksQ0FBQyxDQUFDLElBQUksQ0FBQztRQUN2RCxNQUFNLFdBQVcsR0FBRyxVQUFVLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDO1FBQzVDLE1BQU0sVUFBVSxHQUFHLE1BQU0sTUFBTSxDQUFDLElBQUksRUFBRSxDQUFDO1FBRXZDLGlCQUFpQixDQUFDLFVBQVUsRUFBRSxXQUFXLEVBQUUsRUFBRSxDQUFDLENBQUM7SUFDakQsQ0FBQyxDQUFDLENBQUM7SUFFSCxJQUFJLEVBQUUsQ0FBQyxHQUFHLEVBQUUsQ0FBQyxPQUFPLENBQUMsV0FBVyxDQUFDLEVBQUU7UUFDakMsRUFBRSxDQUFDLDRCQUE0QixFQUFFLEtBQUssSUFBSSxFQUFFO1lBQzFDLE1BQU0sY0FBYyxHQUFHLENBQUMsQ0FBQztZQUN6QixNQUFNLGVBQWUsR0FBRyxDQUFDLENBQUM7WUFDMUIsTUFBTSxVQUFVLEdBQUcsQ0FBQyxDQUFDO1lBQ3JCLE1BQU0sTUFBTSxHQUFHLElBQUksU0FBUyxDQUFDLGNBQWMsRUFBRSxlQUFlLENBQUMsQ0FBQztZQUM5RCxLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsY0FBYyxHQUFHLGVBQWUsR0FBRyxDQUFDLEVBQUUsRUFBRSxDQUFDLEVBQUU7Z0JBQzdELElBQUksQ0FBQyxHQUFHLENBQUMsS0FBSyxDQUFDLEVBQUU7b0JBQ2YsTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsR0FBRyxHQUFHLENBQUM7aUJBQ3RCO3FCQUFNO29CQUNMLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDO2lCQUNwQjthQUNGO1lBRUQsTUFBTSxXQUFXLEdBQUcsTUFBTSxpQkFBaUIsQ0FBQyxNQUFNLENBQUMsQ0FBQztZQUNwRCxNQUFNLEdBQUcsR0FBRyxFQUFFLENBQUMsT0FBTyxDQUFDLFVBQVUsQ0FBQyxXQUFXLEVBQUUsVUFBVSxDQUFDLENBQUM7WUFDM0QsV0FBVyxDQUFDLEtBQUssRUFBRSxDQUFDO1lBQ3BCLE1BQU0sQ0FBQyxHQUFHLENBQUMsS0FBSyxDQUFDLENBQUMsT0FBTyxDQUFDLENBQUMsZUFBZSxFQUFFLGNBQWMsRUFBRSxVQUFVLENBQUMsQ0FBQyxDQUFDO1lBQ3pFLE1BQU0sSUFBSSxHQUFHLE1BQU0sR0FBRyxDQUFDLElBQUksRUFBRSxDQUFDO1lBQzlCLE1BQU0sQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDO2lCQUNkLE9BQU8sQ0FBQyxlQUFlLEdBQUcsY0FBYyxHQUFHLFVBQVUsQ0FBQyxDQUFDO1lBQzVELGlCQUFpQixDQUFDLE1BQU0sR0FBRyxDQUFDLElBQUksRUFBRSxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQzFELENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLDZDQUE2QyxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQzNELE1BQU0sY0FBYyxHQUFHLENBQUMsQ0FBQztZQUN6QixNQUFNLGVBQWUsR0FBRyxDQUFDLENBQUM7WUFDMUIsSUFBSSxVQUFVLEdBQUcsQ0FBQyxDQUFDO1lBQ25CLE1BQU0sTUFBTSxHQUFHLElBQUksU0FBUyxDQUFDLGNBQWMsRUFBRSxlQUFlLENBQUMsQ0FBQztZQUM5RCxLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsY0FBYyxHQUFHLGVBQWUsR0FBRyxDQUFDLEVBQUUsRUFBRSxDQUFDLEVBQUU7Z0JBQzdELElBQUksQ0FBQyxHQUFHLENBQUMsS0FBSyxDQUFDLEVBQUU7b0JBQ2YsTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsR0FBRyxHQUFHLENBQUM7aUJBQ3RCO3FCQUFNO29CQUNMLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDO2lCQUNwQjthQUNGO1lBRUQsTUFBTSxXQUFXLEdBQUcsTUFBTSxpQkFBaUIsQ0FBQyxNQUFNLENBQUMsQ0FBQztZQUNwRCxNQUFNLEdBQUcsR0FBRyxFQUFFLENBQUMsT0FBTyxDQUFDLFVBQVUsQ0FBQyxXQUFXLEVBQUUsVUFBVSxDQUFDLENBQUM7WUFDM0QsTUFBTSxDQUFDLEdBQUcsQ0FBQyxLQUFLLENBQUMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxlQUFlLEVBQUUsY0FBYyxFQUFFLFVBQVUsQ0FBQyxDQUFDLENBQUM7WUFDekUsTUFBTSxJQUFJLEdBQUcsTUFBTSxHQUFHLENBQUMsSUFBSSxFQUFFLENBQUM7WUFDOUIsTUFBTSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUM7aUJBQ2QsT0FBTyxDQUFDLGVBQWUsR0FBRyxjQUFjLEdBQUcsVUFBVSxDQUFDLENBQUM7WUFDNUQsaUJBQWlCLENBQ2IsTUFBTSxHQUFHLENBQUMsSUFBSSxFQUFFLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsQ0FBQyxDQUFDLENBQUM7WUFFaEUsdUJBQXVCO1lBQ3ZCLFVBQVUsR0FBRyxDQUFDLENBQUM7WUFDZixNQUFNLGNBQWMsR0FBRyxFQUFFLENBQUMsT0FBTyxDQUFDLFVBQVUsQ0FBQyxXQUFXLEVBQUUsVUFBVSxDQUFDLENBQUM7WUFDdEUsTUFBTSxDQUFDLGNBQWMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxPQUFPLENBQUM7Z0JBQ25DLGVBQWUsRUFBRSxjQUFjLEVBQUUsVUFBVTthQUM1QyxDQUFDLENBQUM7WUFDSCxNQUFNLEtBQUssR0FBRyxNQUFNLGNBQWMsQ0FBQyxJQUFJLEVBQUUsQ0FBQztZQUMxQyxNQUFNLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQztpQkFDZixPQUFPLENBQUMsZUFBZSxHQUFHLGNBQWMsR0FBRyxVQUFVLENBQUMsQ0FBQztZQUM1RCxpQkFBaUIsQ0FDYixNQUFNLGNBQWMsQ0FBQyxJQUFJLEVBQUUsRUFDM0IsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxHQUFHLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsR0FBRyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRSxFQUFFLEdBQUcsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxHQUFHLENBQUMsQ0FBQyxDQUFDO1FBQ3BFLENBQUMsQ0FBQyxDQUFDO0tBQ0o7QUFDSCxDQUFDLENBQUMsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCAyMDIwIEdvb2dsZSBJbmMuIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogaHR0cDovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gKi9cblxuaW1wb3J0ICogYXMgdGYgZnJvbSAnLi4vaW5kZXgnO1xuaW1wb3J0IHtCUk9XU0VSX0VOVlMsIGRlc2NyaWJlV2l0aEZsYWdzLCBOT0RFX0VOVlN9IGZyb20gJy4uL2phc21pbmVfdXRpbCc7XG5pbXBvcnQge2NyZWF0ZVZpZGVvRWxlbWVudCwgZXhwZWN0QXJyYXlzQ2xvc2UsIGV4cGVjdEFycmF5c0VxdWFsLCBwbGF5fSBmcm9tICcuLi90ZXN0X3V0aWwnO1xuXG5jbGFzcyBNb2NrQ29udGV4dCB7XG4gIGdldEltYWdlRGF0YSh4OiBudW1iZXIsIHk6IG51bWJlciwgd2lkdGg6IG51bWJlciwgaGVpZ2h0OiBudW1iZXIpIHtcbiAgICBjb25zdCBkYXRhID0gbmV3IFVpbnQ4Q2xhbXBlZEFycmF5KHdpZHRoICogaGVpZ2h0ICogNCk7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBkYXRhLmxlbmd0aDsgKytpKSB7XG4gICAgICBkYXRhW2ldID0gaSArIDE7XG4gICAgfVxuICAgIHJldHVybiB7ZGF0YX07XG4gIH1cbn1cblxuY2xhc3MgTW9ja0NhbnZhcyB7XG4gIGNvbnN0cnVjdG9yKHB1YmxpYyB3aWR0aDogbnVtYmVyLCBwdWJsaWMgaGVpZ2h0OiBudW1iZXIpIHt9XG4gIGdldENvbnRleHQodHlwZTogJzJkJyk6IE1vY2tDb250ZXh0IHtcbiAgICByZXR1cm4gbmV3IE1vY2tDb250ZXh0KCk7XG4gIH1cbn1cblxuZGVzY3JpYmVXaXRoRmxhZ3MoJ2Zyb21QaXhlbHMsIG1vY2sgY2FudmFzJywgTk9ERV9FTlZTLCAoKSA9PiB7XG4gIGl0KCdhY2NlcHRzIGEgY2FudmFzLWxpa2UgZWxlbWVudCcsIGFzeW5jICgpID0+IHtcbiAgICBjb25zdCBjID0gbmV3IE1vY2tDYW52YXMoMiwgMik7XG4gICAgLy8gdHNsaW50OmRpc2FibGUtbmV4dC1saW5lOm5vLWFueVxuICAgIGNvbnN0IHQgPSB0Zi5icm93c2VyLmZyb21QaXhlbHMoYyBhcyBhbnkpO1xuICAgIGV4cGVjdCh0LmR0eXBlKS50b0JlKCdpbnQzMicpO1xuICAgIGV4cGVjdCh0LnNoYXBlKS50b0VxdWFsKFsyLCAyLCAzXSk7XG4gICAgZXhwZWN0QXJyYXlzRXF1YWwoXG4gICAgICAgIGF3YWl0IHQuZGF0YSgpLCBbMSwgMiwgMywgNSwgNiwgNywgOSwgMTAsIDExLCAxMywgMTQsIDE1XSk7XG4gIH0pO1xuXG4gIGl0KCdhY2NlcHRzIGEgY2FudmFzLWxpa2UgZWxlbWVudCwgbnVtQ2hhbm5lbHM9NCcsIGFzeW5jICgpID0+IHtcbiAgICBjb25zdCBjID0gbmV3IE1vY2tDYW52YXMoMiwgMik7XG4gICAgLy8gdHNsaW50OmRpc2FibGUtbmV4dC1saW5lOm5vLWFueVxuICAgIGNvbnN0IHQgPSB0Zi5icm93c2VyLmZyb21QaXhlbHMoYyBhcyBhbnksIDQpO1xuICAgIGV4cGVjdCh0LmR0eXBlKS50b0JlKCdpbnQzMicpO1xuICAgIGV4cGVjdCh0LnNoYXBlKS50b0VxdWFsKFsyLCAyLCA0XSk7XG4gICAgZXhwZWN0QXJyYXlzRXF1YWwoXG4gICAgICAgIGF3YWl0IHQuZGF0YSgpLFxuICAgICAgICBbMSwgMiwgMywgNCwgNSwgNiwgNywgOCwgOSwgMTAsIDExLCAxMiwgMTMsIDE0LCAxNSwgMTZdKTtcbiAgfSk7XG5cbiAgaXQoJ2Vycm9ycyB3aGVuIHBhc3NlZCBhIG5vbi1jYW52YXMgb2JqZWN0JywgKCkgPT4ge1xuICAgIC8vIHRzbGludDpkaXNhYmxlLW5leHQtbGluZTpuby1hbnlcbiAgICBleHBlY3QoKCkgPT4gdGYuYnJvd3Nlci5mcm9tUGl4ZWxzKDUgYXMgYW55KSkudG9UaHJvd0Vycm9yKCk7XG4gIH0pO1xufSk7XG5cbmRlc2NyaWJlV2l0aEZsYWdzKCdmcm9tUGl4ZWxzJywgQlJPV1NFUl9FTlZTLCAoKSA9PiB7XG4gIGl0KCdJbWFnZURhdGEgMXgxeDMnLCBhc3luYyAoKSA9PiB7XG4gICAgY29uc3QgcGl4ZWxzID0gbmV3IEltYWdlRGF0YSgxLCAxKTtcbiAgICBwaXhlbHMuZGF0YVswXSA9IDA7XG4gICAgcGl4ZWxzLmRhdGFbMV0gPSA4MDtcbiAgICBwaXhlbHMuZGF0YVsyXSA9IDE2MDtcbiAgICBwaXhlbHMuZGF0YVszXSA9IDI0MDtcblxuICAgIGNvbnN0IGFycmF5ID0gdGYuYnJvd3Nlci5mcm9tUGl4ZWxzKHBpeGVscywgMyk7XG5cbiAgICBleHBlY3RBcnJheXNFcXVhbChhd2FpdCBhcnJheS5kYXRhKCksIFswLCA4MCwgMTYwXSk7XG4gIH0pO1xuXG4gIGl0KCdJbWFnZURhdGEgMXgxeDQnLCBhc3luYyAoKSA9PiB7XG4gICAgY29uc3QgcGl4ZWxzID0gbmV3IEltYWdlRGF0YSgxLCAxKTtcbiAgICBwaXhlbHMuZGF0YVswXSA9IDA7XG4gICAgcGl4ZWxzLmRhdGFbMV0gPSA4MDtcbiAgICBwaXhlbHMuZGF0YVsyXSA9IDE2MDtcbiAgICBwaXhlbHMuZGF0YVszXSA9IDI0MDtcblxuICAgIGNvbnN0IGFycmF5ID0gdGYuYnJvd3Nlci5mcm9tUGl4ZWxzKHBpeGVscywgNCk7XG5cbiAgICBleHBlY3RBcnJheXNFcXVhbChhd2FpdCBhcnJheS5kYXRhKCksIFswLCA4MCwgMTYwLCAyNDBdKTtcbiAgfSk7XG5cbiAgaXQoJ0ltYWdlRGF0YSAyeDJ4MycsIGFzeW5jICgpID0+IHtcbiAgICBjb25zdCBwaXhlbHMgPSBuZXcgSW1hZ2VEYXRhKDIsIDIpO1xuXG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCA4OyBpKyspIHtcbiAgICAgIHBpeGVscy5kYXRhW2ldID0gaSAqIDI7XG4gICAgfVxuICAgIGZvciAobGV0IGkgPSA4OyBpIDwgMTY7IGkrKykge1xuICAgICAgcGl4ZWxzLmRhdGFbaV0gPSBpICogMjtcbiAgICB9XG5cbiAgICBjb25zdCBhcnJheSA9IHRmLmJyb3dzZXIuZnJvbVBpeGVscyhwaXhlbHMsIDMpO1xuXG4gICAgZXhwZWN0QXJyYXlzRXF1YWwoXG4gICAgICAgIGF3YWl0IGFycmF5LmRhdGEoKSwgWzAsIDIsIDQsIDgsIDEwLCAxMiwgMTYsIDE4LCAyMCwgMjQsIDI2LCAyOF0pO1xuICB9KTtcblxuICBpdCgnSW1hZ2VEYXRhIDJ4Mng0JywgYXN5bmMgKCkgPT4ge1xuICAgIGNvbnN0IHBpeGVscyA9IG5ldyBJbWFnZURhdGEoMiwgMik7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCA4OyBpKyspIHtcbiAgICAgIHBpeGVscy5kYXRhW2ldID0gaSAqIDI7XG4gICAgfVxuICAgIGZvciAobGV0IGkgPSA4OyBpIDwgMTY7IGkrKykge1xuICAgICAgcGl4ZWxzLmRhdGFbaV0gPSBpICogMjtcbiAgICB9XG5cbiAgICBjb25zdCBhcnJheSA9IHRmLmJyb3dzZXIuZnJvbVBpeGVscyhwaXhlbHMsIDQpO1xuXG4gICAgZXhwZWN0QXJyYXlzQ2xvc2UoXG4gICAgICAgIGF3YWl0IGFycmF5LmRhdGEoKSxcbiAgICAgICAgbmV3IEludDMyQXJyYXkoXG4gICAgICAgICAgICBbMCwgMiwgNCwgNiwgOCwgMTAsIDEyLCAxNCwgMTYsIDE4LCAyMCwgMjIsIDI0LCAyNiwgMjgsIDMwXSkpO1xuICB9KTtcblxuICBpdCgnZnJvbVBpeGVscywgMyBjaGFubmVscycsIGFzeW5jICgpID0+IHtcbiAgICBjb25zdCBwaXhlbHMgPSBuZXcgSW1hZ2VEYXRhKDEsIDIpO1xuICAgIHBpeGVscy5kYXRhWzBdID0gMjtcbiAgICBwaXhlbHMuZGF0YVsxXSA9IDM7XG4gICAgcGl4ZWxzLmRhdGFbMl0gPSA0O1xuICAgIHBpeGVscy5kYXRhWzNdID0gMjU1OyAgLy8gTm90IHVzZWQuXG4gICAgcGl4ZWxzLmRhdGFbNF0gPSA1O1xuICAgIHBpeGVscy5kYXRhWzVdID0gNjtcbiAgICBwaXhlbHMuZGF0YVs2XSA9IDc7XG4gICAgcGl4ZWxzLmRhdGFbN10gPSAyNTU7ICAvLyBOb3QgdXNlZC5cbiAgICBjb25zdCByZXMgPSB0Zi5icm93c2VyLmZyb21QaXhlbHMocGl4ZWxzLCAzKTtcbiAgICBleHBlY3QocmVzLnNoYXBlKS50b0VxdWFsKFsyLCAxLCAzXSk7XG4gICAgZXhwZWN0KHJlcy5kdHlwZSkudG9CZSgnaW50MzInKTtcbiAgICBleHBlY3RBcnJheXNDbG9zZShhd2FpdCByZXMuZGF0YSgpLCBbMiwgMywgNCwgNSwgNiwgN10pO1xuICB9KTtcblxuICBpdCgnZnJvbVBpeGVscywgcmVzaGFwZSwgdGhlbiBkbyB0Zi5hZGQoKScsIGFzeW5jICgpID0+IHtcbiAgICBjb25zdCBwaXhlbHMgPSBuZXcgSW1hZ2VEYXRhKDEsIDEpO1xuICAgIHBpeGVscy5kYXRhWzBdID0gMjtcbiAgICBwaXhlbHMuZGF0YVsxXSA9IDM7XG4gICAgcGl4ZWxzLmRhdGFbMl0gPSA0O1xuICAgIHBpeGVscy5kYXRhWzNdID0gMjU1OyAgLy8gTm90IHVzZWQuXG4gICAgY29uc3QgYSA9IHRmLmJyb3dzZXIuZnJvbVBpeGVscyhwaXhlbHMsIDMpLnJlc2hhcGUoWzEsIDEsIDEsIDNdKTtcbiAgICBjb25zdCByZXMgPSBhLmFkZCh0Zi5zY2FsYXIoMiwgJ2ludDMyJykpO1xuICAgIGV4cGVjdChyZXMuc2hhcGUpLnRvRXF1YWwoWzEsIDEsIDEsIDNdKTtcbiAgICBleHBlY3QocmVzLmR0eXBlKS50b0JlKCdpbnQzMicpO1xuICAgIGV4cGVjdEFycmF5c0Nsb3NlKGF3YWl0IHJlcy5kYXRhKCksIFs0LCA1LCA2XSk7XG4gIH0pO1xuXG4gIGl0KCdmcm9tUGl4ZWxzICsgZnJvbVBpeGVscycsIGFzeW5jICgpID0+IHtcbiAgICBjb25zdCBwaXhlbHNBID0gbmV3IEltYWdlRGF0YSgxLCAxKTtcbiAgICBwaXhlbHNBLmRhdGFbMF0gPSAyNTU7XG4gICAgcGl4ZWxzQS5kYXRhWzFdID0gMztcbiAgICBwaXhlbHNBLmRhdGFbMl0gPSA0O1xuICAgIHBpeGVsc0EuZGF0YVszXSA9IDI1NTsgIC8vIE5vdCB1c2VkLlxuICAgIGNvbnN0IHBpeGVsc0IgPSBuZXcgSW1hZ2VEYXRhKDEsIDEpO1xuICAgIHBpeGVsc0IuZGF0YVswXSA9IDU7XG4gICAgcGl4ZWxzQi5kYXRhWzFdID0gNjtcbiAgICBwaXhlbHNCLmRhdGFbMl0gPSA3O1xuICAgIHBpeGVsc0IuZGF0YVszXSA9IDI1NTsgIC8vIE5vdCB1c2VkLlxuICAgIGNvbnN0IGEgPSB0Zi5icm93c2VyLmZyb21QaXhlbHMocGl4ZWxzQSwgMykudG9GbG9hdCgpO1xuICAgIGNvbnN0IGIgPSB0Zi5icm93c2VyLmZyb21QaXhlbHMocGl4ZWxzQiwgMykudG9GbG9hdCgpO1xuICAgIGNvbnN0IHJlcyA9IGEuYWRkKGIpO1xuICAgIGV4cGVjdChyZXMuc2hhcGUpLnRvRXF1YWwoWzEsIDEsIDNdKTtcbiAgICBleHBlY3QocmVzLmR0eXBlKS50b0JlKCdmbG9hdDMyJyk7XG4gICAgZXhwZWN0QXJyYXlzQ2xvc2UoYXdhaXQgcmVzLmRhdGEoKSwgWzI2MCwgOSwgMTFdKTtcbiAgfSk7XG4gIGl0KCdmcm9tUGl4ZWxzIGZvciBQaXhlbERhdGEgdHlwZScsIGFzeW5jICgpID0+IHtcbiAgICBjb25zdCBkYXRhQSA9IG5ldyBVaW50OEFycmF5KFsyNTUsIDMsIDQsIDI1NV0pO1xuICAgIGNvbnN0IHBpeGVsc0EgPSB7d2lkdGg6IDEsIGhlaWdodDogMSwgZGF0YTogZGF0YUF9O1xuXG4gICAgY29uc3QgZGF0YUIgPSBuZXcgVWludDhBcnJheShbNSwgNiwgNywgMjU1XSk7XG4gICAgY29uc3QgcGl4ZWxzQiA9IHt3aWR0aDogMSwgaGVpZ2h0OiAxLCBkYXRhOiBkYXRhQn07XG4gICAgY29uc3QgYSA9IHRmLmJyb3dzZXIuZnJvbVBpeGVscyhwaXhlbHNBLCAzKS50b0Zsb2F0KCk7XG4gICAgY29uc3QgYiA9IHRmLmJyb3dzZXIuZnJvbVBpeGVscyhwaXhlbHNCLCAzKS50b0Zsb2F0KCk7XG4gICAgY29uc3QgcmVzID0gYS5hZGQoYik7XG4gICAgZXhwZWN0KHJlcy5zaGFwZSkudG9FcXVhbChbMSwgMSwgM10pO1xuICAgIGV4cGVjdChyZXMuZHR5cGUpLnRvQmUoJ2Zsb2F0MzInKTtcbiAgICBleHBlY3RBcnJheXNDbG9zZShhd2FpdCByZXMuZGF0YSgpLCBbMjYwLCA5LCAxMV0pO1xuICB9KTtcblxuICBpdCgnZnJvbVBpeGVscyBmb3IgSFRNTENhbnZhc0VsZW1lbnQnLCBhc3luYyAoKSA9PiB7XG4gICAgY29uc3QgY2FudmFzID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnY2FudmFzJyk7XG4gICAgY2FudmFzLndpZHRoID0gMTtcbiAgICBjYW52YXMuaGVpZ2h0ID0gMTtcbiAgICBjb25zdCBjdHggPSBjYW52YXMuZ2V0Q29udGV4dCgnMmQnKTtcbiAgICBjb25zdCBwaXhlbHMgPSBuZXcgSW1hZ2VEYXRhKDEsIDEpO1xuICAgIHBpeGVscy5kYXRhWzBdID0gMDtcbiAgICBwaXhlbHMuZGF0YVsxXSA9IDgwO1xuICAgIHBpeGVscy5kYXRhWzJdID0gMTYwO1xuICAgIHBpeGVscy5kYXRhWzNdID0gMjQwO1xuICAgIGN0eC5wdXRJbWFnZURhdGEocGl4ZWxzLCAxLCAxKTtcbiAgICBjb25zdCByZXMgPSB0Zi5icm93c2VyLmZyb21QaXhlbHMoY2FudmFzKTtcbiAgICBleHBlY3QocmVzLnNoYXBlKS50b0VxdWFsKFsxLCAxLCAzXSk7XG4gICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlcy5kYXRhKCk7XG4gICAgZXhwZWN0KGRhdGEubGVuZ3RoKS50b0VxdWFsKDEgKiAxICogMyk7XG4gIH0pO1xuICBpdCgnZnJvbVBpeGVscyBmb3IgSFRNTEltYWdlRWxlbWVudCcsIGFzeW5jICgpID0+IHtcbiAgICBjb25zdCBpbWcgPSBuZXcgSW1hZ2UoMTAsIDEwKTtcbiAgICBpbWcuc3JjID0gJ2RhdGE6aW1hZ2UvZ2lmO2Jhc2U2NCcgK1xuICAgICAgICAnLFIwbEdPRGxoQVFBQkFJQUFBUC8vL3dBQUFDSDVCQUVBQUFBQUxBQUFBQUFCQUFFQUFBSUNSQUVBT3c9PSc7XG4gICAgY29uc3QgcmVzID0gdGYuYnJvd3Nlci5mcm9tUGl4ZWxzKGltZyk7XG4gICAgZXhwZWN0KHJlcy5zaGFwZSkudG9FcXVhbChbMTAsIDEwLCAzXSk7XG4gICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlcy5kYXRhKCk7XG4gICAgZXhwZWN0KGRhdGEubGVuZ3RoKS50b0VxdWFsKDEwICogMTAgKiAzKTtcbiAgfSk7XG4gIGl0KCdmcm9tUGl4ZWxzIGZvciBIVE1MVmlkZW9FbGVtZW50JywgYXN5bmMgKCkgPT4ge1xuICAgIGNvbnN0IHNvdXJjZSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ3NvdXJjZScpO1xuICAgIHNvdXJjZS5zcmMgPVxuICAgICAgICAvLyB0c2xpbnQ6ZGlzYWJsZS1uZXh0LWxpbmU6bWF4LWxpbmUtbGVuZ3RoXG4gICAgICAgICdkYXRhOnZpZGVvL21wNDtiYXNlNjQsQUFBQUlHWjBlWEJwYzI5dEFBQUNBR2x6YjIxcGMyOHlZWFpqTVcxd05ERUFBQUFJWm5KbFpRQUFBdTF0WkdGMEFBQUNyUVlGLy8rcDNFWHB2ZWJaU0xlV0xOZ2cyU1B1NzNneU5qUWdMU0JqYjNKbElERTFOU0J5TWprd01TQTNaREJtWmpJeUlDMGdTQzR5TmpRdlRWQkZSeTAwSUVGV1F5QmpiMlJsWXlBdElFTnZjSGxzWldaMElESXdNRE10TWpBeE9DQXRJR2gwZEhBNkx5OTNkM2N1ZG1sa1pXOXNZVzR1YjNKbkwzZ3lOalF1YUhSdGJDQXRJRzl3ZEdsdmJuTTZJR05oWW1GalBURWdjbVZtUFRNZ1pHVmliRzlqYXoweE9qQTZNQ0JoYm1Gc2VYTmxQVEI0TXpvd2VERXhNeUJ0WlQxb1pYZ2djM1ZpYldVOU55QndjM2s5TVNCd2MzbGZjbVE5TVM0d01Eb3dMakF3SUcxcGVHVmtYM0psWmoweElHMWxYM0poYm1kbFBURTJJR05vY205dFlWOXRaVDB4SUhSeVpXeHNhWE05TVNBNGVEaGtZM1E5TVNCamNXMDlNQ0JrWldGa2VtOXVaVDB5TVN3eE1TQm1ZWE4wWDNCemEybHdQVEVnWTJoeWIyMWhYM0Z3WDI5bVpuTmxkRDB0TWlCMGFISmxZV1J6UFRNZ2JHOXZhMkZvWldGa1gzUm9jbVZoWkhNOU1TQnpiR2xqWldSZmRHaHlaV0ZrY3owd0lHNXlQVEFnWkdWamFXMWhkR1U5TVNCcGJuUmxjbXhoWTJWa1BUQWdZbXgxY21GNVgyTnZiWEJoZEQwd0lHTnZibk4wY21GcGJtVmtYMmx1ZEhKaFBUQWdZbVp5WVcxbGN6MHpJR0pmY0hseVlXMXBaRDB5SUdKZllXUmhjSFE5TVNCaVgySnBZWE05TUNCa2FYSmxZM1E5TVNCM1pXbG5hSFJpUFRFZ2IzQmxibDluYjNBOU1DQjNaV2xuYUhSd1BUSWdhMlY1YVc1MFBUSTFNQ0JyWlhscGJuUmZiV2x1UFRFZ2MyTmxibVZqZFhROU5EQWdhVzUwY21GZmNtVm1jbVZ6YUQwd0lISmpYMnh2YjJ0aGFHVmhaRDAwTUNCeVl6MWpjbVlnYldKMGNtVmxQVEVnWTNKbVBUSTRMakFnY1dOdmJYQTlNQzQyTUNCeGNHMXBiajB3SUhGd2JXRjRQVFk1SUhGd2MzUmxjRDAwSUdsd1gzSmhkR2x2UFRFdU5EQWdZWEU5TVRveExqQXdBSUFBQUFBd1pZaUVBRC8vOG0rUDVPWGZCZUxHT2ZLRTN4a09EdkZadUJmbEh2LytWd0pJdGE2Y2JwSW80QUJMb0tCYVlUa1RBQUFDN20xdmIzWUFBQUJzYlhab1pBQUFBQUFBQUFBQUFBQUFBQUFBQStnQUFBUG9BQUVBQUFFQUFBQUFBQUFBQUFBQUFBQUJBQUFBQUFBQUFBQUFBQUFBQUFBQUFRQUFBQUFBQUFBQUFBQUFBQUFBUUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBSUFBQUlZZEhKaGF3QUFBRngwYTJoa0FBQUFBd0FBQUFBQUFBQUFBQUFBQVFBQUFBQUFBQVBvQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFCQUFBQUFBQUFBQUFBQUFBQUFBQUFBUUFBQUFBQUFBQUFBQUFBQUFBQVFBQUFBQUNnQUFBQVdnQUFBQUFBSkdWa2RITUFBQUFjWld4emRBQUFBQUFBQUFBQkFBQUQ2QUFBQUFBQUFRQUFBQUFCa0cxa2FXRUFBQUFnYldSb1pBQUFBQUFBQUFBQUFBQUFBQUFBUUFBQUFFQUFWY1FBQUFBQUFDMW9aR3h5QUFBQUFBQUFBQUIyYVdSbEFBQUFBQUFBQUFBQUFBQUFWbWxrWlc5SVlXNWtiR1Z5QUFBQUFUdHRhVzVtQUFBQUZIWnRhR1FBQUFBQkFBQUFBQUFBQUFBQUFBQWtaR2x1WmdBQUFCeGtjbVZtQUFBQUFBQUFBQUVBQUFBTWRYSnNJQUFBQUFFQUFBRDdjM1JpYkFBQUFKZHpkSE5rQUFBQUFBQUFBQUVBQUFDSFlYWmpNUUFBQUFBQUFBQUJBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQ2dBRm9BU0FBQUFFZ0FBQUFBQUFBQUFRQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUJqLy93QUFBREZoZG1OREFXUUFDdi9oQUJoblpBQUtyTmxDamZraEFBQURBQUVBQUFNQUFnOFNKWllCQUFabzYrSkxJc0FBQUFBWWMzUjBjd0FBQUFBQUFBQUJBQUFBQVFBQVFBQUFBQUFjYzNSell3QUFBQUFBQUFBQkFBQUFBUUFBQUFFQUFBQUJBQUFBRkhOMGMzb0FBQUFBQUFBQzVRQUFBQUVBQUFBVWMzUmpid0FBQUFBQUFBQUJBQUFBTUFBQUFHSjFaSFJoQUFBQVdtMWxkR0VBQUFBQUFBQUFJV2hrYkhJQUFBQUFBQUFBQUcxa2FYSmhjSEJzQUFBQUFBQUFBQUFBQUFBQUxXbHNjM1FBQUFBbHFYUnZid0FBQUIxa1lYUmhBQUFBQVFBQUFBQk1ZWFptTlRndU1USXVNVEF3JztcbiAgICBzb3VyY2UudHlwZSA9ICd2aWRlby9tcDQnO1xuXG4gICAgY29uc3QgdmlkZW8gPSBhd2FpdCBjcmVhdGVWaWRlb0VsZW1lbnQoc291cmNlKTtcbiAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKHZpZGVvKTtcbiAgICBhd2FpdCBwbGF5KHZpZGVvKTtcblxuICAgIGNvbnN0IHJlcyA9IHRmLmJyb3dzZXIuZnJvbVBpeGVscyh2aWRlbyk7XG4gICAgZXhwZWN0KHJlcy5zaGFwZSkudG9FcXVhbChbOTAsIDE2MCwgM10pO1xuICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXMuZGF0YSgpO1xuICAgIGV4cGVjdChkYXRhLmxlbmd0aCkudG9FcXVhbCg5MCAqIDE2MCAqIDMpO1xuICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQodmlkZW8pO1xuICB9LCAzMF8wMDAgLyogMzAgc2Vjb25kcyAqLyk7XG5cbiAgaXQoJ3Rocm93cyB3aGVuIHBhc3NlZCBhIHByaW1pdGl2ZSBudW1iZXInLCAoKSA9PiB7XG4gICAgY29uc3QgbXNnID0gL3BpeGVscyBwYXNzZWQgdG8gdGYuYnJvd3Nlci5mcm9tUGl4ZWxzXFwoXFwpIG11c3QgYmUgZWl0aGVyLztcbiAgICAvLyB0c2xpbnQ6ZGlzYWJsZS1uZXh0LWxpbmU6bm8tYW55XG4gICAgZXhwZWN0KCgpID0+IHRmLmJyb3dzZXIuZnJvbVBpeGVscygzIGFzIGFueSkpLnRvVGhyb3dFcnJvcihtc2cpO1xuICB9KTtcblxuICBpdCgndGhyb3dzIHdoZW4gcGFzc2VkIGEgc3RyaW5nJywgKCkgPT4ge1xuICAgIGNvbnN0IG1zZyA9IC9waXhlbHMgcGFzc2VkIHRvIHRmLmJyb3dzZXIuZnJvbVBpeGVsc1xcKFxcKSBtdXN0IGJlIGVpdGhlci87XG4gICAgLy8gdHNsaW50OmRpc2FibGUtbmV4dC1saW5lOm5vLWFueVxuICAgIGV4cGVjdCgoKSA9PiB0Zi5icm93c2VyLmZyb21QaXhlbHMoJ3Rlc3QnIGFzIGFueSkpLnRvVGhyb3dFcnJvcihtc2cpO1xuICB9KTtcblxuICBpdCgnY2FudmFzIGFuZCBpbWFnZSBtYXRjaCcsIGFzeW5jICgpID0+IHtcbiAgICBjb25zdCBpbWcgPSBuZXcgSW1hZ2UoKTtcbiAgICBjb25zdCBzaXplID0gODA7XG4gICAgaW1nLnNyYyA9XG4gICAgICAgIC8vIHRzbGludDpkaXNhYmxlLW5leHQtbGluZTptYXgtbGluZS1sZW5ndGhcbiAgICAgICAgJ2RhdGE6aW1hZ2UvanBlZztiYXNlNjQsLzlqLzRBQVFTa1pKUmdBQkFRQUFTQUJJQUFELzRRQ01SWGhwWmdBQVRVMEFLZ0FBQUFnQUJRRVNBQU1BQUFBQkFBRUFBQUVhQUFVQUFBQUJBQUFBU2dFYkFBVUFBQUFCQUFBQVVnRW9BQU1BQUFBQkFBSUFBSWRwQUFRQUFBQUJBQUFBV2dBQUFBQUFBQUJJQUFBQUFRQUFBRWdBQUFBQkFBT2dBUUFEQUFBQUFRQUJBQUNnQWdBRUFBQUFBUUFBQUZDZ0F3QUVBQUFBQVFBQUFEd0FBQUFBLyswQU9GQm9iM1J2YzJodmNDQXpMakFBT0VKSlRRUUVBQUFBQUFBQU9FSkpUUVFsQUFBQUFBQVExQjJNMlk4QXNnVHBnQW1ZN1BoQ2Z2L0FBQkVJQUR3QVVBTUJJZ0FDRVFFREVRSC94QUFmQUFBQkJRRUJBUUVCQVFBQUFBQUFBQUFBQVFJREJBVUdCd2dKQ2d2L3hBQzFFQUFDQVFNREFnUURCUVVFQkFBQUFYMEJBZ01BQkJFRkVpRXhRUVlUVVdFSEluRVVNb0dSb1FnalFySEJGVkxSOENRelluS0NDUW9XRnhnWkdpVW1KeWdwS2pRMU5qYzRPVHBEUkVWR1IwaEpTbE5VVlZaWFdGbGFZMlJsWm1kb2FXcHpkSFYyZDNoNWVvT0VoWWFIaUltS2twT1VsWmFYbUptYW9xT2twYWFucUttcXNyTzB0YmEzdUxtNndzUEV4Y2JIeU1uSzB0UFUxZGJYMk5uYTRlTGo1T1htNStqcDZ2SHk4L1QxOXZmNCtmci94QUFmQVFBREFRRUJBUUVCQVFFQkFBQUFBQUFBQVFJREJBVUdCd2dKQ2d2L3hBQzFFUUFDQVFJRUJBTUVCd1VFQkFBQkFuY0FBUUlERVFRRklURUdFa0ZSQjJGeEV5SXlnUWdVUXBHaHNjRUpJek5TOEJWaWN0RUtGaVEwNFNYeEZ4Z1pHaVluS0NrcU5UWTNPRGs2UTBSRlJrZElTVXBUVkZWV1YxaFpXbU5rWldabmFHbHFjM1IxZG5kNGVYcUNnNFNGaG9lSWlZcVNrNVNWbHBlWW1acWlvNlNscHFlb3FhcXlzN1MxdHJlNHVickN3OFRGeHNmSXljclMwOVRWMXRmWTJkcmk0K1RsNXVmbzZlcnk4L1QxOXZmNCtmci8yd0JEQUFrR0J4TVNFaFVTRWhJVkZSVVhGeFVXRlJVVkZSVVZEeFVWRmhVV0Z4VVZGUlVZSFNnZ0dCb2xHeFVWSVRFaEpTa3JMaTR1Rng4ek9ETXROeWd0TGl2LzJ3QkRBUW9LQ2c0TkRoc1FFQm90SUI4ZkxTMHRMUzB0TFMwdExTc3RMUzB0TFMwdExTMHRMUzB0TFMwdExTMHRMUzB0TFMwdExTMHRMUzB0TFNzdExTMHRMUzMvM1FBRUFBWC8yZ0FNQXdFQUFoRURFUUEvQVBQL0FBbFBJM25GT1gyZzVKOU81cm9QRHVvdVdaSnBFUGRTQ00xeWR4YmVYQ1dVdHV6aktFNDJucm5GTnRybFI1ZUFDVjV3UnlPUFd0WXlXZzFDN3NlaGF2Zk5FdThPRkdPNHpuNlZrNkpjenlPcFdRdTBwNFA4S0R2a2RnQUNhd2RmY3ZHdVg5OEE1cnAvQ01lMjJtUXB0Mng5ZjRtTGhpMmZUb0tLaytWTi9jVjdLMGt0N25TNmNYSlhjamxXTEFTRmxDbkhRNEhJM2R2d3B1dFdHN0R4dTBiS1JrZy9LYzlBeW5zZW1lb05PT3RySkU0Z1V5RkJqQTRCSTR3RDdHcXh2RzJxMHFGR0lLc0QzRGRmMUFOY2NLOG03czJxVUVsN3BVYThsWjlpdXk5c2tBakk2ODF2VzY4RFBYRmN4ZlhLeE1raEo1Ynkvd0RaeldzbDQzbUJjakhwam5HT3RkMEpjMnB5U1ZtYlBsaW5vb3hWZFpLZWoxb3lELy9ROGxzdFRraDNBZENDcEJHUjZWREhjWXg2anY3VjIxell4UXdrakJjY2s5Vk9lb0ttc1NOWTVRUnNBVUFrWUdNWXEzb1BVMkJwbTVJWlRoZHdYSTRIUFVHdGpVdzhGbzViNzdBNEFIWHNDM3NNMXpYaG1CSm5Lek14UUx3dVQxelhaYXJhanlBdVNRMmRvUEpDQWQvYmpIMU5aMXBMays0Mmh6U2tteVhROVk4NkZUQ3FvQ3FoaUF2RGZ4YnZRNUhvYU5aTDdQbmI3eHdnNXpuSEI1NUp6ejBybHZCVU1ndXN4bGxUeWdYeDkzZHdGOWllRFdsZlc4aHVsTWtwZjcyemNNYlNRUnhqanZYRHpKU3RmME94WGxHOWhkUVR6cmFmQTVHSFVmN1NBTUIvTWZqV0ZCNHBkWUVERE1neVVrSUhLWjRCL1N1cDA1TUNSTzZPUVIvc2tESDRFVmtXVmdqeWZaVEhsRzNGVy91TG5rWitwcnRvVlpKTkk0YWtGYzZMUTdyelZXVldESmpCOVEvY0dya3VxUnhzcU0yR2JwLyt1c1c2MGcya0pTTm1hTGZ1WUE4ajhmU3FFT3Nyekc0eUI4eHhna0RxT2E2ZWU3c1ljaC8vMGZNdE91REpJSW5ZbE1uZ250bnBtdEx4TEFJcEVRZkxDeWpoZU9oNUdmeXJOdkxEN1BkaUpIRGRDR0l4MXp3ZnlyUzhTeEdXU0JRNjRiQ2JpZmtWdStUV25MdmNhZWdvbmp0ZkxhTDVpNTY3dVFuQXgrZGRvbHBKZWtwRzJ5TWZma0k1NllDcXZ0emp0MzlqeHY4QVlBU2RiZVNYek00MnRBQXdHNG5nNXp0NmRUWHJHbDI0aWl3UC9yK25QdldHSjNTN0crSGQ3bE9MVFVoVUpFTnBBQUI2N2lPaGNkNnJYRUlsR2RvV1JUeU9wVmh6d2U0UFkxWmVZQ1FaUFU0RlZkZm5NVHh6SjN5akRzNEFMQUg4anoyelhQSlJPMmpHVTN5TGZwL2taMXpJdVIxU1E4RWpHVGpzUWVvcXRZcDVkeHpuSlVrRThBcVR6V3ZxQ0xQRUpJamhnY2cveEt3NmhoV1Vzcmx0c21Bd0h5NUlQM3ZRbm5GWFI5eVZucytwelZxYjE2TmRCK29Yak1qZ2NqRGNWNVE5MGQ1WmNqUEhYbkhwWHNzbGlvaDQ2a2ZxUlhpOXloVjJCNmhtQitvSkJyc2tySEhlNS8vUzhsYTRaNWxlWWRTdVIweUFlYTY5TkxRektqUlpnSjNvQ2M0SUhJSTlEbXNDT3pXVnlHend1Umc0cnRiVmY5V1BSVHozNkN1dWpDTHVuMHNRMjB0RGtUS2JlVm50VkNrYjBLa0U3aVRrQUFmUVkrdGV2d2xoQ203NzJ4ZDMxd00vclhpdW95c3Q0d1YyR0pSakhZa3FDZjFOZTQzUjRyaHhTVnpzd3ozT1d2eVRjUXJrajVpVDdqR1A2MUY0bzFKSEtSSnlJOGxqMjNFYmRvK2d6bjN4V1A0dm5ZWGNZQkkrVTlPRDFIZXFKcmlxNlNQYnkra3Y0ajZHaGI2ZzhSM0kyT3hCNVZoNk1POVBtdnpOSkdHVURhM0FHZS9xZTQ5cXlDMWFmaCtNTmNJRDJCUDRnZ2YxTmFVcjNTTnNXb05TbTQ2cE0zYm04d01kODE1UnFhRnBwbVVFZ094UHR6L2pYc3V1V0NJUnR6eUNmWWZUMnJ5VHhNTmx4SXE4QnRwSUhRazVyMEpNK1Z0WS85az0nO1xuXG4gICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiB7XG4gICAgICBpbWcub25sb2FkID0gKCkgPT4gcmVzb2x2ZShpbWcpO1xuICAgIH0pO1xuXG4gICAgaW1nLndpZHRoID0gc2l6ZTtcbiAgICBpbWcuaGVpZ2h0ID0gc2l6ZTtcblxuICAgIGNvbnN0IHBpeGVscyA9IGF3YWl0IHRmLmJyb3dzZXIuZnJvbVBpeGVscyhpbWcsIDQpO1xuXG4gICAgY29uc3QgY2FudmFzID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnY2FudmFzJyk7XG4gICAgY2FudmFzLndpZHRoID0gc2l6ZTtcbiAgICBjYW52YXMuaGVpZ2h0ID0gc2l6ZTtcbiAgICBjb25zdCBjdHggPSBjYW52YXMuZ2V0Q29udGV4dCgnMmQnKTtcbiAgICBjdHguZHJhd0ltYWdlKGltZywgMCwgMCwgc2l6ZSwgc2l6ZSk7XG4gICAgY29uc3QgYWN0dWFsID0gY3R4LmdldEltYWdlRGF0YSgwLCAwLCBzaXplLCBzaXplKS5kYXRhO1xuICAgIGNvbnN0IGFjdHVhbEludDMyID0gSW50MzJBcnJheS5mcm9tKGFjdHVhbCk7XG4gICAgY29uc3QgcGl4ZWxzRGF0YSA9IGF3YWl0IHBpeGVscy5kYXRhKCk7XG5cbiAgICBleHBlY3RBcnJheXNDbG9zZShwaXhlbHNEYXRhLCBhY3R1YWxJbnQzMiwgMTApO1xuICB9KTtcblxuICBpZiAodGYuZW52KCkuZ2V0Qm9vbCgnSVNfQ0hST01FJykpIHtcbiAgICBpdCgnZnJvbVBpeGVscyBmb3IgSW1hZ2VCaXRtYXAnLCBhc3luYyAoKSA9PiB7XG4gICAgICBjb25zdCBpbWFnZURhdGFXaWR0aCA9IDE7XG4gICAgICBjb25zdCBpbWFnZURhdGFIZWlnaHQgPSAyO1xuICAgICAgY29uc3QgbnVtQ2hhbm5lbCA9IDM7XG4gICAgICBjb25zdCBwaXhlbHMgPSBuZXcgSW1hZ2VEYXRhKGltYWdlRGF0YVdpZHRoLCBpbWFnZURhdGFIZWlnaHQpO1xuICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBpbWFnZURhdGFXaWR0aCAqIGltYWdlRGF0YUhlaWdodCAqIDQ7ICsraSkge1xuICAgICAgICBpZiAoaSAlIDQgPT09IDMpIHtcbiAgICAgICAgICBwaXhlbHMuZGF0YVtpXSA9IDI1NTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBwaXhlbHMuZGF0YVtpXSA9IGk7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgY29uc3QgaW1hZ2VCaXRtYXAgPSBhd2FpdCBjcmVhdGVJbWFnZUJpdG1hcChwaXhlbHMpO1xuICAgICAgY29uc3QgcmVzID0gdGYuYnJvd3Nlci5mcm9tUGl4ZWxzKGltYWdlQml0bWFwLCBudW1DaGFubmVsKTtcbiAgICAgIGltYWdlQml0bWFwLmNsb3NlKCk7XG4gICAgICBleHBlY3QocmVzLnNoYXBlKS50b0VxdWFsKFtpbWFnZURhdGFIZWlnaHQsIGltYWdlRGF0YVdpZHRoLCBudW1DaGFubmVsXSk7XG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzLmRhdGEoKTtcbiAgICAgIGV4cGVjdChkYXRhLmxlbmd0aClcbiAgICAgICAgICAudG9FcXVhbChpbWFnZURhdGFIZWlnaHQgKiBpbWFnZURhdGFXaWR0aCAqIG51bUNoYW5uZWwpO1xuICAgICAgZXhwZWN0QXJyYXlzRXF1YWwoYXdhaXQgcmVzLmRhdGEoKSwgWzAsIDEsIDIsIDQsIDUsIDZdKTtcbiAgICB9KTtcblxuICAgIGl0KCdmcm9tUGl4ZWxzIGZvciBJbWFnZUJpdG1hcCBvdXRTaGFwZSBjaGFuZ2VzJywgYXN5bmMgKCkgPT4ge1xuICAgICAgY29uc3QgaW1hZ2VEYXRhV2lkdGggPSAyO1xuICAgICAgY29uc3QgaW1hZ2VEYXRhSGVpZ2h0ID0gMjtcbiAgICAgIGxldCBudW1DaGFubmVsID0gMztcbiAgICAgIGNvbnN0IHBpeGVscyA9IG5ldyBJbWFnZURhdGEoaW1hZ2VEYXRhV2lkdGgsIGltYWdlRGF0YUhlaWdodCk7XG4gICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGltYWdlRGF0YVdpZHRoICogaW1hZ2VEYXRhSGVpZ2h0ICogNDsgKytpKSB7XG4gICAgICAgIGlmIChpICUgNCA9PT0gMykge1xuICAgICAgICAgIHBpeGVscy5kYXRhW2ldID0gMjU1O1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHBpeGVscy5kYXRhW2ldID0gaTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBjb25zdCBpbWFnZUJpdG1hcCA9IGF3YWl0IGNyZWF0ZUltYWdlQml0bWFwKHBpeGVscyk7XG4gICAgICBjb25zdCByZXMgPSB0Zi5icm93c2VyLmZyb21QaXhlbHMoaW1hZ2VCaXRtYXAsIG51bUNoYW5uZWwpO1xuICAgICAgZXhwZWN0KHJlcy5zaGFwZSkudG9FcXVhbChbaW1hZ2VEYXRhSGVpZ2h0LCBpbWFnZURhdGFXaWR0aCwgbnVtQ2hhbm5lbF0pO1xuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlcy5kYXRhKCk7XG4gICAgICBleHBlY3QoZGF0YS5sZW5ndGgpXG4gICAgICAgICAgLnRvRXF1YWwoaW1hZ2VEYXRhSGVpZ2h0ICogaW1hZ2VEYXRhV2lkdGggKiBudW1DaGFubmVsKTtcbiAgICAgIGV4cGVjdEFycmF5c0VxdWFsKFxuICAgICAgICAgIGF3YWl0IHJlcy5kYXRhKCksIFswLCAxLCAyLCA0LCA1LCA2LCA4LCA5LCAxMCwgMTIsIDEzLCAxNF0pO1xuXG4gICAgICAvLyBDaGFuZ2Ugb3V0cHV0IHNoYXBlc1xuICAgICAgbnVtQ2hhbm5lbCA9IDQ7XG4gICAgICBjb25zdCByZXNTaGFwZUNoYW5nZSA9IHRmLmJyb3dzZXIuZnJvbVBpeGVscyhpbWFnZUJpdG1hcCwgbnVtQ2hhbm5lbCk7XG4gICAgICBleHBlY3QocmVzU2hhcGVDaGFuZ2Uuc2hhcGUpLnRvRXF1YWwoW1xuICAgICAgICBpbWFnZURhdGFIZWlnaHQsIGltYWdlRGF0YVdpZHRoLCBudW1DaGFubmVsXG4gICAgICBdKTtcbiAgICAgIGNvbnN0IGRhdGEyID0gYXdhaXQgcmVzU2hhcGVDaGFuZ2UuZGF0YSgpO1xuICAgICAgZXhwZWN0KGRhdGEyLmxlbmd0aClcbiAgICAgICAgICAudG9FcXVhbChpbWFnZURhdGFIZWlnaHQgKiBpbWFnZURhdGFXaWR0aCAqIG51bUNoYW5uZWwpO1xuICAgICAgZXhwZWN0QXJyYXlzRXF1YWwoXG4gICAgICAgICAgYXdhaXQgcmVzU2hhcGVDaGFuZ2UuZGF0YSgpLFxuICAgICAgICAgIFswLCAxLCAyLCAyNTUsIDQsIDUsIDYsIDI1NSwgOCwgOSwgMTAsIDI1NSwgMTIsIDEzLCAxNCwgMjU1XSk7XG4gICAgfSk7XG4gIH1cbn0pO1xuIl19