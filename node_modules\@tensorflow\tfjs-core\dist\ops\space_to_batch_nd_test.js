/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../index';
import { ALL_ENVS, describeWithFlags } from '../jasmine_util';
import { expectArraysClose } from '../test_util';
describeWithFlags('spaceToBatchND', ALL_ENVS, () => {
    it('tensor4d, input shape=[1, 2, 2, 1], blockShape=[2, 2]', async () => {
        const t = tf.tensor4d([[[[1], [2]], [[3], [4]]]], [1, 2, 2, 1]);
        const blockShape = [2, 2];
        const paddings = [[0, 0], [0, 0]];
        const res = tf.spaceToBatchND(t, blockShape, paddings);
        expect(res.shape).toEqual([4, 1, 1, 1]);
        expectArraysClose(await res.data(), [1, 2, 3, 4]);
    });
    it('tensor4d, input shape=[1, 2, 2, 3], blockShape=[2, 2]', async () => {
        const t = tf.tensor4d([[[[1, 2, 3], [4, 5, 6]], [[7, 8, 9], [10, 11, 12]]]], [1, 2, 2, 3]);
        const blockShape = [2, 2];
        const paddings = [[0, 0], [0, 0]];
        const res = tf.spaceToBatchND(t, blockShape, paddings);
        expect(res.shape).toEqual([4, 1, 1, 3]);
        expectArraysClose(await res.data(), [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]);
    });
    it('tensor4d, input shape=[1, 4, 4, 1], blockShape=[2, 2]', async () => {
        const t = tf.tensor4d([[
                [[1], [2], [3], [4]], [[5], [6], [7], [8]], [[9], [10], [11], [12]],
                [[13], [14], [15], [16]]
            ]], [1, 4, 4, 1]);
        const blockShape = [2, 2];
        const paddings = [[0, 0], [0, 0]];
        const res = tf.spaceToBatchND(t, blockShape, paddings);
        expect(res.shape).toEqual([4, 2, 2, 1]);
        expectArraysClose(await res.data(), [1, 3, 9, 11, 2, 4, 10, 12, 5, 7, 13, 15, 6, 8, 14, 16]);
    });
    it('tensor4d, input shape=[2, 6, 6, 1], blockShape=[2, 2]', async () => {
        const t = tf.tensor4d([
            1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15,
            16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30,
            31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45,
            46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60,
            61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72
        ], [2, 6, 6, 1]);
        const blockShape = [2, 2];
        const paddings = [[0, 0], [0, 0]];
        const res = tf.spaceToBatchND(t, blockShape, paddings);
        expect(res.shape).toEqual([8, 3, 3, 1]);
        expectArraysClose(await res.data(), [
            1, 3, 5, 13, 15, 17, 25, 27, 29, 37, 39, 41, 49, 51, 53, 61, 63, 65,
            2, 4, 6, 14, 16, 18, 26, 28, 30, 38, 40, 42, 50, 52, 54, 62, 64, 66,
            7, 9, 11, 19, 21, 23, 31, 33, 35, 43, 45, 47, 55, 57, 59, 67, 69, 71,
            8, 10, 12, 20, 22, 24, 32, 34, 36, 44, 46, 48, 56, 58, 60, 68, 70, 72
        ]);
    });
    it('tensor4d, input shape=[2, 2, 4, 1], blockShape=[2, 2]', async () => {
        const t = tf.tensor4d([
            [[[1], [2], [3], [4]], [[5], [6], [7], [8]]],
            [[[9], [10], [11], [12]], [[13], [14], [15], [16]]]
        ], [2, 2, 4, 1]);
        const blockShape = [2, 2];
        const paddings = [[0, 0], [2, 0]];
        const res = tf.spaceToBatchND(t, blockShape, paddings);
        expect(res.shape).toEqual([8, 1, 3, 1]);
        expectArraysClose(await res.data(), [
            0, 1, 3, 0, 9, 11, 0, 2, 4, 0, 10, 12,
            0, 5, 7, 0, 13, 15, 0, 6, 8, 0, 14, 16
        ]);
    });
    it('tensor2d, blockShape [2]', async () => {
        const t = tf.tensor2d([1, 3, 2, 4], [1, 4]);
        const blockShape = [2];
        const paddings = [[0, 0]];
        const res = tf.spaceToBatchND(t, blockShape, paddings);
        expect(res.shape).toEqual([2, 2]);
        expectArraysClose(await res.data(), [1, 2, 3, 4]);
    });
    it('throws when blockShape equal to input rank', () => {
        const t = tf.tensor4d([1, 2, 3, 4], [1, 2, 2, 1]);
        const blockShape = [2, 2, 2, 2];
        const paddings = [[0, 0], [0, 0], [0, 0], [0, 0]];
        expect(() => tf.spaceToBatchND(t, blockShape, paddings))
            .toThrowError('input rank 4 should be > than [blockShape] 4');
    });
    it('throws when paddings row dimension not equal to blockshape', () => {
        const t = tf.tensor4d([1, 2, 3, 4], [1, 2, 2, 1]);
        const blockShape = [2, 2];
        const paddings = [[0, 0]];
        expect(() => tf.spaceToBatchND(t, blockShape, paddings))
            .toThrowError('paddings.shape[0] 1 must be equal to [blockShape] 2');
    });
    it('throws when input tensor spatial dimension not divisible by blockshapes', () => {
        const t = tf.tensor4d([1, 2, 3, 4, 5, 6], [1, 2, 3, 1]);
        const blockShape = [2, 2];
        const paddings = [[0, 0], [0, 0]];
        expect(() => tf.spaceToBatchND(t, blockShape, paddings))
            .toThrowError('input spatial dimensions 2,3,1 with paddings 0,0,0,0 must be ' +
            'divisible by blockShapes 2,2');
    });
    it('accepts a tensor-like object', async () => {
        const t = [[[[1], [2]], [[3], [4]]]];
        const blockShape = [2, 2];
        const paddings = [[0, 0], [0, 0]];
        const res = tf.spaceToBatchND(t, blockShape, paddings);
        expect(res.shape).toEqual([4, 1, 1, 1]);
        expectArraysClose(await res.data(), [1, 2, 3, 4]);
    });
});
describeWithFlags('batchToSpaceND X spaceToBatchND', ALL_ENVS, () => {
    it('tensor4d, input shape=[4, 1, 1, 1], blockShape=[2, 2]', async () => {
        const t = tf.tensor4d([1, 2, 3, 4], [4, 1, 1, 1]);
        const blockShape = [2, 2];
        const crops = [[0, 0], [0, 0]];
        const paddings = [[0, 0], [0, 0]];
        const b2s = tf.batchToSpaceND(t, blockShape, crops);
        expect(b2s.shape).toEqual([1, 2, 2, 1]);
        expectArraysClose(await b2s.data(), [1, 2, 3, 4]);
        const s2b = tf.spaceToBatchND(b2s, blockShape, paddings);
        expect(s2b.shape).toEqual([4, 1, 1, 1]);
        expectArraysClose(await s2b.data(), [1, 2, 3, 4]);
    });
    it('tensor4d, input shape=[2, 6, 6, 1], blockShape=[2, 2]', async () => {
        const t = tf.tensor4d([
            1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15,
            16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30,
            31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45,
            46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60,
            61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72
        ], [2, 6, 6, 1]);
        const blockShape = [2, 2];
        const crops = [[0, 0], [0, 0]];
        const paddings = [[0, 0], [0, 0]];
        const s2b = tf.spaceToBatchND(t, blockShape, paddings);
        expect(s2b.shape).toEqual([8, 3, 3, 1]);
        expectArraysClose(await s2b.data(), [
            1, 3, 5, 13, 15, 17, 25, 27, 29, 37, 39, 41, 49, 51, 53, 61, 63, 65,
            2, 4, 6, 14, 16, 18, 26, 28, 30, 38, 40, 42, 50, 52, 54, 62, 64, 66,
            7, 9, 11, 19, 21, 23, 31, 33, 35, 43, 45, 47, 55, 57, 59, 67, 69, 71,
            8, 10, 12, 20, 22, 24, 32, 34, 36, 44, 46, 48, 56, 58, 60, 68, 70, 72
        ]);
        const b2s = tf.batchToSpaceND(s2b, blockShape, crops);
        expect(b2s.shape).toEqual([2, 6, 6, 1]);
        expectArraysClose(await b2s.data(), [
            1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18,
            19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36,
            37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54,
            55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72
        ]);
    });
    it('gradients,  input shape=[4, 2, 2], block shape=[2]', async () => {
        const t = tf.tensor([-61, 37, -68, 72, 31, 62, 0, -13, 28, 54, 96, 44, -55, -64, -88, -94], [4, 2, 2]);
        const blockShape = [2];
        const paddings = [[0, 2]];
        const dy = tf.tensor([
            1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16,
            17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32
        ], [8, 2, 2]);
        const gradient = tf.grad(t => tf.spaceToBatchND(t, blockShape, paddings))(t, dy);
        expect(gradient.shape).toEqual([4, 2, 2]);
        expectArraysClose(await gradient.data(), [1, 2, 17, 18, 5, 6, 21, 22, 9, 10, 25, 26, 13, 14, 29, 30]);
    });
    it('gradient with clones input=[4, 2, 2], block shape=[2]', async () => {
        const t = tf.tensor([-61, 37, -68, 72, 31, 62, 0, -13, 28, 54, 96, 44, -55, -64, -88, -94], [4, 2, 2]);
        const blockShape = [2];
        const paddings = [[0, 2]];
        const dy = tf.tensor([
            1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16,
            17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32
        ], [8, 2, 2]);
        const gradient = tf.grad(t => tf.spaceToBatchND(t.clone(), blockShape, paddings).clone())(t, dy);
        expect(gradient.shape).toEqual([4, 2, 2]);
        expectArraysClose(await gradient.data(), [1, 2, 17, 18, 5, 6, 21, 22, 9, 10, 25, 26, 13, 14, 29, 30]);
    });
    it('gradients, input shape=[2, 2, 4, 1], block shape=[2, 2]', async () => {
        const t = tf.tensor4d([
            [[[1], [2], [3], [4]], [[5], [6], [7], [8]]],
            [[[9], [10], [11], [12]], [[13], [14], [15], [16]]]
        ], [2, 2, 4, 1]);
        const blockShape = [2, 2];
        const paddings = [[0, 0], [2, 0]];
        const dy = tf.tensor([
            1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12,
            13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24
        ], [8, 1, 3, 1]);
        const gradient = tf.grad(t => tf.spaceToBatchND(t, blockShape, paddings))(t, dy);
        expect(gradient.shape).toEqual([2, 2, 4, 1]);
        expectArraysClose(await gradient.data(), [2, 8, 3, 9, 14, 20, 15, 21, 5, 11, 6, 12, 17, 23, 18, 24]);
    });
});
//# sourceMappingURL=data:application/json;base64,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