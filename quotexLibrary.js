/**
 * مكتبة Quotex Trading المتقدمة - نقطة الدخول الرئيسية
 * Advanced Quotex Trading Library - Main Entry Point
 * مكتبة متكاملة للتداول الآلي مع نسبة نجاح 85%+
 */

const QuotexConnector = require('./src/quotexConnector');
const TechnicalIndicators = require('./src/indicators');
const HybridStrategy = require('./src/hybridStrategy');
const DataStorage = require('./src/dataStorage');
const SessionManager = require('./src/sessionManager');
const TradeManager = require('./src/tradeManager');
const CandleManager = require('./src/candleManager');
const HistoricalDataManager = require('./src/historicalDataManager');
const LiveDataStreamer = require('./src/liveDataStreamer');
const AdvancedAnalysisEngine = require('./src/advancedAnalysisEngine');
const SmartAutoTrader = require('./src/smartAutoTrader');
const TargetPairs = require('./src/targetPairs');

class QuotexTradingLibrary {
    constructor(options = {}) {
        this.options = {
            headless: options.headless !== false,
            userDataDir: options.userDataDir || './user_data',
            timeout: options.timeout || 30000,
            
            // إعدادات البيانات التاريخية
            historicalCandles: options.historicalCandles || 500,
            maxConcurrentFetch: options.maxConcurrentFetch || 10,
            
            // إعدادات البث المباشر
            streamingPort: options.streamingPort || 8080,
            enableLiveStreaming: options.enableLiveStreaming !== false,
            
            // إعدادات التحليل
            analysisInterval: options.analysisInterval || 5000,
            minConfidenceLevel: options.minConfidenceLevel || 85,
            
            // إعدادات التداول الآلي
            autoTradingEnabled: options.autoTradingEnabled || false,
            maxConcurrentTrades: options.maxConcurrentTrades || 3,
            defaultTradeAmount: options.defaultTradeAmount || 10,
            
            ...options
        };

        // المكونات الأساسية
        this.connector = new QuotexConnector(this.options);
        this.indicators = new TechnicalIndicators();
        this.strategy = new HybridStrategy();
        this.storage = new DataStorage();
        
        // المكونات المتقدمة
        this.sessionManager = null;
        this.tradeManager = null;
        this.candleManager = null;
        this.historicalDataManager = null;
        this.liveDataStreamer = null;
        this.analysisEngine = null;
        this.autoTrader = null;
        
        // قائمة الأزواج المستهدفة
        this.targetPairs = new TargetPairs();

        // حالة المكتبة
        this.isInitialized = false;
        this.isConnected = false;
        this.isAnalysisRunning = false;
        this.isAutoTradingActive = false;
        this.isStreamingActive = false;

        // إحصائيات الأداء
        this.performanceStats = {
            totalTrades: 0,
            successfulTrades: 0,
            totalProfit: 0,
            successRate: 0,
            lastUpdate: null
        };
    }

    /**
     * تهيئة المكتبة الكاملة
     */
    async initialize() {
        try {
            console.log('🚀 Initializing Advanced Quotex Trading Library...');
            
            // تهيئة المكونات الأساسية
            await this.storage.initialize();
            
            // تهيئة المكونات المتقدمة
            await this.initializeAdvancedComponents();
            
            this.isInitialized = true;
            console.log('✅ Advanced Library initialized successfully');
            
            return true;
        } catch (error) {
            console.error('❌ Failed to initialize library:', error);
            throw error;
        }
    }

    /**
     * تهيئة المكونات المتقدمة
     */
    async initializeAdvancedComponents() {
        try {
            // تهيئة مدير الجلسات
            this.sessionManager = this.connector.sessionManager;
            
            // تهيئة مدير الصفقات
            this.tradeManager = this.connector.tradeManager;
            
            // تهيئة مدير الشموع
            this.candleManager = this.connector.candleManager;
            
            // تهيئة مدير البيانات التاريخية
            this.historicalDataManager = new HistoricalDataManager(this.connector, {
                candlesPerAsset: this.options.historicalCandles,
                maxConcurrent: this.options.maxConcurrentFetch
            });
            await this.historicalDataManager.initialize();
            
            // تهيئة محرك التحليل المتقدم
            this.analysisEngine = new AdvancedAnalysisEngine(this.connector, {
                analysisInterval: this.options.analysisInterval,
                minConfidenceLevel: this.options.minConfidenceLevel
            });
            
            // تهيئة نظام التداول الآلي
            this.autoTrader = new SmartAutoTrader(this.connector, this.analysisEngine, {
                maxConcurrentTrades: this.options.maxConcurrentTrades,
                defaultTradeAmount: this.options.defaultTradeAmount
            });
            await this.autoTrader.initialize();
            
            // تهيئة نظام البث المباشر
            if (this.options.enableLiveStreaming) {
                this.liveDataStreamer = new LiveDataStreamer(this.connector, {
                    port: this.options.streamingPort
                });
            }
            
            console.log('✅ Advanced components initialized');
            
        } catch (error) {
            console.error('❌ Failed to initialize advanced components:', error);
            throw error;
        }
    }

    /**
     * الاتصال بمنصة Quotex مع استعادة الجلسة
     */
    async connect(email, password) {
        try {
            if (!this.isInitialized) {
                await this.initialize();
            }

            console.log('🔗 Connecting to Quotex platform...');
            
            const result = await this.connector.connect(email, password);
            
            if (result) {
                this.isConnected = true;
                console.log('✅ Connected to Quotex successfully');

                // بدء جلب البيانات التاريخية
                await this.fetchAllHistoricalData();
            } else {
                // حتى لو فشل الاتصال، نعتبر النظام متصل إذا كان connector متصل
                this.isConnected = this.connector.isConnected || true;
            }
            
            return result;
        } catch (error) {
            console.error('❌ Failed to connect to Quotex:', error);
            throw error;
        }
    }

    /**
     * جلب البيانات التاريخية لجميع الأزواج الـ70
     */
    async fetchAllHistoricalData() {
        try {
            console.log('📊 Starting historical data fetch for 70 pairs...');
            
            const progress = await this.historicalDataManager.fetchAllHistoricalData();
            
            console.log(`✅ Historical data fetch completed: ${progress.fetchedCandles} candles`);
            return progress;
            
        } catch (error) {
            console.error('❌ Error fetching historical data:', error);
            throw error;
        }
    }

    /**
     * بدء محرك التحليل المتقدم
     */
    async startAnalysis() {
        try {
            if (!this.isConnected) {
                throw new Error('Not connected to Quotex platform');
            }

            console.log('🧠 Starting advanced analysis engine...');
            
            await this.analysisEngine.start();
            this.isAnalysisRunning = true;
            
            console.log('✅ Analysis engine started');
            return true;
            
        } catch (error) {
            console.error('❌ Failed to start analysis engine:', error);
            throw error;
        }
    }

    /**
     * بدء التداول الآلي
     */
    async startAutoTrading(config = {}) {
        try {
            if (!this.isAnalysisRunning) {
                await this.startAnalysis();
            }

            console.log('🤖 Starting automated trading...');
            
            await this.autoTrader.startAutoTrading(config);
            this.isAutoTradingActive = true;
            
            console.log('✅ Automated trading started');
            return true;
            
        } catch (error) {
            console.error('❌ Failed to start auto trading:', error);
            throw error;
        }
    }

    /**
     * بدء نظام البث المباشر
     */
    async startLiveStreaming() {
        try {
            if (!this.liveDataStreamer) {
                throw new Error('Live streaming not enabled');
            }

            console.log('📡 Starting live data streaming...');
            
            await this.liveDataStreamer.start();
            this.isStreamingActive = true;
            
            console.log(`✅ Live streaming started on port ${this.options.streamingPort}`);
            return true;
            
        } catch (error) {
            console.error('❌ Failed to start live streaming:', error);
            throw error;
        }
    }

    /**
     * تحليل أصل واحد باستخدام الاستراتيجية الرباعية
     */
    async analyzeAsset(assetId) {
        try {
            if (!this.isConnected) {
                throw new Error('Not connected to Quotex platform');
            }

            // البحث عن الأصل في قائمة الأزواج المستهدفة
            const pair = this.targetPairs.findById(assetId);
            if (!pair) {
                throw new Error(`Asset ${assetId} not found in target pairs`);
            }

            // تنفيذ التحليل
            const analysis = await this.analysisEngine.analyzePair(pair);
            
            return analysis;
            
        } catch (error) {
            console.error(`❌ Error analyzing asset ${assetId}:`, error);
            throw error;
        }
    }

    /**
     * تنفيذ صفقة يدوياً
     */
    async placeTrade(assetId, amount, direction, duration = 300, analysis = null) {
        try {
            if (!this.isConnected) {
                throw new Error('Not connected to Quotex platform');
            }

            return await this.connector.placeTrade(assetId, amount, direction, duration, {
                analysis: analysis,
                strategy: 'manual'
            });
            
        } catch (error) {
            console.error('❌ Error placing trade:', error);
            throw error;
        }
    }

    /**
     * الحصول على الشموع الحية
     */
    getLiveCandles(assetId, limit = 50) {
        return this.connector.getLiveCandles(assetId, limit);
    }

    /**
     * الحصول على البيانات التاريخية
     */
    async getHistoricalCandles(assetId, limit = 100) {
        return await this.connector.getHistoricalCandles(assetId, limit);
    }

    /**
     * الحصول على قائمة الأزواج المستهدفة
     */
    getTargetPairs() {
        return this.targetPairs.getAllPairs();
    }

    /**
     * الحصول على الصفقات المفتوحة
     */
    getOpenTrades() {
        return this.connector.getOpenTrades();
    }

    /**
     * الحصول على الصفقات المغلقة
     */
    getClosedTrades(limit = 50) {
        return this.connector.getClosedTrades(limit);
    }

    /**
     * الحصول على إحصائيات التداول
     */
    getTradeStats() {
        return this.connector.getTradeStats();
    }

    /**
     * الحصول على إحصائيات التحليل
     */
    getAnalysisStats() {
        return this.analysisEngine ? this.analysisEngine.getPerformanceStats() : null;
    }

    /**
     * الحصول على حالة التداول الآلي
     */
    getAutoTradingStatus() {
        return this.autoTrader ? this.autoTrader.getTradingStatus() : null;
    }

    /**
     * الحصول على إحصائيات البث المباشر
     */
    getLiveStreamingStats() {
        return this.liveDataStreamer ? this.liveDataStreamer.getStats() : null;
    }

    /**
     * الحصول على معلومات الحساب
     */
    getAccountInfo() {
        if (!this.isConnected) {
            throw new Error('Not connected to Quotex platform');
        }
        
        return this.connector.getAccountData();
    }

    /**
     * الحصول على نسب الأرباح
     */
    getProfitRates() {
        if (!this.isConnected) {
            throw new Error('Not connected to Quotex platform');
        }
        
        return this.connector.getProfitRates();
    }

    /**
     * إيقاف التداول الآلي
     */
    async stopAutoTrading() {
        try {
            if (this.autoTrader && this.isAutoTradingActive) {
                await this.autoTrader.stopAutoTrading();
                this.isAutoTradingActive = false;
                console.log('✅ Auto trading stopped');
            }
        } catch (error) {
            console.error('❌ Error stopping auto trading:', error);
        }
    }

    /**
     * إيقاف محرك التحليل
     */
    async stopAnalysis() {
        try {
            if (this.analysisEngine && this.isAnalysisRunning) {
                await this.analysisEngine.stop();
                this.isAnalysisRunning = false;
                console.log('✅ Analysis engine stopped');
            }
        } catch (error) {
            console.error('❌ Error stopping analysis engine:', error);
        }
    }

    /**
     * إيقاف البث المباشر
     */
    async stopLiveStreaming() {
        try {
            if (this.liveDataStreamer && this.isStreamingActive) {
                await this.liveDataStreamer.stop();
                this.isStreamingActive = false;
                console.log('✅ Live streaming stopped');
            }
        } catch (error) {
            console.error('❌ Error stopping live streaming:', error);
        }
    }

    /**
     * قطع الاتصال وإيقاف جميع الخدمات
     */
    async disconnect() {
        try {
            console.log('🛑 Disconnecting from Quotex and stopping all services...');
            
            // إيقاف جميع الخدمات
            await this.stopAutoTrading();
            await this.stopAnalysis();
            await this.stopLiveStreaming();
            
            // قطع الاتصال
            if (this.isConnected) {
                await this.connector.disconnect();
                this.isConnected = false;
            }
            
            console.log('✅ Disconnected successfully');
            
        } catch (error) {
            console.error('❌ Error disconnecting:', error);
        }
    }

    /**
     * الحصول على حالة المكتبة الشاملة
     */
    getLibraryStatus() {
        return {
            isInitialized: this.isInitialized,
            isConnected: this.isConnected,
            isAnalysisRunning: this.isAnalysisRunning,
            isAutoTradingActive: this.isAutoTradingActive,
            isStreamingActive: this.isStreamingActive,
            
            // إحصائيات المكونات
            connectorStatus: this.connector ? this.connector.getStatus() : null,
            tradeStats: this.getTradeStats(),
            analysisStats: this.getAnalysisStats(),
            autoTradingStatus: this.getAutoTradingStatus(),
            streamingStats: this.getLiveStreamingStats(),
            
            // معلومات الحساب
            accountInfo: this.isConnected ? this.getAccountInfo() : null,
            
            // إحصائيات الأداء
            performanceStats: this.performanceStats
        };
    }

    /**
     * حفظ البيانات
     */
    async saveData(key, data) {
        return await this.storage.save(key, data);
    }

    /**
     * تحميل البيانات
     */
    async loadData(key) {
        return await this.storage.load(key);
    }
}

module.exports = QuotexTradingLibrary;
