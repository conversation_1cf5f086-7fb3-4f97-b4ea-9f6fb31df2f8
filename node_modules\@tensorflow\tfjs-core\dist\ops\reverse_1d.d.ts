/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
/// <amd-module name="@tensorflow/tfjs-core/dist/ops/reverse_1d" />
import { Tensor1D } from '../tensor';
import { TensorLike } from '../types';
/**
 * Reverses a `tf.Tensor1D`.
 *
 * @param x The input tensor.
 */
declare function reverse1d_(x: Tensor1D | TensorLike): Tensor1D;
export declare const reverse1d: typeof reverse1d_;
export {};
