/**
 * Validate sparseToDense inputs.
 *
 * @param sparseIndices A 0-D, 1-D, or 2-D Tensor of type int32.
 * sparseIndices[i] contains the complete index where sparseValues[i] will be
 * placed.
 * @param sparseValues A 0-D or 1-D Tensor. Values
 * corresponding to each row of sparseIndices, or a scalar value to be used for
 * all sparse indices.
 * @param outputShape number[]. Shape of the dense output tensor.
 * @param validateIndices boolean. indice validation is not supported, error
 * will be thrown if it is set.
 */
export function validateInput(sparseIndices, sparseValues, outputShape, defaultValues) {
    if (sparseIndices.dtype !== 'int32') {
        throw new Error('tf.sparseToDense() expects the indices to be int32 type,' +
            ` but the dtype was ${sparseIndices.dtype}.`);
    }
    if (sparseIndices.rank > 2) {
        throw new Error('sparseIndices should be a scalar, vector, or matrix,' +
            ` but got shape ${sparseIndices.shape}.`);
    }
    const numElems = sparseIndices.rank > 0 ? sparseIndices.shape[0] : 1;
    const numDims = sparseIndices.rank > 1 ? sparseIndices.shape[1] : 1;
    if (outputShape.length !== numDims) {
        throw new Error('outputShape has incorrect number of elements:,' +
            ` ${outputShape.length}, should be: ${numDims}.`);
    }
    const numValues = sparseValues.size;
    if (!(sparseValues.rank === 0 ||
        sparseValues.rank === 1 && numValues === numElems)) {
        throw new Error('sparseValues has incorrect shape ' +
            `${sparseValues.shape}, should be [] or [${numElems}]`);
    }
    if (sparseValues.dtype !== defaultValues.dtype) {
        throw new Error('sparseValues.dtype must match defaultValues.dtype');
    }
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic3BhcnNlX3RvX2RlbnNlX3V0aWwuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi90ZmpzLWNvcmUvc3JjL29wcy9zcGFyc2VfdG9fZGVuc2VfdXRpbC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFrQkE7Ozs7Ozs7Ozs7OztHQVlHO0FBQ0gsTUFBTSxVQUFVLGFBQWEsQ0FDekIsYUFBcUIsRUFBRSxZQUFvQixFQUFFLFdBQXFCLEVBQ2xFLGFBQXFCO0lBQ3ZCLElBQUksYUFBYSxDQUFDLEtBQUssS0FBSyxPQUFPLEVBQUU7UUFDbkMsTUFBTSxJQUFJLEtBQUssQ0FDWCwwREFBMEQ7WUFDMUQsc0JBQXNCLGFBQWEsQ0FBQyxLQUFLLEdBQUcsQ0FBQyxDQUFDO0tBQ25EO0lBQ0QsSUFBSSxhQUFhLENBQUMsSUFBSSxHQUFHLENBQUMsRUFBRTtRQUMxQixNQUFNLElBQUksS0FBSyxDQUNYLHNEQUFzRDtZQUN0RCxrQkFBa0IsYUFBYSxDQUFDLEtBQUssR0FBRyxDQUFDLENBQUM7S0FDL0M7SUFFRCxNQUFNLFFBQVEsR0FBRyxhQUFhLENBQUMsSUFBSSxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsYUFBYSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO0lBQ3JFLE1BQU0sT0FBTyxHQUFHLGFBQWEsQ0FBQyxJQUFJLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxhQUFhLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7SUFFcEUsSUFBSSxXQUFXLENBQUMsTUFBTSxLQUFLLE9BQU8sRUFBRTtRQUNsQyxNQUFNLElBQUksS0FBSyxDQUNYLGdEQUFnRDtZQUNoRCxJQUFJLFdBQVcsQ0FBQyxNQUFNLGdCQUFnQixPQUFPLEdBQUcsQ0FBQyxDQUFDO0tBQ3ZEO0lBRUQsTUFBTSxTQUFTLEdBQUcsWUFBWSxDQUFDLElBQUksQ0FBQztJQUNwQyxJQUFJLENBQUMsQ0FBQyxZQUFZLENBQUMsSUFBSSxLQUFLLENBQUM7UUFDdkIsWUFBWSxDQUFDLElBQUksS0FBSyxDQUFDLElBQUksU0FBUyxLQUFLLFFBQVEsQ0FBQyxFQUFFO1FBQ3hELE1BQU0sSUFBSSxLQUFLLENBQ1gsbUNBQW1DO1lBQ25DLEdBQUcsWUFBWSxDQUFDLEtBQUssc0JBQXNCLFFBQVEsR0FBRyxDQUFDLENBQUM7S0FDN0Q7SUFFRCxJQUFJLFlBQVksQ0FBQyxLQUFLLEtBQUssYUFBYSxDQUFDLEtBQUssRUFBRTtRQUM5QyxNQUFNLElBQUksS0FBSyxDQUFDLG1EQUFtRCxDQUFDLENBQUM7S0FDdEU7QUFDSCxDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IDIwMTggR29vZ2xlIExMQy4gQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiBodHRwOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICogPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAqL1xuaW1wb3J0IHtUZW5zb3J9IGZyb20gJy4uL3RlbnNvcic7XG5cbi8qKlxuICogVmFsaWRhdGUgc3BhcnNlVG9EZW5zZSBpbnB1dHMuXG4gKlxuICogQHBhcmFtIHNwYXJzZUluZGljZXMgQSAwLUQsIDEtRCwgb3IgMi1EIFRlbnNvciBvZiB0eXBlIGludDMyLlxuICogc3BhcnNlSW5kaWNlc1tpXSBjb250YWlucyB0aGUgY29tcGxldGUgaW5kZXggd2hlcmUgc3BhcnNlVmFsdWVzW2ldIHdpbGwgYmVcbiAqIHBsYWNlZC5cbiAqIEBwYXJhbSBzcGFyc2VWYWx1ZXMgQSAwLUQgb3IgMS1EIFRlbnNvci4gVmFsdWVzXG4gKiBjb3JyZXNwb25kaW5nIHRvIGVhY2ggcm93IG9mIHNwYXJzZUluZGljZXMsIG9yIGEgc2NhbGFyIHZhbHVlIHRvIGJlIHVzZWQgZm9yXG4gKiBhbGwgc3BhcnNlIGluZGljZXMuXG4gKiBAcGFyYW0gb3V0cHV0U2hhcGUgbnVtYmVyW10uIFNoYXBlIG9mIHRoZSBkZW5zZSBvdXRwdXQgdGVuc29yLlxuICogQHBhcmFtIHZhbGlkYXRlSW5kaWNlcyBib29sZWFuLiBpbmRpY2UgdmFsaWRhdGlvbiBpcyBub3Qgc3VwcG9ydGVkLCBlcnJvclxuICogd2lsbCBiZSB0aHJvd24gaWYgaXQgaXMgc2V0LlxuICovXG5leHBvcnQgZnVuY3Rpb24gdmFsaWRhdGVJbnB1dChcbiAgICBzcGFyc2VJbmRpY2VzOiBUZW5zb3IsIHNwYXJzZVZhbHVlczogVGVuc29yLCBvdXRwdXRTaGFwZTogbnVtYmVyW10sXG4gICAgZGVmYXVsdFZhbHVlczogVGVuc29yKSB7XG4gIGlmIChzcGFyc2VJbmRpY2VzLmR0eXBlICE9PSAnaW50MzInKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgICAndGYuc3BhcnNlVG9EZW5zZSgpIGV4cGVjdHMgdGhlIGluZGljZXMgdG8gYmUgaW50MzIgdHlwZSwnICtcbiAgICAgICAgYCBidXQgdGhlIGR0eXBlIHdhcyAke3NwYXJzZUluZGljZXMuZHR5cGV9LmApO1xuICB9XG4gIGlmIChzcGFyc2VJbmRpY2VzLnJhbmsgPiAyKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgICAnc3BhcnNlSW5kaWNlcyBzaG91bGQgYmUgYSBzY2FsYXIsIHZlY3Rvciwgb3IgbWF0cml4LCcgK1xuICAgICAgICBgIGJ1dCBnb3Qgc2hhcGUgJHtzcGFyc2VJbmRpY2VzLnNoYXBlfS5gKTtcbiAgfVxuXG4gIGNvbnN0IG51bUVsZW1zID0gc3BhcnNlSW5kaWNlcy5yYW5rID4gMCA/IHNwYXJzZUluZGljZXMuc2hhcGVbMF0gOiAxO1xuICBjb25zdCBudW1EaW1zID0gc3BhcnNlSW5kaWNlcy5yYW5rID4gMSA/IHNwYXJzZUluZGljZXMuc2hhcGVbMV0gOiAxO1xuXG4gIGlmIChvdXRwdXRTaGFwZS5sZW5ndGggIT09IG51bURpbXMpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgICdvdXRwdXRTaGFwZSBoYXMgaW5jb3JyZWN0IG51bWJlciBvZiBlbGVtZW50czosJyArXG4gICAgICAgIGAgJHtvdXRwdXRTaGFwZS5sZW5ndGh9LCBzaG91bGQgYmU6ICR7bnVtRGltc30uYCk7XG4gIH1cblxuICBjb25zdCBudW1WYWx1ZXMgPSBzcGFyc2VWYWx1ZXMuc2l6ZTtcbiAgaWYgKCEoc3BhcnNlVmFsdWVzLnJhbmsgPT09IDAgfHxcbiAgICAgICAgc3BhcnNlVmFsdWVzLnJhbmsgPT09IDEgJiYgbnVtVmFsdWVzID09PSBudW1FbGVtcykpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgICdzcGFyc2VWYWx1ZXMgaGFzIGluY29ycmVjdCBzaGFwZSAnICtcbiAgICAgICAgYCR7c3BhcnNlVmFsdWVzLnNoYXBlfSwgc2hvdWxkIGJlIFtdIG9yIFske251bUVsZW1zfV1gKTtcbiAgfVxuXG4gIGlmIChzcGFyc2VWYWx1ZXMuZHR5cGUgIT09IGRlZmF1bHRWYWx1ZXMuZHR5cGUpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ3NwYXJzZVZhbHVlcy5kdHlwZSBtdXN0IG1hdGNoIGRlZmF1bHRWYWx1ZXMuZHR5cGUnKTtcbiAgfVxufVxuIl19