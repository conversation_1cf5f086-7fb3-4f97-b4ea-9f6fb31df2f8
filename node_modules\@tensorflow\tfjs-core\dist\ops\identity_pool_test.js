/**
 * @license
 * Copyright 2022 Google LLC.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../index';
import { expectArraysClose } from '../test_util';
/**
 * Test utility for testing AvgPool, MaxPool, etc where kernel size is 1x1,
 * effectively making them act as the identity function except where strides
 * affect the output.
 */
export function identityPoolTest(pool) {
    it('1x1 pool size (identity)', async () => {
        // tslint:disable-next-line: no-unnecessary-type-assertion
        const a = tf.range(0, 10).reshape([1, 1, 1, 10]);
        const result = pool(a, [1, 1], [1, 1], 'valid');
        expectArraysClose(await result.data(), await a.data());
    });
    it('1x1 pool size with strides', async () => {
        // tslint:disable-next-line: no-unnecessary-type-assertion
        const a = tf.range(0, 150).reshape([1, 10, 15, 1]);
        const result = pool(a, [1, 1], [3, 4], 'valid');
        expectArraysClose(await result.data(), [
            0, 4, 8, 12,
            45, 49, 53, 57,
            90, 94, 98, 102,
            135, 139, 143, 147,
        ]);
    });
    it('1x1 pool size batched', async () => {
        // 7 batches of 3 x 4
        const shape = [7, 3, 4, 1];
        const size = shape.reduce((a, b) => a * b, 1);
        // tslint:disable-next-line: no-unnecessary-type-assertion
        const a = tf.range(0, size).reshape(shape);
        const result = pool(a, [1, 1], [1, 1], 'valid');
        expectArraysClose(await result.data(), await a.data());
    });
    it('1x1 pool size batched with strides', async () => {
        // tslint:disable-next-line: no-unnecessary-type-assertion
        const a = tf.range(0, 300).reshape([2, 10, 15, 1]);
        const result = pool(a, [1, 1], [3, 4], 'valid');
        expectArraysClose(await result.data(), [
            // Batch 0
            0, 4, 8, 12,
            45, 49, 53, 57,
            90, 94, 98, 102,
            135, 139, 143, 147,
            // Batch 1
            150, 154, 158, 162,
            195, 199, 203, 207,
            240, 244, 248, 252,
            285, 289, 293, 297,
        ]);
    });
    it('1x1 pool size batched with strides and channels', async () => {
        // tslint:disable-next-line: no-unnecessary-type-assertion
        const a = tf.range(0, 900).reshape([2, 10, 15, 3]);
        const result = pool(a, [1, 1], [3, 4], 'valid');
        expectArraysClose(await result.data(), [
            // Batch 0
            0, 1, 2, 12, 13, 14, 24, 25, 26, 36, 37, 38,
            135, 136, 137, 147, 148, 149, 159, 160, 161, 171, 172, 173,
            270, 271, 272, 282, 283, 284, 294, 295, 296, 306, 307, 308,
            405, 406, 407, 417, 418, 419, 429, 430, 431, 441, 442, 443,
            // Batch 1
            450, 451, 452, 462, 463, 464, 474, 475, 476, 486, 487, 488,
            585, 586, 587, 597, 598, 599, 609, 610, 611, 621, 622, 623,
            720, 721, 722, 732, 733, 734, 744, 745, 746, 756, 757, 758,
            855, 856, 857, 867, 868, 869, 879, 880, 881, 891, 892, 893,
        ]);
    });
}
//# sourceMappingURL=data:application/json;base64,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