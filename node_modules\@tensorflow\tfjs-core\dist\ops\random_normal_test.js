/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../index';
import { ALL_ENVS, describeWithFlags } from '../jasmine_util';
import { expectArrayInMeanStdRange, jarqueBeraNormalityTest } from './rand_util';
describeWithFlags('randomNormal', ALL_ENVS, () => {
    const SEED = 2002;
    const EPSILON = 0.05;
    it('should return a float32 1D of random normal values', async () => {
        const SAMPLES = 10000;
        // Ensure defaults to float32.
        let result = tf.randomNormal([SAMPLES], 0, 0.5, null, SEED);
        expect(result.dtype).toBe('float32');
        expect(result.shape).toEqual([SAMPLES]);
        jarqueBeraNormalityTest(await result.data());
        expectArrayInMeanStdRange(await result.data(), 0, 0.5, EPSILON);
        result = tf.randomNormal([SAMPLES], 0, 1.5, 'float32', SEED);
        expect(result.dtype).toBe('float32');
        expect(result.shape).toEqual([SAMPLES]);
        jarqueBeraNormalityTest(await result.data());
        expectArrayInMeanStdRange(await result.data(), 0, 1.5, EPSILON);
    });
    it('should return a int32 1D of random normal values', async () => {
        const SAMPLES = 10000;
        const result = tf.randomNormal([SAMPLES], 0, 2, 'int32', SEED);
        expect(result.dtype).toBe('int32');
        expect(result.shape).toEqual([SAMPLES]);
        jarqueBeraNormalityTest(await result.data());
        expectArrayInMeanStdRange(await result.data(), 0, 2, EPSILON);
    });
    it('should return a float32 2D of random normal values', async () => {
        const SAMPLES = 100;
        // Ensure defaults to float32.
        let result = tf.randomNormal([SAMPLES, SAMPLES], 0, 2.5, null, SEED);
        expect(result.dtype).toBe('float32');
        expect(result.shape).toEqual([SAMPLES, SAMPLES]);
        jarqueBeraNormalityTest(await result.data());
        expectArrayInMeanStdRange(await result.data(), 0, 2.5, EPSILON);
        result = tf.randomNormal([SAMPLES, SAMPLES], 0, 3.5, 'float32', SEED);
        expect(result.dtype).toBe('float32');
        expect(result.shape).toEqual([SAMPLES, SAMPLES]);
        jarqueBeraNormalityTest(await result.data());
        expectArrayInMeanStdRange(await result.data(), 0, 3.5, EPSILON);
    });
    it('should return a int32 2D of random normal values', async () => {
        const SAMPLES = 100;
        const result = tf.randomNormal([SAMPLES, SAMPLES], 0, 2, 'int32', SEED);
        expect(result.dtype).toBe('int32');
        expect(result.shape).toEqual([SAMPLES, SAMPLES]);
        jarqueBeraNormalityTest(await result.data());
        expectArrayInMeanStdRange(await result.data(), 0, 2, EPSILON);
    });
    it('should return a float32 3D of random normal values', async () => {
        const SAMPLES_SHAPE = [20, 20, 20];
        // Ensure defaults to float32.
        let result = tf.randomNormal(SAMPLES_SHAPE, 0, 0.5, null, SEED);
        expect(result.dtype).toBe('float32');
        expect(result.shape).toEqual(SAMPLES_SHAPE);
        jarqueBeraNormalityTest(await result.data());
        expectArrayInMeanStdRange(await result.data(), 0, 0.5, EPSILON);
        result = tf.randomNormal(SAMPLES_SHAPE, 0, 1.5, 'float32', SEED);
        expect(result.dtype).toBe('float32');
        expect(result.shape).toEqual(SAMPLES_SHAPE);
        jarqueBeraNormalityTest(await result.data());
        expectArrayInMeanStdRange(await result.data(), 0, 1.5, EPSILON);
    });
    it('should return a int32 3D of random normal values', async () => {
        const SAMPLES_SHAPE = [20, 20, 20];
        const result = tf.randomNormal(SAMPLES_SHAPE, 0, 2, 'int32', SEED);
        expect(result.dtype).toBe('int32');
        expect(result.shape).toEqual(SAMPLES_SHAPE);
        jarqueBeraNormalityTest(await result.data());
        expectArrayInMeanStdRange(await result.data(), 0, 2, EPSILON);
    });
    it('should return a float32 4D of random normal values', async () => {
        const SAMPLES_SHAPE = [10, 10, 10, 10];
        // Ensure defaults to float32.
        let result = tf.randomNormal(SAMPLES_SHAPE, 0, 0.5, null, SEED);
        expect(result.dtype).toBe('float32');
        expect(result.shape).toEqual(SAMPLES_SHAPE);
        jarqueBeraNormalityTest(await result.data());
        expectArrayInMeanStdRange(await result.data(), 0, 0.5, EPSILON);
        result = tf.randomNormal(SAMPLES_SHAPE, 0, 1.5, 'float32', SEED);
        expect(result.dtype).toBe('float32');
        expect(result.shape).toEqual(SAMPLES_SHAPE);
        jarqueBeraNormalityTest(await result.data());
        expectArrayInMeanStdRange(await result.data(), 0, 1.5, EPSILON);
    });
    it('should return a int32 4D of random normal values', async () => {
        const SAMPLES_SHAPE = [10, 10, 10, 10];
        const result = tf.randomNormal(SAMPLES_SHAPE, 0, 2, 'int32', SEED);
        expect(result.dtype).toBe('int32');
        expect(result.shape).toEqual(SAMPLES_SHAPE);
        jarqueBeraNormalityTest(await result.data());
        expectArrayInMeanStdRange(await result.data(), 0, 2, EPSILON);
    });
    it('should return a int32 5D of random normal values', async () => {
        const SAMPLES_SHAPE = [10, 10, 10, 10, 10];
        const result = tf.randomNormal(SAMPLES_SHAPE, 0, 2, 'int32', SEED);
        expect(result.dtype).toBe('int32');
        expect(result.shape).toEqual(SAMPLES_SHAPE);
        jarqueBeraNormalityTest(await result.data());
        expectArrayInMeanStdRange(await result.data(), 0, 2, EPSILON);
    });
    it('should throw error when shape is not integer', () => {
        expect(() => tf.randomNormal([2, 2.22, 3.33], 0, 2, 'int32', SEED))
            .toThrow();
    });
});
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicmFuZG9tX25vcm1hbF90ZXN0LmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vLi4vdGZqcy1jb3JlL3NyYy9vcHMvcmFuZG9tX25vcm1hbF90ZXN0LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNBOzs7Ozs7Ozs7Ozs7Ozs7R0FlRztBQUVILE9BQU8sS0FBSyxFQUFFLE1BQU0sVUFBVSxDQUFDO0FBQy9CLE9BQU8sRUFBQyxRQUFRLEVBQUUsaUJBQWlCLEVBQUMsTUFBTSxpQkFBaUIsQ0FBQztBQUM1RCxPQUFPLEVBQUMseUJBQXlCLEVBQUUsdUJBQXVCLEVBQUMsTUFBTSxhQUFhLENBQUM7QUFFL0UsaUJBQWlCLENBQUMsY0FBYyxFQUFFLFFBQVEsRUFBRSxHQUFHLEVBQUU7SUFDL0MsTUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDO0lBQ2xCLE1BQU0sT0FBTyxHQUFHLElBQUksQ0FBQztJQUVyQixFQUFFLENBQUMsb0RBQW9ELEVBQUUsS0FBSyxJQUFJLEVBQUU7UUFDbEUsTUFBTSxPQUFPLEdBQUcsS0FBSyxDQUFDO1FBRXRCLDhCQUE4QjtRQUM5QixJQUFJLE1BQU0sR0FBRyxFQUFFLENBQUMsWUFBWSxDQUFDLENBQUMsT0FBTyxDQUFDLEVBQUUsQ0FBQyxFQUFFLEdBQUcsRUFBRSxJQUFJLEVBQUUsSUFBSSxDQUFDLENBQUM7UUFDNUQsTUFBTSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUM7UUFDckMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDO1FBQ3hDLHVCQUF1QixDQUFDLE1BQU0sTUFBTSxDQUFDLElBQUksRUFBRSxDQUFDLENBQUM7UUFDN0MseUJBQXlCLENBQUMsTUFBTSxNQUFNLENBQUMsSUFBSSxFQUFFLEVBQUUsQ0FBQyxFQUFFLEdBQUcsRUFBRSxPQUFPLENBQUMsQ0FBQztRQUVoRSxNQUFNLEdBQUcsRUFBRSxDQUFDLFlBQVksQ0FBQyxDQUFDLE9BQU8sQ0FBQyxFQUFFLENBQUMsRUFBRSxHQUFHLEVBQUUsU0FBUyxFQUFFLElBQUksQ0FBQyxDQUFDO1FBQzdELE1BQU0sQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDO1FBQ3JDLE1BQU0sQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLENBQUMsT0FBTyxDQUFDLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQztRQUN4Qyx1QkFBdUIsQ0FBQyxNQUFNLE1BQU0sQ0FBQyxJQUFJLEVBQUUsQ0FBQyxDQUFDO1FBQzdDLHlCQUF5QixDQUFDLE1BQU0sTUFBTSxDQUFDLElBQUksRUFBRSxFQUFFLENBQUMsRUFBRSxHQUFHLEVBQUUsT0FBTyxDQUFDLENBQUM7SUFDbEUsQ0FBQyxDQUFDLENBQUM7SUFFSCxFQUFFLENBQUMsa0RBQWtELEVBQUUsS0FBSyxJQUFJLEVBQUU7UUFDaEUsTUFBTSxPQUFPLEdBQUcsS0FBSyxDQUFDO1FBQ3RCLE1BQU0sTUFBTSxHQUFHLEVBQUUsQ0FBQyxZQUFZLENBQUMsQ0FBQyxPQUFPLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLE9BQU8sRUFBRSxJQUFJLENBQUMsQ0FBQztRQUMvRCxNQUFNLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQztRQUNuQyxNQUFNLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLE9BQU8sQ0FBQyxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUM7UUFDeEMsdUJBQXVCLENBQUMsTUFBTSxNQUFNLENBQUMsSUFBSSxFQUFFLENBQUMsQ0FBQztRQUM3Qyx5QkFBeUIsQ0FBQyxNQUFNLE1BQU0sQ0FBQyxJQUFJLEVBQUUsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLE9BQU8sQ0FBQyxDQUFDO0lBQ2hFLENBQUMsQ0FBQyxDQUFDO0lBRUgsRUFBRSxDQUFDLG9EQUFvRCxFQUFFLEtBQUssSUFBSSxFQUFFO1FBQ2xFLE1BQU0sT0FBTyxHQUFHLEdBQUcsQ0FBQztRQUVwQiw4QkFBOEI7UUFDOUIsSUFBSSxNQUFNLEdBQUcsRUFBRSxDQUFDLFlBQVksQ0FBQyxDQUFDLE9BQU8sRUFBRSxPQUFPLENBQUMsRUFBRSxDQUFDLEVBQUUsR0FBRyxFQUFFLElBQUksRUFBRSxJQUFJLENBQUMsQ0FBQztRQUNyRSxNQUFNLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQztRQUNyQyxNQUFNLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLE9BQU8sQ0FBQyxDQUFDLE9BQU8sRUFBRSxPQUFPLENBQUMsQ0FBQyxDQUFDO1FBQ2pELHVCQUF1QixDQUFDLE1BQU0sTUFBTSxDQUFDLElBQUksRUFBRSxDQUFDLENBQUM7UUFDN0MseUJBQXlCLENBQUMsTUFBTSxNQUFNLENBQUMsSUFBSSxFQUFFLEVBQUUsQ0FBQyxFQUFFLEdBQUcsRUFBRSxPQUFPLENBQUMsQ0FBQztRQUVoRSxNQUFNLEdBQUcsRUFBRSxDQUFDLFlBQVksQ0FBQyxDQUFDLE9BQU8sRUFBRSxPQUFPLENBQUMsRUFBRSxDQUFDLEVBQUUsR0FBRyxFQUFFLFNBQVMsRUFBRSxJQUFJLENBQUMsQ0FBQztRQUN0RSxNQUFNLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQztRQUNyQyxNQUFNLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLE9BQU8sQ0FBQyxDQUFDLE9BQU8sRUFBRSxPQUFPLENBQUMsQ0FBQyxDQUFDO1FBQ2pELHVCQUF1QixDQUFDLE1BQU0sTUFBTSxDQUFDLElBQUksRUFBRSxDQUFDLENBQUM7UUFDN0MseUJBQXlCLENBQUMsTUFBTSxNQUFNLENBQUMsSUFBSSxFQUFFLEVBQUUsQ0FBQyxFQUFFLEdBQUcsRUFBRSxPQUFPLENBQUMsQ0FBQztJQUNsRSxDQUFDLENBQUMsQ0FBQztJQUVILEVBQUUsQ0FBQyxrREFBa0QsRUFBRSxLQUFLLElBQUksRUFBRTtRQUNoRSxNQUFNLE9BQU8sR0FBRyxHQUFHLENBQUM7UUFDcEIsTUFBTSxNQUFNLEdBQUcsRUFBRSxDQUFDLFlBQVksQ0FBQyxDQUFDLE9BQU8sRUFBRSxPQUFPLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLE9BQU8sRUFBRSxJQUFJLENBQUMsQ0FBQztRQUN4RSxNQUFNLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQztRQUNuQyxNQUFNLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLE9BQU8sQ0FBQyxDQUFDLE9BQU8sRUFBRSxPQUFPLENBQUMsQ0FBQyxDQUFDO1FBQ2pELHVCQUF1QixDQUFDLE1BQU0sTUFBTSxDQUFDLElBQUksRUFBRSxDQUFDLENBQUM7UUFDN0MseUJBQXlCLENBQUMsTUFBTSxNQUFNLENBQUMsSUFBSSxFQUFFLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxPQUFPLENBQUMsQ0FBQztJQUNoRSxDQUFDLENBQUMsQ0FBQztJQUVILEVBQUUsQ0FBQyxvREFBb0QsRUFBRSxLQUFLLElBQUksRUFBRTtRQUNsRSxNQUFNLGFBQWEsR0FBRyxDQUFDLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxDQUFDLENBQUM7UUFFbkMsOEJBQThCO1FBQzlCLElBQUksTUFBTSxHQUFHLEVBQUUsQ0FBQyxZQUFZLENBQUMsYUFBYSxFQUFFLENBQUMsRUFBRSxHQUFHLEVBQUUsSUFBSSxFQUFFLElBQUksQ0FBQyxDQUFDO1FBQ2hFLE1BQU0sQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDO1FBQ3JDLE1BQU0sQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLENBQUMsT0FBTyxDQUFDLGFBQWEsQ0FBQyxDQUFDO1FBQzVDLHVCQUF1QixDQUFDLE1BQU0sTUFBTSxDQUFDLElBQUksRUFBRSxDQUFDLENBQUM7UUFDN0MseUJBQXlCLENBQUMsTUFBTSxNQUFNLENBQUMsSUFBSSxFQUFFLEVBQUUsQ0FBQyxFQUFFLEdBQUcsRUFBRSxPQUFPLENBQUMsQ0FBQztRQUVoRSxNQUFNLEdBQUcsRUFBRSxDQUFDLFlBQVksQ0FBQyxhQUFhLEVBQUUsQ0FBQyxFQUFFLEdBQUcsRUFBRSxTQUFTLEVBQUUsSUFBSSxDQUFDLENBQUM7UUFDakUsTUFBTSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUM7UUFDckMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQyxPQUFPLENBQUMsYUFBYSxDQUFDLENBQUM7UUFDNUMsdUJBQXVCLENBQUMsTUFBTSxNQUFNLENBQUMsSUFBSSxFQUFFLENBQUMsQ0FBQztRQUM3Qyx5QkFBeUIsQ0FBQyxNQUFNLE1BQU0sQ0FBQyxJQUFJLEVBQUUsRUFBRSxDQUFDLEVBQUUsR0FBRyxFQUFFLE9BQU8sQ0FBQyxDQUFDO0lBQ2xFLENBQUMsQ0FBQyxDQUFDO0lBRUgsRUFBRSxDQUFDLGtEQUFrRCxFQUFFLEtBQUssSUFBSSxFQUFFO1FBQ2hFLE1BQU0sYUFBYSxHQUFHLENBQUMsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLENBQUMsQ0FBQztRQUNuQyxNQUFNLE1BQU0sR0FBRyxFQUFFLENBQUMsWUFBWSxDQUFDLGFBQWEsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLE9BQU8sRUFBRSxJQUFJLENBQUMsQ0FBQztRQUNuRSxNQUFNLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQztRQUNuQyxNQUFNLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLE9BQU8sQ0FBQyxhQUFhLENBQUMsQ0FBQztRQUM1Qyx1QkFBdUIsQ0FBQyxNQUFNLE1BQU0sQ0FBQyxJQUFJLEVBQUUsQ0FBQyxDQUFDO1FBQzdDLHlCQUF5QixDQUFDLE1BQU0sTUFBTSxDQUFDLElBQUksRUFBRSxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsT0FBTyxDQUFDLENBQUM7SUFDaEUsQ0FBQyxDQUFDLENBQUM7SUFFSCxFQUFFLENBQUMsb0RBQW9ELEVBQUUsS0FBSyxJQUFJLEVBQUU7UUFDbEUsTUFBTSxhQUFhLEdBQUcsQ0FBQyxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLENBQUMsQ0FBQztRQUV2Qyw4QkFBOEI7UUFDOUIsSUFBSSxNQUFNLEdBQUcsRUFBRSxDQUFDLFlBQVksQ0FBQyxhQUFhLEVBQUUsQ0FBQyxFQUFFLEdBQUcsRUFBRSxJQUFJLEVBQUUsSUFBSSxDQUFDLENBQUM7UUFDaEUsTUFBTSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUM7UUFDckMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQyxPQUFPLENBQUMsYUFBYSxDQUFDLENBQUM7UUFDNUMsdUJBQXVCLENBQUMsTUFBTSxNQUFNLENBQUMsSUFBSSxFQUFFLENBQUMsQ0FBQztRQUM3Qyx5QkFBeUIsQ0FBQyxNQUFNLE1BQU0sQ0FBQyxJQUFJLEVBQUUsRUFBRSxDQUFDLEVBQUUsR0FBRyxFQUFFLE9BQU8sQ0FBQyxDQUFDO1FBRWhFLE1BQU0sR0FBRyxFQUFFLENBQUMsWUFBWSxDQUFDLGFBQWEsRUFBRSxDQUFDLEVBQUUsR0FBRyxFQUFFLFNBQVMsRUFBRSxJQUFJLENBQUMsQ0FBQztRQUNqRSxNQUFNLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQztRQUNyQyxNQUFNLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLE9BQU8sQ0FBQyxhQUFhLENBQUMsQ0FBQztRQUM1Qyx1QkFBdUIsQ0FBQyxNQUFNLE1BQU0sQ0FBQyxJQUFJLEVBQUUsQ0FBQyxDQUFDO1FBQzdDLHlCQUF5QixDQUFDLE1BQU0sTUFBTSxDQUFDLElBQUksRUFBRSxFQUFFLENBQUMsRUFBRSxHQUFHLEVBQUUsT0FBTyxDQUFDLENBQUM7SUFDbEUsQ0FBQyxDQUFDLENBQUM7SUFFSCxFQUFFLENBQUMsa0RBQWtELEVBQUUsS0FBSyxJQUFJLEVBQUU7UUFDaEUsTUFBTSxhQUFhLEdBQUcsQ0FBQyxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLENBQUMsQ0FBQztRQUV2QyxNQUFNLE1BQU0sR0FBRyxFQUFFLENBQUMsWUFBWSxDQUFDLGFBQWEsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLE9BQU8sRUFBRSxJQUFJLENBQUMsQ0FBQztRQUNuRSxNQUFNLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQztRQUNuQyxNQUFNLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLE9BQU8sQ0FBQyxhQUFhLENBQUMsQ0FBQztRQUM1Qyx1QkFBdUIsQ0FBQyxNQUFNLE1BQU0sQ0FBQyxJQUFJLEVBQUUsQ0FBQyxDQUFDO1FBQzdDLHlCQUF5QixDQUFDLE1BQU0sTUFBTSxDQUFDLElBQUksRUFBRSxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsT0FBTyxDQUFDLENBQUM7SUFDaEUsQ0FBQyxDQUFDLENBQUM7SUFFSCxFQUFFLENBQUMsa0RBQWtELEVBQUUsS0FBSyxJQUFJLEVBQUU7UUFDaEUsTUFBTSxhQUFhLEdBQUcsQ0FBQyxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxDQUFDLENBQUM7UUFFM0MsTUFBTSxNQUFNLEdBQUcsRUFBRSxDQUFDLFlBQVksQ0FBQyxhQUFhLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxPQUFPLEVBQUUsSUFBSSxDQUFDLENBQUM7UUFDbkUsTUFBTSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUM7UUFDbkMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQyxPQUFPLENBQUMsYUFBYSxDQUFDLENBQUM7UUFDNUMsdUJBQXVCLENBQUMsTUFBTSxNQUFNLENBQUMsSUFBSSxFQUFFLENBQUMsQ0FBQztRQUM3Qyx5QkFBeUIsQ0FBQyxNQUFNLE1BQU0sQ0FBQyxJQUFJLEVBQUUsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLE9BQU8sQ0FBQyxDQUFDO0lBQ2hFLENBQUMsQ0FBQyxDQUFDO0lBRUgsRUFBRSxDQUFDLDhDQUE4QyxFQUFFLEdBQUcsRUFBRTtRQUN0RCxNQUFNLENBQUMsR0FBRyxFQUFFLENBQUMsRUFBRSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsRUFBRSxJQUFJLEVBQUUsSUFBSSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxPQUFPLEVBQUUsSUFBSSxDQUFDLENBQUM7YUFDOUQsT0FBTyxFQUFFLENBQUM7SUFDakIsQ0FBQyxDQUFDLENBQUM7QUFDTCxDQUFDLENBQUMsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIlxuLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IDIwMjAgR29vZ2xlIExMQy4gQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiBodHRwOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICogPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAqL1xuXG5pbXBvcnQgKiBhcyB0ZiBmcm9tICcuLi9pbmRleCc7XG5pbXBvcnQge0FMTF9FTlZTLCBkZXNjcmliZVdpdGhGbGFnc30gZnJvbSAnLi4vamFzbWluZV91dGlsJztcbmltcG9ydCB7ZXhwZWN0QXJyYXlJbk1lYW5TdGRSYW5nZSwgamFycXVlQmVyYU5vcm1hbGl0eVRlc3R9IGZyb20gJy4vcmFuZF91dGlsJztcblxuZGVzY3JpYmVXaXRoRmxhZ3MoJ3JhbmRvbU5vcm1hbCcsIEFMTF9FTlZTLCAoKSA9PiB7XG4gIGNvbnN0IFNFRUQgPSAyMDAyO1xuICBjb25zdCBFUFNJTE9OID0gMC4wNTtcblxuICBpdCgnc2hvdWxkIHJldHVybiBhIGZsb2F0MzIgMUQgb2YgcmFuZG9tIG5vcm1hbCB2YWx1ZXMnLCBhc3luYyAoKSA9PiB7XG4gICAgY29uc3QgU0FNUExFUyA9IDEwMDAwO1xuXG4gICAgLy8gRW5zdXJlIGRlZmF1bHRzIHRvIGZsb2F0MzIuXG4gICAgbGV0IHJlc3VsdCA9IHRmLnJhbmRvbU5vcm1hbChbU0FNUExFU10sIDAsIDAuNSwgbnVsbCwgU0VFRCk7XG4gICAgZXhwZWN0KHJlc3VsdC5kdHlwZSkudG9CZSgnZmxvYXQzMicpO1xuICAgIGV4cGVjdChyZXN1bHQuc2hhcGUpLnRvRXF1YWwoW1NBTVBMRVNdKTtcbiAgICBqYXJxdWVCZXJhTm9ybWFsaXR5VGVzdChhd2FpdCByZXN1bHQuZGF0YSgpKTtcbiAgICBleHBlY3RBcnJheUluTWVhblN0ZFJhbmdlKGF3YWl0IHJlc3VsdC5kYXRhKCksIDAsIDAuNSwgRVBTSUxPTik7XG5cbiAgICByZXN1bHQgPSB0Zi5yYW5kb21Ob3JtYWwoW1NBTVBMRVNdLCAwLCAxLjUsICdmbG9hdDMyJywgU0VFRCk7XG4gICAgZXhwZWN0KHJlc3VsdC5kdHlwZSkudG9CZSgnZmxvYXQzMicpO1xuICAgIGV4cGVjdChyZXN1bHQuc2hhcGUpLnRvRXF1YWwoW1NBTVBMRVNdKTtcbiAgICBqYXJxdWVCZXJhTm9ybWFsaXR5VGVzdChhd2FpdCByZXN1bHQuZGF0YSgpKTtcbiAgICBleHBlY3RBcnJheUluTWVhblN0ZFJhbmdlKGF3YWl0IHJlc3VsdC5kYXRhKCksIDAsIDEuNSwgRVBTSUxPTik7XG4gIH0pO1xuXG4gIGl0KCdzaG91bGQgcmV0dXJuIGEgaW50MzIgMUQgb2YgcmFuZG9tIG5vcm1hbCB2YWx1ZXMnLCBhc3luYyAoKSA9PiB7XG4gICAgY29uc3QgU0FNUExFUyA9IDEwMDAwO1xuICAgIGNvbnN0IHJlc3VsdCA9IHRmLnJhbmRvbU5vcm1hbChbU0FNUExFU10sIDAsIDIsICdpbnQzMicsIFNFRUQpO1xuICAgIGV4cGVjdChyZXN1bHQuZHR5cGUpLnRvQmUoJ2ludDMyJyk7XG4gICAgZXhwZWN0KHJlc3VsdC5zaGFwZSkudG9FcXVhbChbU0FNUExFU10pO1xuICAgIGphcnF1ZUJlcmFOb3JtYWxpdHlUZXN0KGF3YWl0IHJlc3VsdC5kYXRhKCkpO1xuICAgIGV4cGVjdEFycmF5SW5NZWFuU3RkUmFuZ2UoYXdhaXQgcmVzdWx0LmRhdGEoKSwgMCwgMiwgRVBTSUxPTik7XG4gIH0pO1xuXG4gIGl0KCdzaG91bGQgcmV0dXJuIGEgZmxvYXQzMiAyRCBvZiByYW5kb20gbm9ybWFsIHZhbHVlcycsIGFzeW5jICgpID0+IHtcbiAgICBjb25zdCBTQU1QTEVTID0gMTAwO1xuXG4gICAgLy8gRW5zdXJlIGRlZmF1bHRzIHRvIGZsb2F0MzIuXG4gICAgbGV0IHJlc3VsdCA9IHRmLnJhbmRvbU5vcm1hbChbU0FNUExFUywgU0FNUExFU10sIDAsIDIuNSwgbnVsbCwgU0VFRCk7XG4gICAgZXhwZWN0KHJlc3VsdC5kdHlwZSkudG9CZSgnZmxvYXQzMicpO1xuICAgIGV4cGVjdChyZXN1bHQuc2hhcGUpLnRvRXF1YWwoW1NBTVBMRVMsIFNBTVBMRVNdKTtcbiAgICBqYXJxdWVCZXJhTm9ybWFsaXR5VGVzdChhd2FpdCByZXN1bHQuZGF0YSgpKTtcbiAgICBleHBlY3RBcnJheUluTWVhblN0ZFJhbmdlKGF3YWl0IHJlc3VsdC5kYXRhKCksIDAsIDIuNSwgRVBTSUxPTik7XG5cbiAgICByZXN1bHQgPSB0Zi5yYW5kb21Ob3JtYWwoW1NBTVBMRVMsIFNBTVBMRVNdLCAwLCAzLjUsICdmbG9hdDMyJywgU0VFRCk7XG4gICAgZXhwZWN0KHJlc3VsdC5kdHlwZSkudG9CZSgnZmxvYXQzMicpO1xuICAgIGV4cGVjdChyZXN1bHQuc2hhcGUpLnRvRXF1YWwoW1NBTVBMRVMsIFNBTVBMRVNdKTtcbiAgICBqYXJxdWVCZXJhTm9ybWFsaXR5VGVzdChhd2FpdCByZXN1bHQuZGF0YSgpKTtcbiAgICBleHBlY3RBcnJheUluTWVhblN0ZFJhbmdlKGF3YWl0IHJlc3VsdC5kYXRhKCksIDAsIDMuNSwgRVBTSUxPTik7XG4gIH0pO1xuXG4gIGl0KCdzaG91bGQgcmV0dXJuIGEgaW50MzIgMkQgb2YgcmFuZG9tIG5vcm1hbCB2YWx1ZXMnLCBhc3luYyAoKSA9PiB7XG4gICAgY29uc3QgU0FNUExFUyA9IDEwMDtcbiAgICBjb25zdCByZXN1bHQgPSB0Zi5yYW5kb21Ob3JtYWwoW1NBTVBMRVMsIFNBTVBMRVNdLCAwLCAyLCAnaW50MzInLCBTRUVEKTtcbiAgICBleHBlY3QocmVzdWx0LmR0eXBlKS50b0JlKCdpbnQzMicpO1xuICAgIGV4cGVjdChyZXN1bHQuc2hhcGUpLnRvRXF1YWwoW1NBTVBMRVMsIFNBTVBMRVNdKTtcbiAgICBqYXJxdWVCZXJhTm9ybWFsaXR5VGVzdChhd2FpdCByZXN1bHQuZGF0YSgpKTtcbiAgICBleHBlY3RBcnJheUluTWVhblN0ZFJhbmdlKGF3YWl0IHJlc3VsdC5kYXRhKCksIDAsIDIsIEVQU0lMT04pO1xuICB9KTtcblxuICBpdCgnc2hvdWxkIHJldHVybiBhIGZsb2F0MzIgM0Qgb2YgcmFuZG9tIG5vcm1hbCB2YWx1ZXMnLCBhc3luYyAoKSA9PiB7XG4gICAgY29uc3QgU0FNUExFU19TSEFQRSA9IFsyMCwgMjAsIDIwXTtcblxuICAgIC8vIEVuc3VyZSBkZWZhdWx0cyB0byBmbG9hdDMyLlxuICAgIGxldCByZXN1bHQgPSB0Zi5yYW5kb21Ob3JtYWwoU0FNUExFU19TSEFQRSwgMCwgMC41LCBudWxsLCBTRUVEKTtcbiAgICBleHBlY3QocmVzdWx0LmR0eXBlKS50b0JlKCdmbG9hdDMyJyk7XG4gICAgZXhwZWN0KHJlc3VsdC5zaGFwZSkudG9FcXVhbChTQU1QTEVTX1NIQVBFKTtcbiAgICBqYXJxdWVCZXJhTm9ybWFsaXR5VGVzdChhd2FpdCByZXN1bHQuZGF0YSgpKTtcbiAgICBleHBlY3RBcnJheUluTWVhblN0ZFJhbmdlKGF3YWl0IHJlc3VsdC5kYXRhKCksIDAsIDAuNSwgRVBTSUxPTik7XG5cbiAgICByZXN1bHQgPSB0Zi5yYW5kb21Ob3JtYWwoU0FNUExFU19TSEFQRSwgMCwgMS41LCAnZmxvYXQzMicsIFNFRUQpO1xuICAgIGV4cGVjdChyZXN1bHQuZHR5cGUpLnRvQmUoJ2Zsb2F0MzInKTtcbiAgICBleHBlY3QocmVzdWx0LnNoYXBlKS50b0VxdWFsKFNBTVBMRVNfU0hBUEUpO1xuICAgIGphcnF1ZUJlcmFOb3JtYWxpdHlUZXN0KGF3YWl0IHJlc3VsdC5kYXRhKCkpO1xuICAgIGV4cGVjdEFycmF5SW5NZWFuU3RkUmFuZ2UoYXdhaXQgcmVzdWx0LmRhdGEoKSwgMCwgMS41LCBFUFNJTE9OKTtcbiAgfSk7XG5cbiAgaXQoJ3Nob3VsZCByZXR1cm4gYSBpbnQzMiAzRCBvZiByYW5kb20gbm9ybWFsIHZhbHVlcycsIGFzeW5jICgpID0+IHtcbiAgICBjb25zdCBTQU1QTEVTX1NIQVBFID0gWzIwLCAyMCwgMjBdO1xuICAgIGNvbnN0IHJlc3VsdCA9IHRmLnJhbmRvbU5vcm1hbChTQU1QTEVTX1NIQVBFLCAwLCAyLCAnaW50MzInLCBTRUVEKTtcbiAgICBleHBlY3QocmVzdWx0LmR0eXBlKS50b0JlKCdpbnQzMicpO1xuICAgIGV4cGVjdChyZXN1bHQuc2hhcGUpLnRvRXF1YWwoU0FNUExFU19TSEFQRSk7XG4gICAgamFycXVlQmVyYU5vcm1hbGl0eVRlc3QoYXdhaXQgcmVzdWx0LmRhdGEoKSk7XG4gICAgZXhwZWN0QXJyYXlJbk1lYW5TdGRSYW5nZShhd2FpdCByZXN1bHQuZGF0YSgpLCAwLCAyLCBFUFNJTE9OKTtcbiAgfSk7XG5cbiAgaXQoJ3Nob3VsZCByZXR1cm4gYSBmbG9hdDMyIDREIG9mIHJhbmRvbSBub3JtYWwgdmFsdWVzJywgYXN5bmMgKCkgPT4ge1xuICAgIGNvbnN0IFNBTVBMRVNfU0hBUEUgPSBbMTAsIDEwLCAxMCwgMTBdO1xuXG4gICAgLy8gRW5zdXJlIGRlZmF1bHRzIHRvIGZsb2F0MzIuXG4gICAgbGV0IHJlc3VsdCA9IHRmLnJhbmRvbU5vcm1hbChTQU1QTEVTX1NIQVBFLCAwLCAwLjUsIG51bGwsIFNFRUQpO1xuICAgIGV4cGVjdChyZXN1bHQuZHR5cGUpLnRvQmUoJ2Zsb2F0MzInKTtcbiAgICBleHBlY3QocmVzdWx0LnNoYXBlKS50b0VxdWFsKFNBTVBMRVNfU0hBUEUpO1xuICAgIGphcnF1ZUJlcmFOb3JtYWxpdHlUZXN0KGF3YWl0IHJlc3VsdC5kYXRhKCkpO1xuICAgIGV4cGVjdEFycmF5SW5NZWFuU3RkUmFuZ2UoYXdhaXQgcmVzdWx0LmRhdGEoKSwgMCwgMC41LCBFUFNJTE9OKTtcblxuICAgIHJlc3VsdCA9IHRmLnJhbmRvbU5vcm1hbChTQU1QTEVTX1NIQVBFLCAwLCAxLjUsICdmbG9hdDMyJywgU0VFRCk7XG4gICAgZXhwZWN0KHJlc3VsdC5kdHlwZSkudG9CZSgnZmxvYXQzMicpO1xuICAgIGV4cGVjdChyZXN1bHQuc2hhcGUpLnRvRXF1YWwoU0FNUExFU19TSEFQRSk7XG4gICAgamFycXVlQmVyYU5vcm1hbGl0eVRlc3QoYXdhaXQgcmVzdWx0LmRhdGEoKSk7XG4gICAgZXhwZWN0QXJyYXlJbk1lYW5TdGRSYW5nZShhd2FpdCByZXN1bHQuZGF0YSgpLCAwLCAxLjUsIEVQU0lMT04pO1xuICB9KTtcblxuICBpdCgnc2hvdWxkIHJldHVybiBhIGludDMyIDREIG9mIHJhbmRvbSBub3JtYWwgdmFsdWVzJywgYXN5bmMgKCkgPT4ge1xuICAgIGNvbnN0IFNBTVBMRVNfU0hBUEUgPSBbMTAsIDEwLCAxMCwgMTBdO1xuXG4gICAgY29uc3QgcmVzdWx0ID0gdGYucmFuZG9tTm9ybWFsKFNBTVBMRVNfU0hBUEUsIDAsIDIsICdpbnQzMicsIFNFRUQpO1xuICAgIGV4cGVjdChyZXN1bHQuZHR5cGUpLnRvQmUoJ2ludDMyJyk7XG4gICAgZXhwZWN0KHJlc3VsdC5zaGFwZSkudG9FcXVhbChTQU1QTEVTX1NIQVBFKTtcbiAgICBqYXJxdWVCZXJhTm9ybWFsaXR5VGVzdChhd2FpdCByZXN1bHQuZGF0YSgpKTtcbiAgICBleHBlY3RBcnJheUluTWVhblN0ZFJhbmdlKGF3YWl0IHJlc3VsdC5kYXRhKCksIDAsIDIsIEVQU0lMT04pO1xuICB9KTtcblxuICBpdCgnc2hvdWxkIHJldHVybiBhIGludDMyIDVEIG9mIHJhbmRvbSBub3JtYWwgdmFsdWVzJywgYXN5bmMgKCkgPT4ge1xuICAgIGNvbnN0IFNBTVBMRVNfU0hBUEUgPSBbMTAsIDEwLCAxMCwgMTAsIDEwXTtcblxuICAgIGNvbnN0IHJlc3VsdCA9IHRmLnJhbmRvbU5vcm1hbChTQU1QTEVTX1NIQVBFLCAwLCAyLCAnaW50MzInLCBTRUVEKTtcbiAgICBleHBlY3QocmVzdWx0LmR0eXBlKS50b0JlKCdpbnQzMicpO1xuICAgIGV4cGVjdChyZXN1bHQuc2hhcGUpLnRvRXF1YWwoU0FNUExFU19TSEFQRSk7XG4gICAgamFycXVlQmVyYU5vcm1hbGl0eVRlc3QoYXdhaXQgcmVzdWx0LmRhdGEoKSk7XG4gICAgZXhwZWN0QXJyYXlJbk1lYW5TdGRSYW5nZShhd2FpdCByZXN1bHQuZGF0YSgpLCAwLCAyLCBFUFNJTE9OKTtcbiAgfSk7XG5cbiAgaXQoJ3Nob3VsZCB0aHJvdyBlcnJvciB3aGVuIHNoYXBlIGlzIG5vdCBpbnRlZ2VyJywgKCkgPT4ge1xuICAgIGV4cGVjdCgoKSA9PiB0Zi5yYW5kb21Ob3JtYWwoWzIsIDIuMjIsIDMuMzNdLCAwLCAyLCAnaW50MzInLCBTRUVEKSlcbiAgICAgICAgLnRvVGhyb3coKTtcbiAgfSk7XG59KTtcbiJdfQ==