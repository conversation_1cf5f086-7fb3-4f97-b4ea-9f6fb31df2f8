/**
 * @license
 * Copyright 2022 Google LLC.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../index';
import { ALL_ENVS, describeWithFlags } from '../jasmine_util';
import { expectArraysClose } from '../test_util';
async function runRaggedGather(starts, limits, deltas) {
    const output = tf.raggedRange(starts, limits, deltas);
    expect(output.rtNestedSplits.dtype).toEqual('int32');
    expect(output.rtNestedSplits.shape.length).toEqual(1);
    expect(output.rtDenseValues.dtype).toEqual(starts.dtype);
    expect(output.rtDenseValues.shape.length).toEqual(1);
    return {
        rtNestedSplits: await output.rtNestedSplits.data(),
        rtDenseValues: await output.rtDenseValues.data(),
        tensors: Object.values(output)
    };
}
describeWithFlags('raggedRange ', ALL_ENVS, () => {
    it('IntValues', async () => {
        const result = await runRaggedGather(tf.tensor1d([0, 5, 8, 5], 'int32'), tf.tensor1d([8, 7, 8, 1], 'int32'), tf.tensor1d([2, 1, 1, -1], 'int32'));
        // Expected: [[0, 2, 4, 6], [5, 6], [], [5, 4, 3, 2]]
        expectArraysClose(result.rtNestedSplits, [0, 4, 6, 6, 10]);
        expectArraysClose(result.rtDenseValues, [0, 2, 4, 6, 5, 6, 5, 4, 3, 2]);
    });
    it('FloatValues', async () => {
        const result = await runRaggedGather(tf.tensor1d([0, 5, 8, 5], 'float32'), tf.tensor1d([8, 7, 8, 1], 'float32'), tf.tensor1d([2, 1, 1, -1], 'float32'));
        // Expected: [[0, 2, 4, 6], [5, 6], [], [5, 4, 3, 2]]
        expectArraysClose(result.rtNestedSplits, [0, 4, 6, 6, 10]);
        expectArraysClose(result.rtDenseValues, [0, 2, 4, 6, 5, 6, 5, 4, 3, 2]);
    });
    it('RangeSizeOverflow', async () => {
        await expectAsync(runRaggedGather(tf.tensor1d([1.1, 0.1], 'float32'), tf.tensor1d([10, 1e10], 'float32'), tf.tensor1d([1, 1e-10], 'float32')))
            .toBeRejectedWithError('Requires ((limit - start) / delta) <= 2147483647');
    });
    it('BroadcastDeltas', async () => {
        const result = await runRaggedGather(tf.tensor1d([0, 5, 8], 'int32'), tf.tensor1d([8, 7, 8], 'int32'), tf.scalar(1, 'int32'));
        // Expected: [[0, 1, 2, 3, 4, 5, 6, 7], [5, 6], []]
        expectArraysClose(result.rtNestedSplits, [0, 8, 10, 10]);
        expectArraysClose(result.rtDenseValues, [0, 1, 2, 3, 4, 5, 6, 7, 5, 6]);
    });
    it('BroadcastLimitsAndDeltas', async () => {
        const result = await runRaggedGather(tf.scalar(0, 'int32'), tf.tensor1d([3, 0, 2], 'int32'), tf.scalar(1, 'int32'));
        // Expected: [[0, 1, 2], [], [0, 1]]
        expectArraysClose(result.rtNestedSplits, [0, 3, 3, 5]);
        expectArraysClose(result.rtDenseValues, [0, 1, 2, 0, 1]);
    });
    it('BroadcastStartsAndLimits', async () => {
        const result = await runRaggedGather(tf.scalar(0, 'int32'), tf.scalar(12, 'int32'), tf.tensor1d([3, 4, 5], 'int32'));
        // Expected: [[0, 3, 6, 9], [0, 4, 8], [0, 5, 10]]
        expectArraysClose(result.rtNestedSplits, [0, 4, 7, 10]);
        expectArraysClose(result.rtDenseValues, [0, 3, 6, 9, 0, 4, 8, 0, 5, 10]);
    });
    it('AllScalarInputs', async () => {
        const result = await runRaggedGather(tf.scalar(0, 'int32'), tf.scalar(5, 'int32'), tf.scalar(1, 'int32'));
        // Expected: [[0, 1, 2, 3, 4]]
        expectArraysClose(result.rtNestedSplits, [0, 5]);
        expectArraysClose(result.rtDenseValues, [0, 1, 2, 3, 4]);
    });
    it('InvalidArgsStarts', async () => {
        await expectAsync(runRaggedGather(tf.tensor2d([0, 5, 8, 5], [4, 1], 'int32'), tf.tensor1d([8, 7, 8, 1], 'int32'), tf.tensor1d([2, 1, 1, -1], 'int32')))
            .toBeRejectedWithError('starts must be a scalar or vector');
    });
    it('InvalidArgsLimits', async () => {
        await expectAsync(runRaggedGather(tf.tensor1d([0, 5, 8, 5], 'int32'), tf.tensor2d([8, 7, 8, 1], [4, 1], 'int32'), tf.tensor1d([2, 1, 1, -1], 'int32')))
            .toBeRejectedWithError('limits must be a scalar or vector');
    });
    it('InvalidArgsDeltas', async () => {
        await expectAsync(runRaggedGather(tf.tensor1d([0, 5, 8, 5], 'int32'), tf.tensor1d([8, 7, 8, 1], 'int32'), tf.tensor2d([2, 1, 1, -1], [4, 1], 'int32')))
            .toBeRejectedWithError('deltas must be a scalar or vector');
    });
    it('InvalidArgsShapeMismatch', async () => {
        await expectAsync(runRaggedGather(tf.tensor1d([0, 5, 8, 5], 'int32'), tf.tensor1d([7, 8, 1], 'int32'), tf.tensor1d([2, 1, 1, -1], 'int32')))
            .toBeRejectedWithError('starts, limits, and deltas must have the same shape');
    });
    it('InvalidArgsZeroDelta', async () => {
        await expectAsync(runRaggedGather(tf.tensor1d([0, 5, 8, 5], 'int32'), tf.tensor1d([7, 8, 8, 1], 'int32'), tf.tensor1d([2, 1, 0, -1], 'int32')))
            .toBeRejectedWithError('Requires delta != 0');
    });
    it('EmptyRangePositiveDelta', async () => {
        const result = await runRaggedGather(tf.tensor1d([0, 5], 'int32'), tf.tensor1d([5, 0], 'int32'), tf.scalar(2, 'int32'));
        // Expected: [[0, 2, 4], []]
        expectArraysClose(result.rtNestedSplits, [0, 3, 3]);
        expectArraysClose(result.rtDenseValues, [0, 2, 4]);
    });
    it('EmptyRangeNegativeDelta', async () => {
        const result = await runRaggedGather(tf.tensor1d([0, 5], 'int32'), tf.tensor1d([5, 0], 'int32'), tf.scalar(-2, 'int32'));
        // Expected: [[], [5, 3, 1]]
        expectArraysClose(result.rtNestedSplits, [0, 0, 3]);
        expectArraysClose(result.rtDenseValues, [5, 3, 1]);
    });
    it('does not have memory leak.', async () => {
        const beforeDataIds = tf.engine().backend.numDataIds();
        const starts = tf.tensor1d([0, 5, 8, 5], 'int32');
        const limits = tf.tensor1d([8, 7, 8, 1], 'int32');
        const deltas = tf.tensor1d([2, 1, 1, -1], 'int32');
        const result = await runRaggedGather(starts, limits, deltas);
        const afterResDataIds = tf.engine().backend.numDataIds();
        expect(afterResDataIds).toEqual(beforeDataIds + 5);
        tf.dispose([starts, limits, deltas]);
        tf.dispose(result.tensors);
        const afterDisposeDataIds = tf.engine().backend.numDataIds();
        expect(afterDisposeDataIds).toEqual(beforeDataIds);
    });
});
//# sourceMappingURL=data:application/json;base64,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