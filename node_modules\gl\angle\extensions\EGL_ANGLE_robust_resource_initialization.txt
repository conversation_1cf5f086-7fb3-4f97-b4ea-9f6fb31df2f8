Name

    EGL_ANGLE_robust_resource_initialization.txt

Name Strings

    EGL_ANGLE_robust_resource_intialization

Contributors

    <PERSON>, <PERSON>, Google

Contacts

    <PERSON>, Google (shannon<PERSON> 'at' google.com)

Status

    Draft

Version

    Version 1, January 7, 2015

Number

    EGL Extension TBD

Dependencies

    This extension is written against the wording of the EGL 1.5
    specification.

    An OpenGL ES implementation supporting ANGLE_robust_resource_initialization
    or an implementation supporting equivalent functionality is required.

Overview

    This extension allows creating an OpenGL ES context supporting
    robust resource initialization.

New Types

    None

New Procedures and Functions

    None

New Tokens

    Accepted as an attribute name in the <*attrib_list> argument to
    eglCreateContext:

        EGL_CONTEXT_OPENGL_ROBUST_RESOURCE_INITIALIZATION_ANGLE    0x320F

Additions to the EGL 1.5 Specification

    Add a new section entitled "OpenGL ES Robust Resource Initialization"
    to section 3.7.1:

    "If the attribute EGL_CONTEXT_OPENGL_ROBUST_RESOURCE_INITIALIZATION_ANGLE
    is set to EGL_TRUE, a context supporting <robust resource initialization>
    will be created. OpenGL ES contexts must support the
    ANGLE_robust_resource_initialization extension, or equivalent core API
    functionality.
    This attribute is supported only for OpenGL ES contexts. If the
    implementation does not support robust resource initialization,
    context creation will fail.
    The default value of EGL_CONTEXT_OPENGL_ROBUST_RESOURCE_INITIALIZATION_ANGLE
    is EGL_FALSE."

Issues

    None

Revision History

    Version 1, 2015/01/07 - first draft.
