{"version": 3, "file": "readFile.js", "names": ["fsReadFileAsync", "pathname", "encoding", "Promise", "resolve", "reject", "fs", "readFile", "error", "contents", "filepath", "options", "throwNotFound", "content", "code", "readFileSync"], "sources": ["../src/readFile.ts"], "sourcesContent": ["import fs from 'fs';\n\nasync function fsReadFileAsync(\n  pathname: string,\n  encoding: BufferEncoding,\n): Promise<string> {\n  return new Promise((resolve, reject): void => {\n    fs.readFile(pathname, encoding, (error, contents): void => {\n      if (error) {\n        reject(error);\n        return;\n      }\n\n      resolve(contents);\n    });\n  });\n}\n\ninterface Options {\n  throwNotFound?: boolean;\n}\n\nasync function readFile(\n  filepath: string,\n  options: Options = {},\n): Promise<string | null> {\n  const throwNotFound = options.throwNotFound === true;\n\n  try {\n    const content = await fsReadFileAsync(filepath, 'utf8');\n\n    return content;\n  } catch (error: any) {\n    if (\n      throwNotFound === false &&\n      (error.code === 'ENOENT' || error.code === 'EISDIR')\n    ) {\n      return null;\n    }\n\n    throw error;\n  }\n}\n\nfunction readFileSync(filepath: string, options: Options = {}): string | null {\n  const throwNotFound = options.throwNotFound === true;\n\n  try {\n    const content = fs.readFileSync(filepath, 'utf8');\n\n    return content;\n  } catch (error: any) {\n    if (\n      throwNotFound === false &&\n      (error.code === 'ENOENT' || error.code === 'EISDIR')\n    ) {\n      return null;\n    }\n\n    throw error;\n  }\n}\n\nexport { readFile, readFileSync };\n"], "mappings": ";;;;;;;;AAAA;;;;AAEA,eAAeA,eAAf,CACEC,QADF,EAEEC,QAFF,EAGmB;EACjB,OAAO,IAAIC,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAA2B;IAC5CC,WAAA,CAAGC,QAAH,CAAYN,QAAZ,EAAsBC,QAAtB,EAAgC,CAACM,KAAD,EAAQC,QAAR,KAA2B;MACzD,IAAID,KAAJ,EAAW;QACTH,MAAM,CAACG,KAAD,CAAN;QACA;MACD;;MAEDJ,OAAO,CAACK,QAAD,CAAP;IACD,CAPD;EAQD,CATM,CAAP;AAUD;;AAMD,eAAeF,QAAf,CACEG,QADF,EAEEC,OAAgB,GAAG,EAFrB,EAG0B;EACxB,MAAMC,aAAa,GAAGD,OAAO,CAACC,aAAR,KAA0B,IAAhD;;EAEA,IAAI;IACF,MAAMC,OAAO,GAAG,MAAMb,eAAe,CAACU,QAAD,EAAW,MAAX,CAArC;IAEA,OAAOG,OAAP;EACD,CAJD,CAIE,OAAOL,KAAP,EAAmB;IACnB,IACEI,aAAa,KAAK,KAAlB,KACCJ,KAAK,CAACM,IAAN,KAAe,QAAf,IAA2BN,KAAK,CAACM,IAAN,KAAe,QAD3C,CADF,EAGE;MACA,OAAO,IAAP;IACD;;IAED,MAAMN,KAAN;EACD;AACF;;AAED,SAASO,YAAT,CAAsBL,QAAtB,EAAwCC,OAAgB,GAAG,EAA3D,EAA8E;EAC5E,MAAMC,aAAa,GAAGD,OAAO,CAACC,aAAR,KAA0B,IAAhD;;EAEA,IAAI;IACF,MAAMC,OAAO,GAAGP,WAAA,CAAGS,YAAH,CAAgBL,QAAhB,EAA0B,MAA1B,CAAhB;;IAEA,OAAOG,OAAP;EACD,CAJD,CAIE,OAAOL,KAAP,EAAmB;IACnB,IACEI,aAAa,KAAK,KAAlB,KACCJ,KAAK,CAACM,IAAN,KAAe,QAAf,IAA2BN,KAAK,CAACM,IAAN,KAAe,QAD3C,CADF,EAGE;MACA,OAAO,IAAP;IACD;;IAED,MAAMN,KAAN;EACD;AACF"}