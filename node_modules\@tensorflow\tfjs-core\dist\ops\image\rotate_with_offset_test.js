/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { getTestImageAsTensor4d } from '../../image_test_util';
import * as tf from '../../index';
import { BROWSER_ENVS, describeWithFlags } from '../../jasmine_util';
import { expectArraysClose } from '../../test_util';
describeWithFlags('rotateWithOffset', BROWSER_ENVS, () => {
    it('should rotate counterclockwise 90 degrees', async () => {
        const rotatedPixels = tf.image.rotateWithOffset(getTestImageAsTensor4d(), 90 * Math.PI / 180)
            .toInt();
        const rotatedPixelsData = await rotatedPixels.data();
        const expected = [
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 230, 133, 18, 255, 233, 148, 31, 255, 222, 164, 41, 255, 191,
            170, 61, 255, 135, 166, 86, 255, 71, 143, 97, 255, 15, 115, 105, 255,
            0, 102, 113, 255, 241, 153, 43, 255, 250, 177, 64, 255, 247, 201, 81,
            255, 218, 210, 103, 255, 162, 206, 127, 255, 98, 181, 135, 255, 39, 150,
            141, 255, 6, 133, 140, 255, 224, 156, 55, 255, 241, 188, 82, 255, 243,
            220, 106, 255, 213, 230, 126, 255, 155, 226, 146, 255, 94, 206, 156, 255,
            37, 177, 164, 255, 3, 158, 162, 255, 212, 157, 75, 255, 230, 193, 104,
            255, 235, 227, 128, 255, 201, 236, 142, 255, 141, 232, 162, 255, 87, 220,
            175, 255, 35, 200, 186, 255, 4, 182, 186, 255, 200, 155, 98, 255, 220,
            190, 128, 255, 225, 228, 151, 255, 191, 239, 165, 255, 130, 235, 179, 255,
            76, 225, 193, 255, 30, 209, 205, 255, 0, 194, 204, 255, 183, 138, 109,
            255, 202, 174, 137, 255, 211, 216, 162, 255, 184, 234, 181, 255, 121, 231,
            192, 255, 64, 219, 201, 255, 19, 203, 211, 255, 0, 189, 209, 255, 171,
            120, 117, 255, 186, 152, 140, 255, 199, 198, 168, 255, 179, 226, 194, 255,
            119, 226, 206, 255, 62, 217, 213, 255, 19, 204, 222, 255, 0, 192, 221,
            255
        ];
        expectArraysClose(expected, rotatedPixelsData);
    });
    it('should rotate clockwise 90 degrees', async () => {
        const rotatedPixels = tf.image.rotateWithOffset(getTestImageAsTensor4d(), -90 * Math.PI / 180)
            .toInt();
        const rotatedPixelsData = await rotatedPixels.data();
        const expected = [
            0, 0, 0, 0, 0, 193, 228, 255, 18, 200, 224, 255, 55, 207, 212,
            255, 108, 214, 202, 255, 163, 208, 187, 255, 179, 176, 159, 255, 168, 129,
            130, 255, 0, 0, 0, 0, 0, 192, 221, 255, 19, 204, 222, 255, 62,
            217, 213, 255, 119, 226, 206, 255, 179, 226, 194, 255, 199, 198, 168, 255,
            186, 152, 140, 255, 0, 0, 0, 0, 0, 189, 209, 255, 19, 203, 211,
            255, 64, 219, 201, 255, 121, 231, 192, 255, 184, 234, 181, 255, 211, 216,
            162, 255, 202, 174, 137, 255, 0, 0, 0, 0, 0, 194, 204, 255, 30,
            209, 205, 255, 76, 225, 193, 255, 130, 235, 179, 255, 191, 239, 165, 255,
            225, 228, 151, 255, 220, 190, 128, 255, 0, 0, 0, 0, 4, 182, 186,
            255, 35, 200, 186, 255, 87, 220, 175, 255, 141, 232, 162, 255, 201, 236,
            142, 255, 235, 227, 128, 255, 230, 193, 104, 255, 0, 0, 0, 0, 3,
            158, 162, 255, 37, 177, 164, 255, 94, 206, 156, 255, 155, 226, 146, 255,
            213, 230, 126, 255, 243, 220, 106, 255, 241, 188, 82, 255, 0, 0, 0,
            0, 6, 133, 140, 255, 39, 150, 141, 255, 98, 181, 135, 255, 162, 206,
            127, 255, 218, 210, 103, 255, 247, 201, 81, 255, 250, 177, 64, 255, 0,
            0, 0, 0, 0, 102, 113, 255, 15, 115, 105, 255, 71, 143, 97, 255,
            135, 166, 86, 255, 191, 170, 61, 255, 222, 164, 41, 255, 233, 148, 31,
            255
        ];
        expectArraysClose(expected, rotatedPixelsData);
    });
    it('offset center of rotation', async () => {
        const rotatedPixels = tf.image
            .rotateWithOffset(getTestImageAsTensor4d(), 45 * Math.PI / 180, 0, [0.25, 0.75])
            .toInt();
        const rotatedPixelsData = await rotatedPixels.data();
        const expected = [
            224, 156, 55, 255, 250, 177, 64, 255, 247, 201, 81, 255, 222, 164, 41,
            255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 230, 193, 104, 255, 243, 220, 106, 255, 247, 201, 81, 255, 218,
            210, 103, 255, 135, 166, 86, 255, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 225, 228, 151, 255, 235, 227, 128, 255, 213, 230, 126,
            255, 162, 206, 127, 255, 98, 181, 135, 255, 71, 143, 97, 255, 0, 0,
            0, 0, 0, 0, 0, 0, 225, 228, 151, 255, 191, 239, 165, 255, 141,
            232, 162, 255, 94, 206, 156, 255, 98, 181, 135, 255, 39, 150, 141, 255,
            0, 102, 113, 255, 0, 0, 0, 0, 184, 234, 181, 255, 130, 235, 179,
            255, 76, 225, 193, 255, 87, 220, 175, 255, 37, 177, 164, 255, 6, 133,
            140, 255, 6, 133, 140, 255, 0, 0, 0, 0, 119, 226, 206, 255, 64,
            219, 201, 255, 76, 225, 193, 255, 30, 209, 205, 255, 4, 182, 186, 255,
            3, 158, 162, 255, 0, 0, 0, 0, 0, 0, 0, 0, 62, 217, 213,
            255, 62, 217, 213, 255, 19, 203, 211, 255, 0, 194, 204, 255, 0, 194,
            204, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 55,
            207, 212, 255, 19, 204, 222, 255, 0, 192, 221, 255, 0, 189, 209, 255,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0
        ];
        expectArraysClose(expected, rotatedPixelsData);
    });
    it('offset center of rotation with white fill', async () => {
        const rotatedPixels = tf.image
            .rotateWithOffset(getTestImageAsTensor4d(), 45 * Math.PI / 180, 255, [0.25, 0.75])
            .toInt();
        const rotatedPixelsData = await rotatedPixels.data();
        const expected = [
            224, 156, 55, 255, 250, 177, 64, 255, 247, 201, 81, 255, 222, 164, 41,
            255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
            255, 255, 230, 193, 104, 255, 243, 220, 106, 255, 247, 201, 81, 255, 218,
            210, 103, 255, 135, 166, 86, 255, 255, 255, 255, 255, 255, 255, 255, 255,
            255, 255, 255, 255, 225, 228, 151, 255, 235, 227, 128, 255, 213, 230, 126,
            255, 162, 206, 127, 255, 98, 181, 135, 255, 71, 143, 97, 255, 255, 255,
            255, 255, 255, 255, 255, 255, 225, 228, 151, 255, 191, 239, 165, 255, 141,
            232, 162, 255, 94, 206, 156, 255, 98, 181, 135, 255, 39, 150, 141, 255,
            0, 102, 113, 255, 255, 255, 255, 255, 184, 234, 181, 255, 130, 235, 179,
            255, 76, 225, 193, 255, 87, 220, 175, 255, 37, 177, 164, 255, 6, 133,
            140, 255, 6, 133, 140, 255, 255, 255, 255, 255, 119, 226, 206, 255, 64,
            219, 201, 255, 76, 225, 193, 255, 30, 209, 205, 255, 4, 182, 186, 255,
            3, 158, 162, 255, 255, 255, 255, 255, 255, 255, 255, 255, 62, 217, 213,
            255, 62, 217, 213, 255, 19, 203, 211, 255, 0, 194, 204, 255, 0, 194,
            204, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 55,
            207, 212, 255, 19, 204, 222, 255, 0, 192, 221, 255, 0, 189, 209, 255,
            255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
            255
        ];
        expectArraysClose(expected, rotatedPixelsData);
    });
    it('throws when input is int32', async () => {
        expect(() => tf.image.rotateWithOffset(tf.tensor4d([1, 2, 3, 255], [1, 1, 1, 4], 'int32'), 90 * Math.PI / 180))
            .toThrowError(/Argument 'image' passed to 'rotateWithOffset' must be float32/);
    });
});
//# sourceMappingURL=data:application/json;base64,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