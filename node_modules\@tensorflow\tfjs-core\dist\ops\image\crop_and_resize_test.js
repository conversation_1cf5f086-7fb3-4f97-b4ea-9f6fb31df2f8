/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../../index';
import { ALL_ENVS, describeWithFlags } from '../../jasmine_util';
import { expectArraysClose } from '../../test_util';
describeWithFlags('cropAndResize', ALL_ENVS, () => {
    it('1x1-bilinear', async () => {
        const image = tf.tensor4d([1, 2, 3, 4], [1, 2, 2, 1]);
        const boxes = tf.tensor2d([0, 0, 1, 1], [1, 4]);
        const boxInd = tf.tensor1d([0], 'int32');
        const output = tf.image.cropAndResize(image, boxes, boxInd, [1, 1], 'bilinear', 0);
        expect(output.shape).toEqual([1, 1, 1, 1]);
        expect(output.dtype).toBe('float32');
        expectArraysClose(await output.data(), [2.5]);
    });
    it('5x5-bilinear, no change in shape', async () => {
        const image = tf.ones([1, 5, 5, 3]);
        const boxes = tf.tensor2d([0, 0, 1, 1], [1, 4]);
        const boxInd = tf.tensor1d([0], 'int32');
        const output = tf.image.cropAndResize(image, boxes, boxInd, [5, 5], 'bilinear', 0);
        expect(output.shape).toEqual([1, 5, 5, 3]);
        expect(output.dtype).toBe('float32');
        expectArraysClose(await output.data(), await image.data());
    });
    it('5x5-bilinear, no arguments passed in for method or extrapolation', async () => {
        const image = tf.ones([1, 5, 5, 3]);
        const boxes = tf.tensor2d([0, 0, 1, 1], [1, 4]);
        const boxInd = tf.tensor1d([0], 'int32');
        const output = tf.image.cropAndResize(image, boxes, boxInd, [5, 5]);
        expect(output.shape).toEqual([1, 5, 5, 3]);
        expect(output.dtype).toBe('float32');
        expectArraysClose(await output.data(), await image.data());
    });
    it('5x5-bilinear, just a crop, no resize', async () => {
        const image = tf.ones([1, 6, 6, 3]);
        const boxes = tf.tensor2d([0.5, 0.5, 1, 1], [1, 4]);
        const boxInd = tf.tensor1d([0], 'int32');
        const output = tf.image.cropAndResize(image, boxes, boxInd, [3, 3], 'bilinear', 0);
        expect(output.shape).toEqual([1, 3, 3, 3]);
        expect(output.dtype).toBe('float32');
        expectArraysClose(await output.data(), await tf.ones([1, 3, 3, 3]).data());
    });
    it('1x1-nearest', async () => {
        const image = tf.tensor4d([1, 2, 3, 4], [1, 2, 2, 1]);
        const boxes = tf.tensor2d([0, 0, 1, 1], [1, 4]);
        const boxInd = tf.tensor1d([0], 'int32');
        const output = tf.image.cropAndResize(image, boxes, boxInd, [1, 1], 'nearest', 0);
        expect(output.shape).toEqual([1, 1, 1, 1]);
        expect(output.dtype).toBe('float32');
        expectArraysClose(await output.data(), [4.0]);
    });
    it('1x1Flipped-bilinear', async () => {
        const image = tf.tensor4d([1, 2, 3, 4], [1, 2, 2, 1]);
        const boxes = tf.tensor2d([1, 1, 0, 0], [1, 4]);
        const boxInd = tf.tensor1d([0], 'int32');
        const output = tf.image.cropAndResize(image, boxes, boxInd, [1, 1], 'bilinear', 0);
        expect(output.shape).toEqual([1, 1, 1, 1]);
        expect(output.dtype).toBe('float32');
        expectArraysClose(await output.data(), [2.5]);
    });
    it('1x1Flipped-nearest', async () => {
        const image = tf.tensor4d([1, 2, 3, 4], [1, 2, 2, 1]);
        const boxes = tf.tensor2d([1, 1, 0, 0], [1, 4]);
        const boxInd = tf.tensor1d([0], 'int32');
        const output = tf.image.cropAndResize(image, boxes, boxInd, [1, 1], 'nearest', 0);
        expect(output.shape).toEqual([1, 1, 1, 1]);
        expect(output.dtype).toBe('float32');
        expectArraysClose(await output.data(), [4.0]);
    });
    it('3x3-bilinear', async () => {
        const image = tf.tensor4d([1, 2, 3, 4], [1, 2, 2, 1]);
        const boxes = tf.tensor2d([0, 0, 1, 1], [1, 4]);
        const boxInd = tf.tensor1d([0], 'int32');
        const output = tf.image.cropAndResize(image, boxes, boxInd, [3, 3], 'bilinear', 0);
        expect(output.shape).toEqual([1, 3, 3, 1]);
        expect(output.dtype).toBe('float32');
        expectArraysClose(await output.data(), [1, 1.5, 2, 2, 2.5, 3, 3, 3.5, 4]);
    });
    it('3x3-nearest', async () => {
        const image = tf.tensor4d([1, 2, 3, 4], [1, 2, 2, 1]);
        const boxes = tf.tensor2d([0, 0, 1, 1], [1, 4]);
        const boxInd = tf.tensor1d([0], 'int32');
        const output = tf.image.cropAndResize(image, boxes, boxInd, [3, 3], 'nearest', 0);
        expect(output.shape).toEqual([1, 3, 3, 1]);
        expect(output.dtype).toBe('float32');
        expectArraysClose(await output.data(), [1, 2, 2, 3, 4, 4, 3, 4, 4]);
    });
    it('3x3Flipped-bilinear', async () => {
        const image = tf.tensor4d([1, 2, 3, 4], [1, 2, 2, 1]);
        const boxes = tf.tensor2d([1, 1, 0, 0], [1, 4]);
        const boxInd = tf.tensor1d([0], 'int32');
        const output = tf.image.cropAndResize(image, boxes, boxInd, [3, 3], 'bilinear', 0);
        expect(output.shape).toEqual([1, 3, 3, 1]);
        expect(output.dtype).toBe('float32');
        expectArraysClose(await output.data(), [4, 3.5, 3, 3, 2.5, 2, 2, 1.5, 1]);
    });
    it('3x3Flipped-nearest', async () => {
        const image = tf.tensor4d([1, 2, 3, 4], [1, 2, 2, 1]);
        const boxes = tf.tensor2d([1, 1, 0, 0], [1, 4]);
        const boxInd = tf.tensor1d([0], 'int32');
        const output = tf.image.cropAndResize(image, boxes, boxInd, [3, 3], 'nearest', 0);
        expect(output.shape).toEqual([1, 3, 3, 1]);
        expect(output.dtype).toBe('float32');
        expectArraysClose(await output.data(), [4, 4, 3, 4, 4, 3, 2, 2, 1]);
    });
    it('3x3to2x2-bilinear', async () => {
        const image = tf.tensor4d([1, 2, 3, 4, 5, 6, 7, 8, 9], [1, 3, 3, 1]);
        const boxes = tf.tensor2d([0, 0, 1, 1, 0, 0, 0.5, 0.5], [2, 4]);
        const boxInd = tf.tensor1d([0, 0], 'int32');
        const output = tf.image.cropAndResize(image, boxes, boxInd, [2, 2], 'bilinear', 0);
        expect(output.shape).toEqual([2, 2, 2, 1]);
        expect(output.dtype).toBe('float32');
        expectArraysClose(await output.data(), [1, 3, 7, 9, 1, 2, 4, 5]);
    });
    it('3x3to2x2-nearest', async () => {
        const image = tf.tensor4d([1, 2, 3, 4, 5, 6, 7, 8, 9], [1, 3, 3, 1]);
        const boxes = tf.tensor2d([0, 0, 1, 1, 0, 0, 0.5, 0.5], [2, 4]);
        const boxInd = tf.tensor1d([0, 0], 'int32');
        const output = tf.image.cropAndResize(image, boxes, boxInd, [2, 2], 'nearest', 0);
        expect(output.shape).toEqual([2, 2, 2, 1]);
        expect(output.dtype).toBe('float32');
        expectArraysClose(await output.data(), [1, 3, 7, 9, 1, 2, 4, 5]);
    });
    it('3x3to2x2Flipped-bilinear', async () => {
        const image = tf.tensor4d([1, 2, 3, 4, 5, 6, 7, 8, 9], [1, 3, 3, 1]);
        const boxes = tf.tensor2d([1, 1, 0, 0, 0.5, 0.5, 0, 0], [2, 4]);
        const boxInd = tf.tensor1d([0, 0], 'int32');
        const output = tf.image.cropAndResize(image, boxes, boxInd, [2, 2], 'bilinear', 0);
        expect(output.shape).toEqual([2, 2, 2, 1]);
        expect(output.dtype).toBe('float32');
        expectArraysClose(await output.data(), [9, 7, 3, 1, 5, 4, 2, 1]);
    });
    it('3x3to2x2Flipped-nearest', async () => {
        const image = tf.tensor4d([1, 2, 3, 4, 5, 6, 7, 8, 9], [1, 3, 3, 1]);
        const boxes = tf.tensor2d([1, 1, 0, 0, 0.5, 0.5, 0, 0], [2, 4]);
        const boxInd = tf.tensor1d([0, 0], 'int32');
        const output = tf.image.cropAndResize(image, boxes, boxInd, [2, 2], 'nearest', 0);
        expect(output.shape).toEqual([2, 2, 2, 1]);
        expect(output.dtype).toBe('float32');
        expectArraysClose(await output.data(), [9, 7, 3, 1, 5, 4, 2, 1]);
    });
    it('3x3-BoxisRectangular', async () => {
        const image = tf.tensor4d([1, 2, 3, 4], [1, 2, 2, 1]);
        const boxes = tf.tensor2d([0, 0, 1, 1.5], [1, 4]);
        const boxInd = tf.tensor1d([0], 'int32');
        const output = tf.image.cropAndResize(image, boxes, boxInd, [3, 3], 'bilinear', 0);
        expect(output.shape).toEqual([1, 3, 3, 1]);
        expect(output.dtype).toBe('float32');
        expectArraysClose(await output.data(), [1, 1.75, 0, 2, 2.75, 0, 3, 3.75, 0]);
    });
    it('3x3-BoxisRectangular-nearest', async () => {
        const image = tf.tensor4d([1, 2, 3, 4], [1, 2, 2, 1]);
        const boxes = tf.tensor2d([0, 0, 1, 1.5], [1, 4]);
        const boxInd = tf.tensor1d([0], 'int32');
        const output = tf.image.cropAndResize(image, boxes, boxInd, [3, 3], 'nearest', 0);
        expect(output.shape).toEqual([1, 3, 3, 1]);
        expect(output.dtype).toBe('float32');
        expectArraysClose(await output.data(), [1, 2, 0, 3, 4, 0, 3, 4, 0]);
    });
    it('2x2to3x3-Extrapolated', async () => {
        const val = -1;
        const image = tf.tensor4d([1, 2, 3, 4], [1, 2, 2, 1]);
        const boxes = tf.tensor2d([-1, -1, 1, 1], [1, 4]);
        const boxInd = tf.tensor1d([0], 'int32');
        const output = tf.image.cropAndResize(image, boxes, boxInd, [3, 3], 'bilinear', val);
        expect(output.shape).toEqual([1, 3, 3, 1]);
        expect(output.dtype).toBe('float32');
        expectArraysClose(await output.data(), [val, val, val, val, 1, 2, val, 3, 4]);
    });
    it('2x2to3x3-Extrapolated-Float', async () => {
        const val = -1.5;
        const image = tf.tensor4d([1, 2, 3, 4], [1, 2, 2, 1]);
        const boxes = tf.tensor2d([-1, -1, 1, 1], [1, 4]);
        const boxInd = tf.tensor1d([0], 'int32');
        const output = tf.image.cropAndResize(image, boxes, boxInd, [3, 3], 'bilinear', val);
        expect(output.shape).toEqual([1, 3, 3, 1]);
        expect(output.dtype).toBe('float32');
        expectArraysClose(await output.data(), [val, val, val, val, 1, 2, val, 3, 4]);
    });
    it('2x2to3x3-NoCrop', async () => {
        const val = -1.0;
        const image = tf.tensor4d([1, 2, 3, 4], [1, 2, 2, 1]);
        const boxes = tf.tensor2d([], [0, 4]);
        const boxInd = tf.tensor1d([], 'int32');
        const output = tf.image.cropAndResize(image, boxes, boxInd, [3, 3], 'bilinear', val);
        expect(output.shape).toEqual([0, 3, 3, 1]);
        expect(output.dtype).toBe('float32');
        expectArraysClose(await output.data(), []);
    });
    it('MultipleBoxes-DifferentBoxes', async () => {
        const image = tf.tensor4d([1, 2, 3, 4, 5, 6, 7, 8], [2, 2, 2, 1]);
        const boxes = tf.tensor2d([0, 0, 1, 1.5, 0, 0, 1.5, 1], [2, 4]);
        const boxInd = tf.tensor1d([0, 1], 'int32');
        const output = tf.image.cropAndResize(image, boxes, boxInd, [3, 3], 'bilinear', 0);
        expect(output.shape).toEqual([2, 3, 3, 1]);
        expect(output.dtype).toBe('float32');
        expectArraysClose(await output.data(), [1, 1.75, 0, 2, 2.75, 0, 3, 3.75, 0, 5, 5.5, 6, 6.5, 7, 7.5, 0, 0, 0]);
    });
    it('MultipleBoxes-DifferentBoxes-Nearest', async () => {
        const image = tf.tensor4d([1, 2, 3, 4, 5, 6, 7, 8], [2, 2, 2, 1]);
        const boxes = tf.tensor2d([0, 0, 1, 1.5, 0, 0, 2, 1], [2, 4]);
        const boxInd = tf.tensor1d([0, 1], 'int32');
        const output = tf.image.cropAndResize(image, boxes, boxInd, [3, 3], 'nearest', 0);
        expect(output.shape).toEqual([2, 3, 3, 1]);
        expect(output.dtype).toBe('float32');
        expectArraysClose(await output.data(), [1, 2, 0, 3, 4, 0, 3, 4, 0, 5, 6, 6, 7, 8, 8, 0, 0, 0]);
    });
    it('int32 image returns float output', async () => {
        const image = tf.tensor4d([1, 2, 3, 4, 5, 6, 7, 8], [2, 2, 2, 1], 'int32');
        const boxes = tf.tensor2d([0, 0, 1, 1.5, 0, 0, 1.5, 1], [2, 4]);
        const boxInd = tf.tensor1d([0, 1], 'int32');
        const output = tf.image.cropAndResize(image, boxes, boxInd, [3, 3], 'bilinear', 0);
        expect(output.shape).toEqual([2, 3, 3, 1]);
        expect(output.dtype).toBe('float32');
        expectArraysClose(await output.data(), [1, 1.75, 0, 2, 2.75, 0, 3, 3.75, 0, 5, 5.5, 6, 6.5, 7, 7.5, 0, 0, 0]);
    });
});
//# sourceMappingURL=data:application/json;base64,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