Name

    ANGLE_robust_resource_initialization.txt

Name Strings

    ANGLE_robust_resource_intialization

Contributors

    <PERSON>, <PERSON>, Google

Contacts

    <PERSON>, Google (shannon<PERSON> 'at' google.com)

Status

    Draft

Version

    Version 1, January 7, 2015

Number

    OpenGL ES Extension TBD

Dependencies

    OpenGL ES 2.0 is required.

    This extension is written against the wording of the OpenGL ES
    3.1 specification.

    EGL_EXT_create_context_robust_initialization is used to request a
    context supporting this extension to perform resource initialization.

Overview

    This extension specifies the behavior for initialization of
    resources such as textures and buffers to default values. This
    initialization ensures that access will not be provided by the
    GL to previously allocated data not owned by the application.

New Types

    None

New Procedures and Functions

    None

New Tokens

    Accepted by the <value> parameter of GetBooleanv, GetIntegerv,
    GetFloatv, GetDoublev, and GetInteger54v:

        CONTEXT_ROBUST_RESOURCE_INITIALIZATION_ANGLE    0x93A7


Additions to Chapter 6 of the OpenGL ES 3.1 Specification (Buffer
Objects)

    Replace the last sentence of the first paragraph of section 6.2
    "BufferData":

    If <data> is NULL, and robust resource initialization is enabled,
    the contents of the buffer object's data store are set to zero.
    Otherwise, the contents of the buffer object's data store are
    undefined.

Additions to Chapter 8 of the OpenGL ES 3.1 Specification (Textures and
Samplers)

    Replace the first two sentances of the final paragraph in section
    8.5.3 "Texture Image Structure":

    If the <data> argument of TexImage2D or TexImage3D is NULL, and the
    pixel unpack buffer object is zero, a two- or three-dimensional
    texel array is created with the specified <target>, <level>,
    <internalformat>, <border>, <width>, <height>, and <depth>. If
    robust resource initialization is enabled, the contents of the image
    are initialized as though a zero value were provided for each
    component of each pixel, and processed and transferred to the GL
    as described above. The components comprising this zero-filled data
    are determined by <internalformat>. If robust resource
    initialization is not enabled, the image contents are undefined, and
    no pixel processing is performed. In either case, no pixel values
    are accessed in client memory.

    Replace the first sentence of the fifth paragraph in section 8.8
    "Multisample Textures":

    Upon success, TexStorage2DMultisample deletes any existing image
    for target. If robust resource initialization is enabled, the
    contents of each texel are initialized as though a zero value were
    written to each channel of each sample; otherwise the contents of
    texels are undefined.

    Add to the final paragraph of section 8.17 "Immutable-Format Texture
    Images":

    If robust resource initialization is enabled, the contents of each
    texel is initialized as though a zero value were provided for each
    component of each pixel, and processed and transferred to the GL
    as for a call to the appropriate TexSubImage* call for <target>.
    Otherwise, the contents of texels are undefined.

Additions to Chapter 9 of the OpenGL ES 3.1 Specification (Framebuffers
and Framebuffer Objects)

    Replace the sentence in section 9.2.4 "Renderbuffer Objects"
    beginning "Upon success, RenderbufferStorageMultisample":

    Upon success, RenderbufferStorageMultisample deletes any existing
    data store for the renderbuffer image. If robust resource
    initialization is enabled, the contents of each pixel in the data
    store are initialized as though a zero value was written to each
    channel of each sample; otherwise, the contents of the data store
    are undefined.

Interactions with EGL_ANGLE_create_context_robust_resource

    If the EGL window-system binding API is used to create a context,
    the EGL_ANGLE_create_context_robust_initialization extension is
    supported, and the attribute
    EGL_CONTEXT_OPENGL_ROBUST_INITIALIZATION_ANGLE is set to
    EGL_TRUE when eglCreateContext is called, the resulting context
    will perform robust resource initialization as described above in
    section <section>, and the
    CONTEXT_ROBUST_RESOURCE_INITIALIZATION_ANGLE
    query will return GL_TRUE as described above in section 2.6.1.1.

Issues

    None

Revision History

    Version 1, 2015/01/07 - first draft.
